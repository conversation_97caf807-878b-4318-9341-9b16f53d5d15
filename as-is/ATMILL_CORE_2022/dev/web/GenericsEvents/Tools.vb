﻿Imports System.Web.UI.WebControls
Imports UsersGUI

Friend Class Tools

    Public Shared Sub ShowGenericControl(ByVal root As System.Web.UI.Control, ByVal name As String)
        Dim ctrl As System.Web.UI.Control = UsersGUI.tools.FindControlRecursive(root, name)

        Select Case ctrl.GetType
            Case GetType(myDropDownList)
                ShowDropDownList(root, name)

            Case GetType(myCheckBox)
                ShowCheckBox(root, name)

            Case GetType(myTextBox)
                ShowTextBox(root, name)

            Case GetType(myRadioButton)
                ShowRadioButton(root, name)

            Case Else
                ctrl.Visible = True
        End Select
    End Sub

    Public Shared Sub HideGenericControl(ByVal root As System.Web.UI.Control, ByVal name As String)
        Dim ctrl As System.Web.UI.Control = UsersGUI.tools.FindControlRecursive(root, name)

        Select Case ctrl.GetType
            Case GetType(myDropDownList)
                HideDropDownList(root, name)

            Case GetType(myCheckBox)
                HideCheckBox(root, name)

            Case GetType(myTextBox)
                HideTextBox(root, name)

            Case GetType(myRadioButton)
                HideRadioButton(root, name)

            Case Else
                ctrl.Visible = False
        End Select
    End Sub

    Public Shared Sub ShowDropDownList(ByVal root As System.Web.UI.Control, ByVal name As String)
        Dim ddl As myDropDownList = Nothing
        Dim lbl As Label = Nothing

        ddl = UsersGUI.tools.FindControlRecursive(root, name)
        ddl.Visible = True
        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        lbl.Visible = True
    End Sub

    Public Shared Sub HideDropDownList(ByVal root As System.Web.UI.Control, ByVal name As String)
        Dim ddl As myDropDownList = Nothing
        Dim lbl As Label = Nothing

        ddl = UsersGUI.tools.FindControlRecursive(root, name)
        ddl.SelectedIndex = 0
        ddl.Visible = False
        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        lbl.Visible = False
    End Sub

    Public Shared Sub ShowCheckBox(ByVal root As System.Web.UI.Control, ByVal name As String)
        Dim chk As myCheckBox = Nothing
        Dim lbl As Label = Nothing

        chk = UsersGUI.tools.FindControlRecursive(root, name)
        chk.Visible = True
        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        lbl.Visible = True
    End Sub

    Public Shared Sub HideCheckBox(ByVal root As System.Web.UI.Control, ByVal name As String)
        Dim chk As myCheckBox = Nothing
        Dim lbl As Label = Nothing

        chk = UsersGUI.tools.FindControlRecursive(root, name)
        chk.Checked = False
        chk.Visible = False
        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        lbl.Visible = False
    End Sub

    Public Shared Sub ShowTextBox(ByVal root As System.Web.UI.Control, ByVal name As String, Optional ByVal unit As String = "")
        Dim txt As myTextBox = Nothing
        Dim lbl As Label = Nothing

        txt = UsersGUI.tools.FindControlRecursive(root, name)
        txt.Visible = True
        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        lbl.Visible = True

        If unit <> "" Then
            lbl = UsersGUI.tools.FindControlRecursive(root, name & "_" & unit)
            lbl.Visible = True
        End If
    End Sub

    Public Shared Sub HideTextBox(ByVal root As System.Web.UI.Control, ByVal name As String, Optional ByVal unit As String = "")
        Dim txt As myTextBox = Nothing
        Dim lbl As Label = Nothing

        txt = UsersGUI.tools.FindControlRecursive(root, name)
        txt.Text = String.Empty
        txt.Visible = False
        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        lbl.Visible = False

        If unit <> "" Then
            lbl = UsersGUI.tools.FindControlRecursive(root, name & "_" & unit)
            lbl.Visible = False
        End If
    End Sub

    Public Shared Sub ShowRadioButton(ByVal root As System.Web.UI.Control, ByVal name As String, Optional selected_index As Integer = 0)
        Dim radio As myRadioButton = Nothing
        Dim lbl As Label = Nothing
        Dim div As System.Web.UI.HtmlControls.HtmlGenericControl = Nothing
        Dim i As Integer = 1

        Do
            radio = UsersGUI.tools.FindControlRecursive(root, name & "_" & i)
            If radio IsNot Nothing Then
                radio.Visible = True

                If i = selected_index Then
                    radio.Checked = True
                End If
            End If

            div = UsersGUI.tools.FindControlRecursive(root, "div_rb_" & name & "_" & i)
            If div IsNot Nothing Then
                div.Visible = True
            End If

            i += 1
        Loop While radio IsNot Nothing

        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        If (lbl IsNot Nothing) Then
        	lbl.Visible = True
        End If
    End Sub

    Public Shared Sub HideRadioButton(ByVal root As System.Web.UI.Control, ByVal name As String)
        Dim radio As myRadioButton = Nothing
        Dim lbl As Label = Nothing
        Dim div As System.Web.UI.HtmlControls.HtmlGenericControl = Nothing
        Dim i As Integer = 1

        Do
            radio = UsersGUI.tools.FindControlRecursive(root, name & "_" & i)
            If radio IsNot Nothing Then
                radio.Checked = False
                radio.Visible = False
            End If

            div = UsersGUI.tools.FindControlRecursive(root, "div_rb_" & name & "_" & i)
            If div IsNot Nothing Then
                div.Visible = False
            End If

            i += 1
        Loop While radio IsNot Nothing

        lbl = UsersGUI.tools.FindControlRecursive(root, "lbl_" & name)
        If (lbl IsNot Nothing) Then
        	lbl.Visible = False
        End If
    End Sub

    Public Shared Sub InitRadioButtonTwoOptions(ByVal sender As Object, ByVal asp_name As String, ByVal default_position As Integer)
        Dim rb1 As myRadioButton = Nothing
        Dim rb2 As myRadioButton = Nothing

        If TypeOf sender Is myRadioButton Then
            rb1 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_1")
            rb2 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_2")
        Else
            Return
        End If

        If Not rb1.Checked And Not rb2.Checked Then

            Select Case default_position
                Case 1
                    rb1.Checked = True

                Case 2
                    rb2.Checked = True

                Case Else
                    ' nothing to do
            End Select
        End If

        rb1.AutoPostBack = False
        rb2.AutoPostBack = False
    End Sub

    Public Shared Sub InitRadioButtonThreeOptions(ByVal sender As Object, ByVal asp_name As String, ByVal default_position As Integer)
        Dim rb1 As myRadioButton = Nothing
        Dim rb2 As myRadioButton = Nothing
        Dim rb3 As myRadioButton = Nothing

        If TypeOf sender Is myRadioButton Then
            rb1 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_1")
            rb2 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_2")
            rb3 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_3")
        Else
            Return
        End If

        If Not rb1.Checked And Not rb2.Checked And Not rb3.Checked Then

            Select Case default_position
                Case 1
                    rb1.Checked = True

                Case 2
                    rb2.Checked = True

                Case 3
                    rb3.Checked = True

                Case Else
                    ' nothing to do
            End Select
        End If

        rb1.AutoPostBack = False
        rb2.AutoPostBack = False
        rb3.AutoPostBack = False
    End Sub

    Public Shared Sub InitRadioButtonFourOptions(ByVal sender As Object, ByVal asp_name As String, ByVal default_position As Integer)
        Dim rb1 As myRadioButton = Nothing
        Dim rb2 As myRadioButton = Nothing
        Dim rb3 As myRadioButton = Nothing
        Dim rb4 As myRadioButton = Nothing

        If TypeOf sender Is myRadioButton Then
            rb1 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_1")
            rb2 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_2")
            rb3 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_3")
            rb4 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, asp_name & "_4")
        Else
            Return
        End If

        If Not rb1.Checked And Not rb2.Checked And Not rb3.Checked And Not rb4.Checked Then

            Select Case default_position
                Case 1
                    rb1.Checked = True

                Case 2
                    rb2.Checked = True

                Case 3
                    rb3.Checked = True

                Case 4
                    rb4.Checked = True

                Case Else
                    ' nothing to do
            End Select
        End If

        rb1.AutoPostBack = False
        rb2.AutoPostBack = False
        rb3.AutoPostBack = False
        rb4.AutoPostBack = False
    End Sub

    Public Shared Function EventProIdAutoSelectRecipe(ByVal sender As Object) As Boolean
        Dim ddl_pro_id As myDropDownList = Nothing
        Dim ddl_rec_id As myDropDownList = Nothing
        Dim old_rec_id_index As Integer = 0
        Dim b_recipe_changed As Boolean = False
        Dim str_select As String
        Dim dt As DataTable

        ddl_pro_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PRO_ID"), myDropDownList)
        ddl_rec_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "REC_ID"), myDropDownList)

        If Not IsNothing(ddl_pro_id) AndAlso Not IsNothing(ddl_rec_id) Then

            old_rec_id_index = ddl_rec_id.SelectedIndex

            If ddl_pro_id.SelectedIndex > 0 AndAlso IsNumeric(ddl_pro_id.SelectedItem.Value) Then

                str_select = "SELECT REC_ID, RECIPE_DESCRIPTION FROM VIEW_RECIPE_TO_PRODUCTS WHERE PRO_ID = '" & ddl_pro_id.SelectedItem.Value & "'"
                dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_select, False)

                Select Case dt.Rows.Count
                    Case 0
                        ddl_rec_id.Enabled = False

                        ddl_rec_id.SelectedIndex = 0

                    Case 1
                        ddl_rec_id.Enabled = False

                        For Each dr As DataRow In dt.Rows
                            ddl_rec_id.SelectedIndex = ddl_rec_id.Items.IndexOf(ddl_rec_id.Items.FindByText(dr.Item("RECIPE_DESCRIPTION").ToString))
                        Next

                    Case Else
                        ' should never happen
                        ' lo screen RECIPE_TO_PRODUCTS impedisce di associare lo stesso prodotto a più ricette

                        ddl_rec_id.Enabled = True

                End Select
            Else
                ddl_rec_id.Enabled = True

                ddl_rec_id.SelectedIndex = 0
            End If

            If old_rec_id_index <> ddl_rec_id.SelectedIndex Then
                b_recipe_changed = True
            End If
        End If

        Return b_recipe_changed
    End Function

    Public Shared Function GetRecipeParameter(ByVal rcp_param_name As String, ByVal rec_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT FIELD_VALUE FROM VIEW_RECIPE_PARAMETERS WHERE ASP_NAME=N'" & rcp_param_name & "' AND REC_ID = '" & rec_id & "'"
        Dim dt As DataTable

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' La funzione crea l'evento per ogni field con proprietà <callevent>
    ''' </summary>
    ''' <param name="root"></param>
    ''' <param name="uf"></param>
    ''' <param name="m_config"></param>
    Public Shared Sub AddedCallEvent(root As Web.UI.Control, uf As Field, m_config As UsersGUI.config)
        Dim ctrl As Web.UI.Control = Nothing
        If uf.CallEvent = String.Empty Then
            Return
        End If
        Select Case uf.GetFieldType
            Case EnumFieldType.FieldList
                ctrl = UsersGUI.tools.FindControlRecursive(root, uf.FieldDb)
                If (ctrl IsNot Nothing) Then
                    If uf.CallEvent <> String.Empty Then
                        Dim ge As New GenericsEvents(m_config, CType(ctrl, myDropDownList), uf)
                        ge.CallEvent(CType(ctrl, myDropDownList), uf)
                    End If
                End If
            Case EnumFieldType.FieldCheckBoxYesNo
                ctrl = UsersGUI.tools.FindControlRecursive(root, uf.FieldDb)
                If (ctrl IsNot Nothing) Then
                    If uf.CallEvent <> String.Empty Then
                        Dim ge As New GenericsEvents(m_config, CType(ctrl, myCheckBox), uf)
                        ge.CallEvent(CType(ctrl, myCheckBox), uf)
                    End If
                End If
            Case EnumFieldType.FieldCheckBoxOnOff
                ctrl = UsersGUI.tools.FindControlRecursive(root, uf.FieldDb)
                If (ctrl IsNot Nothing) Then
                    If uf.CallEvent <> String.Empty Then
                        Dim ge As New GenericsEvents(m_config, CType(ctrl, myCheckBox), uf)
                        ge.CallEvent(CType(ctrl, myCheckBox), uf)
                    End If
                End If
            Case EnumFieldType.FieldCheckBoxOneZero
                ctrl = UsersGUI.tools.FindControlRecursive(root, uf.FieldDb)
                If (ctrl IsNot Nothing) Then
                    If uf.CallEvent <> String.Empty Then
                        Dim ge As New GenericsEvents(m_config, CType(ctrl, myCheckBox), uf)
                        ge.CallEvent(CType(ctrl, myCheckBox), uf)
                    End If
                End If
            Case EnumFieldType.FieldChkList
                'Evento non gestito
            Case EnumFieldType.FieldCalendar
                'Evento non gestito
            Case EnumFieldType.FieldLabel
                'Evento non gestito
            Case EnumFieldType.FieldRadioButton

                Dim s_filter As String() = Split(uf.RadioButtonAspFilter, ":")
                If s_filter.Length > 1 Then
                    Select Case s_filter(0)
                        Case "CONFIG"
                            Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                            Dim n As Integer = 0
                            Dim i As Integer = 0

                            For i = 1 To n_ripetizioni
                                ctrl = UsersGUI.tools.FindControlRecursive(root, uf.FieldName & "_" & i)
                                If (ctrl IsNot Nothing) Then
                                    If uf.CallEvent <> String.Empty Then
                                        Dim ge As New GenericsEvents(m_config, CType(ctrl, myRadioButton), uf)
                                        ge.CallEvent(CType(ctrl, myRadioButton), uf)
                                    End If
                                End If
                                n += 1
                            Next

                        Case Else
                            Throw New myException.myException(
                                System.Reflection.Assembly.GetExecutingAssembly.GetName.Name,
                                System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name,
                                System.Reflection.MethodBase.GetCurrentMethod().Name,
                                "Modalità di disegno del radio button non gestita: """ & s_filter(0) & """ per il fieldDb """ & uf.FieldDb & """"
                            )

                    End Select
                End If
            Case Else
                ctrl = UsersGUI.tools.FindControlRecursive(root, uf.FieldDb)
                If (ctrl IsNot Nothing) Then
                    If uf.CallEvent <> String.Empty Then
                        Dim ge As New GenericsEvents(m_config, CType(ctrl, myTextBox), uf)
                        ge.CallEvent(CType(ctrl, myTextBox), uf)
                    End If
                End If
        End Select

    End Sub

End Class