﻿Option Strict On

Imports UsersGUI
Imports ReportsTools.myReports
Imports CommonDefines.Defines

Public Class TransferReport

#Region "Configuration"

    Public Class TransferConfig

        ' TODO
        Public Shared SourcesDict As New Dictionary(Of SSCycles, SourceParametersQuery)

        Public Shared SourcesBins As New Dictionary(Of SSCycles, SSBins())

        Public Shared DestinationsBins As New Dictionary(Of SSCycles, SSBins())

    End Class

#End Region

#Region "Constants and classes"

    Public Class SourceParametersQuery
        Public Bin As List(Of PrefixNumber)
        Public Percentage As List(Of PrefixNumber)
    End Class

    Public Class PrefixNumber
        Public Prefix As String
        Public Number As Integer
    End Class

    Protected Class TransferData
        Public ReferenceKey As String
        Public Source As String = String.Empty
        Public SourceId As Integer = m_InvalidInteger
        Public PercentageSetpoint As Double = m_InvalidDblValue
        Public ActualWeight As Double = m_InvalidDblValue

        Public Function ToNameJSON() As String
            Return "{ ""id"": """ & Me.ReferenceKey & """, ""val"": """ & Me.Source & """ }"
        End Function

        Public Function ToValueJSON() As String
            Return "{ ""id"": """ & Me.ReferenceKey & """, ""val"": [ " & If(PercentageSetpoint.Equals(m_InvalidDblValue), "null", Tools.ConvertValueToJs(PercentageSetpoint)) & ", " & Tools.ConvertValueToJs(ActualWeight) & " ] }"
        End Function

    End Class

#End Region

#Region "Methods"

    Public Shared Function GetTransferScript(ByVal mConfig As config, ByVal jobId As Integer) As String
        Dim script As String = String.Empty

        Try
            Dim dateFrom As Date
            Dim dateTo As Date
            Dim productDescription As String
            Dim recipeDescription As String = String.Empty
            Dim jobTotal As Double
            Dim destinationBins As New List(Of String)

            Dim destinationBinsIDs As New List(Of Integer)

            Dim cycleId As SSCycles
            Dim recipeLogId As Integer = m_InvalidInteger

            Dim query As String
            Dim result As DataTable
            Dim data As New List(Of TransferData)

            ' Recover data
            ' 1 - Get info about the job
            query = "SELECT CYC_ID, RECIPE_LOG_ID, PRODUCED_AMOUNT, RECIPE_DESCRIPTION, PRODUCTS.NAME AS PRODUCT_NAME, START_DATE, STOP_DATE FROM PRODUCTION_REPORTS INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID WHERE COUNTER = " & jobId
            result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            If result.Rows.Count > 0 Then
                cycleId = CType(result.Rows(0).Item("CYC_ID"), SSCycles)
                recipeLogId = If(IsDBNull(result.Rows(0).Item("RECIPE_LOG_ID")), m_InvalidInteger, CInt(result.Rows(0).Item("RECIPE_LOG_ID")))
                recipeDescription = myReports.ParseTranslationForJSON(result.Rows(0).Item("RECIPE_DESCRIPTION").ToString())
                jobTotal = Double.Parse(result.Rows(0).Item("PRODUCED_AMOUNT").ToString())
                productDescription = myReports.ParseTranslationForJSON(result.Rows(0).Item("PRODUCT_NAME").ToString())
                dateFrom = CDate(result.Rows(0).Item("START_DATE"))
                dateTo = CDate(result.Rows(0).Item("STOP_DATE"))

                If (TransferConfig.SourcesBins.ContainsKey(cycleId)) Then
                    If (TransferConfig.SourcesBins.Count > 0) Then

                        'recupero i dati della sorgente
                        query = "SELECT SOURCE_CELL, CELLS.DESCRIPTION AS CELL_DESCRIPTION, SUM(WEIGHT) AS EXTRACTED_WEIGHT FROM FLOW_LOGS INNER JOIN CELLS ON SOURCE_CELL = CELLS.ID WHERE COUNTER = " & jobId &
                                " GROUP BY SOURCE_CELL, CELLS.DESCRIPTION"
                        result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                        'Recupero la lista delle sorgenti configurate per il ciclo in essere
                        Dim query_l = From v In TransferConfig.SourcesBins.ToList() Where v.Key = cycleId

                        For Each row As DataRow In result.Rows

                            For Each v In query_l
                                If v.Value.ToList().Contains(CType(row.Item("SOURCE_CELL"), SSBins)) Then

                                    If (TransferConfig.SourcesDict.ContainsKey(cycleId)) Then

                                        Dim query_params As String = "SELECT PARAMETER_VALUE, ASP_NAME,CELLS.ID as CELL_ID, CELLS.DESCRIPTION AS CELL_DESCRIPTION" &
                                            " FROM ORDER_PARAM_VALUES_ARCHIVE" &
                                            " INNER JOIN META_CYCLE_PARAMS ON ORDER_PARAM_VALUES_ARCHIVE.MCP_ID = META_CYCLE_PARAMS.ID" &
                                            " LEFT JOIN CELLS ON (CASE WHEN ISNUMERIC(ORDER_PARAM_VALUES_ARCHIVE.PARAMETER_VALUE + 'e0') = 1 THEN ORDER_PARAM_VALUES_ARCHIVE.PARAMETER_VALUE ELSE -1 END) = CELLS.ID" &
                                            " WHERE COUNTER = " & jobId & " AND PARAMETER_VALUE IS NOT NULL"

                                        Dim result_params As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query_params, False)

                                        Dim query_values = From qv In TransferConfig.SourcesDict.ToList() Where qv.Key = cycleId

                                        For Each qv In query_values

                                            For Each row_params As DataRow In result_params.Rows
                                                Dim currData As TransferData

                                                ' Sources.Bin
                                                Dim referenceKey As String = CheckForMatches(row_params.Item("ASP_NAME").ToString(), qv.Value.Bin)
                                                If Not referenceKey.Equals(String.Empty) Then
                                                    If data.Exists(Function(v1) v1.ReferenceKey.Equals(referenceKey)) Then
                                                        currData = data.Find(Function(v1) v1.ReferenceKey.Equals(referenceKey))
                                                        currData.Source = row_params.Item("CELL_DESCRIPTION").ToString()
                                                        currData.SourceId = CInt(row_params.Item("PARAMETER_VALUE"))
                                                    Else
                                                        data.Add(New TransferData() With {.ReferenceKey = referenceKey, .Source = row_params.Item("CELL_DESCRIPTION").ToString(), .SourceId = CInt(row_params.Item("PARAMETER_VALUE"))})
                                                    End If
                                                    Continue For
                                                End If

                                                ' Sources.Percentage
                                                referenceKey = CheckForMatches(row_params.Item("ASP_NAME").ToString(), qv.Value.Percentage)
                                                If Not referenceKey.Equals(String.Empty) Then
                                                    If data.Exists(Function(v1) v1.ReferenceKey.Equals(referenceKey)) Then
                                                        currData = data.Find(Function(v1) v1.ReferenceKey.Equals(referenceKey))
                                                        currData.PercentageSetpoint = Double.Parse(row_params.Item("PARAMETER_VALUE").ToString())
                                                    Else
                                                        data.Add(New TransferData() With {.ReferenceKey = referenceKey, .PercentageSetpoint = Double.Parse(row_params.Item("PARAMETER_VALUE").ToString())})
                                                    End If
                                                    Continue For
                                                End If
                                            Next
                                        Next

                                        ' 4 - Analyze flows
                                        query = "SELECT SOURCE_CELL, CELLS.DESCRIPTION AS CELL_DESCRIPTION, SUM(WEIGHT) AS EXTRACTED_WEIGHT FROM FLOW_LOGS INNER JOIN CELLS ON SOURCE_CELL = CELLS.ID WHERE COUNTER = " & jobId &
                                        " GROUP BY SOURCE_CELL, CELLS.DESCRIPTION"
                                        result_params = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                                        For Each row_params As DataRow In result_params.Rows
                                            If data.Exists(Function(v1) v1.SourceId.Equals(CInt(row_params.Item("SOURCE_CELL")))) Then
                                                data.Find(Function(v1) v1.SourceId.Equals(CInt(row_params.Item("SOURCE_CELL")))).ActualWeight = Double.Parse(row_params.Item("EXTRACTED_WEIGHT").ToString()) / 1000 ' kgs -> tons
                                            End If
                                        Next
                                    Else
                                        data.Add(New TransferData() With {
                                                        .ReferenceKey = row.Item("SOURCE_CELL").ToString(),
                                                        .Source = row.Item("CELL_DESCRIPTION").ToString(),
                                                        .SourceId = CInt(row.Item("SOURCE_CELL")),
                                                        .PercentageSetpoint = 0,
                                                        .ActualWeight = Double.Parse(row.Item("EXTRACTED_WEIGHT").ToString()) / 1000 ' kgs -> tons
                                        })
                                    End If

                                End If
                            Next
                        Next
                    End If
                Else
                    ' In case of no configuration, don't show the setpoint
                    ' 2b - Analyze flows
                    query = "SELECT SOURCE_CELL, CELLS.DESCRIPTION AS CELL_DESCRIPTION, SUM(WEIGHT) AS EXTRACTED_WEIGHT, STRING_AGG(CONCAT(FLO_ID, FORMAT(FLOW_LOGS.START_TIME, 'yyyyMMddhhmmss')), '') AS REFERENCE FROM FLOW_LOGS INNER JOIN CELLS ON SOURCE_CELL = CELLS.ID WHERE COUNTER = " & jobId &
                    " GROUP BY SOURCE_CELL, CELLS.DESCRIPTION"
                    result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                    For Each row As DataRow In result.Rows
                        data.Add(New TransferData() With {
                        .ReferenceKey = row.Item("REFERENCE").ToString(),
                        .SourceId = CInt(row.Item("SOURCE_CELL")),
                        .Source = row.Item("CELL_DESCRIPTION").ToString(),
                            .PercentageSetpoint = m_InvalidDblValue,
                        .ActualWeight = CDbl(row.Item("EXTRACTED_WEIGHT").ToString()) / 1000
                    })
                    Next
                End If

                ' Remove where the setpoint is invalid
                RemoveAllFromDataList(cycleId, data)

                ' 5 - Retrieve destinations
                query = "SELECT DISTINCT DEST_CELL, CELLS.DESCRIPTION AS CELL_DESCRIPTION FROM FLOW_LOGS INNER JOIN CELLS ON DEST_CELL = CELLS.ID" &
                            " WHERE COUNTER = " & jobId & " ORDER BY DEST_CELL ASC"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                For Each row As DataRow In result.Rows

                    If (TransferConfig.DestinationsBins.ContainsKey(cycleId)) Then

                        Dim query_l = From v In TransferConfig.DestinationsBins.ToList() Where v.Key = cycleId

                        For Each v In query_l
                            If v.Value.ToList().Contains(CType(row.Item("DEST_CELL"), SSBins)) Then
                                destinationBins.Add(row.Item("CELL_DESCRIPTION").ToString())
                            End If
                        Next
                    End If

                Next

                ' 6 - Translate to JSON
                script &= "nameJSON = {"
                script &= """top"": [" &
                            "{ ""id"": ""productDescription"", ""val"": """ & GetTranslationForJSON("Product", mConfig) & """ }," &
                            "{ ""id"": ""recipeDescription"", ""val"": """ & GetTranslationForJSON("Recipe description", mConfig) & """ }," &
                            "{ ""id"": ""jobTotal"", ""val"": """ & GetTranslationForJSON("JOB_TOTAL", mConfig) & """ }," &
                            "{ ""id"": ""cycle"", ""val"": """ & GetTranslationForJSON("CYCLE_NAME", mConfig) & """ }," &
                            "{ ""id"": ""lot"", ""val"": """ & GetTranslationForJSON("Lotto", mConfig) & """ }," &
                            "{ ""id"": ""destinationBin"", ""val"": """ & GetTranslationForJSON("Destination bin", mConfig) & """ }" &
                        "],"
                script &= """product"": [" &
                "{""name"": ""tb_a"", " &
                " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(data.ToArray(), Function(v) v.ToNameJSON())) &
                    "]}," &
                "{""name"": ""tot""," &
                " ""sum"": [""tb_a""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
            "]"
                script &= "};"
                script &= "sessionStorage.setItem(""nameJSON"", JSON.stringify(nameJSON));" &
                            "initializeReport( " & GetIdReport(cycleId) & ", " & EnumTypeReport.TransferReport & ");"

                script &= "valueJSON = {"
                script &= """top"": [" &
                    "{ ""id"": ""job"", ""val"": " & jobId & " }," &
                    "{ ""id"": ""dateFrom"", ""val"": """ & dateFrom.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                    "{ ""id"": ""dateTo"", ""val"": """ & dateTo.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                    "{ ""id"": ""productDescription"", ""val"": """ & productDescription & """ }," &
                    "{ ""id"": ""recipeDescription"", ""val"": " & If(recipeLogId.Equals(m_InvalidInteger) OrElse recipeDescription.Trim().Equals(String.Empty), "null", """" & recipeDescription & """") & " }," &
                    "{ ""id"": ""jobTotal"", ""val"": " & Tools.ConvertValueToJs(Math.Round(jobTotal / 1000, 3)) & " }," &
                    "{ ""id"": ""cycle"", ""val"": """ & GetTranslationForJSON("MAIN_CYCLE_" & CInt(cycleId) & "_TITLE", mConfig) & """ }," &
                    "{ ""id"": ""lot"", ""val"": " & GetLotValueLinkForJob(cycleId, jobId) & " }," &
                    "{ ""id"": ""destinationBin"", ""val"": """ & String.Join(",", destinationBins) & """ }" &
                "],"
                script &= """product"": [" &
                    "{""name"": ""tb_a"", " &
                    " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(data.ToArray(), Function(v) v.ToValueJSON())) &
                    "]}" &
                "]"
                script &= "};"
                script &= "sessionStorage.setItem(""valueJSON"", JSON.stringify(valueJSON));" &
                "updateReport();"
            End If
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Private Shared Function GetIdReport(CycleId As SSCycles) As String
        Dim IdReport As String = "12" 'Report standard per TRANSFER

        'Creare le condizioni nel caso in cui sia necessario chiamamre una configurazione specifica per questo report
        ' TODO

        Return IdReport
    End Function

    Private Shared Sub RemoveAllFromDataList(cycleid As SSCycles, ByRef data As List(Of TransferData))

        Select Case cycleid
            ' TODO customizzazioni per specifico ciclo
            Case Else
                data.RemoveAll(Function(v) v.PercentageSetpoint.Equals(m_InvalidDblValue))
        End Select

    End Sub

    ''' <summary>
    ''' Checks for matches in the sources configuration and, if successful, returns the reference key (the ASP_NAME without the matching configuration string).
    ''' </summary>
    ''' <param name="aspName"></param>
    ''' <param name="configuration"></param>
    ''' <param name="configAttribute"></param>
    ''' <returns></returns>
    Protected Shared Function CheckForMatches(ByVal aspName As String, ByVal pns As List(Of PrefixNumber)) As String
        Dim retVal As String = String.Empty

        Dim matches As New List(Of String)

        Dim num As Integer

        For i = 0 To pns.Count - 1
            If aspName.Contains(pns(i).Prefix) AndAlso IsNumeric(aspName.Replace(pns(i).Prefix, "")) Then
                num = CInt(aspName.Replace(pns(i).Prefix, ""))

                ' The reference key is the concatenation of the index of the PrefixNumber in the config and the index of the parameter in the single PrefixNumber
                If num <= pns(i).Number Then
                    retVal = i & "_" & num
                End If
            End If
        Next

        Return retVal
    End Function

    Protected Shared Function GetParamNamesList(ByVal pns As List(Of PrefixNumber)) As List(Of String)
        Dim retVal As New List(Of String)

        For Each pn As PrefixNumber In pns
            For i = 1 To pn.Number
                retVal.Add(pn.Prefix & i)
            Next
        Next

        Return retVal
    End Function

#End Region

End Class