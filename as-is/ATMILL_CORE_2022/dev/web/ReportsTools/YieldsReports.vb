﻿Option Strict On

Public Class YieldsReports
    Private m_TagsList As New Generic.List(Of TagYield)

    Public Sub New()

    End Sub
    Public Sub AddToList(ByVal IdScale As Integer, ByVal IdTag As Integer)
        Dim t As New TagYield(IdScale, IdTag)

        Me.m_TagsList.Add(t)
    End Sub

    Public Function GetIdTagForScale(ByVal IdScale As Integer) As Integer
        Dim ret_val As Integer = -1

        For Each t As TagYield In Me.m_TagsList
            If t.IdScale = IdScale Then
                ret_val = t.IdTag
                Exit For
            End If
        Next

        Return ret_val
    End Function

    Public ReadOnly Property TagsList() As Generic.List(Of TagYield)
        Get
            Return Me.m_TagsList
        End Get
    End Property

    Public Sub Clear()
        m_TagsList.Clear()
    End Sub

End Class

Public Class TagYield

    Private m_IdScale As Integer
    Private m_IdTag As Integer

    Friend Sub New(ByVal IdScale As Integer, ByVal IdTag As Integer)
        Me.m_IdScale = IdScale
        Me.m_IdTag = IdTag
    End Sub

    Public ReadOnly Property IdTag() As Integer
        Get
            Return Me.m_IdTag
        End Get
    End Property

    Public ReadOnly Property IdScale() As Integer
        Get
            Return Me.m_IdScale
        End Get
    End Property
End Class