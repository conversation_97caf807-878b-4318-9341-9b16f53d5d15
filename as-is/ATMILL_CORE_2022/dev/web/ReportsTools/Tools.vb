﻿Option Strict On

Public Class Tools

    ''' <summary>
    ''' Recupera i dati per il calcolo delle rese periodiche
    ''' </summary>
    ''' <param name="IdTag"></param>
    ''' <param name="whereClause"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetSum(ByVal IdTag As Long, ByVal whereClause As String) As Double
        Dim strSql As String
        Dim dt As DataTable
        Dim ret_val As Double = 0.0

        strSql = "SELECT SUM(FIELD_VALUE) AS TOTVALUE FROM YIELDS_DATA WHERE TAG_ID = '" & IdTag & "'"
        If whereClause <> String.Empty Then
            strSql &= " AND " & whereClause
        End If

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(strSql, False)

        If dt Is Nothing Then
            Return ret_val
        End If

        For Each dr As DataRow In dt.Rows()
            Try
                ret_val = Double.Parse(dr(0).ToString)
            Catch ex As Exception
                ret_val = 0.0
            End Try
        Next

        Return ret_val
    End Function

    Public Shared Function GetSumInteger(ByVal IdTag As Long, ByVal whereClause As String) As Long
        Dim ret_val As Long

        Long.TryParse(GetSum(IdTag, whereClause).ToString, ret_val)

        Return ret_val
    End Function

    ''' <summary>
    ''' Recupera l'ultimo valore letto per la tag
    ''' </summary>
    ''' <param name="TagId"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetVal(ByVal TagId As Long) As Double
        Dim ret_val As Double = 0.0
        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable

        sSelect = "SELECT FIELD_VALUE FROM CURRENT_VALUES WHERE TAG_ID = '" & TagId & "'"
        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)
        If dt.Rows.Count <= 0 Then
            Return ret_val
        End If

        For Each dr As Data.DataRow In dt.Rows
            Try
                ret_val = Double.Parse(dr("FIELD_VALUE").ToString)
            Catch ex As Exception
                ret_val = 0.0
            End Try

        Next

        Return ret_val
    End Function

    Public Shared Function GetValInteger(ByVal IdTag As Long) As Long
        Dim ret_val As Long

        Long.TryParse(GetVal(IdTag).ToString, ret_val)

        Return ret_val
    End Function

    ''' <summary>
    ''' Recupera i dati per il calcolo delle rese di job
    ''' </summary>
    ''' <param name="IdTag"></param>
    ''' <param name="IdJob"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetJobVal(ByVal IdTag As Long, ByVal IdJob As Integer) As Double
        Dim strSql As String
        Dim dt As DataTable
        Dim ret_val As Double = 0.0

        strSql = "SELECT FIELD_VALUE FROM YIELDS_DATA WHERE TAG_ID = '" & IdTag & "' AND JOB_ID ='" & IdJob & "'"

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(strSql, False)

        If dt Is Nothing Then
            Return ret_val
        End If

        For Each dr As DataRow In dt.Rows()
            Try
                ret_val = Double.Parse(dr(0).ToString)
            Catch ex As Exception
                ret_val = 0.0
            End Try
        Next

        Return ret_val
    End Function

    ' funzione di appoggio allo script js di rese per la parte di nomi delle scale
    Public Shared Function BuildNameArray(ByVal scales As List(Of scaleName)) As String

        Dim ret_val As String = String.Empty

        For Each scale As scaleName In scales

            If ret_val <> String.Empty Then
                ret_val &= ", "
            End If

            ret_val &= "{'id': '" & scale.Id & "', 'val': '" & scale.Name & "'}"
        Next

        Return ret_val

    End Function

    ' funzione di appoggio allo script js di rese per la parte dei valori delle scale
    Public Shared Function BuildValueArray(ByVal scales As List(Of scaleVal)) As String

        Dim ret_val As String = String.Empty

        For Each scale As scaleVal In scales

            If ret_val <> String.Empty Then
                ret_val &= ", "
            End If

            ret_val &= "{'id': '" & scale.Id & "', 'val': " & ConvertValueToJs(scale.ValueDouble) & "}"
        Next

        Return ret_val

    End Function

    ' funzione di appoggio allo script js di rese per la parte dei valori delle scale
    Public Shared Function BuildValueArray(ByVal scales As List(Of shipmentScaleVal)) As String

        Dim ret_val As String = String.Empty

        For Each scale As shipmentScaleVal In scales

            If ret_val <> String.Empty Then
                ret_val &= ", "
            End If

            ' prima #bag, poi qty
            ret_val &= "{'id': '" & scale.Id & "', 'val_int': " & ConvertValueToJs(scale.ValueInteger) & ", 'val_dbl': " & ConvertValueToJs(scale.ValueDouble) & "}"

        Next

        Return ret_val

    End Function

    ' funzione di appoggio allo script js di rese per la parte di allarmi delle scale
    Public Shared Function BuildAlarmArray(ByVal scales As List(Of scaleAlarm)) As String

        Dim ret_val As String = String.Empty

        For Each scale As scaleAlarm In scales

            If ret_val <> String.Empty Then
                ret_val &= ", "
            End If

            ret_val &= "{'id': '" & scale.Id & "', 'val_min': " & ConvertValueToJs(scale.AlarmMin) & ", 'val_max': " & ConvertValueToJs(scale.AlarmMax) & "}"

        Next

        Return ret_val

    End Function

    Public Shared Function ConvertValueToJs(ByVal value As Double) As String
        Return value.ToString.Replace(",", ".")
    End Function

    Public Shared Sub AppendTranslation(ByRef script As String, ByVal config As UsersGUI.config, ByVal key_name As String)
        If script <> String.Empty Then
            script &= ","
        End If

        script &= "{""" & key_name & """:""" & myReports.GetTranslationForJSON(key_name, config) & """}"
    End Sub

    Public Shared Sub GetIntakeData(jobId As Long, ByRef Supplier As String, ByRef Carrier As String)
        Dim query As String = String.Empty
        Dim result As Data.DataTable = Nothing
        Supplier = String.Empty
        Carrier = String.Empty

        ' 3 - Get Supplier data
        query = "select SUP_ID,SUP_NAME,
                            C.FULL_NAME AS SUP_FULL_NAME,
                            C.ADDRESS AS SUP_ADDRESS,
                            C.ZIP_CODE AS SUP_ZIP_CODE,
                            C.CITY AS SUP_CITY,
                            C.COUNTRY AS SUP_COUNTRY,
                            C.TELEPHONE AS SUP_PHONE,
                            C.EMAIL AS SUP_EMAIL
                            from VIEW_PARCELS A INNER JOIN
                            PARCELS_TO_JOBS B ON A.PA_ID = B.PA_ID LEFT JOIN
                            SUPPLIERS C ON A.SUP_ID = C.ID
                            WHERE JOB_COUNTER='" & jobId & "'"
        result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)
        For Each row As DataRow In result.Rows
            Supplier = "<div class='contship'><table class='tableship'><tr><td class='sodd'>Fornitore:</td><td colspan='3'>" & row("SUP_NAME").ToString() & "</td></tr>"
            Supplier &= If(row("SUP_FULL_NAME").ToString() <> String.Empty, "<tr><td class='sodd'>Nome completo: </td><td  colspan='3'>" & row("SUP_FULL_NAME").ToString(), "") & "</td></tr>"
            Supplier &= If(row("SUP_ADDRESS").ToString() <> String.Empty, "<tr><td class='sodd'>Indirizzo: </td><td>" & row("SUP_ADDRESS").ToString(), "") & If(row("SUP_ZIP_CODE").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp<td class='soddb'>CAP: </td><td>" & row("SUP_ZIP_CODE").ToString(), "") & "</td></tr>"
            Supplier &= If(row("SUP_CITY").ToString() <> String.Empty, "<tr><td class='sodd'>Città: </td><td>" & row("SUP_CITY").ToString(), "") & If(row("SUP_COUNTRY").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp;<td class='soddb'>Paese: </td><td>" & row("SUP_COUNTRY").ToString(), "") & "</td></tr>"
            Supplier &= If(row("SUP_PHONE").ToString() <> String.Empty, "<tr><td class='sodd'>Telefono: </td><td  colspan='3'>" & row("SUP_PHONE").ToString(), "") & "</td></tr>"
            Supplier &= If(row("SUP_EMAIL").ToString() <> String.Empty, "<tr><td class='sodd'>E-mail: </td><td  colspan='3'>" & row("SUP_EMAIL").ToString(), "") & "</td></tr></div>"

            Exit For
        Next

        ' 4 - Get Carrier data
        query = "select CAR_ID,CAR_NAME,
                            C.FULL_NAME AS CAR_FULL_NAME,
                            C.ADDRESS AS CAR_ADDRESS,
                            C.ZIP_CODE AS CAR_ZIP_CODE,
                            C.CITY AS CAR_CITY,
                            C.COUNTRY AS CAR_COUNTRY,
                            C.TELEPHONE AS CAR_PHONE,
                            C.EMAIL AS CAR_EMAIL
                            from VIEW_PARCELS A INNER JOIN
                            PARCELS_TO_JOBS B ON A.PA_ID = B.PA_ID LEFT JOIN
                            CARRIERS C ON A.CAR_ID = C.ID
                            WHERE JOB_COUNTER='" & jobId & "'"
        result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)
        For Each row As DataRow In result.Rows
            Carrier = "<div class='contship'><table class='tableship'><tr><td class='sodd'>Trasportatore:</td><td colspan='3'>" & row("CAR_NAME").ToString() & "</td></tr>"
            Carrier &= If(row("CAR_FULL_NAME").ToString() <> String.Empty, "<tr><td class='sodd'>Nome completo: </td><td  colspan='3'>" & row("CAR_FULL_NAME").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_ADDRESS").ToString() <> String.Empty, "<tr><td class='sodd'>Indirizzo: </td><td>" & row("CAR_ADDRESS").ToString(), "") & If(row("CAR_ZIP_CODE").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp<td class='soddb'>CAP: </td><td>" & row("CAR_ZIP_CODE").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_CITY").ToString() <> String.Empty, "<tr><td class='sodd'>Città: </td><td>" & row("CAR_CITY").ToString(), "") & If(row("CAR_COUNTRY").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp;<td class='soddb'>Paese: </td><td>" & row("CAR_COUNTRY").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_PHONE").ToString() <> String.Empty, "<tr><td class='sodd'>Telefono: </td><td  colspan='3'>" & row("CAR_PHONE").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_EMAIL").ToString() <> String.Empty, "<tr><td class='sodd'>E-mail: </td><td  colspan='3'>" & row("CAR_EMAIL").ToString(), "") & "</td></tr></div>"

            Exit For
        Next
    End Sub

    Public Shared Sub GetShipmentData(jobId As Long, ByRef Customer As String, ByRef Carrier As String)
        Dim query As String = String.Empty
        Dim result As Data.DataTable = Nothing
        Customer = String.Empty
        Carrier = String.Empty

        ' 3 - Get Customer data
        query = "select CUS_ID,CUS_NAME,
                            C.FULL_NAME AS CUS_FULL_NAME,
                            C.ADDRESS AS CUS_ADDRESS,
                            C.ZIP_CODE AS CUS_ZIP_CODE,
                            C.CITY AS CUS_CITY,
                            C.COUNTRY AS CUS_COUNTRY,
                            C.TELEPHONE AS CUS_PHONE,
                            C.EMAIL AS CUS_EMAIL
                            from VIEW_SHIPMENTS A INNER JOIN
                            SHIPMENTS_TO_JOBS B ON A.SH_ID = B.SH_ID LEFT JOIN
                            CUSTOMERS C ON A.CUS_ID = C.ID
                            WHERE JOB_COUNTER='" & jobId & "'"
        result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)
        For Each row As DataRow In result.Rows
            Customer = "<div class='contship'><table class='tableship'><tr><td class='sodd'>Cliente:</td><td colspan='3'>" & row("CUS_NAME").ToString() & "</td></tr>"
            Customer &= If(row("CUS_FULL_NAME").ToString() <> String.Empty, "<tr><td class='sodd'>Nome completo: </td><td  colspan='3'>" & row("CUS_FULL_NAME").ToString(), "") & "</td></tr>"
            Customer &= If(row("CUS_ADDRESS").ToString() <> String.Empty, "<tr><td class='sodd'>Indirizzo: </td><td>" & row("CUS_ADDRESS").ToString(), "") & If(row("CUS_ZIP_CODE").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp<td class='soddb'>CAP: </td><td>" & row("CUS_ZIP_CODE").ToString(), "") & "</td></tr>"
            Customer &= If(row("CUS_CITY").ToString() <> String.Empty, "<tr><td class='sodd'>Città: </td><td>" & row("CUS_CITY").ToString(), "") & If(row("CUS_COUNTRY").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp;<td class='soddb'>Paese: </td><td>" & row("CUS_COUNTRY").ToString(), "") & "</td></tr>"
            Customer &= If(row("CUS_PHONE").ToString() <> String.Empty, "<tr><td class='sodd'>Telefono: </td><td  colspan='3'>" & row("CUS_PHONE").ToString(), "") & "</td></tr>"
            Customer &= If(row("CUS_EMAIL").ToString() <> String.Empty, "<tr><td class='sodd'>E-mail: </td><td  colspan='3'>" & row("CUS_EMAIL").ToString(), "") & "</td></tr></div>"

            Exit For
        Next

        ' 4 - Get Carrier data
        query = "select CAR_ID,CAR_NAME,
                            C.FULL_NAME AS CAR_FULL_NAME,
                            C.ADDRESS AS CAR_ADDRESS,
                            C.ZIP_CODE AS CAR_ZIP_CODE,
                            C.CITY AS CAR_CITY,
                            C.COUNTRY AS CAR_COUNTRY,
                            C.TELEPHONE AS CAR_PHONE,
                            C.EMAIL AS CAR_EMAIL
                            from VIEW_SHIPMENTS A INNER JOIN
                            SHIPMENTS_TO_JOBS B ON A.SH_ID = B.SH_ID LEFT JOIN
                            CARRIERS C ON A.CAR_ID = C.ID
                            WHERE JOB_COUNTER='" & jobId & "'"
        result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)
        For Each row As DataRow In result.Rows
            Carrier = "<div class='contship'><table class='tableship'><tr><td class='sodd'>Trasportatore:</td><td colspan='3'>" & row("CAR_NAME").ToString() & "</td></tr>"
            Carrier &= If(row("CAR_FULL_NAME").ToString() <> String.Empty, "<tr><td class='sodd'>Nome completo: </td><td  colspan='3'>" & row("CAR_FULL_NAME").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_ADDRESS").ToString() <> String.Empty, "<tr><td class='sodd'>Indirizzo: </td><td>" & row("CAR_ADDRESS").ToString(), "") & If(row("CAR_ZIP_CODE").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp<td class='soddb'>CAP: </td><td>" & row("CAR_ZIP_CODE").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_CITY").ToString() <> String.Empty, "<tr><td class='sodd'>Città: </td><td>" & row("CAR_CITY").ToString(), "") & If(row("CAR_COUNTRY").ToString() <> String.Empty, "&nbsp;&nbsp;&nbsp;&nbsp;<td class='soddb'>Paese: </td><td>" & row("CAR_COUNTRY").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_PHONE").ToString() <> String.Empty, "<tr><td class='sodd'>Telefono: </td><td  colspan='3'>" & row("CAR_PHONE").ToString(), "") & "</td></tr>"
            Carrier &= If(row("CAR_EMAIL").ToString() <> String.Empty, "<tr><td class='sodd'>E-mail: </td><td  colspan='3'>" & row("CAR_EMAIL").ToString(), "") & "</td></tr></div>"

            Exit For
        Next
    End Sub

End Class

Public Class scaleName

    Private m_id As String
    Private m_name As String

    Sub New(ByVal id As String, ByVal name As String)
        Me.m_id = id
        Me.m_name = name
    End Sub

    Public ReadOnly Property Id As String
        Get
            Return Me.m_id
        End Get
    End Property

    Public ReadOnly Property Name As String
        Get
            Return Me.m_name
        End Get
    End Property

End Class

Public Class scaleVal

    Private m_id As String
    Private m_double_val As Double

    Sub New(ByVal id As String, ByVal double_val As Double)
        Me.m_id = id
        Me.m_double_val = double_val
    End Sub

    Public ReadOnly Property Id As String
        Get
            Return Me.m_id
        End Get
    End Property

    Public ReadOnly Property ValueDouble As Double
        Get
            Return Me.m_double_val
        End Get
    End Property

End Class

Public Class shipmentScaleVal
    Inherits scaleVal

    Private m_integer_val As Long

    Sub New(ByVal id As String, ByVal integer_val As Long, ByVal double_val As Double)
        MyBase.New(id, double_val)
        Me.m_integer_val = integer_val
    End Sub

    Public ReadOnly Property ValueInteger As Long
        Get
            Return Me.m_integer_val
        End Get
    End Property

End Class

Public Class scaleAlarm

    Private m_id As String
    Private m_alarm_min As Double
    Private m_alarm_max As Double

    Sub New(ByVal id As String, ByVal alarm_min As Double, ByVal alarm_max As Double)
        With Me
            .m_id = id
            .m_alarm_min = alarm_min
            .m_alarm_max = alarm_max
        End With
    End Sub

    Public ReadOnly Property Id As String
        Get
            Return Me.m_id
        End Get
    End Property

    Public ReadOnly Property AlarmMin As Double
        Get
            Return Me.m_alarm_min
        End Get
    End Property

    Public ReadOnly Property AlarmMax As Double
        Get
            Return Me.m_alarm_max
        End Get
    End Property

End Class