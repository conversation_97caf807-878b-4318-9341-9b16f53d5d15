﻿Option Strict On

Imports UsersGUI
Imports ReportsTools.myReports
Imports CommonDefines.Defines

Public Class MixingReports

#Region "Configurations"

    Private Class MixingFlowsConfig
        Public Shared MacroIngredients As New ReportFlowReference()
        Public Shared MicroIngredients As New ReportFlowReference()
        Public Shared HandTipping As New ReportFlowReference()
        Public Shared Robot As New ReportFlowReference()
        Public Shared Destination As New ReportFlowReference()
    End Class

    Public Shared ListMixingTypeFiled As New List(Of ListMixingTypeConfig) From {}

    Public Shared MixingCyclesConfig As New List(Of MixingCycle) From {}

#End Region

#Region "Constants and classes"

    Public Class ListMixingTypeConfig
        Property CycleId As Integer
        Property Type As MixingIngredientTypes
        Property Product As Boolean = False
        Property Percentage As Boolean = False
        Property Qty As Boolean = False
        Property Tolerance As Boolean = False
        Property Suffisso As String
        Property Indice_Suffisso As Integer
        Property Unit As String

    End Class

    Public Class MixingUnits
        Public Const Percentage As String = "PERCENTAGE"
        Public Const PartsPerMillion As String = "PPM"
    End Class

    ' To remove all mixer-specific types (added because of C27)
    Public Enum MixingIngredientTypes
        MACRO = 0
        MICRO = 1
        HANDTIPPING = 2
        ROBOT = 3
    End Enum

    Private Class MixingIngredient
        Property ProductID As Integer
        Property Description As String
        Property Quantity As Double
        Property UnitLabel As String
        Property Tolerance As Double = m_InvalidDblValue
        Property Type As MixingIngredientTypes
    End Class

    Private Class MixingRecipe
        Property Description As String
        Property Ingredients As New Dictionary(Of String, MixingIngredient)

        Public Function IngredientsToJsonArray() As String
            Dim retVal = "["
            Dim firstIngredientFlag As Boolean = True

            For Each ingredient As MixingIngredient In Me.Ingredients.Values
                If Not firstIngredientFlag Then
                    retVal &= ",{"
                Else
                    retVal &= "{"
                    firstIngredientFlag = False
                End If

                retVal &= """description"": """ & ingredient.Description & """, ""quantity"": " & Tools.ConvertValueToJs(ingredient.Quantity) & ", ""unit"": """ & ingredient.UnitLabel & """, ""type"": " & ingredient.Type

                If Not ingredient.Tolerance.Equals(m_InvalidDblValue) Then
                    retVal &= ",""tolerance"": " & Tools.ConvertValueToJs(ingredient.Tolerance)
                End If

                retVal &= "}"
            Next

            retVal &= "]"

            Return retVal
        End Function

    End Class

    Private Class MixingBatchIngredient
        Property OriginsDescription As String
        Property RequestedWeight As Double
        Property ActualWeight As Double
        Property ProductID As Integer
        Property IngredientKey As String
    End Class

    Private Enum MixingBatchOutcome
        OK = 0
        KO = 1
        Accepted = 2
    End Enum

    Private Class MixingBatch
        Property BatchNumber As Integer
        Property StartDate As Date
        Property StopDate As Date
        Property Ingredients As List(Of MixingBatchIngredient)
        Property Outcome As MixingBatchOutcome

        Public Overrides Function ToString() As String
            Return "{ ""id"": """ & Me.BatchNumber & """, ""val"": [" &
                "[ " & String.Join(",", Array.ConvertAll(Of MixingBatchIngredient, String)(Me.Ingredients.ToArray(), Function(x) If(x.ActualWeight.Equals(m_InvalidDblValue), "null", Tools.ConvertValueToJs(x.ActualWeight)))) & " ]," &
                "[ " & String.Join(",", Array.ConvertAll(Of MixingBatchIngredient, String)(Me.Ingredients.ToArray(), Function(x) If(x.RequestedWeight.Equals(m_InvalidDblValue), "null", Tools.ConvertValueToJs(x.RequestedWeight)))) & " ]," &
                "[ " & String.Join(",", Array.ConvertAll(Of MixingBatchIngredient, String)(Me.Ingredients.ToArray(), Function(x) """" & x.OriginsDescription & """")) & " ]," &
                """" & Me.StartDate.ToString() & """," &
                """" & Me.StopDate.ToString() & """," &
                """" & Me.Outcome & """" &
            "] }"
        End Function

    End Class

    Public Class MixingCycle
        Property CycleId As Integer
        Property IngredientsFlows As List(Of ReportFlowReference)
        Property DestinationFlow As ReportFlowReference
        Property RequestedBatchesParamId As Integer
        Property RequestedTotalParamId As Integer

        Public Function GetIngredientFlowFamiliesConcat() As String
            Dim retVal As String = String.Empty

            For Each ingredientFlow As ReportFlowReference In Me.IngredientsFlows
                If ingredientFlow.FlowFamilyIds IsNot Nothing Then
                    If ingredientFlow.FlowFamilyIds.Length > 0 Then
                        If retVal.Equals(String.Empty) Then
                            retVal &= String.Join(",", ingredientFlow.FlowFamilyIds.Cast(Of Integer))
                        Else
                            retVal &= "," & String.Join(",", ingredientFlow.FlowFamilyIds.Cast(Of Integer))
                        End If
                    End If
                Else
                    retVal = m_InvalidInteger.ToString
                End If
            Next

            Return retVal
        End Function

        Public Function GetIngredientFlowIdsConcat() As String
            Dim retVal As String = String.Empty

            For Each ingredientFlow As ReportFlowReference In Me.IngredientsFlows
                If ingredientFlow.FlowIds IsNot Nothing Then
                    If ingredientFlow.FlowIds.Length > 0 Then
                        If retVal.Equals(String.Empty) Then
                            retVal &= String.Join(",", ingredientFlow.FlowIds.Cast(Of Integer))
                        Else
                            retVal &= "," & String.Join(",", ingredientFlow.FlowIds.Cast(Of Integer))
                        End If
                    End If
                Else
                    retVal = m_InvalidInteger.ToString
                End If
            Next

            Return retVal
        End Function

    End Class

#End Region

#Region "Methods"

    Public Shared Function GetMixingNameScript(ByVal mConfig As UsersGUI.config) As String
        Dim script As String = String.Empty

        Try
            Dim tab1 As New List(Of scaleName)
            script &= "nameJSON = {" &
                        """top"": [" &
                            "{ ""id"": ""startDate"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("FROM", mConfig) & """ }," &
                            "{ ""id"": ""stopDate"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("TO", mConfig) & """ }," &
                            "{ ""id"": ""recipeDescription"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Recipe description", mConfig) & """ }," &
                            "{ ""id"": ""recipeIngredients"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("INGREDIENTI", mConfig) & """ }," &
                            "{ ""id"": ""requestedBatches"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Required batches", mConfig) & """ }," &
                            "{ ""id"": ""requestedTotal"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Quantità richiesta", mConfig) & """ }," &
                            "{ ""id"": ""executedBatches"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Executed batches", mConfig) & """ }," &
                            "{ ""id"": ""productDescription"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Product", mConfig) & """ }," &
                            "{ ""id"": ""lot"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Lotto", mConfig) & """ }," &
                            "{ ""id"": ""destinationBin"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Destination bin", mConfig) & """ }" &
                        "]" &
                    "};"

            script &= "var nameJSON_string = JSON.stringify(nameJSON);" &
                    "sessionStorage.setItem(""nameJSON"",nameJSON_string);" &
                    "initializeReport( 0, " & EnumTypeReport.MixingReport & ");"
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Public Shared Function GetMixingValueScript(ByVal counter As Long) As String
        Dim script As String = String.Empty

        Try
            Dim cycleId As SSCycles
            Dim cycle As MixingCycle

            ' Required info variables definitions
            Dim startDate As Date
            Dim stopDate As Date

            Dim recipe As New MixingRecipe

            Dim requestedBatches As Integer
            Dim requestedTotal As Double

            Dim executedBatches As Integer
            Dim destinationBins As String = String.Empty
            Dim batches As New List(Of MixingBatch)

            Dim productDescription As String = String.Empty

            ' Retrieve data
            Dim query As String = "SELECT PR.START_DATE, PR.STOP_DATE, PR.RECIPE_DESCRIPTION, PR.RECIPE_LOG_ID, PRODUCTS.NAME AS PRODUCT_NAME, PR.CYC_ID" &
            " FROM PRODUCTION_REPORTS AS PR INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID" &
            " WHERE PR.COUNTER = " & counter
            Dim result As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            If result.Rows.Count > 0 Then
                ' Retrieve generic job info
                startDate = Date.Parse(result.Rows(0).Item("START_DATE").ToString())
                stopDate = Date.Parse(result.Rows(0).Item("STOP_DATE").ToString())
                productDescription = myReports.ParseTranslationForJSON(result.Rows(0).Item("PRODUCT_NAME").ToString())
                recipe.Description = myReports.ParseTranslationForJSON(result.Rows(0).Item("RECIPE_DESCRIPTION").ToString())
                cycleId = CType(result.Rows(0).Item("CYC_ID").ToString(), SSCycles)

                ' Find the corresponding cycle configuration
                cycle = MixingCyclesConfig.Find(Function(c As MixingCycle) c.CycleId.Equals(cycleId))

                ' Retrieve all info about the recipe
                query = "SELECT FIELD_VALUE, ASP_NAME FROM RECIPE_PARAM_VALUES_ARCHIVED INNER JOIN META_RECIPE_PARAMS ON MRP_ID = META_RECIPE_PARAMS.ID WHERE RECIPE_LOG_ID = " & result.Rows(0).Item("RECIPE_LOG_ID").ToString
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                Dim ingredientKey As String

                For Each row As DataRow In result.Rows

                    For Each vasp As ListMixingTypeConfig In ListMixingTypeFiled

                        If (vasp.CycleId <> cycleId) Then
                            Continue For
                        End If

                        If row.Item("ASP_NAME").ToString().Contains(vasp.Suffisso) Then
                            ingredientKey = vasp.Type.ToString() & "_" & row.Item("ASP_NAME").ToString().Split(Char.Parse("_"))(vasp.Indice_Suffisso - 1)

                            If Not IsDBNull(row.Item("FIELD_VALUE")) Then
                                If Not recipe.Ingredients.ContainsKey(ingredientKey) Then
                                    recipe.Ingredients.Add(ingredientKey, New MixingIngredient)
                                End If

                                If (vasp.Product) Then
                                    recipe.Ingredients(ingredientKey).ProductID = CInt(row.Item("FIELD_VALUE"))
                                    recipe.Ingredients(ingredientKey).Description = GetProductName(recipe.Ingredients(ingredientKey).ProductID)
                                End If

                                If (vasp.Percentage) OrElse (vasp.Qty) Then
                                    recipe.Ingredients(ingredientKey).Quantity = Double.Parse(row.Item("FIELD_VALUE").ToString())
                                    recipe.Ingredients(ingredientKey).UnitLabel = vasp.Unit
                                End If

                                If (vasp.Tolerance) Then
                                    recipe.Ingredients(ingredientKey).Tolerance = Double.Parse(row.Item("FIELD_VALUE").ToString())
                                End If

                                ' Gets the corresponding ingredient type by checking the description against the MixingIngredientTypes Enum
                                For Each ingredientType As String In System.Enum.GetNames(GetType(MixingIngredientTypes))
                                    If ingredientKey.Contains(ingredientType) Then
                                        recipe.Ingredients(ingredientKey).Type = CType(System.Enum.Parse(GetType(MixingIngredientTypes), ingredientType), MixingIngredientTypes)

                                        Exit For
                                    End If
                                Next
                            End If
                        End If
                    Next
                Next

                ' Retrieve requested batches number and total
                query = "SELECT MCP_ID, PARAMETER_VALUE FROM ORDER_PARAM_VALUES_ARCHIVE WHERE MCP_ID IN (" & cycle.RequestedBatchesParamId & "," & cycle.RequestedTotalParamId & ") AND COUNTER = " & counter
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                For Each row As Data.DataRow In result.Rows
                    If CInt(row.Item("MCP_ID")).Equals(cycle.RequestedBatchesParamId) Then
                        requestedBatches = CInt(row.Item("PARAMETER_VALUE"))
                    Else
                        requestedTotal = If(IsDBNull(row.Item("PARAMETER_VALUE")), m_InvalidDblValue, Double.Parse(row.Item("PARAMETER_VALUE").ToString()))
                    End If
                Next

                ' Retrieve generic batch data from flows
                query = "SELECT COUNT(*) AS EXECUTED_BATCHES FROM (SELECT DISTINCT BATCH_NUMBER FROM FLOW_LOGS WHERE COUNTER = " & counter & ") AS A;"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                If result.Rows.Count > 0 Then
                    executedBatches = CInt(result.Rows(0).Item("EXECUTED_BATCHES"))
                End If

                ' Retrieve specific batch repetition data from flows

                '   Retrieve start and stop dates for all batch repetitions
                query = "SELECT CASE WHEN BATCH_NUMBER is NULL THEN 1 ELSE BATCH_NUMBER end BATCH_NUMBER, MIN(START_TIME) AS BATCH_START, MAX(STOP_TIME) AS BATCH_STOP FROM FLOW_LOGS WHERE COUNTER = " & counter.ToString() & " GROUP BY BATCH_NUMBER ORDER BY BATCH_NUMBER"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                Dim newBatch As MixingBatch
                Dim ingredientsQuery As String
                Dim ingredientsResult As Data.DataTable

                '   Process batches
                For Each row As Data.DataRow In result.Rows
                    newBatch = New MixingBatch With {
                    .BatchNumber = CInt(row.Item("BATCH_NUMBER")),
                    .StartDate = Date.Parse(row.Item("BATCH_START").ToString()),
                    .StopDate = Date.Parse(row.Item("BATCH_STOP").ToString()),
                    .Ingredients = New List(Of MixingBatchIngredient)()
                }

                    ' Initialize ingredients
                    For Each key As String In recipe.Ingredients.Keys
                        newBatch.Ingredients.Add(New MixingBatchIngredient() With {
                        .OriginsDescription = "",
                        .ActualWeight = m_InvalidDblValue,
                        .ProductID = recipe.Ingredients(key).ProductID,
                        .RequestedWeight = 0,
                        .IngredientKey = key
                    })
                    Next

                    ' Generic part
                    ingredientsQuery = "SELECT STRING_AGG(CELLS.DESCRIPTION, ',') AS SOURCES, SUM(FLOW_LOGS.WEIGHT) AS ACTUAL_WEIGHT, FLOW_LOGS.PRO_ID AS PRODUCT_ID, SUM(SETPOINT) AS SETPOINT" &
                            " FROM FLOW_LOGS INNER JOIN CELLS ON SOURCE_CELL = CELLS.ID INNER JOIN FLOWS ON FLOW_LOGS.FLO_ID = FLOWS.ID FULL OUTER JOIN PROCESS_RECIPES_ARCHIVED ON FLOW_LOGS.COUNTER = PROCESS_RECIPES_ARCHIVED.COUNTER AND FLOW_LOGS.SOURCE_CELL = PROCESS_RECIPES_ARCHIVED.CEL_ID" &
                            " WHERE FLOW_LOGS.COUNTER = " & counter & " AND (BATCH_NUMBER = " & newBatch.BatchNumber & " OR BATCH_NUMBER IS NULL) AND " &
                            " ( FFL_ID IN (" & cycle.GetIngredientFlowFamiliesConcat() & " ) Or FLOW_LOGS.FLO_ID IN ( " & cycle.GetIngredientFlowIdsConcat() & " ))" &
                            " GROUP BY FLOW_LOGS.PRO_ID"
                    ingredientsResult = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(ingredientsQuery, False)

                    For Each ingredientRow As Data.DataRow In ingredientsResult.Rows
                        For Each ingredient As MixingBatchIngredient In newBatch.Ingredients
                            If ingredient.ProductID.Equals(CInt(ingredientRow.Item("PRODUCT_ID"))) Then
                                ingredient.ActualWeight = Double.Parse(ingredientRow.Item("ACTUAL_WEIGHT").ToString()) * 1000
                                ingredient.OriginsDescription = String.Join(",", Strings.Split(ingredientRow.Item("SOURCES").ToString(), ",").Distinct())

                                If Not IsDBNull(ingredientRow.Item("SETPOINT")) Then
                                    ingredient.RequestedWeight = Double.Parse(ingredientRow.Item("SETPOINT").ToString()) * 1000
                                Else
                                    ingredient.RequestedWeight = Double.Parse(ingredientRow.Item("ACTUAL_WEIGHT").ToString()) * 1000
                                End If

                                Exit For
                            End If
                        Next
                    Next

                    ' Handle missing flows (I have the setpoint but not the flow, or neither)
                    For Each ingredient As MixingBatchIngredient In newBatch.Ingredients.Where(Function(x) x.ActualWeight.Equals(m_InvalidDblValue))
                        query = "SELECT SETPOINT FROM PROCESS_RECIPES_ARCHIVED WHERE COUNTER = " & counter & " And PRO_ID = " & ingredient.ProductID
                        result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                        If result.Rows.Count.Equals(1) Then
                            ingredient.RequestedWeight = Double.Parse(result.Rows(0).Item("SETPOINT").ToString()) * 1000
                        ElseIf result.Rows.Count.Equals(0) Then
                            ' Handle missing flows for manual ingredients (no setpoint nor flow)
                            ingredient.RequestedWeight = 0
                            ingredient.ActualWeight = 0
                        End If
                    Next

                    batches.Add(newBatch)
                Next

                ' Retrieve destination bin
                query = "SELECT STRING_AGG(DESTINATION, ',') AS DESTINATIONS FROM (SELECT  DISTINCT CELLS.DESCRIPTION AS DESTINATION" &
                " FROM FLOW_LOGS INNER JOIN CELLS ON DEST_CELL = CELLS.ID INNER JOIN FLOWS ON FLOW_LOGS.FLO_ID = FLOWS.ID" &
                " WHERE FLOW_LOGS.COUNTER = " & counter &
                " AND ( FFL_ID IN (" & If(cycle.DestinationFlow.FlowFamilyIds IsNot Nothing AndAlso cycle.DestinationFlow.FlowFamilyIds.Length > 0, String.Join(",", cycle.DestinationFlow.FlowFamilyIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")"

                If cycle.DestinationFlow.FlowIds IsNot Nothing Then
                    query &= " OR FLO_ID IN (" & String.Join(",", cycle.DestinationFlow.FlowIds.Cast(Of Integer)) & ")"
                End If

                query &= ")) AS A"

                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                If result.Rows.Count > 0 Then
                    destinationBins = result.Rows(0).Item("DESTINATIONS").ToString()
                End If
            End If

            ' Compose JSON object
            script &= "valueJSON = {"
            script &= """top"": [" &
                        "{ ""id"": ""startDate"", ""val"": """ & startDate.ToString() & """ }," &
                        "{ ""id"": ""stopDate"", ""val"": """ & stopDate.ToString() & """ }," &
                        "{ ""id"": ""recipeDescription"", ""val"": """ & recipe.Description & """ }," &
                        "{ ""id"": ""recipeIngredients"", ""val"": " & recipe.IngredientsToJsonArray() & " }," &
                        "{ ""id"": ""requestedBatches"", ""val"": " & requestedBatches & " }," &
                        "{ ""id"": ""requestedTotal"", ""val"": " & If(requestedTotal.Equals(m_InvalidDblValue), "null", Tools.ConvertValueToJs(Math.Round(requestedTotal / 1000, 1))) & " }," &
                        "{ ""id"": ""executedBatches"", ""val"": " & executedBatches & " }," &
                        "{ ""id"": ""productDescription"", ""val"": """ & productDescription & """ }," &
                        "{ ""id"": ""destinationBin"", ""val"": """ & destinationBins & """}," &
                        "{ ""id"": ""lot"", ""val"": " & GetLotValueLinkForJob(cycleId, counter) & "}," &
                        "{ ""id"": ""counter"", ""val"": """ & counter & """}" &
                    "],"

            script &= """batch"": [" &
                        "{""name"": ""tb_a"", ""elem"": [" & String.Join(",", Array.ConvertAll(Of MixingBatch, String)(batches.ToArray(), Function(x) x.ToString())) & "]}" &
                    "]"

            script &= "};"

            ' Save object in sessionStorage
            script &= "var valueJSON_string = JSON.stringify(valueJSON);" &
                    "sessionStorage.setItem(""valueJSON"",valueJSON_string);" &
                    "updateReport();"
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Private Shared Function GetProductName(pro_id As Integer) As String
        Dim ret_val As String = String.Empty
        Dim query As String = "SELECT NAME FROM PRODUCTS WHERE ID = '" & pro_id & "'"
        Dim result As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

        If result.Rows.Count > 0 Then
            ret_val = result.Rows(0).Item("NAME").ToString()
        End If

        Return ret_val
    End Function

#End Region

End Class