﻿Option Strict On

Imports System.IO
Imports ReportsTools.Tools
Imports UsersGUI
Imports WebTools.tools
Imports CommonDefines.Defines

Public Class myReports

#Region "Configure constants for each job"

    Public Class YieldsConfig
        ''' <see cref="SingleYieldsReportConfig"/>

        ' TODO

        Public Shared Function GetActiveJobCounterParameterByWebId(ByVal webId As Integer) As String
            Dim retVal As String = String.Empty

            Select Case webId
                ' TODO
            End Select

            Return retVal
        End Function

        Public Shared Function GetCycleIdByWebId(ByVal webId As Integer) As Integer
            Dim retVal As Integer = m_InvalidInteger

            Select Case webId
                ' TODO
            End Select

            Return retVal
        End Function

        ' Used in MillingReport
        Public Shared Function GetPlantCapacityByCycleId(ByVal cycId As Integer) As Integer
            Dim retVal As Integer = m_InvalidInteger

            Select Case cycId
                ' TODO
            End Select

            Return retVal
        End Function

        ' For each YR_ID define which report to printout (because YIELDS_PRINTOUTS is configured per YR_ID)
        Public Shared Function GetYieldsIdByYrId(ByVal yrId As Integer) As Integer
            Dim retVal As Integer = m_InvalidInteger

            Select Case yrId
                ' TODO
            End Select

            Return retVal
        End Function

    End Class

    ' TODO se si fanno i report javascript (key UseSSRSReport = false)
    Public Shared ReportTypePerCycleConfig As New Dictionary(Of SSCycles, EnumTypeReport)()

    ' TODO se si fanno i report con il sql server reporting service (key UseSSRSReport = true)
    Public Shared Function GetReportTypePerCycleConfig() As Dictionary(Of SSCycles, EnumTypeReport)
        ReportTypePerCycleConfig.Clear()
        Dim sSelect As String = "SELECT * FROM VIEW_SSRS_REPORTS_PRINT"
        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)
        If (dt IsNot Nothing AndAlso dt.Rows.Count > 0) Then
            For Each dr As DataRow In dt.Rows

                Dim s_cycle As SSCycles = CType(CInt(dr.Item("CYC_ID").ToString()), SSCycles)
                Dim s_code As EnumTypeReport = CType(CInt(dr.Item("CODE").ToString()), EnumTypeReport)

                If (Not ReportTypePerCycleConfig.ContainsKey(s_cycle)) Then
                    ReportTypePerCycleConfig.Add(s_cycle, s_code)
                End If

            Next
        End If
        Return ReportTypePerCycleConfig
    End Function

    ' Fill only for cycles which produce more than 1 type of lot
    ' TODO
    Private Shared LotTypeIdsPerCycle As New Dictionary(Of SSCycles, SSLotTypes)()

    Private Class ConversionFactor
        Public Const InstantWG As Double = 1 ' t/h -> t/h
        Public Const InstantDM As Double = 1 ' l -> l
        Public Const InstantSWB As Double = 1 ' KW -> KW

        Public Const PeriodicWG As Double = 1 ' t -> t
        Public Const PeriodicDM As Double = 1
        Public Const PeriodicSWB As Double = 1
        Public Const PeriodicBS As Double = 1000
        Public Const PeriodicPL As Double = 1000

        Public Const PlantCapacity = 1 'T -> T
    End Class

#End Region

    Public Class SingleYieldsReportConfig

        ' Unique identifier for the report type
        Public Property WebIdentifier As Integer

        ' ID of the YIELDS_REPORTS table
        Public Property YrId As SSYieldsReports

        ' Plant capacity in tons
        Public Property CapacityTons As Integer

        ' For JOB reports only: the name of the parameter containing the counter of the active job
        Public Property ActiveJobCounterParameter As String

        ' For JOB reports only: the cycle by which the report will filter jobs
        Public Property CycleId As Integer

    End Class

#Region "Configure for each job"

    ''' <summary>
    ''' Generates the link to the page of the yield report of the desired job
    ''' </summary>
    ''' <param name="yrId"></param>
    ''' <param name="counter"></param>
    Public Shared Function GetJobScalesReportUrl(ByVal yrId As Integer, ByVal counter As Long) As String
        Dim menuname As String = String.Empty
        Dim pagename As String = String.Empty
        Dim webId As Integer = m_InvalidInteger

        Select Case yrId
            ' TODO

        End Select

        Return "/Reports.aspx?control=report&pagename=" & pagename & "&typereport=JobReport&yieldsid=" & webId & "&menuname=" & menuname & "&JOB_ID=" & counter
    End Function

    Private Shared Function GetInstantValues(ByVal webId As Integer) As myScales

        Dim scales As New myScales
        Dim whereClausole As String = String.Empty

        ' recuero tutte le tuple scale_id, tag_id delle scale
        Dim sSelect As String = "SELECT * FROM SCALES"
        Dim dt_result As Data.DataTable

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim s As New YieldsReports

        For Each dr As DataRow In dt_result.Rows
            If Not IsDBNull(dr("FLOWRATE_TAG")) Then
                s.AddToList(Integer.Parse(dr("ID").ToString), Integer.Parse(dr("FLOWRATE_TAG").ToString))
            End If
        Next

        Select Case webId
            ' TODO

        End Select

        'plant capacity
        scales.PlantCapacityDay = GetPlantCapacityDay(webId)

        '**********************************************************
        sSelect = "SELECT MAX(TIME_STAMP) FROM CURRENT_VALUES"
        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Try
            scales.LastRead = Date.Parse(dt_result.Rows(0).Item(0).ToString)
        Catch
            scales.LastRead = Now
        End Try

        Return scales
    End Function

    Private Shared Function GetPeriodicValues(ByVal webId As Integer, ByVal date_start As Date, ByVal date_stop As Date) As myScales

        Dim scales As New myScales
        Dim whereClausole As String = String.Empty

        If whereClausole <> String.Empty Then
            whereClausole &= " AND "
        End If
        whereClausole &= " STOP_TIME > CONVERT(datetime,'" & date_start.Year & "-" & date_start.Month &
                 "-" & date_start.Day & " " & date_start.Hour & ":00:00', 120) AND " &
               " STOP_TIME <= CONVERT(datetime, '" & date_stop.Year & "-" & date_stop.Month &
                 "-" & date_stop.Day & " " & date_stop.Hour & ":00:00', 120) "

        '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
        Dim sSelect As String = String.Empty
        Dim dt_result As DataTable

        Dim s As New YieldsReports()
        Dim id_scale As Integer

        ' 1. HOURLY_TOTAL_TAG
        ' recupero tutte le tuple scale_id, tag_id delle scale
        sSelect = "SELECT ID, HOURLY_TOTAL_TAG FROM SCALES"
        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        s.Clear()

        For Each dr As DataRow In dt_result.Rows
            If Not IsDBNull(dr("HOURLY_TOTAL_TAG")) Then

                s.AddToList(Integer.Parse(dr("ID").ToString), Integer.Parse(dr("HOURLY_TOTAL_TAG").ToString))
            End If
        Next

        ' recupero tutte le scale, sarà lo switch a valutare solo quelle nel implicate nel report
        sSelect = "SELECT * FROM SCALES_PER_REPORT"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As DataRow In dt_result.Rows

            id_scale = Integer.Parse(dr("SCALE_ID").ToString)

            Select Case webId
                ' TODO
            End Select
        Next

        ' 2. HOURLY_PULSES_TAG
        ' recupero tutte le tuple scale_id, tag_id delle scale abilitate
        sSelect = "SELECT ID, HOURLY_PULSES_TAG FROM SCALES WHERE ISDISABLED = 0"
        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        s.Clear()

        For Each dr As DataRow In dt_result.Rows
            If Not IsDBNull(dr("HOURLY_PULSES_TAG")) Then

                s.AddToList(Integer.Parse(dr("ID").ToString), Integer.Parse(dr("HOURLY_PULSES_TAG").ToString))
            End If
        Next

        'plant capacity
        scales.PlantCapacityDay = GetPlantCapacityDay(webId)
        ' salvo i valori di start e stop nella struttura
        scales.DateStart = date_start
        scales.DateStop = date_stop

        Return scales
    End Function

    Private Shared Function GetCurrentHourValues(ByVal webId As Integer) As myScales

        Dim scales As New myScales
        Dim whereClausole As String = String.Empty

        Dim sSelect As String = String.Empty
        Dim dt_result As DataTable

        Dim s As New YieldsReports()
        Dim id_scale As Integer

        ' 1. ACT_TOTAL_TAG
        ' recupero tutte le tuple scale_id, tag_id delle scale
        sSelect = "SELECT ID, ACT_TOTAL_TAG FROM SCALES"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        s.Clear()

        For Each dr As DataRow In dt_result.Rows
            If Not IsDBNull(dr("ACT_TOTAL_TAG")) Then

                s.AddToList(Integer.Parse(dr("ID").ToString), Integer.Parse(dr("ACT_TOTAL_TAG").ToString))
            End If
        Next

        ' recupero tutte le scale, sarà lo switch a valutare solo quelle nel implicate nel report
        sSelect = "SELECT * FROM SCALES_PER_REPORT"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As DataRow In dt_result.Rows

            id_scale = Integer.Parse(dr("SCALE_ID").ToString)

            Select Case webId
                ' TODO
            End Select
        Next

        ' 2. ACT_PULSES_TAG
        ' recupero tutte le tuple scale_id, tag_id delle scale abilitate
        sSelect = "SELECT ID, ACT_PULSES_TAG FROM SCALES WHERE ISDISABLED = 0"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        s.Clear()

        For Each dr As DataRow In dt_result.Rows
            If Not IsDBNull(dr("ACT_PULSES_TAG")) Then

                s.AddToList(Integer.Parse(dr("ID").ToString), Integer.Parse(dr("ACT_PULSES_TAG").ToString))
            End If
        Next

        'plant capacity
        scales.PlantCapacityDay = GetPlantCapacityDay(webId)

        ' salvo i valori di start e stop nella struttura
        scales.DateStart = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
        scales.DateStop = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, Now.Minute, Now.Second)

        Return scales
    End Function

    Private Shared Function GetCurrentJobValues(ByVal webId As Integer, ByVal job_id As Integer) As myScales

        Dim scales As New myScales
        Dim whereClausole As String = String.Empty
        Dim sSelect As String = String.Empty
        Dim dt_result As DataTable

        ' recupero tutte le tuple scale_id, tag_id delle scale
        sSelect = "SELECT ID, ACT_JOB_TOTAL_TAG FROM SCALES"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim s As New YieldsReports()

        For Each dr As DataRow In dt_result.Rows
            If Not IsDBNull(dr("ACT_JOB_TOTAL_TAG")) Then
                s.AddToList(Integer.Parse(dr("ID").ToString), Integer.Parse(dr("ACT_JOB_TOTAL_TAG").ToString))
            End If
        Next

        ' recupero tutte le scale, sarà lo switch a valutare solo quelle nel implicate nel report
        sSelect = "SELECT * FROM SCALES_PER_REPORT"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim id_scale As Integer

        For Each dr As DataRow In dt_result.Rows

            id_scale = Integer.Parse(dr("SCALE_ID").ToString)

            Select Case webId
                ' TODO
            End Select
        Next

        'plant capacity
        scales.PlantCapacityDay = GetPlantCapacityDay(webId)

        ' salvo i valori di start e stop nella struttura
        sSelect = "SELECT START_DATE FROM PRODUCTION_PLAN WHERE COUNTER = '" & job_id & "'"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As DataRow In dt_result.Rows
            scales.DateStart = Date.Parse(dr("START_DATE").ToString)
        Next

        scales.DateStop = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, Now.Minute, Now.Second)

        ' salvo il nome della ricetta (se presente)
        sSelect = "SELECT DESCRIPTION FROM VIEW_PRODUCTION_PLAN WHERE COUNTER = '" & job_id & "'"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If Not dt_result Is Nothing AndAlso dt_result.Rows.Count > 0 Then
            For Each dr As DataRow In dt_result.Rows
                scales.RecipeName = dr("DESCRIPTION").ToString
            Next
        End If

        scales.JobId = job_id

        Return scales
    End Function

    Private Shared Function GetJobValues(ByVal webId As Integer, ByVal job_id As Integer) As myScales
        Dim scales As New myScales
        Dim sSelect As String = String.Empty
        Dim dt_result As DataTable

        ' recupero tutte le tuple scale_id, tag_id delle scale
        sSelect = "SELECT ID, JOB_TOTAL_TAG FROM SCALES"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim s As New YieldsReports()

        For Each dr As DataRow In dt_result.Rows
            If Not IsDBNull(dr("JOB_TOTAL_TAG")) Then

                s.AddToList(Integer.Parse(dr("ID").ToString), Integer.Parse(dr("JOB_TOTAL_TAG").ToString))
            End If
        Next

        ' recupero tutte le scale, sarà lo switch a valutare solo quelle nel implicate nel report
        sSelect = "SELECT * FROM SCALES_PER_REPORT"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim id_scale As Integer

        For Each dr As DataRow In dt_result.Rows

            id_scale = Integer.Parse(dr("SCALE_ID").ToString)

            Select Case webId
                ' TODO
            End Select
        Next

        'plant capacity
        scales.PlantCapacityDay = GetPlantCapacityDay(webId)

        ' salvo i valori di start e stop nella struttura
        sSelect = "SELECT DISTINCT START_TIME, STOP_TIME FROM YIELDS_DATA WHERE JOB_ID = '" & job_id & "'"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As DataRow In dt_result.Rows
            scales.DateStart = Date.Parse(dr("START_TIME").ToString)
            scales.DateStop = Date.Parse(dr("STOP_TIME").ToString)
        Next

        ' salvo il nome della ricetta (se presente)
        sSelect = "SELECT RECIPE_DESCRIPTION FROM PRODUCTION_REPORTS WHERE COUNTER = '" & job_id & "'"

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If Not dt_result Is Nothing AndAlso dt_result.Rows.Count > 0 Then
            For Each dr As DataRow In dt_result.Rows
                scales.RecipeName = dr("RECIPE_DESCRIPTION").ToString
            Next
        End If

        scales.JobId = job_id

        Return scales
    End Function

    Public Shared Function GetInstantScalesNameScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String
        Dim script As String = String.Empty

        script = GetScalesNameScript(webId, mConfig)

        script &= "initializeReport(" & webId & ", " & EnumTypeReport.InstantReport & ");"

        Return script
    End Function

    Public Shared Function GetPeriodicScalesNameScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String
        Dim script As String = String.Empty

        script = GetScalesNameScript(webId, mConfig)

        script &= "initializeReport(" & webId & ", " & EnumTypeReport.PeriodicReport & ");"

        Return script
    End Function

    Private Shared Function GetScalesNameScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String

        Dim script As String = String.Empty

        Dim tab1 As New List(Of scaleName)
        Dim tab2 As New List(Of scaleName)
        Dim tab3a As New List(Of scaleName)
        Dim tab3b As New List(Of scaleName)

        Select Case webId
            ' TODO
        End Select

        script &= "nameJSON = {"

        script &= """wheat"": [" &
                    "{""name"": ""tb_a""," &
                    """elem"": [" & Tools.BuildNameArray(tab1) & "]} " &
                "],"

        script &= """water"": [" &
                    "{""name"": ""tb_a""," &
                    " ""elem"": [" & Tools.BuildNameArray(tab2) & "]}," &
                    "{""name"": ""tot"", " &
                    " ""sum"": [""tb_a""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
                "],"

        script &= """product"": [" &
                    "{""name"": ""tb_a"", " &
                    " ""elem"": [" & Tools.BuildNameArray(tab3a) & "]}," &
                    "{""name"": ""sub_a""," &
                    " ""sum"": [""tb_a""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT_PRODUCT", mConfig) & """, ""style"": ""sub_total""}," &
                    "{""name"": ""tb_b""," &
                    " ""elem"": [" & Tools.BuildNameArray(tab3b) & "]}," &
                    "{""name"": ""sub_b""," &
                    " ""sum"": [""tb_b""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT_BY_PRODUCTS", mConfig) & """, ""style"": ""sub_total""}," &
                    "{""name"": ""tot""," &
                    " ""sum"": [""tb_a"", ""tb_b""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
                "]"

        script &= "};"

        script &= "var nameJSON_string = JSON.stringify(nameJSON);" &
                "sessionStorage.setItem(""nameJSON"",nameJSON_string);"

        Return script
    End Function

    Public Shared Function GetJobScalesNameScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String
        Dim script As String = String.Empty

        Dim tab1 As New List(Of scaleName)
        Dim tab3a As New List(Of scaleName)
        Dim tab3b As New List(Of scaleName)

        Select Case webId
            ' TODO
        End Select

        script &= "nameJSON = {"

        script &= """wheat"": [" &
                    "{""name"": ""tb_a""," &
                    """elem"": [" & Tools.BuildNameArray(tab1) & "]} " &
                "],"

        script &= """product"": [" &
                    "{""name"": ""tb_a"", " &
                    " ""elem"": [" & Tools.BuildNameArray(tab3a) & "]}," &
                    "{""name"": ""sub_a""," &
                    " ""sum"": [""tb_a""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT_PRODUCT", mConfig) & """, ""style"": ""sub_total""}," &
                    "{""name"": ""tb_b""," &
                    " ""elem"": [" & Tools.BuildNameArray(tab3b) & "]}," &
                    "{""name"": ""sub_b""," &
                    " ""sum"": [""tb_b""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT_BY_PRODUCTS", mConfig) & """, ""style"": ""sub_total""}," &
                    "{""name"": ""tot""," &
                    " ""sum"": [""tb_a"", ""tb_b""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
                "]"

        script &= "};"

        script &= "var nameJSON_string = JSON.stringify(nameJSON);" &
                "sessionStorage.setItem(""nameJSON"",nameJSON_string);"

        script &= "initializeReport(" & webId & ", " & EnumTypeReport.JobReport & ");"

        Return script
    End Function

    Public Shared Function GetScalesAlarmScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String
        Dim script As String = String.Empty
        Dim alarm_min As Double = 0.0
        Dim alarm_max As Double = 0.0

        Dim tab As New List(Of scaleAlarm)

        Select Case webId
            ' TODO
        End Select

        script &= "alarmJSON = {"

        script &= """alarm"": [" & Tools.BuildAlarmArray(tab) & "]"

        script &= "};"

        script &= "var alarmJSON_string = JSON.stringify(alarmJSON);" &
            "sessionStorage.setItem(""alarmJSON"",alarmJSON_string);"

        Return script
    End Function

    Public Shared Function GetInstantScalesValueScript(ByVal webId As Integer, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetInstantValues(webId)

        script = GetScalesValueScript(webId, scales, EnumTypeReport.InstantReport, mConfig)

        Return script
    End Function

    Public Shared Function GetPeriodicScalesValueScript(ByVal webId As Integer, ByVal date_start As Date, ByVal date_stop As Date, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetPeriodicValues(webId, date_start, date_stop)

        script = GetScalesValueScript(webId, scales, EnumTypeReport.PeriodicReport, mConfig)

        Return script
    End Function

    Public Shared Function GetCurrentHourScalesValueScript(ByVal webId As Integer, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetCurrentHourValues(webId)

        script = GetScalesValueScript(webId, scales, EnumTypeReport.PeriodicReport, mConfig)

        Return script
    End Function

    Public Shared Function GetCurrentJobScalesValueScript(ByVal webId As Integer, ByVal job_id As Integer, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetCurrentJobValues(webId, job_id)

        script = GetJobScalesValueScript(webId, scales, EnumTypeReport.JobReport, mConfig)

        Return script
    End Function

    Public Shared Function GetCompletedJobScalesValueScript(ByVal webId As Integer, ByVal job_id As Integer, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetJobValues(webId, job_id)

        script = GetJobScalesValueScript(webId, scales, EnumTypeReport.JobReport, mConfig)

        Return script
    End Function

    Private Shared Function GetScalesValueScript(ByVal webId As Integer, ByVal scales As myScales, ByVal report_type As EnumTypeReport, ByVal mConfig As config) As String

        Dim script As String = String.Empty

        Dim tab1 As New List(Of scaleVal)
        Dim tab2 As New List(Of scaleVal)
        Dim tab3a As New List(Of scaleVal)
        Dim tab3b As New List(Of scaleVal)

        Select Case webId
            ' TODO
        End Select

        script &= "valueJSON = {"

        script &= """top"": ["

        Select Case report_type
            Case EnumTypeReport.InstantReport, EnumTypeReport.InstantPowerReport
                script &= "{""id"": ""last_read"", ""val"": """ & scales.LastRead.ToString(mConfig.GetLanguage().FormatDateTime) & """},"

            Case EnumTypeReport.PeriodicReport, EnumTypeReport.PeriodicPowerReport,
             EnumTypeReport.JobReport, EnumTypeReport.JobPowerReport

                script &= "{""id"": ""dateFrom"", ""val"": """ & scales.DateStart.ToString(mConfig.GetLanguage().FormatDateTime) & """}," &
                            "{""id"": ""dateTo"", ""val"": """ & scales.DateStop.ToString(mConfig.GetLanguage().FormatDateTime) & """}," &
                            "{""id"": ""EMT"", ""val"": """ & Tools.ConvertValueToJs(scales.Emt.TotalSeconds) & """}," &
							"{""id"": ""quantity"", ""val"": " & Tools.ConvertValueToJs(scales.B1) & "}," &
                            "{""id"": ""plant_capacity_day"", ""val"": " & Tools.ConvertValueToJs(scales.PlantCapacityDay) & "},"
        End Select

		Select Case webId
		    ' TODO (quantity value)
        End Select

		script &= "{""id"": ""B0"", ""val"": " & Tools.ConvertValueToJs(scales.B0) & "},"
		script &= "{""id"": ""B1"", ""val"": " & Tools.ConvertValueToJs(scales.B1) & "}"


        script &= "],"

        script &= """wheat"": [" &
                    "{""name"": ""tb_a""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab1) & "]}" &
                "],"

        script &= """water"": [" &
                    "{""name"": ""tb_a""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab2) & "]}" &
                "],"

        script &= """product"": [" &
                    "{""name"": ""tb_a""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab3a) & "]}," &
                    "{""name"": ""tb_b""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab3b) & "]}" &
                "],"

        script &= "};"

        script &= "var valueJSON_string = JSON.stringify(valueJSON);" &
                "sessionStorage.setItem(""valueJSON"",valueJSON_string);" &
                "updateReport();"

        Return script
    End Function

    Private Shared Function GetJobScalesValueScript(ByVal webId As Integer, ByVal scales As myScales, ByVal report_type As EnumTypeReport, ByVal mConfig As config) As String
        Dim script As String = String.Empty

        Dim tab1 As New List(Of scaleVal)
        Dim tab3a As New List(Of scaleVal)
        Dim tab3b As New List(Of scaleVal)

        Select Case webId
            ' TODO
        End Select

        script &= "valueJSON = {"

        script &= """top"": [" &
                            "{""id"": ""dateFrom"", ""val"": """ & scales.DateStart.ToString(mConfig.GetLanguage().FormatDateTime) & """}," &
                            "{""id"": ""dateTo"", ""val"": """ & scales.DateStop.ToString(mConfig.GetLanguage().FormatDateTime) & """}," &
                            "{""id"": ""EMT"", ""val"": """ & Tools.ConvertValueToJs(scales.Emt.TotalSeconds) & """}," &
                            "{""id"": ""plant_capacity_day"", ""val"": " & Tools.ConvertValueToJs(scales.PlantCapacityDay) & "}," &
                            "{""id"": ""recipe_name"", ""val"": """ & scales.RecipeName & """}," &
                            "{""id"": ""job"", ""val"": " & scales.JobId & "}"

        Select Case webId
            ' TODO (quantity value)
        End Select

        script &= "],"

        script &= """wheat"": [" &
                    "{""name"": ""tb_a""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab1) & "]}" &
                "],"

        script &= """product"": [" &
                    "{""name"": ""tb_a""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab3a) & "]}," &
                    "{""name"": ""tb_b""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab3b) & "]}" &
                "]"

        script &= "};"

        script &= "var valueJSON_string = JSON.stringify(valueJSON);" &
                "sessionStorage.setItem(""valueJSON"",valueJSON_string);" &
                "updateReport();"

        Return script
    End Function

    Public Shared Function GetInstantPowerNameScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String
        Dim script As String = String.Empty

        script = GetPowerNameScript(webId, mConfig)

        script &= "initializeReport(" & webId & ", " & EnumTypeReport.InstantPowerReport & ");"

        Return script
    End Function

    Public Shared Function GetPeriodicPowerNameScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String
        Dim script As String = String.Empty

        script = GetPowerNameScript(webId, mConfig)

        script &= "initializeReport(" & webId & ", " & EnumTypeReport.PeriodicPowerReport & ");"

        Return script
    End Function

    Private Shared Function GetPowerNameScript(ByVal webId As Integer, ByVal mConfig As UsersGUI.config) As String

        Dim script As String = String.Empty

        Dim tab1 As New List(Of scaleName)
        Dim tab4 As New List(Of scaleName)

        Select Case webId
            ' TODO
        End Select

        script &= "nameJSON = {"

        Select Case webId
            ' TODO (quantity label)
        End Select

        script &= """power"": [" &
                    "{""name"": ""tb_a"", " &
                    " ""elem"": [" & Tools.BuildNameArray(tab4) & "]}," &
                    "{""name"": ""tot""," &
                    " ""sum"": [""tb_a""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
                "]"

        script &= "};"

        script &= "var nameJSON_string = JSON.stringify(nameJSON);" &
                "sessionStorage.setItem(""nameJSON"",nameJSON_string);"

        Return script
    End Function

    Public Shared Function GetInstantPowerValueScript(ByVal webId As Integer, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetInstantValues(webId)

        script = GetPowerValueScript(webId, scales, EnumTypeReport.InstantPowerReport, mConfig)

        Return script
    End Function

    Public Shared Function GetPeriodicPowerValueScript(ByVal webId As Integer, ByVal date_start As Date, ByVal date_stop As Date, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetPeriodicValues(webId, date_start, date_stop)

        script = GetPowerValueScript(webId, scales, EnumTypeReport.PeriodicPowerReport, mConfig)

        Return script
    End Function

    Public Shared Function GetCurrentHourPowerValueScript(ByVal webId As Integer, ByVal mConfig As config) As String
        Dim script As String = String.Empty
        Dim scales As myScales

        scales = GetCurrentHourValues(webId)

        script = GetPowerValueScript(webId, scales, EnumTypeReport.PeriodicPowerReport, mConfig)

        Return script
    End Function

    Private Shared Function GetPowerValueScript(ByVal webId As Integer, ByVal scales As myScales, ByVal report_type As EnumTypeReport, ByVal mConfig As config) As String

        Dim script As String = String.Empty

        Dim tab1 As New List(Of scaleVal)
        Dim tab4 As New List(Of scaleVal)

        Select Case webId
            ' TODO
        End Select

        script &= "valueJSON = {"

        script &= """top"": ["

        Select Case report_type
            Case EnumTypeReport.InstantReport, EnumTypeReport.InstantPowerReport
                script &= "{""id"": ""last_read"", ""val"": """ & scales.LastRead.ToString(mConfig.GetLanguage().FormatDateTime) & """},"

            Case EnumTypeReport.PeriodicReport, EnumTypeReport.PeriodicPowerReport,
                 EnumTypeReport.JobReport, EnumTypeReport.JobPowerReport
                script &= "{""id"": ""dateFrom"", ""val"": """ & scales.DateStart.ToString(mConfig.GetLanguage().FormatDateTime) & """}," &
                        "{""id"": ""dateTo"", ""val"": """ & scales.DateStop.ToString(mConfig.GetLanguage().FormatDateTime) & """}," &
                        "{""id"": ""EMT"", ""val"": """ & Tools.ConvertValueToJs(scales.Emt.TotalSeconds) & """}," &
                        "{""id"": ""plant_capacity_day"", ""val"": " & Tools.ConvertValueToJs(scales.PlantCapacityDay) & "},"
        End Select

        Select Case webId
            ' TODO (quantity value)
        End Select
		
		script &= "{""id"": ""B0"", ""val"": " & Tools.ConvertValueToJs(scales.B0) & "},"
		script &= "{""id"": ""B1"", ""val"": " & Tools.ConvertValueToJs(scales.B1) & "}"

        script &= "],"

        script &= """power"": [" &
                    "{""name"": ""tb_a""," &
                    " ""elem"": [" & Tools.BuildValueArray(tab4) & "]}" &
                "]"

        script &= "};"

        script &= "var valueJSON_string = JSON.stringify(valueJSON);" &
                "sessionStorage.setItem(""valueJSON"",valueJSON_string);" &
                "updateReport();"

        Return script
    End Function

    Public Shared Function GetPlantCapacityDay(ByVal webId As Integer) As Integer
        Dim ret_val As Integer = 0

        Select Case webId
            ' TODO
        End Select

        Return ret_val
    End Function

#End Region

    ''' <summary>
    ''' Returns the link to the Lot_History page customized for the desired lot ID.
    ''' </summary>
    ''' <param name="lotId"></param>
    Public Shared Function GetLotHistoryLinkForLotId(ByVal lotId As Long) As String
        Return "Lot_History.aspx?control=lothistory&pagename=lothistory&menuname=MENU_LOTS&sql_where=WHERE ID = '" & lotId & "'&ID=" & lotId
    End Function

    ''' <summary>
    ''' Returns the value to be used by the report with the "LINK" pipe, in the format [descriptor]:[link], or the string "null" if no lot was found for the job.
    ''' </summary>
    ''' <param name="cycId">Cycle ID used for retrieving specific configurations in LotTypeIdsPerCycle</param>
    ''' <param name="counter"></param>
    Public Shared Function GetLotValueLinkForJob(ByVal cycId As SSCycles, ByVal counter As Long) As String
        Dim retVal As String

        Dim sqlWhere As String
        Dim dt As DataTable

        sqlWhere = "SELECT ID, DESCRIPTOR FROM LOTS WHERE COUNTER = " & counter

        ' If a specific lot type is configured for the current cycle ID, append it to sqlWhere
        If LotTypeIdsPerCycle.ContainsKey(cycId) Then
            sqlWhere &= " AND LTE_ID = " & LotTypeIdsPerCycle(cycId)
        End If

        ' nel caso di più lotti per lo stesso job, di default prende l'ultimo lotto creato (ID più elevato)
        sqlWhere &= " ORDER BY ID DESC"

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sqlWhere, False)

        Select Case dt.Rows.Count
            Case 0
                retVal = "null"

            Case 1
                ' [descriptor]:[link]
                retVal = """" & dt.Rows(0).Item("DESCRIPTOR").ToString & ":" & GetLotHistoryLinkForLotId(Long.Parse(dt.Rows(0).Item("ID").ToString)) & """"

            Case Else
                ' In case I have more than the expected 1 lot, return the first and log the event
                ' [descriptor]:[link]
                retVal = """" & dt.Rows(0).Item("DESCRIPTOR").ToString & ":" & GetLotHistoryLinkForLotId(Long.Parse(dt.Rows(0).Item("ID").ToString)) & """"

                LogTools.Tools.LogDebug({"Was expecting 1 lot to return from """ & sqlWhere & """ but got " & dt.Rows.Count & "."})
        End Select

        Return retVal
    End Function

    Public Shared Function GetReportTypeByCycleId(cycId As SSCycles) As EnumTypeReport
        Return If(ReportTypePerCycleConfig.ContainsKey(cycId), ReportTypePerCycleConfig(cycId), Nothing)
    End Function

    Public Shared Function GetReportsTranslations(ByVal config As UsersGUI.config) As String
        Dim script As String = String.Empty

        Tools.AppendTranslation(script, config, "MAIN_INSTANT_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_PERIODIC_YIELDS_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_JOB_YIELDS_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_PRODUCTS_SUMMATION_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_SHIPMENT_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_PARCEL_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_MIXING_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_TRANSFER_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_CLEANING_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_MILLING_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_GRAINS_SUMMATION_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_INSTANT_POWER_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_PERIODIC_POWER_REPORT_TITLE")
        Tools.AppendTranslation(script, config, "DATE_LAST_READ")
        Tools.AppendTranslation(script, config, "PRODUCTS")
        Tools.AppendTranslation(script, config, "QUANTITY")
        Tools.AppendTranslation(script, config, "YIELDS_WATER")
        Tools.AppendTranslation(script, config, "YIELDS_B1_YIELDS")
        Tools.AppendTranslation(script, config, "YIELDS_B0_YIELDS")
        Tools.AppendTranslation(script, config, "YIELDS_TOT_YIELDS")
        Tools.AppendTranslation(script, config, "YIELDS_MILL_LOSS")
        Tools.AppendTranslation(script, config, "FROM")
        Tools.AppendTranslation(script, config, "TO")
        Tools.AppendTranslation(script, config, "YIELDS_PERIOD_LENGTH")
        Tools.AppendTranslation(script, config, "YIELDS_MILL_TIME")
        Tools.AppendTranslation(script, config, "YIELDS_OF_PERIOD")
        Tools.AppendTranslation(script, config, "YIELDS_CAPACITY_UTILIZATION")
        Tools.AppendTranslation(script, config, "YIELDS_EXPECTED_UTILIZATION")
        Tools.AppendTranslation(script, config, "YIELDS_OF_CAPACITY")
        Tools.AppendTranslation(script, config, "YIELDS_AVG_ON_PERIOD")
        Tools.AppendTranslation(script, config, "YIELDS_AVG_ON_WORKING_TIME")
        Tools.AppendTranslation(script, config, "JOB")
        Tools.AppendTranslation(script, config, "RECIPE NAME")
        Tools.AppendTranslation(script, config, "YIELDS_POW_MILL")
        Tools.AppendTranslation(script, config, "CONSUMPTION_PER_QUANTITY")
        Tools.AppendTranslation(script, config, "Lotto")
        Tools.AppendTranslation(script, config, "SHOW_DETAILS")
        Tools.AppendTranslation(script, config, "Source")
        Tools.AppendTranslation(script, config, "Destination")
        Tools.AppendTranslation(script, config, "EXPECTED")
        Tools.AppendTranslation(script, config, "Recipe")
        Tools.AppendTranslation(script, config, "EXPECTED_TOTAL_WEIGHT")
        Tools.AppendTranslation(script, config, "ACTUAL_TOTAL_WEIGHT")
        Tools.AppendTranslation(script, config, "EXPECTED_WEIGHT")
        Tools.AppendTranslation(script, config, "ACTUAL_WEIGHT")
        Tools.AppendTranslation(script, config, "Difference")
        Tools.AppendTranslation(script, config, "Product")
        Tools.AppendTranslation(script, config, "MACRO_INGR_SECTION")
        Tools.AppendTranslation(script, config, "MICRO_INGR_SECTION")
        Tools.AppendTranslation(script, config, "HANDTIPPING_INGR_SECTION")
        Tools.AppendTranslation(script, config, "YIELDS_TOT")
        Tools.AppendTranslation(script, config, "BATCH_REPETITION")
        Tools.AppendTranslation(script, config, "Quantità richiesta")
        Tools.AppendTranslation(script, config, "REQUESTED_PERCENTAGE")
        Tools.AppendTranslation(script, config, "ACTUAL_PERCENTAGE")
        Tools.AppendTranslation(script, config, "REPORTS_MISSING_FLOWS")
        Tools.AppendTranslation(script, config, "BATCH_REPETITION_TOTAL")
        Tools.AppendTranslation(script, config, "SCALE")
        Tools.AppendTranslation(script, config, "CELL_TYPE")
        Tools.AppendTranslation(script, config, "PROD_SUMMATION_ON_B1")
        Tools.AppendTranslation(script, config, "PROD_SUMMATION_ON_TOT")
        Tools.AppendTranslation(script, config, "YIELDS_PLANT_LOSS")
        Tools.AppendTranslation(script, config, "MAIN_INTAKE_GRAINS_SUMMATION_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_MILLA_GRAINS_SUMMATION_TITLE")
        Tools.AppendTranslation(script, config, "MAIN_MILL_GRAINS_SUMMATION_TITLE")

        ' TODO: SPECIFIC TRANSLATIONS FOR JOB (if needed)

        Return "localStorage.setItem(""reportLanguages"", JSON.stringify([" & script & "]));"
    End Function

    Public Class ReportFlowReference
        Property FlowIds As SSFlows() = Nothing
        Property FlowFamilyIds As SSFlowFamilies() = Nothing
    End Class

    ' Resituisce la traduzione aggiungendo alle double quotes l'escape JSON e sostituendo ai new line un <br />
    Public Shared Function GetTranslationForJSON(ByVal key As String, ByVal config As UsersGUI.config) As String
        Return ParseTranslationForJSON(config.GetEntryByKeyName(key).GetValue())
    End Function

    ' Resituisce il testo aggiungendo alle double quotes l'escape JSON e sostituendo ai new line un <br />
    Protected Friend Shared Function ParseTranslationForJSON(ByVal entry As String) As String
        Return entry.Replace("""", "\""").Replace(Environment.NewLine, "<br />")
    End Function

End Class