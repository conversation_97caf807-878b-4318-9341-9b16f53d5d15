<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Lot_History.aspx.vb" Inherits="lots_Lot_History" %>

<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_View.ascx" TagPrefix="WUCView" TagName="View" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<%@ Register Src="~/UserControl/WUC_PlantConf.ascx" TagPrefix="WUCPlantConf" TagName="ePlantConf" %>
<%@ Register Src="~/UserControl/WUC_StatusFull.ascx" TagPrefix="WUCStatusFull" TagName="eStatusFull" %>
<%@ Register Src="~/UserControl/WUC_Main.ascx" TagPrefix="WUCMain" TagName="eMain" %>
<%@ Register Src="~/UserControl/WUC_Lots_menu.ascx" TagPrefix="WUCLotsMenu" TagName="eLotsMenu" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta http-equiv="X-UA-Compatibile" content="IE=8" />

    <link rel="stylesheet" type="text/css" href="<%= "./css/default.css" & myScript.GetQueryClientUpdate() %>" />
    <link rel="stylesheet" type="text/css" href="<%= "./css/print.css" & myScript.GetQueryClientUpdate() %>" media="print" />

    <link rel="stylesheet" type="text/css" href="<%= "./css/lots/index.css" & myScript.GetQueryClientUpdate() %>" />
    <link rel="stylesheet" type="text/css" href="<%= "./css/lots/print.css" & myScript.GetQueryClientUpdate() %>" media="print" />
</head>
<body onload="InitPage()">
    <form id="form_Lot_History" method="post" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true">
        </asp:ScriptManager>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
                <!-- Container principale.... inizio-->
                <div id="dError" runat="server" visible="false">
                    <table id="Table1" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
                <div id="dView" runat="server">
                    <div id="customerHeader"></div>
                    <div class="lothistoryContainer bgWhite txtLeft tabBorder1">
                        <div class="leftSection">
                            <WUCLotsMenu:eLotsMenu ID="WUClotsMenu" runat="server" Visible="false" />
                            <asp:UpdatePanel runat="server" ID="UpdatePanel1" UpdateMode="Conditional" Visible="true" style="">
                                <ContentTemplate>
                                </ContentTemplate>
                                <Triggers>
                                    <asp:AsyncPostBackTrigger ControlID="Timer1" EventName="Tick" />
                                </Triggers>
                            </asp:UpdatePanel>
                            <asp:Timer ID="Timer1" Enabled="true" Interval="3000" OnTick="Timer1_Tick" runat="server"></asp:Timer>
                        </div>
                        <div class="mainSection">
                            <asp:UpdatePanel runat="server" ID="UpdatePanelIFrame" UpdateMode="Conditional" Visible="true" class="noPrint">
                                <ContentTemplate>
                                    <iframe id="IFrameLotDetail" runat="server" marginwidth="1"
                                        marginheight="0" name="LotDetail" scrolling="auto" frameborder="0"></iframe>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </div>
                    </div>
                </div>
                <!-- Container principale.... fine-->
            </div>
            <div class="rightSection">
                <!-- Operazioni.... inizio -->
                <WUCOperation:eOperation ID="WebOperation" runat="server" />
                <!-- Operazioni.... fine-->

                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->
            </div>
        </div>
    </form>
</body>
</html>