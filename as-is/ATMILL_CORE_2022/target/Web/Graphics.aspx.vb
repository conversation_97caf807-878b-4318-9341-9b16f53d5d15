﻿Imports System.Data
Imports System.Drawing
Imports System.Web.UI.DataVisualization.Charting
Imports UsersGUI
Imports WebTools.tools
Imports ReportsTools

Partial Class Graphics
    Inherits myWebPage

    Public m_config As config

    Private mMenuName As String
    Private mPageName As String
    Private mScreen As Screen
    Private mControl As String = String.Empty
    Private m_plant_id As String
    Private mTopMenuName As String = String.Empty
    Private m_type_graph As String = String.Empty

    Private bTypeScales As Boolean = False
    Private bTypeNir As Boolean = False

    Private Const MAX_PERC_ALLOWED_VALUE = 140
    Private Const MIN_PERC_ALLOWED_VALUE = -40

    Private Const MAX_ALLOWED_VALUE = 1.0E+28
    Private Const MIN_ALLOWED_VALUE = -1.0E+28

    Private Const NUMBER_OF_SLICES = 500

    Private Const CONV_FACTOR_CURRENT_VALUES_TO_ASP = 1 ' fattore di conversione da valori salvati in CURRENT_VALUES a valore mostrati nel trend graph dei NIR

    Private Enum EnumScaleTypeNir
        Moisture = 9
        Protein = 10
        Ash = 11
    End Enum

    Private Enum EnumTypeGraph
        Scales = 1
        Nir = 2
    End Enum

    Private Enum EnumScaleFunctionality
        NIR = 8
    End Enum

    Private ListNir As Generic.List(Of Nir)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try

            m_config = CType(Application("Config"), config)

            If Not WebTools.tools.GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            ' Controlla se è necessario fare un update delle risorse del JS
            myScript.UpdateJSResources(m_config)

            If Current.Request("plant_id") Is Nothing Then
                lbl_exception.Visible = True
                lbl_exception.Text = "Manca il parametro plant_id"
                Exit Sub
            End If
            m_plant_id = Current.Request("plant_id").ToString

            If Current.Request("type_graph") Is Nothing Then
                lbl_exception.Visible = True
                lbl_exception.Text = "Manca il parametro type_graph"
                Exit Sub
            End If
            m_type_graph = Current.Request("type_graph").ToString

            Select Case DirectCast([Enum].Parse(GetType(EnumTypeGraph), m_type_graph), EnumTypeGraph)
                Case EnumTypeGraph.Scales
                    bTypeScales = True
                Case EnumTypeGraph.Nir
                    bTypeNir = True
                Case Else
                    bTypeScales = True
            End Select

            CheckPageDB(m_config, mMenuName, mPageName)
            If Not CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
                lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Me.divExtra.Visible = False
                Exit Sub
            End If

            If bTypeNir Then
                CreateListNir()
            End If

            If Not Page.IsPostBack Then
                Me.Timer1.Enabled = False
                Me.txt_hours.Text = String.Empty
                Me.txt_hours.Attributes.Add("type", "number")
                Me.txt_y_axis_offset.Attributes.Add("type", "number")

                Me.LoadGraphInterval(Me.rbl_hours_days)
                Me.rbl_hours_days.DataBind()
                Me.rbl_hours_days.SelectedIndex = EnumGraphInterval.ByHour

                Me.LoadGraphChooser(Me.rbl_choose_type)
                Me.rbl_choose_type.DataBind()
                Me.rbl_choose_type.SelectedIndex = EnumGraphChooser.ByLastHours
                rbl_choose_type_SelectedIndexChanged(sender, e)

                If bTypeScales Then

                    tbl_instant_val.Visible = False

                    Me.ddl_graph_indicator.DataSource = UsersGUI.tools.LoadGraphIndicatorTypes(m_config, m_plant_id)
                    Me.ddl_graph_indicator.DataTextField = "Description"
                    Me.ddl_graph_indicator.DataValueField = "IdType"
                    Me.ddl_graph_indicator.DataBind()

                    ' temp code
                    If m_plant_id = 41 OrElse m_plant_id = 31 OrElse m_plant_id = 21 OrElse m_plant_id = 11 Then
                        Me.ddl_graph_indicator.Enabled = False
                        Me.ddl_graph_indicator.SelectedIndex = 1

                        ddl_graph_indicator_SelectedIndexChanged(sender, e)
                    End If
                ElseIf bTypeNir Then

                    tbl_instant_val.Visible = True

                    Me.LoadGraphIndicatorNir()
                    Me.LoadNir()

                End If
            End If

            Me.btnDraw.Text = m_config.GetEntryByKeyName("GRAPH_PLOT").GetValue

            lblTile.Text = m_config.GetEntryByKeyName("MAIN_" + mPageName + "_TITLE").GetValue()

            dvTitle.Visible = True

            If Me.ddl_graph_indicator.SelectedIndex = 0 AndAlso bTypeNir Then
                Me.DivScales.Visible = False
            End If

            If bTypeScales Then
                Me.cbl_graph_indicator.Visible = False
            ElseIf bTypeNir Then
                Me.cbl_graph_indicator.Visible = True
                Me.ddl_graph_indicator.Visible = False
                Me.dvMinYAxis.Visible = False

                Me.DrawInstantValuesHeader()
                Me.DrawInstantValues()

            End If

            myScript.InvokeJS(Me.Page, "ShowUCCont();")
        Catch ex As Exception
            lbl_exception.Text = ex.Message
        End Try
    End Sub

    Private Function LoadGraphInterval(ByVal rbl As RadioButtonList) As RadioButtonList

        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("STEP")

        Dim dr As Data.DataRow
        dr = dt.NewRow
        dr("ID") = EnumGraphInterval.ByHour
        dr("STEP") = m_config.GetEntryByKeyName("BY_HOUR").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = EnumGraphInterval.ByDay
        dr("STEP") = m_config.GetEntryByKeyName("BY_DAY").GetValue
        dt.Rows.Add(dr)

        rbl.DataSource = dt
        rbl.DataTextField = "STEP"
        rbl.DataValueField = "ID"

        Return rbl
    End Function

    Public Sub Plot()

        Dim s_min As Series = Nothing
        Dim s_max As Series = Nothing
        Dim ValMin_Alarm As Double = -1
        Dim ValMax_Alarm As Double = -1
        Dim ValEna_Alarm As Boolean = False
        Dim hours_span As Integer = 0
        Dim y_axis_offset As Integer = 0

        Dim sSelect As String = String.Empty
        Dim sWhere As String = String.Empty
        Dim sGroupBy As String = String.Empty
        Dim sOrderBy As String = String.Empty
        Dim nCount_indicators As Integer = 0

        Dim dt As Data.DataTable

        Dim b_at_least_one_scale As Boolean = False

        'Graph data
        Chart1.LoadTemplate("./css/" & m_config.GetCharts.TemplateName)
        Chart1.Width = Me.ChartWidth.Value
        Chart1.Height = Me.ChartHeight.Value
        Chart1.AntiAliasing = AntiAliasingStyles.All
        Chart1.TextAntiAliasingQuality = TextAntiAliasingQuality.High

        Chart1.Legends.Add("Legend1")
        Chart1.Legends("Legend1").Enabled = True
        Chart1.Legends("Legend1").Alignment = StringAlignment.Near
        Chart1.Legends("Legend1").LegendStyle = LegendStyle.Table
        Chart1.Legends("Legend1").Docking = Docking.Bottom

        Chart1.BorderSkin.SkinStyle = BorderSkinStyle.Emboss

        ' pulisco la lista per i re-plot
        Chart1.Series.Clear()

        ' controllo che ci sia almeno una scale selezionata
        b_at_least_one_scale = False

        For Each l As ListItem In Me.cbl_scale.Items
            If l.Selected Then
                b_at_least_one_scale = True
                Exit For
            End If
        Next

        If Not b_at_least_one_scale Then
            Chart1.Visible = False
            lblInfo.Text = m_config.GetEntryByKeyName("AT_LEAST_ONE_SCALE").GetValue
            lblInfo.Visible = True
            dvTitle.Visible = False
            Exit Sub
        End If

        For Each l As ListItem In Me.cbl_scale.Items
            If l.Selected Then
                nCount_indicators += 1
                Dim s As New Series(l.Value)
                Chart1.Series.Add(s)
                Chart1.Series(s.Name).ChartType = SeriesChartType.Line
                Chart1.Series(s.Name).BorderWidth = 3
                Chart1.Series(s.Name).LegendText = l.Text
                Chart1.Series(s.Name).ToolTip = m_config.GetEntryByKeyName("Value").GetValue & " : #VALY" & ControlChars.Lf &
                                                m_config.GetEntryByKeyName("Date").GetValue & " : #VALX"

                If sWhere <> String.Empty Then
                    sWhere &= " OR SCALE_ID = '" & l.Value & "' "
                Else
                    sWhere &= " ( SCALE_ID = '" & l.Value & "' "
                End If
            End If
        Next

        If sWhere <> String.Empty Then
            sWhere &= " ) "
        End If

        If sWhere <> String.Empty Then
            sWhere &= " AND "
        End If
        sWhere &= " (INDICATOR_ID = '" & Me.ddl_graph_indicator.SelectedValue & "'"

        ' il mill loss lo associo al % / B1
        If Me.ddl_graph_indicator.SelectedValue = EnumGraphIndicators.PercOnB1 Then
            sWhere &= " OR INDICATOR_ID = '" & EnumGraphIndicators.Loss & "'"
        End If

        sWhere &= ")"

        If rbl_choose_type.SelectedIndex = EnumGraphChooser.ByDate Then
            If sWhere <> String.Empty Then
                sWhere &= " AND "
            End If
            If Me.start_date.Text <> String.Empty AndAlso IsDate(Me.start_date.Text) AndAlso
                     Me.stop_date.Text <> String.Empty AndAlso IsDate(Me.stop_date.Text) Then

                Dim datetime_start As DateTime = Date.ParseExact(Me.start_date.Text & ":00",
                                                        m_config.GetLanguage().FormatDateTime,
                                                                System.Threading.Thread.CurrentThread.CurrentCulture)

                Dim datetime_stop As DateTime = Date.ParseExact(Me.stop_date.Text & ":00",
                                                        m_config.GetLanguage().FormatDateTime,
                                                                System.Threading.Thread.CurrentThread.CurrentCulture)

                sWhere &= " TIME_STAMP BETWEEN " & UsersGUI.tools.PrepareDateForSQL(datetime_start) & " AND " &
                              UsersGUI.tools.PrepareDateForSQL(datetime_stop)

                lblError.Text = String.Empty
                Me.dView.Visible = True
                Me.dError.Visible = False
            Else
                lblError.Text = m_config.GetEntryByKeyName("ERROR_NAN").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If

        ElseIf rbl_choose_type.SelectedIndex = EnumGraphChooser.ByLastHours Then

            If Me.txt_hours.Text = String.Empty Then
                Me.rbl_hours_days.SelectedIndex = EnumGraphInterval.ByHour
                Me.txt_hours.Text = "24"
            End If

            If Int32.TryParse(Trim(Me.txt_hours.Text), hours_span) Then

                If hours_span <= 0 Then
                    Me.txt_hours.Text = "24"
                    hours_span = 24
                    Me.rbl_hours_days.SelectedIndex = EnumGraphInterval.ByHour
                End If

                If Me.rbl_hours_days.SelectedIndex = EnumGraphInterval.ByDay Then
                    hours_span *= 24
                End If

                If sWhere <> String.Empty Then
                    sWhere &= " AND "
                End If
                sWhere &= " TIME_STAMP > DATEADD(hour, -" & hours_span.ToString & ", GETDATE())"

                lblError.Text = String.Empty
                Me.dView.Visible = True
                Me.dError.Visible = False
            Else
                lblError.Text = m_config.GetEntryByKeyName("ERROR_NAN").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If
        End If

        If Me.txt_y_axis_offset.Text = String.Empty Then
            Me.txt_y_axis_offset.Text = "0"
        End If

        If Int32.TryParse(Trim(Me.txt_y_axis_offset.Text), y_axis_offset) Then

            lblError.Text = String.Empty
            Me.dView.Visible = True
            Me.dError.Visible = False
        Else
            lblError.Text = m_config.GetEntryByKeyName("ERROR_NAN").GetValue()
            Me.dView.Visible = False
            Me.dError.Visible = True
            Exit Sub
        End If

        ' recupero i time_stamp massimo e minimo per ricavare gli slice
        ' (anche PARAMETER_MIN, PARAMETER_MAX, PARAMETER_ENA per gli allarmi che userò sse ho 1 solo indicatore)
        sSelect = "SELECT MIN(TIME_STAMP) AS MIN_TIME_STAMP, " &
                    "MAX(TIME_STAMP) AS MAX_TIME_STAMP, " &
                    "MAX(PARAMETER_MIN) AS PARAMETER_MIN, " &
                    "MAX(PARAMETER_MAX) AS PARAMETER_MAX, " &
                    "MAX(PARAMETER_ENA) AS PARAMETER_ENA FROM VIEW_GRAPH_DATA WHERE " & sWhere

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim min_time_stamp As String = String.Empty
        Dim max_time_stamp As String = String.Empty
        Dim seconds_in_slice As Integer

        For Each dr As Data.DataRow In dt.Rows
            min_time_stamp = dr("MIN_TIME_STAMP").ToString
            max_time_stamp = dr("MAX_TIME_STAMP").ToString

            ' utili sse ho un solo indicatore
            If dr("PARAMETER_MIN") IsNot DBNull.Value AndAlso dr("PARAMETER_MIN").ToString <> String.Empty Then
                ValMin_Alarm = Double.Parse(WebTools.tools.GetParameter(dr("PARAMETER_MIN").ToString))
            End If
            If dr("PARAMETER_MAX") IsNot DBNull.Value AndAlso dr("PARAMETER_MAX").ToString <> String.Empty Then
                ValMax_Alarm = Double.Parse(WebTools.tools.GetParameter(dr("PARAMETER_MAX").ToString))
            End If
            If dr("PARAMETER_ENA") IsNot DBNull.Value Then
                If WebTools.tools.GetParameter(dr("PARAMETER_ENA").ToString).ToUpper = m_StringOn Then
                    ValEna_Alarm = True
                End If
            End If
        Next

        'aggiungo le series per gli allarmi
        If nCount_indicators = 1 AndAlso ValEna_Alarm Then
            s_min = New Series("Min")
            Chart1.Series.Add(s_min)
            With Chart1.Series("Min")
                .ChartType = SeriesChartType.Spline
                .BorderWidth = 1
                .Color = Color.Red
            End With

            s_max = New Series("Max")
            Chart1.Series.Add(s_max)
            With Chart1.Series("Max")
                .ChartType = SeriesChartType.Spline
                .BorderWidth = 1
                .Color = Color.Red
            End With

        End If

        Dim max_time_stamp_datetime As DateTime
        Dim min_time_stamp_datetime As DateTime

        Try
            max_time_stamp_datetime = CType(max_time_stamp, DateTime)
            min_time_stamp_datetime = CType(min_time_stamp, DateTime)
            Dim time_diff As New TimeSpan(max_time_stamp_datetime.Ticks - min_time_stamp_datetime.Ticks)
            seconds_in_slice = time_diff.TotalSeconds / NUMBER_OF_SLICES
            If seconds_in_slice = 0 Then
                ' evito divisioni per 0
                seconds_in_slice = 1
            End If
        Catch ex As Exception
            ' equivalente di avere tabella vuota, ma con MAX() torna sempre una riga, alla peggio NULLA
            Chart1.Visible = False
            lblInfo.Text = m_config.GetEntryByKeyName("NO_DATA_FOUND").GetValue
            lblInfo.Visible = True
            dvTitle.Visible = False
            Exit Sub
        End Try

        Dim start_date As String = UsersGUI.tools.PrepareDateForSQL(min_time_stamp_datetime)
        Dim stop_date As String = UsersGUI.tools.PrepareDateForSQL(max_time_stamp_datetime)

        ' query con i dati raggruppati per slice
        sSelect = "SELECT DATEADD(second, " &
                    "(DATEDIFF(second, " & start_date & ", TIME_STAMP) / " & seconds_in_slice & ") * " & seconds_in_slice & ", " & start_date & ") AS TIME_STAMP," &
                    "AVG(DATA_VALUE) AS DATA_VALUE, SCALE_ID, INDICATOR_ID, DB_UNIT, ASP_UNIT, PARAMETER_MIN, PARAMETER_MAX, PARAMETER_ENA " &
                    "FROM VIEW_GRAPH_DATA "

        sGroupBy = "INDICATOR_ID, SCALE_ID, DB_UNIT, ASP_UNIT, PARAMETER_MIN, PARAMETER_MAX, PARAMETER_ENA, DATEDIFF(second, " & start_date & ", TIME_STAMP) / " & seconds_in_slice
        sOrderBy = "DATEDIFF(second, " & start_date & ", TIME_STAMP) / " & seconds_in_slice

        If sWhere <> String.Empty Then
            sSelect &= " WHERE " & sWhere
        End If

        If sGroupBy <> String.Empty Then
            sSelect &= " GROUP BY " & sGroupBy
        End If

        If sOrderBy <> String.Empty Then
            sSelect &= " ORDER BY " & sOrderBy
        End If

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim max_value As Double = 0
        Dim min_value As Double = 0
        Dim conv_factor As Double = 0

        If dt.Rows.Count = 0 Then
            Chart1.Visible = False
            lblInfo.Text = m_config.GetEntryByKeyName("NO_DATA_FOUND").GetValue
            lblInfo.Visible = True
            dvTitle.Visible = False
            Exit Sub
        Else
            Chart1.Visible = True
            lblInfo.Visible = False
            dvTitle.Visible = True

            Dim dv As Data.DataView = dt.DefaultView

            conv_factor = UsersGUI.tools.GetConversionFactorToDB(dv(0).Item("DB_UNIT").ToString, dv(0).Item("ASP_UNIT").ToString)

            ' max value
            dv.Sort = "DATA_VALUE DESC"
            max_value = Double.Parse(dv(0).Item("DATA_VALUE").ToString)

            ''

            If conv_factor <> 0 Then
                max_value = Math.Round((max_value / conv_factor), 2)
            End If

            If ValEna_Alarm = True AndAlso ValMax_Alarm <> -1 AndAlso max_value < ValMax_Alarm Then
                max_value = ValMax_Alarm
            End If

            ' evito di avere valori percentuali troppo grandi/piccoli
            If dv(0).Item("ASP_UNIT").ToString = "%01" OrElse dv(0).Item("ASP_UNIT").ToString = "%" Then
                If max_value > MAX_PERC_ALLOWED_VALUE Then
                    max_value = MAX_PERC_ALLOWED_VALUE
                ElseIf max_value < MIN_PERC_ALLOWED_VALUE Then
                    max_value = MIN_PERC_ALLOWED_VALUE
                End If
            Else
                If max_value > MAX_ALLOWED_VALUE Then
                    max_value = MAX_ALLOWED_VALUE
                ElseIf max_value < MIN_ALLOWED_VALUE Then
                    max_value = MIN_ALLOWED_VALUE
                End If
            End If

            ' min value
            dv.Sort = "DATA_VALUE"
            min_value = Double.Parse(dv(0).Item("DATA_VALUE").ToString)

            If conv_factor <> 0 Then
                min_value = Math.Round((min_value / conv_factor), 2)
            End If

            If ValEna_Alarm = True AndAlso ValMin_Alarm <> -1 AndAlso min_value > ValMin_Alarm Then
                min_value = ValMin_Alarm
            End If

            ' evito di avere valori percentuali troppo grandi/piccoli
            If dv(0).Item("ASP_UNIT").ToString = "%01" OrElse dv(0).Item("ASP_UNIT").ToString = "%" Then
                If min_value > MAX_PERC_ALLOWED_VALUE Then
                    min_value = MAX_PERC_ALLOWED_VALUE
                ElseIf min_value < MIN_PERC_ALLOWED_VALUE Then
                    min_value = MIN_PERC_ALLOWED_VALUE
                End If
            Else
                If min_value > MAX_ALLOWED_VALUE Then
                    min_value = MAX_ALLOWED_VALUE
                ElseIf min_value < MIN_ALLOWED_VALUE Then
                    min_value = MIN_ALLOWED_VALUE
                End If
            End If
        End If

        Dim format_date_time As String = m_config.GetLanguage().FormatDateTime

        For Each dr As Data.DataRow In dt.Rows
            Dim ch1 As Double = Double.Parse(dr("DATA_VALUE").ToString)
            If conv_factor <> 0 Then
                ch1 = Math.Round((ch1 / conv_factor), 2)
            End If

            ' evito di avere valori percentuali troppo grandi/piccoli
            If dr("ASP_UNIT").ToString = "%01" OrElse dr("ASP_UNIT").ToString = "%" Then
                If ch1 > MAX_PERC_ALLOWED_VALUE Then
                    ch1 = MAX_PERC_ALLOWED_VALUE
                ElseIf ch1 < MIN_PERC_ALLOWED_VALUE Then
                    ch1 = MIN_PERC_ALLOWED_VALUE
                End If
            Else
                If ch1 > MAX_ALLOWED_VALUE Then
                    ch1 = MAX_ALLOWED_VALUE
                ElseIf ch1 < MIN_ALLOWED_VALUE Then
                    ch1 = MIN_ALLOWED_VALUE
                End If
            End If

            Dim t As DateTime = Date.Parse(dr("TIME_STAMP").ToString)

            Chart1.Series(dr("SCALE_ID").ToString).Points.AddXY(t.ToString(format_date_time), ch1)

            '******** Draw min and max alarms ********
            If nCount_indicators = 1 AndAlso ValEna_Alarm = True Then
                Chart1.Series("Min").Points.AddXY(t.ToString(format_date_time), ValMin_Alarm)
                Chart1.Series("Max").Points.AddXY(t.ToString(format_date_time), ValMax_Alarm)
            End If
            '****************************************
        Next

        Dim ChartArea1 As New ChartArea("ChartArea1")
        Chart1.ChartAreas.Add(ChartArea1)

        With Chart1.ChartAreas("ChartArea1")
            With .AxisX

                .Title = m_config.GetEntryByKeyName("Date").GetValue
                .LabelStyle.Angle = -45
                .LabelStyle.Interval = NUMBER_OF_SLICES / 10
                .MajorGrid.Interval = NUMBER_OF_SLICES / 10
                .MajorGrid.LineColor = Color.Gray
                .MajorGrid.LineDashStyle = ChartDashStyle.Dot
                .MajorGrid.LineWidth = 1
                .MajorGrid.Enabled = True
                .MajorTickMark.Enabled = False
                .MinorGrid.Enabled = False
                .IsMarginVisible = True
            End With

            With .AxisY
                If max_value > 0 Then
                    .Maximum = Math.Ceiling(max_value + (max_value * 0.1)) ' add 10%
                Else
                    .Maximum = 10.0
                End If

                ' se lo spiazzamento indicato è maggiore del massimo valore disegnato, lo forzo a zero
                If y_axis_offset >= max_value Then
                    y_axis_offset = 0
                End If

                .Minimum = y_axis_offset - 1.0
                .IntervalOffset = 1 ' compenso il -1 per disegnare il mimimo valore richiesto sull'asse y

                .LabelStyle.Interval = Math.Round(((.Maximum - .Minimum) / 10), 0)
                .MajorGrid.Interval = Math.Round(((.Maximum - .Minimum) / 10), 0)
                .MajorGrid.LineColor = Color.Gray
                .MajorGrid.LineDashStyle = ChartDashStyle.Dot
                .MajorGrid.LineWidth = 1
                .MajorGrid.Enabled = True
                .MajorTickMark.Enabled = False
                .MinorGrid.Enabled = False
                .IsMarginVisible = True

            End With
        End With
    End Sub

    Protected Sub btnDraw_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnDraw.Click
        If (bTypeScales) Then
            Me.Plot()
        ElseIf bTypeNir Then
            Me.Plot_NIR()
        End If

        If rbl_choose_type.SelectedIndex = EnumGraphChooser.ByDate Then
            Me.Timer1.Enabled = False
        ElseIf rbl_choose_type.SelectedIndex = EnumGraphChooser.ByLastHours Then
            Me.Timer1.Enabled = True
        End If

    End Sub

    Protected Sub ddl_graph_indicator_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_graph_indicator.SelectedIndexChanged
        Dim sSelect As String = "SELECT * FROM VIEW_INDICATORS_PER_SCALE WHERE (IND_ID = " & ddl_graph_indicator.SelectedValue

        ' il mill loss lo associo al % / B1
        If Me.ddl_graph_indicator.SelectedValue = EnumGraphIndicators.PercOnB1 Then
            sSelect &= " OR IND_ID = '" & EnumGraphIndicators.Loss & "'"
        End If

        sSelect &= ") AND PLANT_ID = '" & m_plant_id & "' ORDER BY SCALE_ID"

        Dim dt As Data.DataTable
        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim dt2 As New Data.DataTable

        dt2.Columns.Add("SCALE_ID")
        dt2.Columns.Add("SCALE_DESC")

        For Each dr As DataRow In dt.Rows
            Dim dr2 As Data.DataRow
            dr2 = dt2.NewRow
            dr2("SCALE_ID") = dr("SCALE_ID")

            If dr("IND_ID") <> EnumGraphIndicators.Loss Then
                dr2("SCALE_DESC") = m_config.GetEntryByKeyName(dr("SCALE_DESC")).GetValue
            Else
                dr2("SCALE_DESC") = m_config.GetEntryByKeyName("YIELDS_MILL_LOSS").GetValue
            End If

            dt2.Rows.Add(dr2)
        Next

        Me.cbl_scale.DataSource = dt2
        Me.cbl_scale.DataTextField = "SCALE_DESC"
        Me.cbl_scale.DataValueField = "SCALE_ID"
        Me.cbl_scale.DataBind()

        If dt.Rows.Count > 0 Then
            Me.DivScales.Visible = True
        Else
            Me.DivScales.Visible = False
        End If
    End Sub

    Protected Sub Timer1_Tick(ByVal sender As Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        If (bTypeScales) Then
            Me.Plot()
        ElseIf bTypeNir Then
            Me.Plot_NIR()
        End If
    End Sub

    Private Function LoadGraphChooser(ByVal rbl As RadioButtonList) As RadioButtonList

        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("STEP")

        Dim dr As Data.DataRow
        dr = dt.NewRow
        dr("ID") = EnumGraphChooser.ByDate
        dr("STEP") = m_config.GetEntryByKeyName("BY_DATE").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = EnumGraphChooser.ByLastHours
        dr("STEP") = m_config.GetEntryByKeyName("BY_PERIOD").GetValue
        dt.Rows.Add(dr)

        rbl.DataSource = dt
        rbl.DataTextField = "STEP"
        rbl.DataValueField = "ID"

        Return rbl
    End Function

    Protected Sub rbl_choose_type_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles rbl_choose_type.SelectedIndexChanged
        Select Case rbl_choose_type.SelectedIndex

            Case EnumGraphChooser.ByDate
                'clear and hide Period section
                Me.txt_hours.Text = String.Empty
                Me.tblByLastHours.Visible = False

                Me.tblByDate.Visible = True

            Case EnumGraphChooser.ByLastHours
                'clear and hide Date section
                Me.start_date.Text = String.Empty
                Me.stop_date.Text = String.Empty
                Me.tblByDate.Visible = False

                Me.tblByLastHours.Visible = True

        End Select
    End Sub

#Region "GESTIONE FUNZIONI PER GRAFICO SU NIR"

    Public Class Nir
        Public Id As Integer = 0
        Public Name As String = String.Empty
        Public CodeItem As String = String.Empty
    End Class

    Private Sub LoadGraphIndicatorNir()
        Dim sSelect As String = "select * from GRAPH_INDICATORS where DESCRIPTION like 'NIR%'"
        Dim dt_result As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("INDICATOR")

        If (dt_result IsNot Nothing AndAlso dt_result.Rows.Count > 0) Then
            For Each dr_result As Data.DataRow In dt_result.Rows
                Dim dr As Data.DataRow
                dr = dt.NewRow
                dr("ID") = Integer.Parse(dr_result("ID").ToString())
                dr("INDICATOR") = m_config.GetEntryByKeyName(dr_result("DESCRIPTION")).GetValue
                dt.Rows.Add(dr)
            Next
        End If

        Me.cbl_graph_indicator.DataSource = dt
        Me.cbl_graph_indicator.DataTextField = "INDICATOR"
        Me.cbl_graph_indicator.DataValueField = "ID"
        Me.cbl_graph_indicator.DataBind()

    End Sub

    Private Sub LoadNir()

        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("DESCRIPTION")

        For Each nx As Nir In ListNir
            Dim drx As Data.DataRow
            drx = dt.NewRow
            drx("ID") = nx.Id
            drx("DESCRIPTION") = nx.Name
            dt.Rows.Add(drx)
        Next

        Me.cbl_scale.DataSource = dt
        Me.cbl_scale.DataTextField = "DESCRIPTION"
        Me.cbl_scale.DataValueField = "ID"
        Me.cbl_scale.DataBind()
    End Sub

    Private Sub CreateListNir()

        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("DESCRIPTION")

        Dim sSelect As String = "SELECT * FROM SCALES WHERE PLANT_ID='" + m_plant_id + "' ORDER BY ID"
        Dim dt_result As Data.DataTable

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        ListNir = New List(Of Nir)

        Dim s As New YieldsReports
        If (dt_result IsNot Nothing AndAlso dt_result.Rows.Count > 0) Then
            For Each dr As DataRow In dt_result.Rows
                Dim n As New Nir
                n.Id = Integer.Parse(dr("ID").ToString())
                n.Name = m_config.GetEntryByKeyName(dr("DESCRIPTION").ToString()).GetValue
                n.CodeItem = dr("ITEM_NAME").ToString()
                ListNir.Add(n)
            Next
        End If

    End Sub

    Private Sub Plot_NIR()

        Dim hours_span As Integer = 0
        Dim ind_per_scale_id As Integer = 0
        Dim sSelect As String = String.Empty
        Dim sWhere As String = String.Empty
        Dim sGroupBy As String = String.Empty
        Dim sOrderBy As String = String.Empty

        Dim dt As Data.DataTable

        Dim b_at_least_one_scale As Boolean = False
        Dim b_at_least_one_indicator As Boolean = False

        'Graph data
        Chart1.LoadTemplate("./css/" & m_config.GetCharts.TemplateName)
        Chart1.Width = Me.ChartWidth.Value
        Chart1.Height = Me.ChartHeight.Value
        Chart1.AntiAliasing = AntiAliasingStyles.All
        Chart1.TextAntiAliasingQuality = TextAntiAliasingQuality.High

        Chart1.Legends.Add("Legend1")
        Chart1.Legends("Legend1").Enabled = True
        Chart1.Legends("Legend1").Alignment = StringAlignment.Near
        Chart1.Legends("Legend1").LegendStyle = LegendStyle.Table
        Chart1.Legends("Legend1").Docking = Docking.Bottom

        Chart1.BorderSkin.SkinStyle = BorderSkinStyle.Emboss

        ' pulisco la lista per i re-plot
        Chart1.Series.Clear()

        ' controllo che ci sia almeno una scale ed un indicatore selezionati
        For Each l As ListItem In Me.cbl_scale.Items
            If l.Selected Then
                b_at_least_one_scale = True
                Exit For
            End If
        Next

        For Each l As ListItem In Me.cbl_graph_indicator.Items
            If l.Selected Then
                b_at_least_one_indicator = True
                Exit For
            End If
        Next

        If Not (b_at_least_one_scale And b_at_least_one_indicator) Then
            Chart1.Visible = False
            dvTitle.Visible = False
            tbl_instant_val.Visible = False
            Exit Sub
        End If

        For Each l_scale As ListItem In Me.cbl_scale.Items
            If l_scale.Selected Then

                For Each l_ind As ListItem In Me.cbl_graph_indicator.Items
                    If l_ind.Selected Then

                        cbl_graph_indicator.Items.IndexOf(l_ind)

                        ind_per_scale_id = GetIndicatorPerScaleIdFromWebIndicatorAndWebScale(l_ind.Value, cbl_scale.Items.FindByText(l_scale.Text).Value)
                        If ind_per_scale_id <> 0 Then
                            Dim s As New Series(ind_per_scale_id)
                            Chart1.Series.Add(s)
                            Chart1.Series(s.Name).ChartType = SeriesChartType.Line
                            Chart1.Series(s.Name).BorderWidth = 3
                            Chart1.Series(s.Name).LegendText = l_scale.Text & " " & l_ind.Text
                            Chart1.Series(s.Name).ToolTip = m_config.GetEntryByKeyName("Value").GetValue & " : #VALY" & ControlChars.Lf &
                                                            m_config.GetEntryByKeyName("Date").GetValue & " : #VALX"

                            If sWhere <> String.Empty Then
                                sWhere &= " OR SCALE_ID = '" & cbl_scale.Items.FindByText(l_scale.Text).Value & "' "
                            Else
                                sWhere &= " ( SCALE_ID = '" & cbl_scale.Items.FindByText(l_scale.Text).Value & "' "
                            End If
                        End If
                    End If
                Next
            End If
        Next

        If sWhere <> String.Empty Then
            sWhere &= " ) "
        End If

        If sWhere <> String.Empty Then
            Dim bSelected As Boolean = False
            For Each item As ListItem In cbl_graph_indicator.Items
                If (item.Selected) Then
                    If Not bSelected Then
                        bSelected = True
                        sWhere &= " And ( "
                    Else
                        sWhere &= " OR "
                    End If
                    sWhere &= "INDICATOR_ID ='" & item.Value.ToString() & "'"
                End If
            Next
            If bSelected Then
                sWhere &= ")"
            End If
        End If

        If rbl_choose_type.SelectedIndex = EnumGraphChooser.ByDate Then
            If sWhere <> String.Empty Then
                sWhere &= " AND "
            End If
            If Me.start_date.Text <> String.Empty AndAlso IsDate(Me.start_date.Text) AndAlso
                     Me.stop_date.Text <> String.Empty AndAlso IsDate(Me.stop_date.Text) Then

                Dim datetime_start As DateTime = Date.ParseExact(Me.start_date.Text & ":00",
                                                        m_config.GetLanguage().FormatDateTime,
                                                                System.Threading.Thread.CurrentThread.CurrentCulture)

                Dim datetime_stop As DateTime = Date.ParseExact(Me.stop_date.Text & ":00",
                                                        m_config.GetLanguage().FormatDateTime,
                                                                System.Threading.Thread.CurrentThread.CurrentCulture)

                sWhere &= " TIME_STAMP BETWEEN " & UsersGUI.tools.PrepareDateForSQL(datetime_start) & " AND " &
                              UsersGUI.tools.PrepareDateForSQL(datetime_stop)

                lblError.Text = String.Empty
                Me.dView.Visible = True
                Me.dError.Visible = False
            Else
                lblError.Text = m_config.GetEntryByKeyName("ERROR_NAN").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If

        ElseIf rbl_choose_type.SelectedIndex = EnumGraphChooser.ByLastHours Then

            If Me.txt_hours.Text = String.Empty Then
                Me.rbl_hours_days.SelectedIndex = EnumGraphInterval.ByHour
                Me.txt_hours.Text = "24"
            End If

            If Int32.TryParse(Trim(Me.txt_hours.Text), hours_span) Then

                If hours_span <= 0 Then
                    Me.txt_hours.Text = "24"
                    hours_span = 24
                    Me.rbl_hours_days.SelectedIndex = EnumGraphInterval.ByHour
                End If

                If Me.rbl_hours_days.SelectedIndex = EnumGraphInterval.ByDay Then
                    hours_span *= 24
                End If

                If sWhere <> String.Empty Then
                    sWhere &= " AND "
                End If
                sWhere &= " TIME_STAMP > DATEADD(hour, -" & hours_span.ToString & ", GETDATE())"

                lblError.Text = String.Empty
                Me.dView.Visible = True
                Me.dError.Visible = False
            Else
                lblError.Text = m_config.GetEntryByKeyName("ERROR_NAN").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If
        End If

        ' recupero i time_stamp massimo e minimo per ricavare gli slice
        sSelect = "SELECT MIN(TIME_STAMP) AS MIN_TIME_STAMP, " &
                    "MAX(TIME_STAMP) AS MAX_TIME_STAMP FROM VIEW_GRAPH_DATA WHERE " & sWhere

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim min_time_stamp As String = String.Empty
        Dim max_time_stamp As String = String.Empty
        Dim seconds_in_slice As Integer

        For Each dr As Data.DataRow In dt.Rows
            min_time_stamp = dr("MIN_TIME_STAMP").ToString
            max_time_stamp = dr("MAX_TIME_STAMP").ToString
        Next

        Dim max_time_stamp_datetime As DateTime
        Dim min_time_stamp_datetime As DateTime

        Try
            max_time_stamp_datetime = CType(max_time_stamp, DateTime)
            min_time_stamp_datetime = CType(min_time_stamp, DateTime)
            Dim time_diff As New TimeSpan(max_time_stamp_datetime.Ticks - min_time_stamp_datetime.Ticks)
            seconds_in_slice = time_diff.TotalSeconds / NUMBER_OF_SLICES
            If seconds_in_slice = 0 Then
                ' evito divisioni per 0
                seconds_in_slice = 1
            End If
        Catch ex As Exception
            ' equivalente di avere tabella vuota, ma con MAX() torna sempre una riga, alla peggio NULLA
            Chart1.Visible = False
            lblInfo.Text = m_config.GetEntryByKeyName("NO_DATA_FOUND").GetValue
            lblInfo.Visible = True
            dvTitle.Visible = False
            tbl_instant_val.Visible = False
            Exit Sub
        End Try

        Dim start_date As String = UsersGUI.tools.PrepareDateForSQL(min_time_stamp_datetime)
        Dim stop_date As String = UsersGUI.tools.PrepareDateForSQL(max_time_stamp_datetime)

        ' query con i dati raggruppati per slice
        sSelect = "SELECT DATEADD(second, " &
                    "(DATEDIFF(second, " & start_date & ", TIME_STAMP) / " & seconds_in_slice & ") * " & seconds_in_slice & ", " & start_date & ") AS TIME_STAMP," &
                    "AVG(DATA_VALUE) AS DATA_VALUE, SCALE_ID, INDICATOR_ID, DB_UNIT, ASP_UNIT, PARAMETER_MIN, PARAMETER_MAX, PARAMETER_ENA, IND_PER_SCALE_ID " &
                    "FROM VIEW_GRAPH_DATA "

        sGroupBy = "INDICATOR_ID, SCALE_ID, DB_UNIT, ASP_UNIT, PARAMETER_MIN, PARAMETER_MAX, PARAMETER_ENA, IND_PER_SCALE_ID,DATEDIFF(second, " & start_date & ", TIME_STAMP) / " & seconds_in_slice
        sOrderBy = "DATEDIFF(second, " & start_date & ", TIME_STAMP) / " & seconds_in_slice

        If sWhere <> String.Empty Then
            sSelect &= " WHERE " & sWhere
        End If

        If sGroupBy <> String.Empty Then
            sSelect &= " GROUP BY " & sGroupBy
        End If

        If sOrderBy <> String.Empty Then
            sSelect &= " ORDER BY " & sOrderBy
        End If

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt.Rows.Count = 0 Then
            Chart1.Visible = False
            lblInfo.Text = m_config.GetEntryByKeyName("NO_DATA_FOUND").GetValue
            lblInfo.Visible = True
            dvTitle.Visible = False
            tbl_instant_val.Visible = False
            Exit Sub
        Else
            Chart1.Visible = True
            lblInfo.Visible = False
            dvTitle.Visible = True
            tbl_instant_val.Visible = True
        End If

        Dim format_date_time As String = m_config.GetLanguage().FormatDateTime 'System.Configuration.ConfigurationManager.AppSettings("FormatDateTime").ToString()

        For Each dr As Data.DataRow In dt.Rows
            Dim m_Rapporto As Double = UsersGUI.tools.GetConversionFactorToDB(dr("DB_UNIT").ToString, dr("ASP_UNIT").ToString)
            Dim ch1 As Double = Double.Parse(dr("DATA_VALUE").ToString)
            If m_Rapporto <> 0 Then
                ch1 = Math.Round((ch1 / m_Rapporto), 2)
            End If

            ' evito di avere valori percentuali troppo grandi/piccoli
            If dr("ASP_UNIT").ToString = "%01" OrElse dr("ASP_UNIT").ToString = "%" Then
                If ch1 > MAX_PERC_ALLOWED_VALUE Then
                    ch1 = MAX_PERC_ALLOWED_VALUE
                ElseIf ch1 < MIN_PERC_ALLOWED_VALUE Then
                    ch1 = MIN_PERC_ALLOWED_VALUE
                End If
            End If

            Dim t As DateTime = Date.Parse(dr("TIME_STAMP").ToString)

            Chart1.Series(dr("IND_PER_SCALE_ID").ToString).Points.AddXY(t.ToString(format_date_time), ch1)
        Next

        ' recupero i valori di max e min per regolare l'asse Y
        sSelect = "SELECT MAX(DATA_VALUE),MIN(DATA_VALUE),COUNT(*),MAX(DB_UNIT) AS DB_UNIT,MAX(ASP_UNIT) AS ASP_UNIT FROM VIEW_GRAPH_DATA "
        If sWhere <> String.Empty Then
            sSelect &= " WHERE " & sWhere
        End If

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim max_value As Double = 0
        Dim min_value As Double = 0
        Dim max_count As Long = 0

        For Each dr As Data.DataRow In dt.Rows
            Dim m_Rapporto As Double = UsersGUI.tools.GetConversionFactorToDB(dr("DB_UNIT").ToString, dr("ASP_UNIT").ToString)

            If dr(0) IsNot DBNull.Value Then
                max_value = Double.Parse(dr(0).ToString)
                If m_Rapporto <> 0 Then
                    max_value = Math.Round((max_value / m_Rapporto), 2)
                End If

                ' evito di avere valori percentuali troppo grandi/piccoli
                If dr("ASP_UNIT").ToString = "%01" OrElse dr("ASP_UNIT").ToString = "%" Then
                    If max_value > MAX_PERC_ALLOWED_VALUE Then
                        max_value = MAX_PERC_ALLOWED_VALUE
                    ElseIf max_value < MIN_PERC_ALLOWED_VALUE Then
                        max_value = MIN_PERC_ALLOWED_VALUE
                    End If
                End If
            End If

            If dr(1) IsNot DBNull.Value Then
                Try
                    min_value = Double.Parse(dr(1).ToString)
                    If m_Rapporto <> 0 Then
                        min_value = Math.Round((min_value / m_Rapporto), 2)
                    End If
                Catch ex As Exception
                    min_value = 0
                End Try

                ' evito di avere valori percentuali troppo grandi/piccoli
                If dr("ASP_UNIT").ToString = "%01" OrElse dr("ASP_UNIT").ToString = "%" Then
                    If min_value > MAX_PERC_ALLOWED_VALUE Then
                        min_value = MAX_PERC_ALLOWED_VALUE
                    ElseIf min_value < MIN_PERC_ALLOWED_VALUE Then
                        min_value = MIN_PERC_ALLOWED_VALUE
                    End If
                End If
            End If
            If dr(0) IsNot DBNull.Value Then
                max_count = Long.Parse(dr(2).ToString)
            End If
        Next

        Dim ChartArea1 As New ChartArea("ChartArea1")
        Chart1.ChartAreas.Add(ChartArea1)

        With Chart1.ChartAreas("ChartArea1")
            With .AxisX
                .Title = m_config.GetEntryByKeyName("Date").GetValue
                .LabelStyle.Angle = -45
                .LabelStyle.Interval = NUMBER_OF_SLICES / 10
                .MajorGrid.Interval = NUMBER_OF_SLICES / 10
                .MajorGrid.LineColor = Color.Gray
                .MajorGrid.LineDashStyle = ChartDashStyle.Dot
                .MajorGrid.LineWidth = 1
                .MajorGrid.Enabled = True
                .MajorTickMark.Enabled = False
                .MinorGrid.Enabled = False
                .IsMarginVisible = True
            End With

            With .AxisY
                If max_value > 0 Then
                    .Maximum = Math.Ceiling(max_value + (max_value * 0.1)) ' add 10%
                Else
                    .Maximum = 10.0
                End If

                If min_value > 0 Then
                    .Minimum = Math.Floor(min_value - (min_value * 0.1)) ' sub 10%
                ElseIf min_value < 0 Then
                    .Minimum = Math.Floor(min_value + (min_value * 0.1)) ' add 10%
                Else
                    .Minimum = -1.0
                    .IntervalOffset = 1 ' compenso il -1 per disegnare lo 0 sull'asse y
                End If

                .LabelStyle.Interval = Math.Round(((.Maximum - .Minimum) / 10), 0)
                .MajorGrid.Interval = Math.Round(((.Maximum - .Minimum) / 10), 0)
                .MajorGrid.LineColor = Color.Gray
                .MajorGrid.LineDashStyle = ChartDashStyle.Dot
                .MajorGrid.LineWidth = 1
                .MajorGrid.Enabled = True
                .MajorTickMark.Enabled = False
                .MinorGrid.Enabled = False
                .IsMarginVisible = True

            End With
        End With
    End Sub

    Private Sub DrawInstantValuesHeader()
        tbl_instant_val.Rows.Clear()

        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        row.BgColor = "#F0F0F0"
        Me.tbl_instant_val.Rows.Add(row)

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        tc.InnerText = String.Empty
        tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tc)

        Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
        tc2.InnerText = m_config.GetEntryByKeyName("NIR MOISTURE").GetValue
        tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tc2)

        Dim tc3 As New System.Web.UI.HtmlControls.HtmlTableCell
        tc3.InnerText = m_config.GetEntryByKeyName("NIR PROTEIN").GetValue
        tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tc3)

        Dim tc4 As New System.Web.UI.HtmlControls.HtmlTableCell
        tc4.InnerText = m_config.GetEntryByKeyName("NIR ASH").GetValue
        tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tc4)

    End Sub

    Private Sub DrawInstantValues()

        Dim row As System.Web.UI.HtmlControls.HtmlTableRow = Nothing
        Dim tc As System.Web.UI.HtmlControls.HtmlTableCell = Nothing
        For Each obj_nir As Nir In ListNir

            row = New System.Web.UI.HtmlControls.HtmlTableRow
            Me.tbl_instant_val.Rows.Add(row)

            tc = New System.Web.UI.HtmlControls.HtmlTableCell
            tc.InnerText = obj_nir.Name
            tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tc)

            Dim tcr As New System.Web.UI.HtmlControls.HtmlTableCell
            tcr.InnerText = Math.Round((ReportsTools.Tools.GetVal(GetFlowrateTagFromScaleID(obj_nir.Id.ToString(), EnumGraphIndicators.NIRMoisture)) / CONV_FACTOR_CURRENT_VALUES_TO_ASP), 3) & " %"
            tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tcr)

            tcr = New System.Web.UI.HtmlControls.HtmlTableCell
            tcr.InnerText = Math.Round((ReportsTools.Tools.GetVal(GetFlowrateTagFromScaleID(obj_nir.Id.ToString(), EnumGraphIndicators.NIRProtein)) / CONV_FACTOR_CURRENT_VALUES_TO_ASP), 3) & " %"
            tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tcr)

            tcr = New System.Web.UI.HtmlControls.HtmlTableCell
            tcr.InnerText = Math.Round((ReportsTools.Tools.GetVal(GetFlowrateTagFromScaleID(obj_nir.Id.ToString(), EnumGraphIndicators.NIRAsh)) / CONV_FACTOR_CURRENT_VALUES_TO_ASP), 3) & " %"
            tbl_instant_val.Rows(tbl_instant_val.Rows.Count - 1).Cells.Add(tcr)

        Next

    End Sub

    Private Function GetFlowrateTagFromScaleID(ByVal scale_id As Integer, graph_ind As EnumGraphIndicators) As Integer
        Dim sSelect As String = "SELECT * FROM SCALES WHERE ID = " & scale_id
        Dim dt_result As Data.DataTable
        Dim ret_val As Integer = 0

        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim s As New YieldsReports

        For Each dr As DataRow In dt_result.Rows
            Select Case graph_ind
                Case EnumGraphIndicators.NIRMoisture
                    If Not IsDBNull(dr("NIR_MOISTURE_FLOWRATE_TAG")) Then
                        ret_val = Integer.Parse(dr("NIR_MOISTURE_FLOWRATE_TAG").ToString)
                    End If
                Case EnumGraphIndicators.NIRProtein
                    If Not IsDBNull(dr("NIR_PROTEIN_FLOWRATE_TAG")) Then
                        ret_val = Integer.Parse(dr("NIR_PROTEIN_FLOWRATE_TAG").ToString)
                    End If
                Case EnumGraphIndicators.NIRAsh
                    If Not IsDBNull(dr("NIR_ASH_FLOWRATE_TAG")) Then
                        ret_val = Integer.Parse(dr("NIR_ASH_FLOWRATE_TAG").ToString)
                    End If
                Case Else
                    lblError.Text = "Errore colonna SCALES per NIR non gestita " + graph_ind
                    Me.dView.Visible = False
                    Me.dError.Visible = True
            End Select

        Next

        Return ret_val
    End Function

    Private Function GetIndicatorPerScaleIdFromWebIndicatorAndWebScale(ByVal scale_type_id As Integer, ByVal web_scale_id As Integer) As Integer
        Dim ret_val As Integer = 0
        Dim sSelect As String = String.Empty
        Dim dt_result As Data.DataTable = Nothing

        sSelect = "SELECT DISTINCT(GRAPH_INDICATORS.ID),GRAPH_INDICATORS.DESCRIPTION,SCALE_ID,SCALES.ITEM_NAME,INDICATORS_PER_SCALE.ID AS INDS_ID FROM GRAPH_INDICATORS " &
                    "INNER JOIN INDICATORS_PER_SCALE On GRAPH_INDICATORS.ID = INDICATORS_PER_SCALE.IND_ID " &
                    "INNER JOIN SCALES ON INDICATORS_PER_SCALE.SCALE_ID = SCALES.ID " &
                    "WHERE SCALES.PLANT_ID = " & m_plant_id & " AND " &
                    "GRAPH_INDICATORS.ID = " & scale_type_id & " AND " &
                    "SCALE_ID=" & web_scale_id & " ORDER BY GRAPH_INDICATORS.ID"
        dt_result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)
        If (dt_result IsNot Nothing AndAlso dt_result.Rows.Count > 0) Then
            ret_val = Integer.Parse(dt_result(0)("INDS_ID").ToString())
        End If

        Return ret_val
    End Function

#End Region

End Class