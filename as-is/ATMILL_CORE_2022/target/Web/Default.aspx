<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Default.aspx.vb" Inherits="_Default" %>

<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_View.ascx" TagPrefix="WUCView" TagName="View" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<%@ Register Src="~/UserControl/WUC_PlantConf.ascx" TagPrefix="WUCPlantConf" TagName="ePlantConf" %>
<%@ Register Src="~/UserControl/WUC_StatusFull.ascx" TagPrefix="WUCStatusFull" TagName="eStatusFull" %>
<%@ Register Src="~/UserControl/WUC_Main.ascx" TagPrefix="WUCMain" TagName="eMain" %>
<%@ Register Src="~/UserControl/WUC_Procexecution.ascx" TagPrefix="WUCProcexecution" TagName="eProcexecution" %>
<%@ Register Src="~/UserControl/WUC_AlarmConf.ascx" TagPrefix="WUCAlarmConf" TagName="eAlarmConf" %>
<%@ Register Src="~/UserControl/WUC_Pdf.ascx" TagPrefix="WUCPdf" TagName="ePdf" %>
<%@ Register Src="~/UserControl/WUC_SystemGroupsRights.ascx" TagPrefix="WUCSystemGroupsRights" TagName="eSystemGroupsRights" %>
<%@ Register Src="~/UserControl/WUC_PLCStatus.ascx" TagPrefix="WUCPLCStatus" TagName="eWUCPLCStatus" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta http-equiv="X-UA-Compatibile" content="IE=8" />
</head>
<body onload="InitPage();">
    <form id="form_Default" method="post" runat="server">
        <asp:HiddenField ID="HiddenDelete" runat="server" />
        <asp:HiddenField ID="HiddenAbortCycle" runat="server" />
        <asp:HiddenField ID="HiddenCloneCycle" runat="server" />
        <asp:HiddenField ID="HiddenCloneRecipe" runat="server" />
        <asp:HiddenField ID="HiddenCloneRecipeText" runat="server" />
        <asp:HiddenField ID="HiddenForceCompleteOrder" runat="server" />
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true">
        </asp:ScriptManager>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer" runat="server">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
                <!-- Container principale.... inizio-->
                <WUCView:View ID="UCView" runat="server" Visible="false" />
                <WUCPlantConf:ePlantConf ID="UCPlant" runat="server" Visible="false" />
                <WUCStatusFull:eStatusFull ID="UCStatus" runat="server" Visible="false" />
                <WUCMain:eMain ID="UCMain" runat="server" Visible="false" />
                <WUCProcexecution:eProcexecution ID="UCProcexecution" runat="server" Visible="false" />
                <WUCAlarmConf:eAlarmConf ID="UCAlarmConf" runat="server" Visible="false" />
                <WUCPdf:ePdf ID="UCPdf" runat="server" Visible="false" />
                <WUCSystemGroupsRights:eSystemGroupsRights ID="UCSystemGroupsRights" runat="server" Visible="false" />
                <!-- Container principale.... fine-->
            </div>
            <div class="rightSection">
                <!-- Operazioni.... inizio -->
                <WUCOperation:eOperation ID="WebOperation" runat="server" />
                <!-- Operazioni.... fine-->

                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->

                <!-- PLC Status .... inizio -->
                <WUCPLCStatus:eWUCPLCStatus ID="PLCStatus" runat="server" Visible="true"/>
                <!-- PLC Status .... fine -->
            </div>
        </div>
    </form>
</body>
</html>