<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Export.aspx.vb" Inherits="Export" %>

<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_View.ascx" TagPrefix="WUCView" TagName="View" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<%@ Register Src="~/UserControl/WUC_PlantConf.ascx" TagPrefix="WUCPlantConf" TagName="ePlantConf" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta http-equiv="X-UA-Compatibile" content="IE=8" />
    <link rel="stylesheet" href="./css/wcf.css" />
    <script type="text/javascript" src="js/wcf.js"></script>
</head>
<body onload="InitPage()">
    <form id="from_export_data" method="post" runat="server">
        <asp:HiddenField ID="HiddenExecuteWithError" runat="server" />
        <asp:HiddenField ID="HiddenExportPath" runat="server" />
        <asp:HiddenField ID="HiddenWcfPath" runat="server" />
        <asp:HiddenField ID="HiddenExportResultOk" runat="server" />
        <asp:HiddenField ID="HiddenExportResultFailedFromDate" runat="server" />
        <asp:HiddenField ID="HiddenExportResultFailedToDate" runat="server" />
        <asp:Label ID="lbl_exception" CssClass="lbl_exception" runat="server"></asp:Label>
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true">
        </asp:ScriptManager>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
                <div id="dView" runat="server" visible="true">
                    <table class="tabMrg3">
                        <tr>
                            <td class="bgImg2">&nbsp;
                            </td>
                            <td class="tabBground1">&nbsp;
                            </td>
                        </tr>
                        <tr>
                            <td class="txtBold" colspan="2">
                                <%= m_config.GetEntryByKeyName("MAIN_EXPORT_DATA_TITLE").GetValue()%>
                            </td>
                        </tr>
                    </table>

                    <table>
                        <tr>
                            <td colspan="2"><asp:RadioButton ID="rbIntake" runat="server" Text="Intake Data" GroupName="getdata" Checked="true"/></td>
                        </tr>
                        <tr>
                            <td colspan="2"><asp:RadioButton ID="rbBagging" runat="server" Text="Bagging Data" GroupName="getdata"/></td>
                        </tr>
                        <tr>
                            <td colspan="2"><asp:RadioButton ID="rbLoadout" runat="server" Text="Loadout Data" GroupName="getdata"/></td>
                        </tr>
                        <tr>
                            <td colspan="2"><asp:RadioButton ID="rbShip" runat="server" Text="Shipments Data" GroupName="getdata"/></td>
                        </tr>
                        <tr>
                            <td colspan="2"><asp:RadioButton ID="rbFlows" runat="server" Text="Flow Logs Data" GroupName="getdata"/></td>
                        </tr>
                        <tr>
                            <td colspan="2"><asp:RadioButton ID="rbProd" runat="server" Text="Production Data" GroupName="getdata"/></td>
                        </tr>
                        <tr>
                            <td colspan="2"><hr /></td>
                        </tr>
                        <tr>
                            <td style="Width:100px;text-align:end;">
                                <asp:Label ID="lblFromData" runat="server" Text="From Data:" ></asp:Label>
                            </td>
                            <td><asp:TextBox ID="txtFromData" runat="server" CssClass="text DateTimePicker_jq" ></asp:TextBox></td>
                        </tr>
                        <tr>
                            <td style="Width:100px;text-align:end;">
                                <asp:Label ID="lblToData" runat="server" Text="To Data:"></asp:Label>
                            </td>
                            <td><asp:TextBox ID="txtToData" runat="server" CssClass="text DateTimePicker_jq" ></asp:TextBox></td>
                        </tr>
                    </table>
                    <div style="margin-top:10px;">
                        <button type="button" id="myBtn" class="myButtonExec" onclick="ExportOWS()"><%=m_config.GetEntryByKeyName("MAIN_EXPORT_DATA_TITLE").GetValue() %></button>
                        <asp:LinkButton ID="hLink" runat="server" CssClass="myButtonLink" Text="Download file" style="display:none;" OnClick="hLink_Click"></asp:LinkButton>
                    </div>
                </div>
                <div id="dError" runat="server" visible="false" class="margin1">
                    <table id="Table1" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="rightSection">
                <!-- Operazioni.... inizio -->
                <WUCOperation:eOperation ID="WebOperation" runat="server" />
                <!-- Operazioni.... fine-->

                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->
            </div>
        </div>
    </form>
</body>
</html>