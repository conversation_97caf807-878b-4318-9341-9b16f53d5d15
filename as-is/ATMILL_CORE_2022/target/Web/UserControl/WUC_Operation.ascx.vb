﻿Imports UsersGUI
Imports WebTools.tools

Partial Class UserControl_WUC_Operation
    Inherits UserControl

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing

    Public mMenuName As String = String.Empty
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Private msgDeleteAll As New myConfirm
    Private mTypeReport As EnumTypeReport

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then

        End If

        ' message init
        msgDeleteAll.Init(<PERSON><PERSON>, Me.Hidden<PERSON>eleteAll, myMessageBoxParam.NoParameter)

        Select Case (mControl)
            Case "report"
                If WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Printing) Then
                    DrawPrintReportButton()
                    'DrawMailReportButton()

                End If

            Case "graph"
                If WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Printing) Then
                    DrawPrintGraphButton()
                End If

            Case "lothistory"
                If WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Printing) Then
                    DrawPrintWholeButton()
                End If

            Case Else
                If mScreen IsNot Nothing Then
                    If mScreen.HasAddButton And WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Adding) Then
                        DrawAddButton()
                    End If

                    If mScreen.AddMenuItemNames IsNot Nothing AndAlso WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Adding) Then
                        DrawAddMenuButtons()
                    End If

                    If mScreen.HasAllDeleteButton And WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Deleting) Then
                        ' tenuto nell'ascx per problema con la confirm
                        DrawDeleteAllButton()
                    End If

                    If mScreen.HasReportButton And WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Printing) Then
                        DrawPrintButton()
                    End If

                    ' Import ( TODO )
                    If mScreen.HasImportFileButton Then
                        DrawImportButton()
                    End If

                End If
        End Select

        ' Back
        DrawBackButton()

        If msgDeleteAll.GetAnswer() Then
            DeleteAll()
            myScript.InvokeJS(Me.Page, "DoneEvents.ActionGeneric();")
        End If
    End Sub

    Private Sub DrawAddButton()
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New HyperLink
        link.ID = "lbAdd"
        link.Text = m_config.GetEntryByKeyName("ADD_BUTTON").GetValue()
        link.NavigateUrl = "../New.aspx?control=new&pagename=" & mPageName & "&menuname=" & mMenuName
        If mScreen.AddButtonParams IsNot Nothing Then
            For Each p As AddButtonParam In mScreen.AddButtonParams
                link.NavigateUrl &= "&" & p.Parameter & "=" & Current.Request(p.Parameter).ToString
            Next
        End If
        If Current.Request.QueryString("sql_where") IsNot Nothing AndAlso Current.Request.QueryString("sql_where").ToString <> String.Empty Then
            link.NavigateUrl &= "&sql_where=" & Current.Request.QueryString("sql_where").ToString
        End If

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub DrawAddMenuButtons()
        Dim bAtLeastOneMissingParam As Boolean = False

        For Each r As UsersGUI.AddMenuItemName In mScreen.AddMenuItemNames
            bAtLeastOneMissingParam = False

            ' Controllo che tutti i parametri siano presenti nella Request
            For Each p As AddMenuItemNameParameter In r.ListParameters
                If Current.Request(p.FieldDB) Is Nothing Then
                    bAtLeastOneMissingParam = True
                    Exit For
                End If
            Next

            If Not bAtLeastOneMissingParam Then
                Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
                tblOperation.Rows.Add(row)

                AddImageToOperation()

                Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
                Dim link As New HyperLink
                link.ID = "lbAddMenu"
                link.Text = m_config.GetEntryByKeyName(r.Name).GetValue()
                link.NavigateUrl = r.GetNavigateUrl
                For Each p As AddMenuItemNameParameter In r.ListParameters
                    link.NavigateUrl &= "&" & p.FieldDB & "=" & Current.Request(p.FieldDB).ToString
                Next

                tc.Attributes("class") = "txtButtonGreen"
                tc.Controls.Add(link)

                tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
            End If
        Next
    End Sub

    Private Sub DrawDeleteAllButton()
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New LinkButton
        link.ID = "lbDeleteAll"
        link.Text = m_config.GetEntryByKeyName("DELETEALL_BUTTON").GetValue()
        link.Attributes("href") = "#"

        link.OnClientClick = "myMessageBox.confirm('" & m_config.GetEntryByKeyName("CONFIRM_DELETE_ALL").GetValue() & "', " &
                                               "function(){ document.getElementById('WebOperation_HiddenDeleteAll').value = true; ButtonEvents.MessageBoxConfirm.Submit(); }, " &
                                                "function(){ document.getElementById('WebOperation_HiddenDeleteAll').value = ''; ButtonEvents.MessageBoxConfirm.Cancel(); }" &
                                            ");"

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)

    End Sub

    Private Sub DrawImportButton()
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New HyperLink
        link.ID = "lbImport"
        link.Text = m_config.GetEntryByKeyName("IMPORT_BUTTON").GetValue()
        '  link.NavigateUrl = ""

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub DrawBackButton()
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        row.ID = "BackCont"
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New HyperLink
        With link
            .ID = "lbBack"
            .Text = m_config.GetEntryByKeyName("Back").GetValue()
            .CssClass = "hoverHand"
            .Attributes.Add("onclick", "ButtonEvents.Back();")
        End With

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub AddImageToOperation()
        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim img As New System.Web.UI.HtmlControls.HtmlImage

        img.Src = "~/image/RightArrow.png"
        tc.Attributes("class") = "buttonGreen"
        tc.Controls.Add(img)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Protected Sub DeleteAll()
        Dim sDelete As String
        sDelete = "DELETE FROM " & mScreen.DBName
        WebDataBaseLayer.DataBase.ExecuteSQL(sDelete)
    End Sub

    Private Sub DrawMailReportButton()
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New LinkButton
        link.ID = "lb_Mail"
        link.Text = m_config.GetEntryByKeyName("Mail_BUTTON").GetValue()
        link.Attributes("href") = "#"
        link.OnClientClick = "ButtonEvents.RptMail(null);"

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    ' print functions
    Private Sub DrawPrintButtonBase(ByVal calling_function As String)
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New LinkButton
        link.ID = "lb_Stampa"
        link.Text = m_config.GetEntryByKeyName("PRINT_BUTTON").GetValue()
        link.Attributes("href") = "#"
        link.OnClientClick = calling_function & ";"

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub DrawPrintButton()
        DrawPrintButtonBase("ButtonEvents.Print()")
    End Sub

    Private Sub DrawPrintReportButton()
        DrawPrintButtonBase("ButtonEvents.PrintReport()")
    End Sub

    Private Sub DrawPrintGraphButton()
        DrawPrintButtonBase("ButtonEvents.PrintGraph()")
    End Sub

    Private Sub DrawPrintIframeButton(ByVal iframe_id As String, Optional ByVal stylesheets As String = "")
        DrawPrintButtonBase("ButtonEvents.PrintIframe('" & iframe_id & "'" & If(stylesheets.Trim().Length > 0, ",[" & stylesheets & "]", "") & ")")
    End Sub

    Private Sub DrawPrintWholeButton()
        DrawPrintButtonBase("ButtonEvents.PrintWhole()")
    End Sub

End Class