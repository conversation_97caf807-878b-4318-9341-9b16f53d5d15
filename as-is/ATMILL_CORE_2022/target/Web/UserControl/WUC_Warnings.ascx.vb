﻿Imports UsersGUI

Partial Class UserControl_WUC_Warnings
    Inherits UserControl

    Public m_ListProcessAnomalies As New Generic.List(Of Anomaly)
    Public mConfig As config

    Private Const diff_view_warningin_min As Integer = 60

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        mConfig = CType(Application("Config"), config)

        GetAnomalies(EnumSystemAnomalies.ProcessWarning)
    End Sub

    Protected Sub TimerReloadWUC_Tick(ByVal sender As Object, ByVal e As System.EventArgs) Handles TimerReloadWUC.Tick
        GetAnomalies(EnumSystemAnomalies.ProcessWarning)
        UpdatePanelDiv.Update()
    End Sub

    Private Sub GetAnomalies(ByVal m_Type As EnumSystemAnomalies)
        Dim m As New Anomaly(m_Type)

        m_ListProcessAnomalies.Clear()

        Select Case m_Type
            Case EnumSystemAnomalies.ProcessWarning
                m_ListProcessAnomalies = m.GetListAnomalies(m_Type, diff_view_warningin_min)
        End Select
    End Sub

End Class