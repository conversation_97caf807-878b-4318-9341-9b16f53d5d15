﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_Lots_menu.ascx.vb" Inherits="WUC_Lots_menu" %>

<div class="lotsMenuCont">
    <div class="lothistory--settings noPrint">
        <div class="lothistory--mode">
            <span><%= m_config.GetEntryByKeyName("LOTHISTORY_MODE").GetValue %>:</span>
            <asp:RadioButton ID="rbRinTracciabilita" runat="server" GroupName="options" />
            <asp:RadioButton ID="rbTracciabilita" runat="server" Checked="true" GroupName="options" />
        </div>
        <div class="lothistory--comp">
            <asp:CheckBox ID="cbComposizione" runat="server" Visible="true" />
        </div>
        <div class="lothistory--actions">
            <asp:Button ID="btnEsegui" runat="server" CssClass="btnGeneral" />
        </div>
    </div>

    <div class="hr noPrint"></div>

    <div ID="compTreeSection" runat="server" Visible="false" class="lothistory--comptree noPrint">
        <asp:CheckBox ID="cbCompTree" runat="server" Visible="true" AutoPostBack="true" Checked="true" />
    </div>

    <div class="lothistory--printtree noPrint">
        <asp:CheckBox ID="cbPrintTree" runat="server" Visible="true" AutoPostBack="true" Checked="true" />
    </div>

    <asp:UpdatePanel runat="server" ID="UpdatePanelTreeView" UpdateMode="Conditional" Visible="true" onclick="lotsTreeViewPanelOnClick(event)">
        <ContentTemplate>
            <asp:Table ID="tblLots" runat="server">
                <asp:TableRow ID="TableRow1" runat="server">
                    <asp:TableCell ID="TableCell1" runat="server" CssClass="tdExpanse">
                        <asp:TreeView ID="treeLots" CssClass="floatLeft" runat="server" ShowLines="true" SkipLinkText="">
                            <SelectedNodeStyle CssClass="selectedNode" />
                        </asp:TreeView>
                    </asp:TableCell>
                </asp:TableRow>
            </asp:Table>
            <p id="userNotes_indicatorDescription" runat="server" class="txtSmall"></p>
        </ContentTemplate>
    </asp:UpdatePanel>
</div>