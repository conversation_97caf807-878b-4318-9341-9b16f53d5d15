﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_Warnings.ascx.vb"
    Inherits="UserControl_WUC_Warnings" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>

<link rel="stylesheet" type="text/css" href="<%= "./css/newsMarquee.css" & myScript.GetQueryClientUpdate() %>" />
<script src="<%= "./js/newsMarquee.js" & myScript.GetQueryClientUpdate() %>" type="text/javascript"></script>
<script type="text/javascript">
    $(document).ready(function () {
        updateTicker();
    });
</script>

<div id="DivWarnings">
    <div class="menuTOP">
        <div class="txtCenter txtWhite txtBold">

            <%= mConfig.GetEntryByKeyName("Process warnings").GetValue%>
        </div>
    </div>
    <div id="HiddenDiv" style="display: none;">
        <asp:UpdatePanel ID="UpdatePanelDiv" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <asp:Timer ID="TimerReloadWUC" runat="server" Interval="10000" Enabled="true">
                </asp:Timer>
                <ul id="HiddenTicker">
                    <%
                        For Each a As UsersGUI.Anomaly In m_ListProcessAnomalies
                    %>
                    <li><%=a.CodeAnomaly & "_$_" & a.Data.ToShortDateString & "_$_" & a.Data.ToShortTimeString & "_$_" & a.Description%></li>
                    <%
                        Next
                    %>
                </ul>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <div class="menuMID">
        <div id="warnings">
            <ul id="ticker" class="ticker">
            </ul>
        </div>
    </div>
    <div id="DivPopMessage" class="DivPopMessage">
    </div>
</div>