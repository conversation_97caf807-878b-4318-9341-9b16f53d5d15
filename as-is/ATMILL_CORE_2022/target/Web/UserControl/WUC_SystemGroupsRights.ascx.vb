﻿Imports System.Data
Imports UsersGUI
Imports WebDataBaseLayer
Imports WebTools.tools

Partial Class WUC_WUC_SystemGroupsRights
    Inherits UserControl

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing
    Private mTopMenuName As String = String.Empty
    Private mMenuName As String = String.Empty
    Private mControl As String = String.Empty
    Private system_groups_rights As New List(Of AccessLevel)

    Private b_edit As Boolean = False
    Private b_view As Boolean = False
    Private m_page_id As Integer = m_InvalidId

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded
        If Me.Visible Then
            If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            If Current.Request("edit") IsNot Nothing AndAlso Current.Request("edit").ToString.ToUpper = m_StringYes Then
                b_edit = True
            Else
                b_edit = False
            End If

            If Not b_edit Then
                b_view = True
            End If

            If Not Current.Request("page_id") Is Nothing Then
                Integer.TryParse(Current.Request("page_id"), m_page_id)
            Else
                m_page_id = 0
            End If

            'Access rights
            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Editing) AndAlso b_edit = True Then
                lblError.Text = m_config.GetEntryByKeyName("EDIT_DENIED").GetValue
                Me.dError.Visible = True
                Me.dView.Visible = False
                Exit Sub
            ElseIf Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) AndAlso b_view = True Then
                lblError.Text = m_config.GetEntryByKeyName("ACCESS_DENIED").GetValue
                Me.dError.Visible = True
                Me.dView.Visible = False
                Exit Sub
            End If

            Me.btnEdit.Text = m_config.GetEntryByKeyName("EDIT_BUTTON").GetValue()
            Me.btnSubmit.Text = m_config.GetEntryByKeyName("SUBMIT_BUTTON").GetValue()
            Me.btnCancel.Text = m_config.GetEntryByKeyName("CANCEL_BUTTON").GetValue()
            Me.lblPageName.Text = m_config.GetEntryByKeyName("PAGE_NAME").GetValue() & ":"

            LoadSystemPages()

            If b_edit Then
                btnEdit.Visible = False
                btnSubmit.Visible = True
                btnCancel.Visible = True

                ddl_pagename.Enabled = False
            End If

            If b_view Then
                btnSubmit.Visible = False
                btnCancel.Visible = False

                ddl_pagename.Enabled = True
            End If

            If m_page_id <> 0 Then
                ddl_pagename.SelectedValue = m_page_id
                ddl_pagename_SelectedIndexChanged(sender, e)
            End If
        End If
    End Sub

    Protected Sub ddl_pagename_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddl_pagename.SelectedIndexChanged
        LoadSystemGroupRights(ddl_pagename.SelectedItem.Value)

        DrawSystemGroupRights()

        If b_view AndAlso ddl_pagename.SelectedValue <> 0 AndAlso WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Editing) Then
            btnEdit.Visible = True
        End If
    End Sub

    Private Sub LoadSystemPages()
        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable
        Dim dt2 As New Data.DataTable

        Dim i As Integer = 1

        sSelect = "SELECT ID, MENU_NAME, PAGE_NAME FROM SYSTEM_PAGES WHERE IS_MOBILE_PAGE LIKE 'NO'"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        dt2.Columns.Add("ID")
        dt2.Columns.Add("FRIENDLY_PAGE_NAME")

        For Each dr As Data.DataRow In dt.Rows
            Dim dr2 As Data.DataRow

            dr2 = dt2.NewRow
            dr2.Item(0) = dr.Item(0)
            dr2.Item(1) = m_config.GetEntryByKeyName(dr.Item(1)).GetValue & " - " &
                            m_config.GetEntryByKeyName("MAIN_" & dr.Item(2) & "_TITLE").GetValue
            dt2.Rows.InsertAt(dr2, i)

            i += 1
        Next

        Dim r As Data.DataRow
        r = dt2.NewRow
        r.Item(0) = 0
        r.Item(1) = String.Empty
        dt2.Rows.InsertAt(r, 0)

        Dim dv As New Data.DataView(dt2)
        dv.Sort = "FRIENDLY_PAGE_NAME"

        ddl_pagename.DataSource = dv
        ddl_pagename.ID = "ID"
        ddl_pagename.DataValueField = "ID"
        ddl_pagename.DataTextField = "FRIENDLY_PAGE_NAME"
        ddl_pagename.DataBind()

    End Sub

    Private Sub LoadSystemGroupRights(ByVal id_page As Integer)
        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable
        Dim dt2 As New Data.DataTable

        system_groups_rights.Clear()

        sSelect = "SELECT ID, ACCESS_LEVEL FROM SYSTEM_ACCESS_LEVELS ORDER BY ACCESS_LEVEL"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        dt2.Columns.Add("ID")
        dt2.Columns.Add("ACCESS_LEVEL")

        For Each dr As Data.DataRow In dt.Rows
            Dim dr2 As Data.DataRow

            dr2 = dt2.NewRow
            dr2.Item(0) = dr.Item(0)
            dr2.Item(1) = m_config.GetEntryByKeyName(dr.Item(1)).GetValue
            dt2.Rows.Add(dr2)
        Next

        Dim dv As New Data.DataView(dt2)
        dv.Sort = "ACCESS_LEVEL"

        For Each dr As Data.DataRow In dv.ToTable.Rows
            Dim al As New AccessLevel(dr("ID"), dr("ACCESS_LEVEL"), id_page)
            system_groups_rights.Add(al)
        Next

    End Sub

    Private Sub DrawSystemGroupRights()
        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable

        tblGroupsRights.Rows.Clear()

        Dim row_header As New System.Web.UI.HtmlControls.HtmlTableRow
        row_header.Attributes("class") = "label-text rowHeader txtBold"
        row_header.Attributes("style") = "color:White;background-color:ORCHID;height:20px;text-align:center;"
        tblGroupsRights.Rows.Add(row_header)

        Dim tc_empty As New System.Web.UI.HtmlControls.HtmlTableCell
        tc_empty.Attributes("style") = "border-style:None;height:25px;"
        tblGroupsRights.Rows(tblGroupsRights.Rows.Count - 1).Cells.Add(tc_empty)

        ' intestazione
        For Each al As AccessLevel In system_groups_rights
            ' intestazione
            Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
            tc.Attributes("style") = "border-style:None;height:25px;"
            tc.InnerText = al.LevelName

            tblGroupsRights.Rows(tblGroupsRights.Rows.Count - 1).Cells.Add(tc)
        Next

        ' table body
        ' recupero tutti i SYSTEM_ACCESS_LEVELS
        sSelect = "SELECT ID, GROUP_NAME FROM SYSTEM_GROUPS ORDER BY GROUP_NAME"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As Data.DataRow In dt.Rows

            Dim row As New System.Web.UI.HtmlControls.HtmlTableRow

            If tblGroupsRights.Rows.Count Mod 2 = 0 Then
                row.Attributes("class") = "rowOdd"
            Else
                row.Attributes("class") = "rowEven"
            End If
            tblGroupsRights.Rows.Add(row)

            Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
            tc.InnerText = dr("GROUP_NAME").ToString

            tblGroupsRights.Rows(tblGroupsRights.Rows.Count - 1).Cells.Add(tc)

            For Each al As AccessLevel In system_groups_rights
                Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                tc2.Align = "center"

                If b_view Then
                    Dim img As New System.Web.UI.HtmlControls.HtmlImage

                    If al.HasAccess(dr("ID")) Then
                        img.Src = "~/image/greenled.png"
                    Else
                        img.Src = "~/image/redled.png"
                    End If
                    tc2.Controls.Add(img)
                End If

                If b_edit Then
                    Dim chk As New CheckBox
                    chk.ID = "chk_" & al.LevelId & "_" & dr("ID").ToString ' chk_IDSAL_IDGROUP

                    If al.HasAccess(dr("ID")) Then
                        chk.Checked = True
                    Else
                        chk.Checked = False
                    End If
                    tc2.Controls.Add(chk)
                End If

                tblGroupsRights.Rows(tblGroupsRights.Rows.Count - 1).Cells.Add(tc2)
            Next
        Next

    End Sub

    Protected Sub btnSubmit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSubmit.Click
        Dim m_control As Control
        Dim dt_groups As DataTable
        Dim dt_sal As DataTable

        ' devo usare le tabelle SYSTEM_GROUPS e SYSTEM_ACCESS_LEVEL
        dt_groups = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable("SELECT ID FROM SYSTEM_GROUPS", False)
        dt_sal = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable("SELECT ID FROM SYSTEM_ACCESS_LEVELS", False)

        For Each dr_group As DataRow In dt_groups.Rows
            For Each dr_sal As DataRow In dt_sal.Rows
                m_control = UsersGUI.tools.FindControlRecursive(Me, "chk_" & dr_sal("ID").ToString & "_" & dr_group("ID").ToString)

                If Not m_control Is Nothing Then

                    Dim sgr As New SystemGroupsRights()

                    sgr.IdGroup = dr_group("ID").ToString
                    sgr.IdSal = dr_sal("ID").ToString
                    sgr.IdPage = m_page_id

                    If CType(m_control, CheckBox).Checked Then
                        sgr.Id = UsersGUI.tools.GetNextId("SYSTEM_GROUPS_RIGHTS", "ID")
                        sgr.Save()
                    Else
                        sgr.Delete()
                    End If
                End If
            Next
        Next

        RedirectFromEditPage()
    End Sub

    Protected Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        RedirectFromEditPage()
    End Sub

    Protected Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        Dim s_redirect_url As String = Request.RawUrl

        If s_redirect_url.Contains("page_id=") Then
            Dim objNewValueCollection As NameValueCollection = HttpUtility.ParseQueryString(Request.QueryString.ToString())
            objNewValueCollection.Remove("page_id")

            Dim sBaseUrl As String() = s_redirect_url.Split("?")
            Dim sNewQueryString As String = "?" + objNewValueCollection.ToString()

            s_redirect_url = sBaseUrl(0) + sNewQueryString
        End If

        Response.Redirect(s_redirect_url & "&edit=yes&page_id=" & ddl_pagename.SelectedValue)
    End Sub

    Private Sub RedirectFromEditPage()
        Dim s_redirect_url As String = Request.RawUrl

        If s_redirect_url.Contains("edit=yes") Then
            Dim objNewValueCollection As NameValueCollection = HttpUtility.ParseQueryString(Request.QueryString.ToString())
            objNewValueCollection.Remove("edit")

            Dim sBaseUrl As String() = s_redirect_url.Split("?")
            Dim sNewQueryString As String = "?" + objNewValueCollection.ToString()

            s_redirect_url = sBaseUrl(0) + sNewQueryString
        End If

        Response.Redirect(s_redirect_url)
    End Sub

End Class

Friend Class AccessLevel
    Private m_level_id As Integer
    Private m_level_name As String
    Private m_group_access_levels As List(Of GroupAccessLevel)

    Public Sub New(ByVal level_id As Integer, ByVal level_name As String, ByVal id_page As Integer)
        m_level_id = level_id
        m_level_name = level_name
        m_group_access_levels = GetGroupsForLevel(level_id, id_page)
    End Sub

    Private Function GetGroupsForLevel(ByVal level_id As Integer, ByVal id_page As Integer) As List(Of GroupAccessLevel)
        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable
        Dim group_access_level As New List(Of GroupAccessLevel)

        ' recupero i SYSTEM_GROUPS_RIGHTS per la pagina e gruppo
        sSelect = "SELECT ID, ID_GROUP FROM SYSTEM_GROUPS_RIGHTS WHERE SAL_ID = " & level_id & " AND ID_PAGE = " & id_page
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As Data.DataRow In dt.Rows

            Dim gal As New GroupAccessLevel(dr("ID_GROUP"))
            group_access_level.Add(gal)

        Next

        Return group_access_level
    End Function

    Public Function HasAccess(ByVal group_id As Integer) As Boolean
        Dim ret_val As Boolean = False

        For Each gal As GroupAccessLevel In m_group_access_levels
            If gal.GroupId = group_id Then
                ret_val = True
                Exit For
            End If
        Next

        Return ret_val
    End Function

    Public ReadOnly Property LevelId As Integer
        Get
            Return m_level_id
        End Get
    End Property

    Public ReadOnly Property LevelName As String
        Get
            Return m_level_name
        End Get
    End Property

    Public ReadOnly Property GroupAccessLevels As List(Of GroupAccessLevel)
        Get
            Return m_group_access_levels
        End Get
    End Property

End Class

Friend Class GroupAccessLevel
    Private m_group_id As Integer

    Public Sub New(ByVal group_id As Integer)
        m_group_id = group_id
    End Sub

    Public ReadOnly Property GroupId As Integer
        Get
            Return m_group_id
        End Get
    End Property

End Class