﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_RecipeUpload.ascx.vb"
    Inherits="UserControl_WUC_RecipeUpload" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>

<div id="DivRecipeUpload">
    <div class="menuTOP">
        <div class="txtCenter txtWhite txtBold">
            <%= m_config.GetEntryByKeyName("RCP_UPLOAD").GetValue%>
        </div>
    </div>
    <div id="divSelect" class="menuMID" runat="server">
        <table>
            <tr>
                <td class="vAlgTop">
                    <%=m_config.GetEntryByKeyName("RCP_SECTION").GetValue()%>
                </td>
            </tr>
            <tr>
                <td>
                    <asp:DropDownList ID="ddl_mrpt" runat="server"></asp:DropDownList>
                </td>
            </tr>
        </table>
        <br />
        <div class="txtCenter">
            <asp:Button ID="btnUpload" runat="server" class="btnGeneral" OnClientClick="myLoader.start();" />
        </div>
    </div>
</div>