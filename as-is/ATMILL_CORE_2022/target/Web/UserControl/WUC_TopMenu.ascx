﻿<%@ Control Language="VB" CodeFile="WUC_TopMenu.ascx.vb" Inherits="WUC_WUC_TopMenu" %>
<%@ Import Namespace="UsersGUI" %>

<script type="text/javascript" src="<%= "./js/TopMenu.js" & myScript.GetQueryClientUpdate() %>"></script>

<link rel="stylesheet" href="<%= "./css/TopMenu.css" & myScript.GetQueryClientUpdate() %>" />

<%
    Dim bPvTitle As Boolean = True

    Dim bPv As Boolean = True
    Dim count As Integer = 1

    Dim ml As NLinkTitleMenu
    Dim m_start As Integer = 0
    Dim m_stop As Integer = 0
    Dim m_split As String()
    Dim i As Integer = 0
    Dim visible_elem_count As Integer = 0

    For Each m As UsersGUI.MenuAtmill In m_config.GetMenusTopLevel

        If bPvTitle Then
            bPvTitle = False
            Response.Write("<ul id=""nav"" class=""dropdown-menu"">")
        End If

        Response.Write("<li ><a id=" & m.Name & "><img src=""" & m.MenuIcon & """ align=""center"" /><span>" & m.Title & "</span></a>")

        ' init variables
        bPv = True
        count = 1

        If m.TitleMenus Is Nothing OrElse m.TitleMenus.Count = 0 Then
            If bPv Then
                bPv = False
                Response.Write("<ul class=""ui-widget-content-" & m.Name & """ id=""sub_" & m.Name & """>")
            End If
            For Each mi As UsersGUI.MenuItem In m.menuItems
                'If myFunction.ViewMenu(mi) Then
                If mi.Visible Then
                    Response.Write("<li><a href=""" & myFunction.GetRoot() & mi.MenuItemLink.GetValue() & """>" & m_config.GetEntryByKeyName(mi.MenuItemName.GetName).GetValue & "</a></li>")
                End If
            Next
            Response.Write("</ul>")
        Else
            For Each mi As TitleMenu In m.TitleMenus
                If bPv Then
                    bPv = False
                    Response.Write("<ul class=""ui-widget-content-" & m.Name & """ id=""sub_" & m.Name & """>")
                End If

                If m.NLinkTitleMenus IsNot Nothing AndAlso m.NLinkTitleMenus.Count > 0 Then

                    ' init variables
                    ml = m.NLinkTitleMenus(count - 1)
                    m_start = 0
                    m_stop = 0
                    i = 0
                    visible_elem_count = 0

                    m_split = Split(ml.GetValue, "-")
                    m_start = Integer.Parse(m_split(0))
                    m_stop = Integer.Parse(m_split(1))

                    ' conto gli elementi visibili
                    For i = m_start To m_start + m_stop - 1
                        If m.menuItems(i).Visible Then
                            visible_elem_count += 1
                        End If
                    Next

                    If m_stop > 1 AndAlso visible_elem_count > 1 Then
                        Response.Write("<li id=""voice_" & count & "_" & m.Name & """><a href=""#"">" & mi.Value & "</a>")
                        Response.Write("<ul class=""ui-widget-content-" & m.Name & """ id=""sub_" & count & "_sub_" & m.Name & """></li>")
                    End If

                    For i = m_start To m_start + m_stop - 1
                        If m.menuItems(i).Visible Then
                            Response.Write("<li><a href=""" & myFunction.GetRoot() & m.menuItems(i).MenuItemLink.GetValue & """>" & m_config.GetEntryByKeyName(m.menuItems(i).MenuItemName.GetName).GetValue & "</a></li>")
                        End If
                    Next

                    If m_stop > 1 AndAlso visible_elem_count > 1 Then
                        Response.Write("</ul>")
                    End If
                Else
                    Response.Write("<li><a href=""#"">" & mi.Value & "</a>")
                End If
                Response.Write("</li>")
                count += 1
            Next
            Response.Write("</ul>")

        End If
        Response.Write("</li>")
    Next
    Response.Write("</ul>")
%>

<script type="text/javascript">
    $(function () {
        $('.dropdown-menu').dropdown_menu();
    });
</script>