﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_View.ascx.vb" Inherits="WUC_WUC_View" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>

<div id="div_query" runat="server" class="tabMrg3 mrgBot" visible="false">
    <table class="tabMrg3">
        <tr>
            <td class="bgImg2">&nbsp;
            </td>
            <td class="tabBground1">&nbsp;
            </td>
        </tr>
        <tr>
            <td class="txtBold center-y" colspan="2">

                <%= mConfig.GetEntryByKeyName("SEARCH").GetValue()%>&nbsp;<asp:Button
                    ID="btnViewSearch" runat="server" CssClass="btnGeneral" Text="Chiudi" />
            </td>
        </tr>
    </table>
    <table class="txtLeft tabMrg3Top brdDas">
        <tr>
            <td>
                <div id="dQuery" runat="server" style="display: none;">
                    <table id="tblQuery" class="txtLeft" runat="server">
                    </table>
                    <asp:HiddenField ID="divQueryVisibility_ControllerField" Value="-1" runat="server" />
                    <br />
                    <asp:Button ID="btnStartQuery" runat="server" CssClass="btnGeneral" Width="200" />
                    <br />
                </div>
            </td>
        </tr>
    </table>
</div>
<div id="dView_cont" style="overflow-x: auto;">
    <div id="print_area" class="tabMrg3">
        <div id="customerHeader"></div>
        <table id="titleTable" class="tabMrg3">
            <tr class="noPrint">
                <td class="bgImg2">&nbsp;
                </td>
                <td class="tabBground1">&nbsp;
                </td>
            </tr>
            <tr>
                <td id="pageTitle" class="txtBold" colspan="2">
                    <%=If(mPageName IsNot Nothing, mConfig.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue(), "")%>
                </td>
            </tr>
        </table>
        <div id="dView" runat="server" visible="true">
            <asp:Timer ID="TimerReloadPage" runat="server" Interval="30000" Enabled="false">
            </asp:Timer>
            <div id="Buttons" runat="server" visible="false">
                <asp:Label ID="lblStatusPlanning" runat="server" CssClass="label-text-login"></asp:Label>
                <div class="verticalSpace"></div>
                <div class="txtCenter">
                    <asp:Button ID="btnRefreshBatchSize" runat="server" Enabled="true" Visible="false" OnClientClick="myLoader.start()" CssClass="btnRefresh btnLeftIcon"/>
                    <asp:Button ID="btnStartPlanning" runat="server" Enabled="false" Visible="false" CssClass="btnCycleStart btnLeftIcon" />
                    <asp:Button ID="btnStopPlanning" runat="server" Enabled="false" Visible="false" CssClass="btnCycleStop btnLeftIcon" />
                    <asp:Button ID="btnPlanningMode" runat="server" Visible="false" CssClass="btnPlanningMode btnLeftIcon" />
                    <div class="verticalSpace"></div>
                </div>
            </div>
            <div id="div_risultati" class="tabMrg3Top" visible="false">
                <table id="tblHeader" runat="server" visible="true"></table>
                <div id="dgRisultatiContainer">
                    <asp:GridView ID="dgRisultati" runat="server" AllowPaging="False" AutoGenerateColumns="false"
                        CssClass="bord1px handleDynamicHeight" AllowSorting="true" HorizontalAlign="Left" BackColor="#FFFFFF" Enabled="True" OnLoad="dgRisultati_Load">
                        <RowStyle CssClass="label-text rowOdd" HorizontalAlign="Center" VerticalAlign="Middle" />
                        <HeaderStyle CssClass="label-text rowHeader" />
                        <AlternatingRowStyle CssClass="label-text rowEven"></AlternatingRowStyle>
                    </asp:GridView>
                </div>
                <asp:HiddenField ID="pager_ControllerField" runat="server" Value="0"></asp:HiddenField>
                <div id="div_pager_js" runat="server" class="div_pager bord1px hideOnPrint" data-selected-page="" data-pages-number="">
                </div>
            </div>
        </div>
        <div id="dError" runat="server" visible="false" class="margin1">
            <table id="Table1" runat="server">
                <tr>
                    <td>
                        <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                    </td>
                </tr>
            </table>
        </div>
        <table class="tabMrg3">
            <tr>
                <td>
                    <div id="dSumColumns" runat="server" visible="true" class="margin1">
                        <table id="tblSum" runat="server">
                        </table>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
<div id="div_blanket" runat="server" class="blanket" blanket></div>

<script type="text/javascript">
    blanket.show();
</script>