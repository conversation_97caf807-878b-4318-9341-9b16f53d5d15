﻿Imports UsersGUI
Imports WebTools
Imports WebTools.tools
Imports CommonDefines.Defines

Public Class myFunction

    Public Shared Function GetRoot() As String
        Return System.Configuration.ConfigurationManager.AppSettings("root")
    End Function

    Public Shared Function GetServerName() As String
        Return System.Configuration.ConfigurationManager.AppSettings("ServerName")
    End Function

    Private Shared Function GetIdCycleFromScreen(scr As Screen, ByRef error_msg As String) As Integer
        Dim ret_val As Integer = m_InvalidId

        Try
            ' tutte le pagine web al momento di un salvataggio chiamano tutte le funzioni
            ' in questo caso mi assicuro che il chiamante sia di tipo CYCLE (EnumPageName.ProductionPlan)
            If (scr.EnumPageNameCode = EnumPageName.ProductionPlan) Then
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("CYC_ID") Then
                        'Recupero il codice del cycle dallo screen di riferimento
                        ret_val = CInt(f.FixedRuntimeValue)
                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            myErrorMessage.AppendCustomErrorMsg(error_msg, "[GetIdCycleFromScreen]: Nome ciclo errato -> " & scr.Name.ToUpper())
        End Try

        Return ret_val
    End Function

    Private Shared Function GetIdRecipeFromScreen(scr As Screen, ByRef error_msg As String) As Integer
        Dim ret_val As Integer = m_InvalidId

        Try
            ' tutte le pagine web al momento di un salvataggio chiamano tutte le funzioni
            ' in questo caso mi assicuro che il chiamante sia di tipo CYCLE (EnumPageName.ProductionPlan)
            If (scr.EnumPageNameCode = EnumPageName.RecipeParameters) Then
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("MTR_ID") Then
                        'Recupero il codice del cycle dallo screen di riferimento
                        ret_val = CInt(f.FixedRuntimeValue)
                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            myErrorMessage.AppendCustomErrorMsg(error_msg, "[GetIdRecipeFromScreen]: Nome meta recipe errato -> " & scr.Name.ToUpper())
        End Try

        Return ret_val
    End Function

    Public Shared Function CtrlDataCycle(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim rec_id As Long = m_InvalidId
        Dim pro_id As Long = m_InvalidId
        Dim source_set As Integer = 0
        Dim source_bin As Long = 0
        Dim list_source As New Generic.List(Of Long)
        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim dest_set As Integer = 0
        Dim dest_bin As Long = 0
        Dim list_dest As New Generic.List(Of Long)
        Dim custom_error_msg As String = String.Empty
        Dim value As Double = 0.0
        Dim cardinality As Integer = 0
        Dim field_split As String() = Nothing
        Dim recipe_param_val As String = String.Empty
        Dim load_mode As Integer = 0
        Dim b_load_mode_continuous_valid_time = False

        Dim IdCyleFromName As Integer = GetIdCycleFromScreen(scr, error_msg)

        ' se il chiamante non è un CYCLE_N esco subito
        If (IdCyleFromName = m_InvalidId) Then
            Return True
        End If

        Select Case CType(IdCyleFromName, SSCycles)
            ' TODO

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("CYCLE_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "[CtrlDataCycle] Unhandled CYCLE")
                End If
        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If
    End Function

    Public Shared Function AskUserUnfilledDataCycle(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim rec_id As Long = m_InvalidId
        Dim pro_id As Long = m_InvalidId
        Dim source_set As Integer = 0
        Dim source_bin As Long = 0
        Dim list_source As New Generic.List(Of Long)
        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim dest_set As Integer = 0
        Dim dest_bin As Long = 0
        Dim list_dest As New Generic.List(Of Long)
        Dim value As Double = 0.0
        Dim recipe_param_val As String = String.Empty

        Dim IdCyleFromName As Integer = GetIdCycleFromScreen(scr, error_msg)

        ' se il chiamante non è un CYCLE_N esco subito
        If (IdCyleFromName = m_InvalidId) Then
            Return True
        End If

        Select Case CType(IdCyleFromName, SSCycles)
            ' TODO

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("CYCLE_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "[AskUserUnfilledDataCycle] Unhandled CYCLE")
                End If
        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If

    End Function

    Public Shared Function AskUserConfirmDataCycle(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim rec_id As Long = m_InvalidId
        Dim pro_id As Long = m_InvalidId
        Dim source_set As Integer = 0
        Dim source_bin As Long = 0
        Dim list_source As New Generic.List(Of Long)
        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim dest_set As Integer = 0
        Dim dest_bin As Long = 0
        Dim list_dest As New Generic.List(Of Long)
        Dim value As Double = 0.0
        Dim cardinality As Integer = 0
        Dim field_split As String() = Nothing
        Dim recipe_param_val As String = String.Empty

        Dim IdCyleFromName As Integer = GetIdCycleFromScreen(scr, error_msg)

        ' se il chiamante non è un CYCLE_N esco subito
        If (IdCyleFromName = m_InvalidId) Then
            Return True
        End If

        Select Case CType(IdCyleFromName, SSCycles)
            ' TODO

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("CYCLE_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "[AskUserConfirmDataCycle] Unhandled CYCLE")
                End If
        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If

    End Function

    Public Shared Function CtrlDataRecipe(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim ddl As DropDownList = Nothing
        Dim custom_error_msg As String = String.Empty
        Dim value As Double = 0.0
        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim field_split As String() = Nothing
        Dim cardinality As Integer = 0

        Dim IdMetaRecipeFromName As Integer = GetIdRecipeFromScreen(scr, error_msg)

        Select Case CType(IdMetaRecipeFromName, SSMetaRecipe)
            ' TODO

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("RECIPE_PARAMS_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "Unhandled RECIPE")
                End If
        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If

    End Function

    Public Shared Function CtrlDataScreen(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control
        Dim custom_error_msg As String = String.Empty
        Dim dbl_value As Double = 0.0
        Dim id As Long = m_InvalidId

        Select Case scr.EnumPageNameCode

            Case EnumPageName.SystemUsers

                Dim password As String = String.Empty
                Dim password_confirm As String = String.Empty

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.Equals("USER_NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("PASSWORD") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            password = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("PASSWORD_CONFIRM") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            password_confirm = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                If password <> String.Empty AndAlso password_confirm <> String.Empty Then
                    If password <> password_confirm Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("PASSWORDS_DONT_MATCH").GetValue
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

            Case EnumPageName.SystemGroups
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("GROUP_NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.SystemUsersGroups
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID_USER") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedIndex = 0 Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If

                    End If
                Next

            Case EnumPageName.EquipmentsModels
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("REFERENCE") OrElse f.FieldDb.Equals("DESCRIPTION") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next

            Case EnumPageName.ProcedureDoc, EnumPageName.EquipmentDoc
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("DESCRIPTION") OrElse f.FieldDb.Equals("LINK") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next

            Case EnumPageName.EquipmentsMainData
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") OrElse f.FieldDb.Equals("EQU_ID_MAINT_DATA") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.Equals("RATIO") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            Double.TryParse(CType(ctrl, myTextBox).Text, dbl_value)

                            If dbl_value < 0 OrElse dbl_value > 1 Then
                                custom_error_msg = scr.Parent.GetEntryByKeyName(f.FieldName).GetValue() & ": " & scr.Parent.GetEntryByKeyName("ERRORRANGE_1_0").GetValue
                                myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.ProductTypes
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PT_TYPE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next

            Case EnumPageName.Products
                Dim pt_id As Long = m_InvalidId

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("PT_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pt_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id, "PT_ID = " & pt_id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SHORT_NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If CType(ctrl, myTextBox).Text.Length > PROD_NAME_BIN_SIZE Then
                                myErrorMessage.AppendTextIsTooLong(error_msg, scr.Parent, f.FieldName, PROD_NAME_BIN_SIZE)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("HECTOLITRIC_WEIGHT") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            dbl_value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)

                            If dbl_value <= 0.0 Then
                                myErrorMessage.AppendValueEqualOrLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.Customers, EnumPageName.Suppliers, EnumPageName.Carriers
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.StockManualCorrReq
                Dim cel_id As Long = m_InvalidId
                Dim pro_id As Long = m_InvalidId

                Dim str_sql As String = String.Empty
                Dim dt As Data.DataTable

                Dim b_bin_not_idle = False

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("CEL_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            cel_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If

                    ElseIf f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.Equals("AMOUNT") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            dbl_value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)

                            If dbl_value < 0.0 Then
                                myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    End If
                Next

                If cel_id <> m_InvalidId Then

                    ' 1. controllo se la cella è idle
                    ' (equivalente della CDatabaseTools::IsIdle di c++)
                    ' 1.1 controllo su tabella FLOWS
                    str_sql = "SELECT * FROM FLOWS WHERE DISABLED = '0' AND STATUS <> 'OFF' AND (SOURCE_CELL = " & cel_id & " OR DEST_CELL = " & cel_id & ")"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    If dt.Rows.Count > 0 Then
                        b_bin_not_idle = True
                    End If

                    ' 1.2 controllo su tabella FLOW_LOGS
                    str_sql = "SELECT * FROM FLOW_LOGS WHERE MANAGED_FLAG = 0 AND (SOURCE_CELL = " & cel_id & " OR DEST_CELL = " & cel_id & ")"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    If dt.Rows.Count > 0 Then
                        b_bin_not_idle = True
                    End If

                    If b_bin_not_idle Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SMC_NOT_POSSIBLE_BIN_NOT_IDLE").GetValue())
                    End If

                    ' 2. non è possibile cambiare prodotto senza prima resettare la cella
                    If pro_id <> m_InvalidId Then
                        str_sql = "SELECT PRO_ID FROM CELLS WHERE ID = " & cel_id
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                        If dt.Rows.Count > 0 Then

                            For Each dr As Data.DataRow In dt.Rows
                                If Not IsDBNull(dr.Item(0)) Then
                                    If pro_id <> Integer.Parse(dr.Item(0).ToString) Then
                                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SMC_NOT_POSSIBLE_BIN_WITH_DIFFERENT_PRODUCT").GetValue())
                                    End If
                                End If
                            Next

                        End If
                    End If
                End If

            Case EnumPageName.Recipes
                Dim mtr_id As Long = m_InvalidId

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("MTR_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl.Visible Then
                            If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                                myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Else
                                mtr_id = Long.Parse(CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("DESCRIPTION") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id, "MTR_ID = " & mtr_id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.PoNumbers

                'Dim po_number_erp As String = String.Empty
                Dim status_po_number As Integer = m_InvalidId
                Dim id_po_number As String = String.Empty

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") AndAlso f.IsHidden = True Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            id_po_number = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("NUMBER") AndAlso f.IsHidden = False Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            'po_number_erp = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("ID_STATUS") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            status_po_number = Integer.Parse(CType(ctrl, myDropDownList).SelectedValue)
                        End If
                    End If
                Next

                Dim max_digit As Integer = Integer.Parse(GetParameter("MAX_PO_NUMBER_DIGIT"))
                If (id_po_number.Length > max_digit) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, String.Format(scr.Parent.GetEntryByKeyName("PO_NUMBER_MAX_LENGTH").GetValue(), max_digit))
                End If

                If (id_po_number <> String.Empty) Then
                    Dim s_actual_status As Integer = WebTools.tools.GetPoNumberStatus(id_po_number)
                    If (s_actual_status > EnumStatusPONumbers.NeverUsed) Then
                        'Se sono qui vuol dire che il sistema ha già cambiato lo stato INSERT del PO_NUMBER
                        'può essere stato fatto in automatico dal sistema perchè presente almeno un flusso con questo PO_NUMBER
                        'oppure può essere stato messo manualmente dall'operatore
                        If (WebTools.tools.PONumberAlreadyUsedInFlows(id_po_number) AndAlso status_po_number = EnumStatusPONumbers.NeverUsed) Then
                            'Ho già almeno un flusso con il PO_NUMBER, non posso metterlo allo stato di INSERT (status_po_number)
                            myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("NO_SET_NEVERUSED_PO_NUMBER").GetValue())
                        End If
                    End If
                End If
            Case EnumPageName.ScalesBatchSize
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("BATCH_SIZE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next
            Case EnumPageName.BatchNumber
                Dim batch_number As String = String.Empty
                Dim id_batch_number As Long = 0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") AndAlso f.IsHidden = True Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            id_batch_number = Long.Parse(CType(ctrl, myTextBox).Text)
                        End If
                    ElseIf f.FieldDb.Equals("BATCH_NUMBER") AndAlso f.IsHidden = False Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            batch_number = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                If (batch_number.Length > MAX_DIGIT_BATCH_NUMBER) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("BATCH_NUMBER_MAX_LENGTH").GetValue())
                End If
        End Select

        ' chiamo la funzione per eventuali controlli custom sugli screen non di ciclo e non di ricetta
        CtrlDataScreenCustom(scr, error_msg, root)

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If
    End Function

    Private Shared Sub CtrlDataScreenCustom(ByVal scr As Screen, ByRef m_Error As String, ByVal root As Control)
        Dim ctrl As Control = Nothing
        Dim custom_error_msg As String = String.Empty
        Dim id As Long = m_InvalidId
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        Select Case scr.EnumPageNameCode
            ' TODO
        End Select

    End Sub

    Public Shared Sub CtrlDeleteCustom(ByVal scr As Screen, ByRef m_Error As String, ByVal id As Integer)
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        Select Case scr.EnumPageNameCode
            ' nothing to do
        End Select

    End Sub

    Public Shared Sub SetDefaultDataCycle(ByVal scr As Screen, ByVal root As Control)
        Dim ctrl As Control = Nothing

        Select Case scr.Name.ToUpper
            ' TODO
        End Select
    End Sub

    Public Shared Sub SetDefaultDataScreen(ByVal scr As Screen, ByVal root As Control)
        Dim ctrl As Control = Nothing

        Select Case scr.EnumPageNameCode
            ' TODO
        End Select
    End Sub

    Public Shared Function WaitCompleted(ByVal scr As Screen, Optional ByVal wait_operation As EnumWaitOperation = EnumWaitOperation.Submit) As Boolean
        Dim bOk As Boolean = False
        Dim temp_string As String

        Select Case wait_operation
            Case EnumWaitOperation.Submit
                Select Case scr.EnumPageNameCode
                    Case EnumPageName.StockManualCorrReq
                        WebTools.tools.SetParameter("STOCK_MANUAL_CORR_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("STOCK_MANUAL_CORR_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ParcelsToBeConfirmed
                        WebTools.tools.SetParameter("PARCEL_TO_BE_CONFIRMED_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("PARCEL_TO_BE_CONFIRMED_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ShipmentsToBeConfirmed
                        WebTools.tools.SetParameter("SHIPMENT_TO_BE_CONFIRMED_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("SHIPMENT_TO_BE_CONFIRMED_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ScalesBatchSize
                        WebTools.tools.SetParameter("SCALES_BATCH_SIZE_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("SCALES_BATCH_SIZE_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case Else
                        bOk = True
                End Select

            Case EnumWaitOperation.Upload
                Select Case scr.EnumPageNameCode
                    Case EnumPageName.RecipeParameters
                        WebTools.tools.SetParameter("RECIPE_UPLOAD_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("RECIPE_UPLOAD_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ScalesBatchSize
                        WebTools.tools.SetParameter("REFRESH_SCALES_BATCH_SIZE_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("REFRESH_SCALES_BATCH_SIZE_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While
                    Case Else
                        bOk = True
                End Select

            Case Else
                bOk = True
        End Select

        Return bOk
    End Function

    Private Shared Sub AutomaticNotesFill(ByVal root As Control, ByVal dest_list As List(Of Long))
        Dim m_control As System.Web.UI.Control = Nothing
        Dim m_control_source_bin As System.Web.UI.Control = Nothing
        Dim txt As myTextBox
        Dim cyc_id As Integer
        Dim perc As Double
        Dim dr_index As Integer
        Dim sSelect As String
        Dim dt As Data.DataTable
        Dim b_source_found = False
        Dim number_of_sources As Integer = 0

        Dim source_bin As String = String.Empty
        Dim source_str As String = String.Empty
        Dim dest_str As String = String.Empty

        txt = UsersGUI.tools.FindControlRecursive(root, "AUTO_NOTES")

        If txt IsNot Nothing Then

            m_control = UsersGUI.tools.FindControlRecursive(root, "CYC_ID")
            If m_control IsNot Nothing AndAlso CType(m_control, myTextBox).Text <> String.Empty Then
                cyc_id = CType(m_control, myTextBox).Text
            Else
                Exit Sub
            End If

            ' recupero il numero di origini
            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each dr As Data.DataRow In dt.Rows
                number_of_sources += 1
            Next

            Dim i As Integer = 1

            While i <= number_of_sources

                m_control = UsersGUI.tools.FindControlRecursive(root, "SOURCE_PERC_" & i)

                If m_control IsNot Nothing AndAlso CType(m_control, myTextBox).Text <> String.Empty Then
                    perc = UsersGUI.tools.WebInputDoubleParse(CType(m_control, myTextBox).Text, 1)
                Else
                    m_control = UsersGUI.tools.FindControlRecursive(root, "SOURCE_ENABLE_" & i)

                    If m_control IsNot Nothing AndAlso CType(m_control, myCheckBox).Checked = True Then
                        perc = 100.0
                    Else
                        perc = 0.0
                    End If
                End If

                If perc > 0.0 Then

                    b_source_found = True

                    ' cerco il source bin associato (se esiste)
                    m_control_source_bin = UsersGUI.tools.FindControlRecursive(root, "SOURCE_BIN_" & i)

                    If m_control_source_bin IsNot Nothing Then
                        ' esiste il source bin associato
                        source_bin = CType(m_control_source_bin, myDropDownList).SelectedValue

                        sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id & " AND CEL_ID = " & source_bin
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                        For Each dr As Data.DataRow In dt.Rows
                            If source_str <> String.Empty Then
                                source_str &= " "
                            End If
                            source_str &= dr("DESCRIPTION").ToString
                        Next
                    Else
                        ' non esiste il source bin associato, cerco l'iesima cella tra le disponibili nella VIEW_CYCLES_TO_BINS
                        sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                        dr_index = 1

                        For Each dr As Data.DataRow In dt.Rows
                            If dr_index = i Then
                                If source_str <> String.Empty Then
                                    source_str &= " "
                                End If
                                source_str &= dr("DESCRIPTION").ToString
                                Exit For
                            Else
                                dr_index += 1
                            End If
                        Next
                    End If

                End If

                i += 1
            End While

            If Not b_source_found Then

                ' cerco il source bin associato (se esiste)
                m_control_source_bin = UsersGUI.tools.FindControlRecursive(root, "SOURCE_BIN")

                If m_control_source_bin IsNot Nothing Then
                    ' esiste il source bin associato
                    source_bin = CType(m_control_source_bin, myDropDownList).SelectedValue

                    sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id & " AND CEL_ID = " & source_bin
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                    For Each dr As Data.DataRow In dt.Rows
                        If source_str <> String.Empty Then
                            source_str &= " "
                        End If
                        source_str &= dr("DESCRIPTION").ToString
                    Next

                End If

            End If

            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_DESTINATION='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each dest As Long In dest_list

                For Each dr As Data.DataRow In dt.Rows
                    If dr("CEL_ID") = dest Then
                        If dest_str <> String.Empty Then
                            dest_str &= " "
                        End If
                        dest_str &= dr("DESCRIPTION").ToString
                        Exit For

                    End If
                Next
            Next

            ' compongo la stringa
            txt.Text = source_str

            If dest_str.Length > 0 Then
                txt.Text &= " -> " & dest_str
            End If
        End If

    End Sub

    Private Shared Sub AutomaticNotesFill(ByVal root As Control, ByVal source_list As List(Of Long), ByVal dest_list As List(Of Long))
        Dim m_control As System.Web.UI.Control = Nothing
        Dim txt As myTextBox
        Dim cyc_id As Integer
        Dim sSelect As String
        Dim dt As Data.DataTable
        Dim number_of_ingredients As Integer = 0

        Dim source_str As String = String.Empty
        Dim dest_str As String = String.Empty

        txt = UsersGUI.tools.FindControlRecursive(root, "AUTO_NOTES")

        If txt IsNot Nothing Then

            m_control = UsersGUI.tools.FindControlRecursive(root, "CYC_ID")
            If m_control IsNot Nothing AndAlso CType(m_control, myTextBox).Text <> String.Empty Then
                cyc_id = CType(m_control, myTextBox).Text
            Else
                Exit Sub
            End If

            ' origini
            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each source As Long In source_list

                For Each dr As Data.DataRow In dt.Rows
                    If dr("CEL_ID") = source Then
                        If source_str <> String.Empty Then
                            source_str &= " "
                        End If
                        source_str &= dr("DESCRIPTION").ToString
                        Exit For

                    End If
                Next
            Next

            ' destinazioni
            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_DESTINATION='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each dest As Long In dest_list

                For Each dr As Data.DataRow In dt.Rows
                    If dr("CEL_ID") = dest Then
                        If dest_str <> String.Empty Then
                            dest_str &= " "
                        End If
                        dest_str &= dr("DESCRIPTION").ToString
                        Exit For

                    End If
                Next
            Next

            ' compongo la stringa
            txt.Text = source_str

            If dest_str.Length > 0 Then
                txt.Text &= " -> " & dest_str
            End If
        End If

    End Sub

    Public Shared Sub ExportDataToERP(ByVal scr As Screen, ByVal pagename As String, ByVal action As SSBorderDatabaseAction, ByVal id As Long)
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        Select Case scr.EnumPageNameCode
            Case Else
                ' nothing to do

        End Select

    End Sub

    ' permette di forzare valori nel job appena clonato
    Public Shared Sub ForceOrderParamValuesOnClonedCycle(ByVal new_ppl_id As Long)
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing
        Dim cycle_id As Integer = m_InvalidId

        str_sql = "SELECT CYC_ID FROM PRODUCTION_PLAN WHERE ID = " & new_ppl_id

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

        If dt.Rows.Count > 0 Then
            For Each dr As Data.DataRow In dt.Rows
                cycle_id = dr(0).ToString
            Next
        End If

        Select Case cycle_id
            ' TODO

            Case Else
                ' nothing to do
        End Select

    End Sub

    Public Shared Sub DestinationCheck(scr As Screen, dest_bin As Long, pro_id As Long, list_source As Generic.List(Of Long), ByRef error_msg As String)
        Dim s_error As String = String.Empty

        ' 1.2.a Controllo di inquinamento prodotto
        CheckDestBinProduct(scr, dest_bin, pro_id, s_error)

        ' 1.2.b Controllo di inquinamento lotti
        If ((list_source Is Nothing) OrElse (Not list_source.Contains(dest_bin))) Then
            CheckBinLots(scr, dest_bin, s_error)
        End If

        ' 1.2.c Load exclusion
        CheckBinLoadStatus(scr, dest_bin, s_error)

        If (s_error <> String.Empty) Then
            If (error_msg <> String.Empty) Then
                error_msg &= "<br><hr>"
            End If

            error_msg &= scr.Parent.GetEntryByKeyName("Destination bin").GetValue() & ": " & WebTools.tools.GetBinNameFromId(dest_bin) & ":<br>"

            error_msg &= s_error
        End If
    End Sub

    Public Shared Sub SourceCheck(scr As Screen, source_bin As Long, pro_id As Long, ByRef error_msg As String)
        Dim s_error As String = String.Empty

        ' Controlli di inquinamento
        ' 1.a Prodotto
        If pro_id <> m_InvalidId Then
            CheckSourceBinProduct(scr, source_bin, pro_id, s_error)
        End If

        ' 1.b Download exclusion
        CheckBinDownloadStatus(scr, source_bin, s_error)

        If (s_error <> String.Empty) Then
            If (error_msg <> String.Empty) Then
                error_msg &= "<br><hr>"
            End If

            error_msg &= scr.Parent.GetEntryByKeyName("Source bin").GetValue() & ": " & WebTools.tools.GetBinNameFromId(source_bin) & ":<br>"

            error_msg &= s_error
        End If
    End Sub

    Public Shared Function IsSpecificControlled(cycle_id As Integer) As Boolean
        Dim ret_val As Boolean = False

        Select Case cycle_id
            'TODO
        End Select

        Return ret_val
    End Function

    Public Shared Function IsSpecificPlanned(cycle_id As Integer) As Boolean
        Dim ret_val As Boolean = False

        Select Case cycle_id
            'TODO
        End Select

        Return ret_val
    End Function

    Public Class AutoSourceRecipeData

        Public Enum TypeAutoSource
            Invalid = 0
            Macro = 1
            Micro = 2
        End Enum

        Public SourceBinId As Long = 0
        Public Ingredient As Integer = 0
        Public Repeat As Integer = 0
        Public Type As TypeAutoSource = TypeAutoSource.Invalid
    End Class

End Class