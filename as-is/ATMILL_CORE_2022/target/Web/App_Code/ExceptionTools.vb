﻿Imports System.IO

Public Class ExceptionTools

    Public Shared Sub LogUnhandledException(ByVal exc As Exception)
        ' If the exception (or its first child) are NOT of type myException
        If Not TypeOf exc Is myException.myException AndAlso (exc.InnerException Is Nothing OrElse Not TypeOf exc.InnerException Is myException.myException) Then

            ' Create a wrapper myException in order to log (the raising context is unknown)
            Dim myExc As New myException.myException(exc, "unhandled", "unhandled", "unhandled")
        End If
    End Sub

    ''' <summary>
    ''' Handles the optional redirection to the homepage after an exception was raised
    ''' </summary>
    ''' <param name="exc"></param>
    Public Shared Sub HandleExceptionRedirect(ByVal exc As Exception)

        Try
            Dim mainException As Exception = exc

            ' Selects the first most notable exception (not myException or HttpUnhandledException)
            While (mainException.InnerException IsNot Nothing)
                If TypeOf mainException Is myException.myException OrElse TypeOf mainException Is HttpUnhandledException Then
                    mainException = mainException.InnerException
                Else
                    Exit While
                End If
            End While

            ' Graphics.aspx: exception thrown inside the Chart element, that is rendered as if th was an IFrame
            If mainException.StackTrace.Contains("enableEventValidation=""true""") Then
                Current.Server.Transfer(myScript.AppendItemToQueryString(myScript.AppendSessionIdToUrl(UsersGUI.costanti.a_error), "hideNavigation", UsersGUI.costanti.m_StringYes))
            End If

            ' Exception thrown because of enableEventValidation="true" when the application pool of IIS recycles and the user clicks on a control which triggers a postback/callback
            If mainException.StackTrace.Contains("System.Web.UI.ClientScriptManager.ValidateEvent") Then
                Current.Response.Redirect(myScript.AppendItemToQueryString(myScript.AppendSessionIdToUrl(UsersGUI.costanti.a_home), "sessionExpired", UsersGUI.costanti.m_StringYes))
            End If

            If Current.Request("PAGENAME") IsNot Nothing Then
                Current.Server.Transfer(myScript.AppendSessionIdToUrl(UsersGUI.costanti.a_error))
            Else
                Current.Server.Transfer(myScript.AppendSessionIdToUrl(UsersGUI.costanti.a_error_iframe))
            End If
        Catch ex As Exception
            ' Some error occured while trying to redirect after an exception
            ' Can't risk doing anything about it
        End Try
    End Sub

End Class