﻿Imports Microsoft.VisualBasic

Public Class UnitsTools

    Public Enum MeasurementUnit
        Kg = 2
        Ton = 3
        Lbs = 4
        Cwt = 5
        L = 6
    End Enum

    Private Class UnitSymbol
        Public Const Kg As String = "kg"
        Public Const Ton = "t"
        Public Const Lbs = "lbs"
        Public Const Cwt = "Cwt"
        Public Const L = "L"
    End Class

    Public Class Decimals

        Public Class Quantity
            Public Const Kg As Integer = 3
            Public Const Ton As Integer = 3
            Public Const Lbs As Integer = 0
            Public Const Cwt As Integer = 2
            Public Const L As Integer = 2
        End Class

        Public Const Ratio As Integer = 2
    End Class

    Public Shared Function GetDecimalsFromMeasurementUnit(ByVal measurement_unit As MeasurementUnit) As Integer
        Dim ret_val As Integer

        Select Case measurement_unit
            Case MeasurementUnit.Kg
                ret_val = Decimals.Quantity.Kg

            Case MeasurementUnit.Ton
                ret_val = Decimals.Quantity.Ton

            Case MeasurementUnit.Lbs
                ret_val = Decimals.Quantity.Lbs

            Case MeasurementUnit.Cwt
                ret_val = Decimals.Quantity.Cwt

            Case MeasurementUnit.L
                ret_val = Decimals.Quantity.L

            Case Else
                ret_val = 0
        End Select

        Return ret_val
    End Function

    Public Shared Function GetUnitSymbolFromMeasurementUnit(ByVal measurement_unit As MeasurementUnit) As String
        Dim ret_val As String = String.Empty

        Select Case measurement_unit
            Case MeasurementUnit.Kg
                ret_val = UnitSymbol.Kg

            Case MeasurementUnit.Ton
                ret_val = UnitSymbol.Ton

            Case MeasurementUnit.Lbs
                ret_val = UnitSymbol.Lbs

            Case MeasurementUnit.Cwt
                ret_val = UnitSymbol.Cwt

            Case MeasurementUnit.L
                ret_val = UnitSymbol.L

            Case Else
                ret_val = String.Empty
        End Select

        Return ret_val
    End Function

End Class