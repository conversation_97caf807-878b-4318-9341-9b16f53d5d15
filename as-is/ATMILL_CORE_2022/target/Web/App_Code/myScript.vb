﻿Option Strict On

Public Class myScript

    Public Shared Sub InvokeJS(ByVal page As System.Web.UI.Page, ByVal script As String)
        page.ClientScript.RegisterStartupScript(GetType(myScript), script, script, True)
    End Sub

    ' Funzione per passare a js la configurazione del calendario
    Public Shared Function UpdateJSCalendar(ByVal m_config As UsersGUI.config) As String
        Dim script As String = String.Empty

        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable
        Dim day_of_week_start As String = String.Empty

        str_sql = "SELECT PARAMETER_VALUE FROM SYSTEM_PARAMETERS WHERE PARAMETER_NAME = '" & "WEEK_FIRST_DAY" & "'"
        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

        If dt.Rows.Count > 0 Then
            For Each dr As Data.DataRow In dt.Rows
                day_of_week_start = dr.Item(0).ToString
            Next
        End If

        script = "localStorage.setItem('calendar', JSON.stringify({" &
                        "FormatDateTimeCalendar: '" & m_config.GetLanguage().FormatDateTimeCalendar & "'," &
                        "dayOfWeekStart: '" & day_of_week_start & "'," &
                        "SetLocale: '" & m_config.GetLanguage.CurrentInfo & "'," &
                        "DateTimeFormat: '" & m_config.GetLanguage().FormatDateTime & "'" &
                        "}));"

        Return script
    End Function

    ' Funzione per passare a js le sole traduzioni necessarie
    Public Shared Function UpdateJSLanguages(ByVal m_config As UsersGUI.config) As String
        Dim script As String = String.Empty

        AppendSingleKeyValue(script, m_config, "ERROR_NAN")
        AppendSingleKeyValue(script, m_config, "WAIT_OPERATION")
        AppendSingleKeyValue(script, m_config, "TIMEOUT_LOADER")
        AppendSingleKeyValue(script, m_config, "TIMESPANFORMAT_YEARS")
        AppendSingleKeyValue(script, m_config, "TIMESPANFORMAT_MONTHS")
        AppendSingleKeyValue(script, m_config, "TIMESPANFORMAT_DAYS")
        AppendSingleKeyValue(script, m_config, "TIMESPANFORMAT_HOURS")
        AppendSingleKeyValue(script, m_config, "TIMESPANFORMAT_MINUTES")
        AppendSingleKeyValue(script, m_config, "TIMESPANFORMAT_SECONDS")
        AppendSingleKeyValue(script, m_config, "CUSTOMER_REPORT_HEADER")
        AppendSingleKeyValue(script, m_config, "CUSTOMER_REPORT_SUBHEADER")
        AppendSingleKeyValue(script, m_config, "SHOW_ALL_PAGER_LINK")

        Return "localStorage.setItem('languages', JSON.stringify([" & script & "]));"
    End Function

    Private Shared Sub AppendSingleKeyValue(ByRef script As String, ByVal m_config As UsersGUI.config, ByVal key_name As String)
        If script <> String.Empty Then
            script &= ","
        End If

        script &= "{""" & key_name & """:""" & ReportsTools.myReports.GetTranslationForJSON(key_name, m_config) & """}"
    End Sub

    ''' <summary>
    ''' Sets, in the localStorage of the client, an item containing an array of the cycle IDs for which there's an associated report.
    ''' </summary>
    ''' <returns></returns>
    Public Shared Function UpdateManagedCycleReports() As String
        Dim script As String = String.Empty

        'Se true usa il sql server reporting services, se false usa javascript
        If (System.Configuration.ConfigurationManager.AppSettings("UseSSRSReport") Is Nothing OrElse
            System.Configuration.ConfigurationManager.AppSettings("UseSSRSReport").ToLower() = "false") Then
            script = "localStorage.setItem('managedCycleReports', JSON.stringify([" &
                String.Join(",", ReportsTools.myReports.ReportTypePerCycleConfig.Keys.Cast(Of Integer)) &
            "]));"
        Else
            script = "localStorage.setItem('managedCycleReports', JSON.stringify([" &
                String.Join(",", ReportsTools.myReports.GetReportTypePerCycleConfig.Keys.Cast(Of Integer)) &
            "]));"
        End If


        Return script
    End Function

    ''' <summary>
    ''' Updates the contents of the client's localStorage: "calendar", "languages" and "managedCycleReports".
    ''' In order to update one of the following must occur:
    ''' - Current.Session("updateLanguagesJS") = "YES" (triggered by the server, IE if the config was reloaded)
    ''' - Current.Request("resources") = "YES" (triggered by the client, if any of the resources are missing)
    ''' </summary>
    ''' <param name="m_config"></param>
    Public Shared Sub UpdateJSResources(ByVal m_config As UsersGUI.config)
        If Current.Session("updateLanguagesJS") IsNot Nothing AndAlso Current.Session("updateLanguagesJS").ToString() = UsersGUI.costanti.m_StringYes OrElse
            Current.Request("resources") IsNot Nothing AndAlso Current.Request("resources") = UsersGUI.costanti.m_StringYes Then
            myScript.InvokeJS(CType(HttpContext.Current.Handler, Page), myScript.UpdateJSLanguages(m_config))   ' "languages"
            myScript.InvokeJS(CType(HttpContext.Current.Handler, Page), myScript.UpdateJSCalendar(m_config))    ' "calendar"
            myScript.InvokeJS(CType(HttpContext.Current.Handler, Page), myScript.UpdateManagedCycleReports())   ' "managedCycleReports"
            myScript.InvokeJS(CType(HttpContext.Current.Handler, Page), myScript.UpdateRootUrl())   ' "rootUrl"

            If Current.Session("updateLanguagesJS") IsNot Nothing Then
                ' Rimuovo il flag solo se il session ID è presente nel querystring.
                ' In caso contrario la mancanza del session ID causa un redirect lato JS dopo l'aggiunta di quest'ultimo all'URL, ignorando lo script
                ' aggiunto da ASP.NET in seguito all'InvokeJS.
                If Current.Request.QueryString("session") IsNot Nothing Then
                    Current.Session.Remove("updateLanguagesJS")
                End If
            End If
        End If

        myScript.InvokeJS(CType(HttpContext.Current.Handler, Page), myScript.CheckLastClientUpdate())
    End Sub

    ''' <summary>
    ''' Parses the query string of the URL and appends the session ID if necessary
    ''' </summary>
    ''' <param name="pageUrl"></param>
    ''' <returns>The modified URL</returns>
    Public Shared Function AppendSessionIdToUrl(ByVal pageUrl As String) As String
        Dim retVal As String = pageUrl

        If Current.Request.QueryString("session") IsNot Nothing Then
            retVal = AppendItemToQueryString(pageUrl, "session", Current.Request.QueryString("session"))
        End If

        Return retVal
    End Function

    ''' <summary>
    ''' Called on SessionStart, makes the client reset the session ID counter.
    ''' </summary>
    Public Shared Sub ResetBrowserSessionIdentifiers()
        Dim script = "localStorage.setItem(""lastSessionIdentifier"", ""0"");"

        myScript.InvokeJS(CType(HttpContext.Current.Handler, Page), script)
    End Sub

    Public Shared Function CheckLastClientUpdate() As String
        Return "checkLastClientUpdate(""" & Current.Application("LastClientUpdate").ToString() & """);"
    End Function

    Public Shared Function GetQueryClientUpdate() As String
        Dim ret_val As String = "?ts="

        If Not IsNothing(Current.Application("LastClientUpdate")) Then
            ret_val &= Current.Application("LastClientUpdate").ToString()
        Else
            ret_val &= "0"
        End If

        Return ret_val
    End Function

    Public Shared Sub TriggerClientUpdate()
        Current.Application("LastClientUpdate") = CLng(DateTime.Now.Subtract(New DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds)
    End Sub

    Public Shared Function GetLastClientUpdateDate() As DateTime
        Dim ret_val As DateTime = Nothing

        If Not IsNothing(Current.Application("LastClientUpdate")) Then
            ret_val = New DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(CLng(Current.Application("LastClientUpdate")))
        End If

        Return ret_val
    End Function

    ''' <summary>
    ''' Returns the URL with the key-value pair concatenated to the query string
    ''' </summary>
    ''' <param name="pageUrl"></param>
    ''' <param name="key"></param>
    ''' <param name="value"></param>
    ''' <returns></returns>
    Public Shared Function AppendItemToQueryString(ByVal pageUrl As String, ByVal key As String, ByVal value As String) As String
        Dim retVal As String = pageUrl

        Try
            Dim queryString As NameValueCollection = HttpUtility.ParseQueryString(pageUrl)

            If queryString.Get(key) Is Nothing Then
                If queryString.HasKeys() Then
                    retVal = pageUrl & "&" & key & "=" & value
                Else
                    retVal = pageUrl & "?" & key & "=" & value
                End If
            End If
        Catch ex As Exception
            retVal = pageUrl
        End Try

        Return retVal
    End Function

    ''' <summary>
    ''' Sets in the localStorage of the client the key-value pair "rootUrl", used for composing hyperlinks
    ''' </summary>
    ''' <returns></returns>
    Private Shared Function UpdateRootUrl() As String
        Return "localStorage.setItem(""rootUrl"", """ & myFunction.GetRoot() & """);"
    End Function

    Public Shared Function GetLotIdFromDescriptor(ByVal descriptor As String) As Long
        Dim retVal As Long = UsersGUI.costanti.m_InvalidInteger

        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String.Format("SELECT ID FROM LOTS WHERE DESCRIPTOR = {0}", descriptor), False)

        If dt.Rows.Count > 0 Then
            retVal = Long.Parse(dt.Rows(0).Item("ID").ToString)
        End If

        Return retVal
    End Function

End Class