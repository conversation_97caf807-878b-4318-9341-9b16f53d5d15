/*******************************************************
 *                    INIT PAGE                        *
 *******************************************************/

/** avvia l'acquisizione del path e dei parametri della pagina creando, se necessario,
 * un nuovo oggetto "pagina" o aggiornando, se già esistente, quello di riferimento
 */
function GetNewPath() {
    MyHistory.GetHistory();
    if (my_history.back_step !== BACK_STEP.APPLIED) {
        current_page = my_history.getActPage();

        MyHistory.RestorePageState();
    }
    else {
        MyHistory.AddPage();
    }
}

// gestione la visibilità dei pulsanti "back" e (TODO) "next"
function SetNavigationButtonVisibility() {
    var lbl_back_cont = document.getElementById("WebOperation_BackCont");
    if (lbl_back_cont != null) {
        if (my_history != null) {
            ////visibilità di back
            var new_index = my_history.GetPrevValidIndex();
            if (new_index != my_history.act_index) {
                lbl_back_cont.style.display = "";
            } else {
                lbl_back_cont.style.display = "none";
            }

            //TODO visibilità di next
        }
    }
}

// usata per inizializzare tutti gli elementi custom
function SetCustomElement() {
    // 1. datetimepicker
    var par = null;
    var all_datetimepicker = document.querySelectorAll("input[datetimepicker]");
    var calendar_param = localStorage.getItem("calendar");
    var datetimepicker_par = {}; // custom parameters obj for calendar initialization
    var par_split = null;
    var sub_split = null;
    var calendar_SetLocale = null;
    var format_date_time_from_storage = null;
    var day_of_week_start_from_storage = null;
    var set_locale_from_storage = null;

    if ((calendar_param != null) && (calendar_param != undefined)) {
        calendar_param = JSON.parse(calendar_param);

        // FormatDateTime
        if ((calendar_param.FormatDateTimeCalendar != null) && (calendar_param.FormatDateTimeCalendar != undefined) && (calendar_param.FormatDateTimeCalendar != "")) {
            format_date_time_from_storage = calendar_param.FormatDateTimeCalendar;
        } else {
            // default value
            format_date_time_from_storage = 'yyyy-MM-dd HH:mm';
        }
        datetimepicker_par.format = ConvertDateTimeForCalendar(format_date_time_from_storage);

        // dayOfWeekStart
        if ((calendar_param.dayOfWeekStart != null) && (calendar_param.dayOfWeekStart != undefined) && (calendar_param.dayOfWeekStart != "") && (!isNaN(calendar_param.dayOfWeekStart))) {
            day_of_week_start_from_storage = parseInt(calendar_param.dayOfWeekStart);
        } else {
            // default value (monday)
            day_of_week_start_from_storage = 1;
        }
        datetimepicker_par.dayOfWeekStart = ConvertDayOfWeekStartForCalendar(day_of_week_start_from_storage);

        // SetLocale
        if ((calendar_param.SetLocale != null) && (calendar_param.SetLocale != undefined) && (calendar_param.SetLocale != "")) {
            set_locale_from_storage = calendar_param.SetLocale;
        } else {
            // default value
            set_locale_from_storage = "en-gb";
        }
        calendar_setLocale = ConvertSetLocaleForCalendar(set_locale_from_storage)
    }
    if (all_datetimepicker.length > 0) {
        $.datetimepicker.setLocale(calendar_setLocale);
        for (var el = 0; el < all_datetimepicker.length; el++) {
            if ((all_datetimepicker[el] != null) && (all_datetimepicker[el] != undefined)) {
                if ((all_datetimepicker[el].id != null) && (all_datetimepicker[el].id != undefined)) {
                    par = all_datetimepicker[el].getAttribute('datetimepicker');
                    // acquisizione parametri dall'attributo datetimepicker con forma "nome_par_1:val_par_1;nome_par_2:val_par_2;..."
                    par_split = par.split(';');
                    for (var p = 0; p < par_split.length; p++) {
                        if (par_split[p] != null) {
                            sub_split = par_split[p].split(":");
                            if (sub_split.length == 2) {
                                datetimepicker_par[sub_split[0]] = sub_split[1];
                            }
                        }
                    }
                    all_datetimepicker[el].setAttribute('PlaceHolder', format_date_time_from_storage);
                    jQuery('#' + all_datetimepicker[el].id).datetimepicker(datetimepicker_par);
                } else {
                    console.log("manca id: " + JSON.stringify(all_datetimepicker[el]));
                }
            }
        }
    }
}

// Funzione di supporto al formato della data per il calendario di jquery
function ConvertDateTimeForCalendar(input) {
    var input_split = null;
    var input_separator = [];
    var ret_val = '';

    for (var i = 0; i < input.length; i++) {
        if (!isLetter(input[i])) {
            input_separator[input_separator.length] = input[i];
            input = input.replaceAt(i, '$')
        }
    }

    input_split = input.split('$');

    for (var i = 0; i < input_split.length; i++) {
        switch (input_split[i]) {
            case 'yyyy':
                ret_val += 'Y';
                break;
            case 'MM':
                ret_val += 'm';
                break;
            case 'dd':
                ret_val += 'd';
                break;
            case 'HH':
                ret_val += 'H';
                break;
            case 'mm':
                ret_val += 'i';
                break;
            case 'ss':
                ret_val += 's';
                break;
            case 'hh':
                ret_val += 'h';
                break;
            case 'tt':
                ret_val += 'A';
                break;
            default:
                ret_val += input_split[i];
                console.log('unrecognized datepart "' + input_split[i] + '"');
                break;
        }

        if (input_separator[i] != undefined) {
            ret_val += input_separator[i];
        }
    }

    return ret_val;
}

// asp days:            (1=Sunday, 2=Monday... 7=Saturday)
// datetimepicker days: (0=Sunday, 1=Monday... 6=Saturday)
function ConvertDayOfWeekStartForCalendar(input) {
    switch (input) {
        case 1:
            return 0;
        case 2:
            return 1;
        case 3:
            return 2;
        case 4:
            return 3;
        case 5:
            return 4;
        case 6:
            return 5;
        case 7:
            return 6;
        default:
            console.log('unrecognized day of week start "' + input + '". Use default');
            return 0;
    }
}

function ConvertSetLocaleForCalendar(input) {
    switch (input) {
        case 'fr':
            return 'fr';
        case 'en-gb':
            return 'en-GB';
        case 'it':
            return 'it';
        case 'es':
            return 'es';
        case 'ru':
            return 'ru';
        case 'pt-br':
            return 'pt-BR';
        case 'de':
            return 'de';
        default:
            console.log('unrecognized SetLocale "' + input + '"');
            return input;
    }
}

/**
 *  Mostra lo #UCCont
 */
function ShowUCCont(param) {
    document.getElementById("UCCont").style.display = "block";
}

/** imposta, a tutti gli "input[number]" del documento, il messaggio di
 * errore validazione recuperato dal dizionario
 */
function SetCustomValidityInput() {
    var elements = document.getElementsByTagName("input");
    for (var i = 0; i < elements.length; i++) {
        elements[i].oninvalid = function (e) {
            e.target.setCustomValidity("");
            if (!e.target.validity.valid) {
                e.target.setCustomValidity(GetTraduct("ERROR_NAN"));
            }
        };
        elements[i].oninput = function (e) {
            e.target.setCustomValidity("");
        };
    }
}

/** myScript.vb
 *  nasconde le righe della tabella desiderata se sono vuote,
 *  nasconde le sezioni se sono vuote,
 *  sistema l'alternaza di classi RowOdd, RowEven
 * @param{string}   id_table    -   id della tabella da manipolare
 */
function HideRows(id_table) {
    var table_tr = null;
    var tr_td = null;
    var is_empty = true;

    table_tr = $('table#' + id_table + ' tbody tr');

    // 1. hide empty rows
    $.each(table_tr, function () {
        tr_td = $(this).children('td');
        is_empty = true;
        for (var i = 0; i < tr_td.length; i++) {
            if ($(tr_td[i]).html() != '') {
                is_empty = false;
            }
        }
        if (is_empty) {
            $(this).hide();
        } else {
            $(this).show();
        }
    });

    // 2. hide empty sections
    let emptyOrAllChildrenHidden;

    for (var i = 0; i < table_tr.length - 1; i++) {
        emptyOrAllChildrenHidden = true

        if ($(table_tr[i]).hasClass("rowSection")) {
            for (var j = i + 1; j < table_tr.length; j++) {
                if ($(table_tr[j]).css("display") !== 'none') {
                    emptyOrAllChildrenHidden = false;

                    if ($(table_tr[j]).hasClass("rowSection")) {
                        $(table_tr[i]).hide();
                    }

                    break;
                }
            }

            if (emptyOrAllChildrenHidden) {
                $(table_tr[i]).hide();
            }
        }
    }

    // 3. FixRowsStyle
    FixRowAlternateStyle(id_table);
}

/** myScript.vb
 *  imposta eventuali "colspan" della tabella desiderata per avere un numero di colonne prestabilito per ogni riga
 * @param{string}   id_table    -   id della tabella da manipolare
 */
function SetTdColspan(id_table) {
    $('table#' + id_table + ' tbody tr').each(function () {
        var count = $(this).children('td').length;
        if (count < 4) {
            $(this).find('td:last').attr('colspan', (5 - count));
        }
    });
}

/** myScript.vb
 *  esegue il login automatico con profilo utente "scada" come se fosse inserito dall'utente stesso, cioè premendo il
 * pulsante di "login"; ciò comporta il PostBack della pagina e la necessità della presenza di "WUC_LoginTop"
 */
function LoginScadaUser() {
    $('#Login_txtUserName').val('scada');
    $('#Login_txtPassword').val('scada');
    $('#Login_btnLogin').click();
}

/**
 *  Funzione per settare l'altezza dell'oggetto pdf nello user control WUCPdf
 */
function SetPdfViewHeight() {
    let $elem = $('.pdfView');

    if ($elem.length > 0) {
        $elem.css("height", "calc(100vh - " + (getTotalOffsetTopOfElement($elem[0]) + 5) + "px)");
    }
}

/*******************************************************
 *                       STAMPA                        *
 *******************************************************/
//**********************************************************************************//
//  1. Stampa delle pagine View.aspx                                                //
//                                                                                  //
//  Viene usata la tripletta di funzioni:                                           //
//      - CallPrint                                                                 //
//      - checkForPrintRequest                                                              //
//      - myPrint                                                                   //
//                                                                                  //
//**********************************************************************************//

// Funzione che esegue effettivamente la stampa del testo html in ingresso
//@param text_to_print {string} testo html da stampare
//@param print_css {array} array dei nomi dei file css da incorporare
//@param title_print {string} [optional] nome da assegnare al file di stampa
function PrintText(text_to_print, orientation, print_css, function_extra, title_print) {
    //Reset the page's HTML with div's HTML only
    var WinPrint = window.open('', '', 'width=600,height=400,toolbar=0,scrollbars=1,status=0,titlebar=0');
    var printPageHtml = "";
    var title_page = "report";
    if ((title_print != null) && (title_print != undefined)) {
        title_page = title_print;
    }

    var css_text = "";
    $.each(print_css, function () {
        css_text += "<link type='text/css' href='./css/" + this + ".css?ts=" + + Number(new Date()) + "' rel='stylesheet' />";
    });

    /*
     * Nel tag "script", vengono messe le funzioni da eseguire da parte della stampa
     * L'ordine di esecuzione delle funzioni viene stabilito in base all'EventListener a cui vengono assegnate:
     * DOMContentLoaded -> appena gli elementi del DOM sono caricati (prima)
     * load -> quando tutta la pagina è caricata (dopo)
     ******** NOTA *******
     * attualmente sono presenti solo funzioni "base"
     */
    printPageHtml = "<!DOCTYPE html><html><head><title>" + title_page + "</title>" + css_text +
        "<script type='text/javascript' src='./js/printFunction.js?ts=" + + Number(new Date()) + "'></script>" +
        "<script>" +
        "function ChangeLedForStamp() {" +
        "var all_green_led = document.getElementsByTagName('img');" +
        "for (var el = 0; el < all_green_led.length; el++) {" +
        "var src_elem = all_green_led[el].getAttribute('src');" +
        "if (src_elem == 'image/greenled.png') {" +
        "all_green_led[el].setAttribute('src', 'image/greenledbn.png');" +
        "} else if (src_elem == 'image/redled.png') {" +
        "all_green_led[el].setAttribute('src', 'image/redledbn.png');" +
        "} else if (src_elem == 'image/yellowled.png') {" +
        "all_green_led[el].setAttribute('src', 'image/yellowledbn.png');" +
        "}" +
        "}" +
        "return true;" +
        "}" +
        "window.addEventListener('DOMContentLoaded', function(event) { " +
        "ChangeLedForStamp();" +
        "(() => {" +
        "document.querySelectorAll(':disabled').forEach((elem) => { if (elem.attributes['disabled'] !== undefined) elem.attributes.removeNamedItem('disabled'); })" +
        "})();";    // Remove all disabled attributes

    if ((function_extra != null) && (function_extra != undefined) && (function_extra != "")) {
        printPageHtml += function_extra;
    }

    if (localStorage.getItem("lista_filtri") != null) {
        printPageHtml += "var lista_filtri_array = JSON.parse(localStorage.getItem('lista_filtri'));" +
            SetFilterIndicators.toString() +
            "SetFilterIndicators(lista_filtri_array);";
    }

    printPageHtml += "}, false);" +
        "window.addEventListener('load', function(event) { " +
        "window.print();" +
        "setTimeout(window.close, 100);" +
        "}, false);" +
        "</script>" +
        "</head><body>" + text_to_print + "</body></html>";

    WinPrint.document.write(printPageHtml);
    WinPrint.document.close();
    WinPrint.focus();
    window.close();
    return true;
}

// Funzione da associare alla richiesta di stampa.
// Genera una redirect alla stessa pagina appendendo il parametro print=YES, cosi che possa essere eseguita una nuova query
//per recuperare tutti i dati da stampare, non solo quelli messi in visualizzazione che possono essere meno in determinate tabelle
// Nel caso venga trovato il carattere "#" al termine dell'url viene rimosso in quanto viene considerato come terminatore
//impedendo poi al WUC_View di interpretare correttamente l'indirizzo
function CallPrint() {
    if (current_page.search_params != null) {
        localStorage.setItem("lista_filtri", JSON.stringify(current_page.search_params.applied_filters));
    }

    var url = AddPrintRequest(window.location.href);
    WinPrint2 = window.open(url, '', 'width=1,height=1,toolbar=0,scrollbars=1,status=0,titlebar=0');
    WinPrint2.focus();
}

// Funzione eseguita dalle pagine che possono stampare sull'onload
// se nell'url è contenuto il parametro print=YES estrapola il print_area
// e lo passa alla funzione myPrint
function checkForPrintRequest() {
    let params = getCurrentQueryParams();

    if (params["print"] === "YES") {
        myPrint("print_area");
        window.close();
    }
}

// Funzione di passaggio che recupera, dall'elemento di id indicato, il contenuto html per essere poi stampato
function myPrint(strid, title_page) {
    //Get the HTML
    var sourcePageHtml = document.getElementById(strid).innerHTML

    PrintText(sourcePageHtml, "", ["default", "print"], null, title_page)
}

function validateEmail(email) {
    var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    return re.test(email);
}

function CallRptMail_confirm() {
    var frame_url = document.getElementById('IFrameReport').src;
    var request_url = frame_url.replace("ReportYields.aspx", "ReportPrintMail.aspx");
    var new_rec_list = temp_return_value;
    if (new_rec_list != null) {
        if (new_rec_list.length > 0) { //verifica che la lista non sia vuota
            if (new_rec_list.length < 256) {	//verifica che non siano piu di 255 caratteri
                var rec_list_valida = true;
                var rec_list_splitted = new_rec_list.split(",");
                var rec_to_send = "";
                for (var m = 0; m < rec_list_splitted.length; m++) {	//per ogni mail, ne verifico la correttezza della struttura
                    if (rec_list_splitted[m].trim() != "") {
                        if (!(validateEmail(rec_list_splitted[m].trim()))) {
                            if (rec_list_splitted[m].trim() == "") {
                                myMessageBox.alert("Mail empty!", function () { CallRptMail(new_rec_list); ButtonEvents.MessageBoxAlert.Submit(); });
                            } else {
                                myMessageBox.alert("Mail invalid: " + rec_list_splitted[m].trim(), function () { CallRptMail(new_rec_list); ButtonEvents.MessageBoxAlert.Submit(); });
                            }
                            rec_list_valida = false;
                            break;
                        } else {
                            rec_to_send += rec_list_splitted[m].trim() + ","
                        }
                    }
                    //rimozione virgola
                }
                if (rec_list_valida) {
                    rec_to_send = rec_to_send.substr(0, rec_to_send.length - 1)
                    request_url += "&REC_LIST=" + rec_to_send;
                    request_url += "&REQ=mail";
                    DoRequest(request_url);
                } else {
                    //CallRptMail(new_rec_list);
                }
            } else {
                myMessageBox.alert("Your mail is too long! Remove: " + (255 - new_rec_list.length) + " character.", function () { CallRptMail(new_rec_list); ButtonEvents.MessageBoxAlert.Submit(); });
                //CallRptMail(new_rec_list);
            }
        } else {
            myMessageBox.alert("Your mail address is empty!", function () { CallRptMail(null); ButtonEvents.MessageBoxAlert.Submit(); });
            //CallRptMail(null);
        }
    }

    if (new_rec_list != null) {
        request_url += "&REC_LIST=" + new_rec_list;
    }

    request_url += "&REQ=mail";

    DoRequest(request_url);
}

function CallRptMail(rec_list_input) {
    var rec_list = "<EMAIL>";

    if (rec_list_input != null) {
        rec_list = rec_list_input;
    }

    myMessageBox.prompt("Please enter mail address", null, rec_list, function () { CallRptMail_confirm(); ButtonEvents.MessageBoxPrompt.Submit(); }, function () { ButtonEvents.MessageBoxPrompt.Cancel(); });
}

function DoRequest(url) {
    //creazione oggetto
    var xmlhttp;
    if (window.XMLHttpRequest) {  // code for IE7+, Firefox, Chrome, Opera, Safari
        xmlhttp = new XMLHttpRequest();
    }
    else {       // code for IE6, IE5
        xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
    }

    xmlhttp.open("GET", url, true);
    xmlhttp.send();
}

function PrintChart() {
    myPrint('print_area', 'Graphic');
}

function PrintIframe(id_iframe, css_stamp) {
    var internal_form_elements = $($("form").find("iframe#" + id_iframe).contents().find("form")).contents();
    var $elem = null;
    var function_extra = null;
    var page_name = localStorage.getItem("page_aspx");

    $.each(internal_form_elements, function () {
        $elem = $(this);
        if ($elem.attr("id") === 'print_area') {
            PrintText($elem.html(), "", css_stamp, function_extra);
            return true;
        }
        id_elem = "";
    });
}

/**
 * Calls the window.print function, showing the dialog for printing the whole loaded page
 */
function PrintWhole() {
    window.print();
}

function AddPrintRequest(base_url) {
    var url = base_url;
    if (url[url.length - 1] == "#") {
        url = url.substr(0, url.length - 1);
    }

    url += "&print=YES";
    return url;
}

/*******************************************************
 *                  WUC_Operations                     *
 *******************************************************/
// Versione standard
function SetWUCOperationSize() {
    var divOp = $("#DivOperation");
    var divEl = $("#DivElements");
    var divTabRows = $("#DivElements table tr");

    var NumOfRows = divTabRows.size();

    if (NumOfRows == 0) {
        divOp.hide();
    }
    else {
        divOp.show();
        divOp.css("height", ((NumOfRows * 30) + 37) + "px");
        divEl.css("height", (NumOfRows * 30) + "px");
    }
}

/*******************************************************
 *                   POPUP MESSAGGI                    *
 *******************************************************
 *  tipologie messaggi gestiti:                        *
 *  - alert                                            *
 *  - confirm                                          *
 *  - prompt                                           *
 *******************************************************/
var func_T = null;
var func_F = null;
var temp_return_value = null;
var margine_myMessageBox = [100, 100];
var marginePerc_myMessageBox = [0.05, 0.05]; //5% - 5%
myMessageBox = {
    alert: function (txt, func_extra) {
        var MessageBox_Alert = new MessageBoxAlert(document.body).open(txt, null, func_extra);
    },

    confirm: function (txt, callback_T, callback_F) {
        var MessageBox_Confirm = new MessageBoxConfirm(document.body).open(txt, null, callback_T, callback_F);
    },

    prompt: function (txt, base_input, placeholder_text, callback_T, callback_F) {
        var MessageBox_Prompt = new MessageBoxPrompt(document.body).open(txt, base_input, placeholder_text, null, callback_T, callback_F);
    },

    setSizeBox: function () {
        //altezza
        var altezza_pagina = window.innerHeight;
        margine_myMessageBox[1] = altezza_pagina * marginePerc_myMessageBox[1];
        var altezza_content = $("#MessageBox__content").innerHeight();
        var marginTop_content = parseInt($("#MessageBox__content").css("margin-top"));
        var marginBottom_content = parseInt($("#MessageBox__content").css("margin-bottom"));
        var altezza_btn_content = $("#MessageBox__btn__content").innerHeight();
        var altezza_input = $("#MessageBox__contentInput").innerHeight();
        var marginTop_btn_content = parseInt($("#MessageBox__btn__content").css("margin-top"));
        var altezza = altezza_content + altezza_btn_content + marginTop_content + marginBottom_content + altezza_input + marginTop_btn_content;

        if ((altezza_pagina - altezza) > (margine_myMessageBox[1] * 2)) {
            $("#MessageBox").height(altezza);
            $("#MessageBox").css("margin-top", ((altezza_pagina - altezza) / 2) + "px");
        } else {
            var max_altezza = altezza_pagina - margine_myMessageBox[1] * 2
            $("#MessageBox").height(max_altezza);
            $("#MessageBox__content").height(max_altezza - paddingBottom_content - paddingTop_content - altezza_btn_content - marginBottom_content - marginTop_content);
            $("#MessageBox").css("margin-top", margine_myMessageBox[1] + "px");
        }

        //larghezza
        var larghezza_minima = 400;
        var larghezza_pagina = window.innerWidth;
        margine_myMessageBox[0] = larghezza_pagina * marginePerc_myMessageBox[0];
        var larghezza_content = $("#MessageBox__content").innerWidth() + 1;
        var larghezza_label = $("#MessageBox__label").innerWidth() + 1;
        var paddingLeft_content = parseInt($("#MessageBox__content").css("padding-left"));
        var paddingRight_content = parseInt($("#MessageBox__content").css("padding-right"));

        var larghezza_massima = (larghezza_content - margine_myMessageBox[0] * 2) - paddingLeft_content - paddingRight_content;
        if (larghezza_label > larghezza_massima) {
            //resize
            $("#MessageBox__label").width(larghezza_massima);
            $("#MessageBox").width(larghezza_massima);
        } else {
            if (larghezza_label < larghezza_minima) {	//minore della minima
                $("#MessageBox__label").width(larghezza_minima);
                $("#MessageBox").width((larghezza_minima + paddingLeft_content + paddingRight_content));
            } else {	//tutto ok
                $("#MessageBox__label").width(larghezza_label);
                $("#MessageBox").width((larghezza_label + paddingLeft_content + paddingRight_content));
            }
        }

        //posizione pulsanti
        var larghezza_btn_content = $("#MessageBox__btn__content").innerWidth();
        var larghezza_btn = $("#MessageBox__install").innerWidth();
        var $btn_close = $("#MessageBox__close");
        if ($btn_close.length > 0) {	//esiste anche il btn_close
            var margine_btn_close = 20;
            $("#MessageBox__install").css({
                "margin-left": ((larghezza_btn_content - $btn_close.innerWidth() - margine_btn_close - larghezza_btn) / 2),
                "margin-right": margine_btn_close
            });
        } else {	//esiste solo il btn_install
            $("#MessageBox__install").css("margin-left", ((larghezza_btn_content - larghezza_btn) / 2));
        }
    }
};

(function (name, context, definition) {
    if (typeof define === 'function' && define.amd) {
        define(definition);
    }
    else if (typeof module !== 'undefined' && module.exports) {
        module.exports = definition();
    }
    else {
        context[name] = definition();
    }
})('MessageBoxConfirm', this, function () {
    var MessageBox = function (element) {
        this.target = element;

        if (!this.isOpen) {
            this._init();
        }
    };

    MessageBox.prototype._init = function () {
        var self = this;

        this.overlay = document.createElement('div');
        this.overlay.className = 'overlay_';
        this.overlay.setAttribute('tabindex', -1);

        this.MessageBoxWindow = document.createElement('div');
        this.MessageBoxWindow.className = 'MessageBox';
        this.MessageBoxWindow.id = 'MessageBox';
        this.MessageBoxWindow.setAttribute('role', 'dialog');
        this.MessageBoxWindow.setAttribute('tabindex', 0);

        this.MessageBoxWrapper = document.createElement('div');
        this.MessageBoxWrapper.className = 'MessageBox__wrapper';
        this.MessageBoxWrapper.id = 'MessageBox__wrapper';

        this.MessageBoxContent = document.createElement('div');
        this.MessageBoxContent.className = 'MessageBox__content';
        this.MessageBoxContent.id = 'MessageBox__content';

        this.MessageBoxLabel = document.createElement('label');
        this.MessageBoxLabel.className = 'MessageBox__label';
        this.MessageBoxLabel.id = 'MessageBox__label';

        this.MessageBoxBtnContent = document.createElement('div');
        this.MessageBoxBtnContent.className = 'MessageBox__btn__content';
        this.MessageBoxBtnContent.id = 'MessageBox__btn__content';

        this.closeButton = document.createElement('button');
        this.closeButton.className = 'MessageBox__close MessageBox__btn__double';
        this.closeButton.id = 'MessageBox__close';
        this.closeButton.innerHTML = 'Close';
        this.closeButton.setAttribute('type', 'button');

        this.closeButton.onclick = function () {
            temp_return_value = false;
            self.close(func_F);
        };

        this.installButton = document.createElement('button');
        this.installButton.className = 'MessageBox__install MessageBox__btn__double';
        this.installButton.id = 'MessageBox__install';
        this.installButton.innerHTML = 'OK';
        this.installButton.setAttribute('type', 'button');

        this.installButton.onclick = function () {
            temp_return_value = true;
            self.close(func_T);
        };

        this.MessageBoxWindow.appendChild(this.MessageBoxWrapper);
        this.MessageBoxWrapper.appendChild(this.MessageBoxContent);
        this.MessageBoxContent.appendChild(this.MessageBoxLabel);
        this.MessageBoxWrapper.appendChild(this.MessageBoxBtnContent);
        this.MessageBoxBtnContent.appendChild(this.installButton);
        this.MessageBoxBtnContent.appendChild(this.closeButton);

        this.isOpen = false;
    };

    MessageBox.prototype.open = function (text_MessageBox, callback, callback_func_T, callback_func_F) {
        if (this.isOpen) {
            return;
        }
        func_T = callback_func_T;
        func_F = callback_func_F;

        this.MessageBoxLabel.innerHTML = text_MessageBox.replace(/\n/g, '<br />');
        this.target.appendChild(this.overlay);
        this.target.appendChild(this.MessageBoxWindow);
        this.MessageBoxWindow.focus();

        this.isOpen = true;

        if (callback) {
            callback.call(this);
        }
        myMessageBox.setSizeBox();
    };

    MessageBox.prototype.close = function (callback, val) {
        this.target.removeChild(this.MessageBoxWindow);
        this.target.removeChild(this.overlay);
        this.isOpen = false;

        if (callback) {
            callback.call(this, val);
        }
    };

    MessageBox.prototype.teardown = function () {
        if (this.isOpen) {
            this.close();
        }

        delete this.installButton;
        delete this.closeButton;
        delete this.MessageBoxContent;
        delete this.MessageBoxWrapper;
        delete this.MessageBoxWindow;
        delete this.overlay;
        delete this.isOpen;
    };
    return MessageBox;
});

(function (name, context, definition) {
    if (typeof define === 'function' && define.amd) {
        define(definition);
    }
    else if (typeof module !== 'undefined' && module.exports) {
        module.exports = definition();
    }
    else {
        context[name] = definition();
    }
})('MessageBoxAlert', this, function () {
    var MessageBox = function (element) {
        this.target = element;

        if (!this.isOpen) {
            this._init();
        }
    };

    MessageBox.prototype._init = function () {
        var self = this;

        this.overlay = document.createElement('div');
        this.overlay.className = 'overlay_';
        this.overlay.setAttribute('tabindex', -1);

        this.MessageBoxWindow = document.createElement('div');
        this.MessageBoxWindow.className = 'MessageBox';
        this.MessageBoxWindow.id = 'MessageBox';
        this.MessageBoxWindow.setAttribute('role', 'dialog');
        this.MessageBoxWindow.setAttribute('tabindex', 0);

        this.MessageBoxWrapper = document.createElement('div');
        this.MessageBoxWrapper.className = 'MessageBox__wrapper';
        this.MessageBoxWrapper.id = 'MessageBox__wrapper';

        this.MessageBoxContent = document.createElement('div');
        this.MessageBoxContent.className = 'MessageBox__content';
        this.MessageBoxContent.id = 'MessageBox__content';

        this.MessageBoxLabel = document.createElement('label');
        this.MessageBoxLabel.className = 'MessageBox__label';
        this.MessageBoxLabel.id = 'MessageBox__label';

        this.MessageBoxBtnContent = document.createElement('div');
        this.MessageBoxBtnContent.className = 'MessageBox__btn__content';
        this.MessageBoxBtnContent.id = 'MessageBox__btn__content';

        this.installButton = document.createElement('button');
        this.installButton.className = 'MessageBox__install MessageBox__btn__solo';
        this.installButton.id = 'MessageBox__install';
        this.installButton.innerHTML = 'OK';
        this.installButton.setAttribute('type', 'button');
        this.installButton.onclick = function () {
            temp_return_value = true;
            self.close(func_T);
        };

        this.MessageBoxWindow.appendChild(this.MessageBoxWrapper);
        this.MessageBoxWrapper.appendChild(this.MessageBoxContent);
        this.MessageBoxContent.appendChild(this.MessageBoxLabel);
        this.MessageBoxWrapper.appendChild(this.MessageBoxBtnContent);
        this.MessageBoxBtnContent.appendChild(this.installButton);
        this.isOpen = false;
    };

    MessageBox.prototype.open = function (text_MessageBox, callback, func_extra) {
        if (this.isOpen) {
            return;
        }
        func_T = func_extra;

        this.MessageBoxLabel.innerHTML = text_MessageBox.replace(/\n/g, '<br />');
        this.target.appendChild(this.overlay);
        this.target.appendChild(this.MessageBoxWindow);
        this.MessageBoxWindow.focus();
        this.isOpen = true;
        if (callback) {
            callback.call(this);
        }
        myMessageBox.setSizeBox();
    };

    MessageBox.prototype.close = function (callback) {
        this.target.removeChild(this.MessageBoxWindow);
        this.target.removeChild(this.overlay);
        this.isOpen = false;
        if (callback) {
            callback.call(this);
        }
    };

    MessageBox.prototype.teardown = function () {
        if (this.isOpen) {
            this.close();
        }

        delete this.installButton;
        delete this.MessageBoxContent;
        delete this.MessageBoxWrapper;
        delete this.MessageBoxWindow;
        delete this.overlay;
        delete this.isOpen;
    };
    return MessageBox;
});

(function (name, context, definition) {
    if (typeof define === 'function' && define.amd) {
        define(definition);
    }
    else if (typeof module !== 'undefined' && module.exports) {
        module.exports = definition();
    }
    else {
        context[name] = definition();
    }
})('MessageBoxPrompt', this, function () {
    var MessageBox = function (element) {
        this.target = element;

        if (!this.isOpen) {
            this._init();
        }
    };

    MessageBox.prototype._init = function () {
        var self = this;

        this.overlay = document.createElement('div');
        this.overlay.className = 'overlay_';
        this.overlay.style.background = 'rgba(0, 0, 0, .8)';
        this.overlay.setAttribute('tabindex', -1);

        this.MessageBoxWindow = document.createElement('div');
        this.MessageBoxWindow.className = 'MessageBox';
        this.MessageBoxWindow.id = 'MessageBox';
        this.MessageBoxWindow.setAttribute('role', 'dialog');
        this.MessageBoxWindow.setAttribute('tabindex', 0);

        this.MessageBoxWrapper = document.createElement('div');
        this.MessageBoxWrapper.className = 'MessageBox__wrapper';
        this.MessageBoxWrapper.id = 'MessageBox__wrapper';

        this.MessageBoxContent = document.createElement('div');
        this.MessageBoxContent.className = 'MessageBox__content';
        this.MessageBoxContent.id = 'MessageBox__content';

        this.MessageBoxLabel = document.createElement('label');
        this.MessageBoxLabel.className = 'MessageBox__label';
        this.MessageBoxLabel.id = 'MessageBox__label';

        this.MessageBoxBtnContent = document.createElement('div');
        this.MessageBoxBtnContent.className = 'MessageBox__btn__content';
        this.MessageBoxBtnContent.id = 'MessageBox__btn__content';

        this.MessageBoxContentInput = document.createElement('div');
        this.MessageBoxContentInput.className = 'MessageBox__contentInput';
        this.MessageBoxContentInput.id = 'MessageBox__contentInput';

        this.closeButton = document.createElement('button');
        this.closeButton.className = 'MessageBox__close MessageBox__btn__double';
        this.closeButton.id = 'MessageBox__close';
        this.closeButton.innerHTML = 'Close';
        this.closeButton.setAttribute('type', 'button');

        this.closeButton.onclick = function () {
            temp_return_value = false;
            self.close(func_F);
        };

        this.installButton = document.createElement('button');
        this.installButton.className = 'MessageBox__install MessageBox__btn__double';
        this.installButton.id = 'MessageBox__install';
        this.installButton.innerHTML = 'OK';
        this.installButton.setAttribute('type', 'button');

        this.installButton.onclick = function () {
            temp_return_value = self.inputBox.value;
            self.close(func_T);
        };

        this.inputBox = document.createElement('input');
        this.inputBox.className = 'MessageBox__input';
        this.inputBox.id = 'MessageBox__input'

        this.MessageBoxWindow.appendChild(this.MessageBoxWrapper);
        this.MessageBoxWrapper.appendChild(this.MessageBoxContent);
        this.MessageBoxContent.appendChild(this.MessageBoxLabel);
        this.MessageBoxWrapper.appendChild(this.MessageBoxContentInput);
        this.MessageBoxWrapper.appendChild(this.MessageBoxBtnContent);
        this.MessageBoxBtnContent.appendChild(this.installButton);
        this.MessageBoxBtnContent.appendChild(this.closeButton);
        this.MessageBoxContentInput.appendChild(this.inputBox);

        this.isOpen = false;
    };

    MessageBox.prototype.open = function (text_MessageBox, base_text_input, placeholder_text, callback, callback_func_T, callback_func_F) {
        if (this.isOpen) {
            return;
        }
        func_T = callback_func_T;
        func_F = callback_func_F;

        this.MessageBoxLabel.innerHTML = text_MessageBox.replace(/\n/g, '<br />');
        if (base_text_input != null) {
            this.inputBox.value = base_text_input;
        } else {
            this.inputBox.placeholder = placeholder_text;
        }

        this.target.appendChild(this.overlay);
        this.target.appendChild(this.MessageBoxWindow);
        this.MessageBoxWindow.focus();

        this.isOpen = true;

        if (callback) {
            callback.call(this);
        }
        myMessageBox.setSizeBox();
    };

    MessageBox.prototype.close = function (callback, val) {
        this.target.removeChild(this.MessageBoxWindow);
        this.target.removeChild(this.overlay);
        this.isOpen = false;

        if (callback) {
            callback.call(this, val);
        }
    };

    MessageBox.prototype.teardown = function () {
        if (this.isOpen) {
            this.close();
        }

        delete this.installButton;
        delete this.closeButton;
        delete this.MessageBoxContent;
        delete this.MessageBoxWrapper;
        delete this.MessageBoxWindow;
        delete this.overlay;
        delete this.isOpen;
    };
    return MessageBox;
});

/*******************************************************
 *                PAGER PER GRID VIEW                  *
 *******************************************************/
var page_for_group = 10;
var num_group_tot = -1;
var num_group_selected = -1;
var selected_page_index = -1;
var num_pages = 0;

function UpdatePagerGroup(cmd) {
    var txt = "";
    var start_index = 0;
    var last_index = 0;

    start_index = Math.max(num_group_selected - 1, 0) * page_for_group;
    last_index = start_index + (page_for_group - 1);
    last_index = (last_index < num_pages) ? last_index : num_pages - 1;

    for (var i = start_index; i <= last_index; i++) {
        if (i !== selected_page_index) {
            txt += "<a data-page-idx='" + i + "'>" + (i + 1) + "</label>";
        } else {
            txt += "<a disabled='disabled'>" + (i + 1) + "</label>";
        }
    }

    return txt;
}

function SetVisibilityPagerGroup() {
    //nascondo "PREV" sse la pagina selezionata è "0"
    if (selected_page_index === 0) {
        $("#prev_page").hide();
    } else {
        $("#prev_page").show();
    }

    //nascondo "FIRST" &".." (sinistra) sse il gruppo selezionato è il primo
    if (num_group_selected === 1) {
        $("#first_page").hide();
        $("#prev_page_group").hide();
    } else {
        $("#first_page").show();
        $("#prev_page_group").show();
    }

    //nascondo ".." (destra) & "LAST" sse il gruppo selezionato è l'ultimo
    if (num_group_selected === num_group_tot) {
        $("#next_page_group").hide();
        $("#last_page").hide();
    } else {
        $("#next_page_group").show();
        $("#last_page").show();
    }

    //nascondo "NEXT" sse la pagina selezionata è "ultima"
    if (selected_page_index === (num_pages - 1)) {
        $("#next_page").hide();
    } else {
        $("#next_page").show();
    }

    // Hides "PREV", "NEXT", "FIRST", "LAST", ".."(left), ".."(right) if the rows are all shown
    if (selected_page_index === -1) {
        $("#prev_page").hide();
        $("#next_page").hide();
        $("#first_page").hide();
        $("#last_page").hide();
        $("#prev_page_group").hide();
        $("#next_page_group").hide();
    }
}

/**
 *  creazione "pager" custom
 */
function CreatePager() {
    num_pages = Number($("#UCView_div_pager_js").attr("data-pages-number"));
    selected_page_index = Number($("#UCView_div_pager_js").attr("data-selected-page"));

    num_group_tot = Math.ceil(num_pages / page_for_group);
    num_group_selected = Math.floor(selected_page_index / page_for_group) + 1;

    var new_pager = "<table class='myPagerTable'><tbody><tr>" +
        "<td>" +
        "<a id='first_page' class='link_hover'>1</a>" +
        "</td>" +
        "<td>" +
        "<a id='prev_page' class='link_hover'><<</a>" +
        "</td>" +
        "<td>" +
        "<a id='prev_page_group' class='link_hover'>..</a>" +
        "</td>" +
        "<td id='page_group_cont'>" + UpdatePagerGroup() + "</td>" +
        "<td>" +
        "<a id='next_page_group' class='link_hover'>..</a>" +
        "</td>" +
        "<td>" +
        "<a id='next_page' class='link_hover'>>></a>" +
        "</td>" +
        "<td>" +
        "<a id='last_page' class='link_hover'>" + num_pages + "</a>" +
        "</td>" +
        "<td>" +
        "<a id='all_pages' data-page-idx='-1' class='link_hover'" + (selected_page_index === -1 ? " disabled='disabled'" : "") + ">" + getTranslation("SHOW_ALL_PAGER_LINK") + "</a>" +
        "</td>" +
        "</tr></tbody></table>";

    $("#UCView_div_pager_js").html(new_pager);

    SetVisibilityPagerGroup();

    $("#page_group_cont a, #all_pages").on("click", (e) => {
        // Retrieves the page index from the link
        let $target = $(e.currentTarget);
        let pageIdx = $target.data("page-idx");

        e.preventDefault();

        changeGridPage(pageIdx)
    });

    $("#first_page").bind("click", function (event) {
        selected_page_index = 0;
        num_group_selected = 1;

        changeGridPage(selected_page_index);
    });

    $("#prev_page_group").bind("click", function (event) {
        selected_page_index = ((num_group_selected - 1) * page_for_group) - 1;

        changeGridPage(selected_page_index);
    });

    $("#prev_page").bind("click", function (event) {
        selected_page_index = (selected_page_index === 0) ? 0 : (selected_page_index - 1);

        if (selected_page_index <= ((num_group_selected - 1) * page_for_group)) {
            num_group_selected = (num_group_selected === 1) ? 1 : (num_group_selected - 1);
        }

        changeGridPage(selected_page_index);
    });

    $("#next_page").bind("click", function (event) {
        selected_page_index = (selected_page_index === (num_pages - 1)) ? (num_pages - 1) : (selected_page_index + 1);

        if (selected_page_index > (num_group_selected * page_for_group)) {
            num_group_selected = (num_group_selected === num_group_tot) ? num_group_tot : (num_group_selected + 1);
        }

        changeGridPage(selected_page_index);
    });

    $("#next_page_group").bind("click", function (event) {
        selected_page_index = num_group_selected * page_for_group;

        changeGridPage(selected_page_index);
    });

    $("#last_page").bind("click", function (event) {
        selected_page_index = num_pages - 1;
        num_group_selected = num_group_tot;

        changeGridPage(selected_page_index);
    });

    if (getCurrentQueryParams()["print"] === "YES" || num_pages <= 1) {
        $('#UCView_div_pager_js').hide();
    }
}

/**
 * Updates the data grid page index in the URL
 * @param {any} pageIdx
 */
function changeGridPage(pageIdx) {
    $("#UCView_pager_ControllerField").attr("value", pageIdx);

    let actPage = my_history.getActPage();
    actPage.pagination_index = pageIdx;
    my_history.updateActPage(actPage);

    MyHistory.SetHistory();

    __doPostBack();
}

/*******************************************************
 *              LARGHEZZA COLONNE TABELLE              *
 *******************************************************
 *  gestite le tabelle riguardanti:                    *
 *      - visualizzazione dati (view)                  *
 *      - selezione parametri (filtri, new, ecc)       *
 *******************************************************/

/**
 * Gets the maximum number of columns contained by a row in the desired element
 * @param {JQuery<HTMLElement>} elem Container element
 */
function GetMaxNumberOfColumnsInRows(elem) {
    let retVal = 0;

    const rows = elem.children("tr:visible");

    $.each(rows, (idx, row) => {
        retVal = Math.max($(row).children().length, retVal);
    });

    return retVal;
}

// TODO: remove in favor of grid layouts
/**
 * Sets an even width to the columns of the table
 * @param {string} tableId ID of the table element
 */
function SetColumnWidth_ParamTable(tableId) {
    // 1 - Calculates [max_num_column]
    const $table = $("#" + tableId);
    const $tHead = $("#" + tableId + " thead");
    const $tBody = $("#" + tableId + " tbody");
    let maxNumberOfColumns;

    //  Gets the max number of columns per rows in the body
    maxNumberOfColumns = GetMaxNumberOfColumnsInRows($tBody);

    //  If an header exists
    if ($tHead.length > 0) {
        //  Gets the max number of columns per row between header and body
        maxNumberOfColumns = Math.max(GetMaxNumberOfColumnsInRows($tHead), maxNumberOfColumns);
    } else {
        //  Otherwise, generates an header with empty <th> elements
        $table.append(
            `
            <thead>
                <tr>
                    ${"<th></th>".repeat(maxNumberOfColumns)}
                </tr>
            </thead>
            `
        );
    }

    // 2 - Sets an even % width to the columns according to [max_num_column]
    const singleColumnPercWidth = 100 / maxNumberOfColumns;
    const $tableTHs = $table.find("thead tr").children("th");

    $.each($tableTHs, (idx, th) => {
        $(th).width(singleColumnPercWidth + "%");
    });
}

/**
 *	identificato l'attributo custom della colonna, percorre tutte le righe per gestirne
 * il comportamento desiderato
 * @param{string}	id_tab	-	id della tabella
 * @param{string}	attr	-	nome attributo
 * @param{string}	cal_attr	-	valore dell'attributo
 * @param{integer}	index	-	indice della colonna con l'attributo
 */
function SetCellsDataTable(id_tab, attr, val_attr, index) {
    var rows_list = document.querySelectorAll("#" + id_tab + " tr");
    var cells_list = null;

    switch (attr) {
        case "MeasurementUnit": //aggiunge l'unità di misura indicata dal valore ad ogni cella della colonna
            for (var r = 0; r < rows_list.length; r++) {
                cells_list = rows_list[r].querySelectorAll("td");
                if (cells_list.length >= index && cells_list[index].innerHTML.replace("&nbsp;", "").length > 0) {
                    cells_list[index].innerHTML += " " + val_attr;
                }
            }
            break;
        case "HideIfEmpty":    //se "true", se tutte le cella della colonna sono vuote -> nasconde la colonna
            var exist_no_empty = false;
            var all_cell_hideifempty = null;
            var visibility = null;
            if ((val_attr.toLowerCase() == "true") || (val_attr == true)) {
                for (var r = 0; r < rows_list.length; r++) {
                    cells_list = rows_list[r].children;
                    if (cells_list.length >= index) {
                        //aggiungo l'attributo e il valore ad ogni cella dedlla colonna cosi da gestirne poi la visibilità facilmente
                        cells_list[index].setAttribute("HideIfEmpty_col" + index, val_attr);

                        if (!(/rowHeader/.test(rows_list[r].className)) &&  //non è la tr di intestazione
                            (cells_list[index].innerHTML != undefined) &&
                            (cells_list[index].innerHTML != null) &&
                            (cells_list[index].innerHTML != "") &&
                            (cells_list[index].innerHTML != "&nbsp;")) {
                            exist_no_empty = true;
                        }
                    }
                }
                all_cell_hideifempty = document.querySelectorAll("#" + id_tab + " tr [HideIfEmpty_col" + index + "]");
                if (exist_no_empty) {
                    visibility = "";
                } else {
                    visibility = "none";
                }

                for (var c = 0; c < all_cell_hideifempty.length; c++) {
                    all_cell_hideifempty[c].style.display = visibility;
                }
            }
            break;
        case "TimeSpanFormatASP":  //ciclo su tutte le cella della colonna per prendere il numero al suo interno (espresso in secondi) e convertirlo nel formato indicato dal valore del parametro
            var measurement_unit_val = null;
            var is_measurement_unit_present;
            var is_time_span_format_present;
            var timespan_unit = null;
            var timespan_format = null;
            var formatDB_val = null

            // inizializzo le variabili
            is_measurement_unit_present = false;
            is_time_span_format_present = false;

            // se ho sia MeasurementUnit che TimeSpanFormatASP/DB ho qualche tipo di errore a livello di config.xml
            measurement_unit_val = document.querySelectorAll("#" + id_tab + " tr.rowHeader th")[index].getAttribute("MeasurementUnit");
            if ((measurement_unit_val != null) && (measurement_unit_val != undefined) && (measurement_unit_val != "")) {
                is_measurement_unit_present = true;
            }

            //verifico se è presente l'attributo "TimeSpanFormatDB"
            formatDB_val = document.querySelectorAll("#" + id_tab + " tr.rowHeader th")[index].getAttribute("TimeSpanFormatDB");
            if ((formatDB_val != null) && (formatDB_val != undefined)) {
                if (formatDB_val != "") {
                    //verifico l'attributo "TimeSpanFormatASP"
                    if ((val_attr != null) && (val_attr != undefined)) {
                        if (val_attr != "") {
                            is_time_span_format_present = true;
                        } else {
                            console.log("TimeSpanFormatASP empty..");
                        }
                    } else {    //caso non possibile in quanto deve esistere per essere in questo "case"
                        console.log("TimeSpanFormatASP not exist..");
                    }
                } else {
                    console.log("TimeSpanFormatDB empty..");
                }
            } else {
                console.log("TimeSpanFormatDB not exist..");
            }

            // faccio la conversione sse ho la coppia di TimeSpanFormat e non ho nessuna MeasurementUnit
            if (is_time_span_format_present && !is_measurement_unit_present) {
                for (var r = 0; r < rows_list.length; r++) {
                    cells_list = rows_list[r].querySelectorAll("td");
                    if ((cells_list.length >= index) && (cells_list[index].innerHTML != "&nbsp;")) {
                        timespan_unit = DefaultParseInt(cells_list[index].innerText);
                        if (timespan_unit != null) {
                            timespan_format = ConvertUnitDBToFormatASP(timespan_unit, formatDB_val, val_attr);

                            cells_list[index].setAttribute("TimeSpan_value", timespan_unit);
                            cells_list[index].innerText = timespan_format;
                        }
                    }
                }
            }
            else if (is_time_span_format_present && is_measurement_unit_present) {
                console.log("nessuna converisone effettuata. Ho sia la coppia di TimeSpanFormat che MeasurementUnit");
            }
            break;

        case "FieldType":
            var largestSpanWidth;

            switch (val_attr) {
                case "Number":
                    // Encloses the data in a <span> with text-align:right
                    for (let i = 0; i < rows_list.length; i++) {
                        if (rows_list[i].querySelectorAll("td")[index]) {
                            rows_list[i].querySelectorAll("td")[index].innerHTML = "<span style='text-align: right; white-space: nowrap;'>" + rows_list[i].querySelectorAll("td")[index].innerHTML + "</span>";
                            rows_list[i].querySelectorAll("td")[index].style.textAlign = "center";
                        }
                    }

                    // Gets the width of the largest <span>
                    rows_array = Array.from(rows_list);
                    largestSpanWidth = Math.max(...(rows_array.map((row) => {
                        if (row.querySelectorAll("td")[index]) {
                            if (row.querySelectorAll("td")[index].querySelectorAll("span").length > 0) {
                                return $(row.querySelectorAll("td")[index].querySelectorAll("span")[0]).width() + 1;
                            }
                        }

                        return 0;
                    })));

                    // Sets the largest width to all <span> elements of the column
                    // This way the values appear right-aligned but centered in the column, without affecting responsivity
                    for (let i = 0; i < rows_list.length; i++) {
                        if (rows_list[i].querySelectorAll("td")[index]) {
                            if (rows_list[i].querySelectorAll("td")[index].querySelectorAll("span").length > 0) {
                                rows_list[i].querySelectorAll("td")[index].querySelectorAll("span")[0].style.width = largestSpanWidth + "px";
                                rows_list[i].querySelectorAll("td")[index].querySelectorAll("span")[0].style.display = "inline-block";
                                rows_list[i].querySelectorAll("td")[index].querySelectorAll("span")[0].style.whiteSpace = "inherit";
                            }
                        }
                    }
                    break;
            }
            break;
        default:
            console.log("[SetCellsDataTable] attr not handled in switch case structure");
            break;
    }
}

/**
 *	data una tabella identificata dall'id_tab, cicla su tutte le cella dell'header
 * alla ricerca di un attributo custom (vedere list_of_attribute) da usare per
 * definire come/cosa settare nella colonna mediante "SetCellsDataTable"
 * @param{string}	id_tab	-	id della tabella da controllora
 */
function SetColumnFromAttribute(id_tab) {
    //lista attributi custom
    var list_of_attribute = ["MeasurementUnit", "HideIfEmpty", "TimeSpanFormatASP", "FieldType"];
    //rowHeader identifica la tr usata dal datagrid come header nel tbody
    var cells_header = document.querySelectorAll("#" + id_tab + " tr.rowHeader th");
    var row_temp = null;
    var cell_temp = null;
    var attribute_val = null;

    for (var index_col = 0; index_col < cells_header.length; index_col++) {
        for (var a = 0; a < list_of_attribute.length; a++) {
            attribute_val = cells_header[index_col].getAttribute(list_of_attribute[a]);
            attribute_val = cells_header[index_col].getAttribute(list_of_attribute[a]);
            if ((attribute_val != null) && (attribute_val != undefined)) {
                SetCellsDataTable(id_tab, list_of_attribute[a], attribute_val, index_col);
            }
        }
    }
}

function SetRowColumnFromAttribute(id_tab) {
    //lista attributi custom
    var list_of_attribute = ["MeasurementUnit"];
    //rowHeader identifica la tr usata dal datagrid come header nel tbody
    var rows_list = document.querySelectorAll("#" + id_tab + " tr");
    var row_temp = null;
    var cell_temp = null;
    var attribute_val = null;
    for (var r = 0; r < rows_list.length; r++) {
        var cells_list = rows_list[r].querySelectorAll("td");
        for (var index_col = 0; index_col < cells_list.length; index_col++) {
            for (var a = 0; a < list_of_attribute.length; a++) {
                attribute_val = cells_list[index_col].getAttribute(list_of_attribute[a]);
                attribute_val = cells_list[index_col].getAttribute(list_of_attribute[a]);
                if ((attribute_val != null) && (attribute_val != undefined)) {
                    //SetRowCellsDataTable(id_tab, list_of_attribute[a], attribute_val, index_col);
                    if (cells_list[index_col].innerHTML.replace("&nbsp;", "").length > 0) {
                        cells_list[index_col].innerHTML += " " + attribute_val;
                    }
                }
            }
        }
    }
}

/**
 *	funzione per settare sia dg_risultati che tblSum all'interno di WUC_View
 */
function SetDataTable() {
    SetColumnFromAttribute('UCView_dgRisultati');
    SetColumnFromAttribute('UCView_tblSum');
    SetRowColumnFromAttribute('UCView_dgRisultati');
}

/**
 * funzione per gestire l'alternanza della classe delle righe (rowOdd, rowEven)
 * @param{string} id_tab	-	id della tabella da impostare
 */
function FixRowAlternateStyle(id_tab) {
    var table_tr = document.querySelectorAll('table#' + id_tab + ' tbody tr');
    var class_tr_i = null;
    var row_count = 1;

    for (var i = 0; i < table_tr.length; i++) {
        if (table_tr[i].style['display'] !== 'none') {
            class_tr_i = table_tr[i].className;
            if (!/rowSection/.test(class_tr_i)) {	//se i non è una riga di sezione
                if (row_count % 2 === 0) {
                    table_tr[i].className = class_tr_i + ' rowEven';
                }
                else {
                    table_tr[i].className = class_tr_i + ' rowOdd';
                }
                row_count++;
            }
            else {
                row_count = 1;
            }
        }
    }
}

/*******************************************************
 *                       BLANKET                       *
 *******************************************************
 *  insieme di funzioni per la gestione del blanket    *
 *******************************************************/
blanket = {
    show: function () {
        var blanket_div = $("div[blanket]");
        if (blanket_div.length > 0) {
            var this_blanket = null;
            var menu_height = $("#topMenuContainer").height();
            var login_height = $("#loginTopCont").height();
            var top_height = menu_height + login_height + 2;
            var parent_height = window.innerHeight - top_height;
            var free_width = window.innerWidth;

            $.each(blanket_div, function () {
                this_blanket = $(this);
                //attivo il blanket solo se l'attributo "IsPostBack" (scritto da vb nel banket) è false, quindi se è il primo load
                if ((this_blanket.attr("IsFirstLoad") == "True") || (this_blanket.attr("IsFirstLoad") == true) ||
                    (sessionStorage.getItem('IsBlanketForced') == "True")) {
                    this_blanket.css({
                        height: parent_height + "px",
                        top: top_height + "px",
                        width: free_width + "px"
                    });
                    if (((this_blanket.attr("IsFirstLoad") == "False") || (this_blanket.attr("IsFirstLoad") == false)) &&
                        (sessionStorage.getItem('IsBlanketForced') == "True")) {
                        sessionStorage.removeItem('IsBlanketForced');
                    }
                    this_blanket.show();
                }
            });
        }
    },

    hide: function () {
        var blanket_div = $("div[blanket]");
        if (blanket_div.length > 0) {
            $.each(blanket_div, function () {
                $(this).hide();
            });
        }
    }
};

/*******************************************************
 *                       UTILITY                       *
 *******************************************************
 *  insieme di funzioni intermedie                     *
 *******************************************************/
// estensione delle funzionalità della classe String
String.prototype.replaceAt = function (index, character) {
    return this.substr(0, index) + character + this.substr(index + character.length);
}

function isLetter(str) {
    return /^[a-zA-Z]+(-[a-zA-Z]+)*$/i.test(str);
}

//imposta la larghezza del contenitore del milling loss in base alla dimensione interna degli elememti
function SetMillingLossContSize() {
    var width_limite = 300;
    var delta_length = 25;
    var milling_loss_cont = document.querySelector("#millingLoss");
    var span_elems = document.querySelectorAll("span[spanloss='true']");
    var total_length = delta_length;

    for (var el = 0; el < span_elems.length; el++) {
        total_length += span_elems[el].offsetWidth;
    }

    if (milling_loss_cont != null) {
        if (total_length <= width_limite) {
            milling_loss_cont.style.width = width_limite + "px";
        } else {
            milling_loss_cont.style.width = total_length + "px";
        }
    }
}

/** recupera dal localStorage il languages e cerca se la chiave (key_name) in ingresso è presente,
 * se c'è restituisce la traduzione, altrimenti ritorna la chiave stessa preceduta da "MISS: ".
 * nel caso di languages completamente mancante: null
 * @param {string} key_name -   nome della chiave del languages
 * @return {string} traduzione, "MISS: " + elemento mancante
 */
function GetTraduct(key_name) {
    var to_return = "";
    var languages = localStorage.getItem("languages");

    if (!isNullOrUndefined(languages)) {
        languages = JSON.parse(languages);
        to_return = "MISS: " + key_name;
        for (var l = 0; l < languages.length; l++) {
            if (!isNullOrUndefined(languages[l][key_name])) {
                to_return = languages[l][key_name];
            }
        }
    } else {
        to_return = null;
    }

    return to_return;
}

/** aggiunge il prototipo per comparare gli array tra loro
 * @param {array} elem - array da paragonare a quello a cui è stato chiamato il metodo
 * @return {bool} if i due array hanno gli stessi elementi allo stesso posto ritorna true, else ritorna false
 */
Array.prototype.equals = function (elem) {
    if ((elem === null) || (elem === undefined)) {
        return false;
    }

    if (this.length !== elem.length) {
        return false;
    }

    for (var i = 0, l = this.length; i < l; i++) {
        if (this[i] instanceof Array && elem[i] instanceof Array) {
            if (!this[i].equals(elem[i]))
                return false;
        }
        else if (this[i] !== elem[i]) {
            return false;
        }
    }
    return true;
}
Object.defineProperty(Array.prototype, "equals", { enumerable: false });

function DefaultParseInt(string_to_parse) {
    if ((typeof string_to_parse) == "string") {
        var repleced_string = string_to_parse.replace(/,/, ".");
        var parsed_string = parseInt(repleced_string);
        if (string_to_parse == "") {
            return null;
        } else if (isNaN(parsed_string)) {
            console.log("'" + string_to_parse + "' not a number (int)!!");
            return null;
        } else {
            return parsed_string;
        }
    } else {
        return string_to_parse;
    }
}

var format_cod = {  //relazione tra carattere del formato e stringa
    /**
     * codifica dei possibili caratteri del formato:
     *	y -> anno
     *	M -> mese
     *	d -> giorni
     *	h -> ore
     *	m -> minuti
     *  s -> secondi
     * vincolo: l'ordine del formato non deve presentare buchi (Mdhm -> si, Mhm -> no)
     */
    y: { lang: "TIMESPANFORMAT_YEARS", val: null, conv_up: 1, conv_dw: 12, from_sec: 60 * 60 * 24 * 30 * 12 },
    M: { lang: "TIMESPANFORMAT_MONTHS", val: null, conv_up: 12, conv_dw: 30, from_sec: 60 * 60 * 24 * 30 },
    d: { lang: "TIMESPANFORMAT_DAYS", val: null, conv_up: 30, conv_dw: 24, from_sec: 60 * 60 * 24 },
    h: { lang: "TIMESPANFORMAT_HOURS", val: null, conv_up: 24, conv_dw: 60, from_sec: 60 * 60 },
    m: { lang: "TIMESPANFORMAT_MINUTES", val: null, conv_up: 60, conv_dw: 60, from_sec: 60 },
    s: { lang: "TIMESPANFORMAT_SECONDS", val: null, conv_up: 60, conv_dw: 1, from_sec: 1 },

    OrderFormat: function (format_in) {
        var to_return = "";
        if (/y/.test(format_in)) {
            to_return += "y";
        }
        if (/M/.test(format_in)) {
            to_return += "M";
        }
        if (/d/.test(format_in)) {
            to_return += "d";
        }
        if (/h/.test(format_in)) {
            to_return += "h";
        }
        if (/m/.test(format_in)) {
            to_return += "m";
        }
        if (/s/.test(format_in)) {
            to_return += "s";
        }
        return to_return;
    },
    Reset: function () {
        format_cod.y.val = null;
        format_cod.M.val = null;
        format_cod.d.val = null;
        format_cod.h.val = null;
        format_cod.m.val = null;
        format_cod.s.val = null;
    },
    GetOutput: function (format) {
        var to_return = "";
        var is_first_format = true;

        /**
         * per ogni carattere del formato, partendo dal "più grande"
         * -se (il valore è diverso da 0 && il valore è non nullo) || non è il primo valore da scrivere || è l'unità più piccola del formato -> lo scrivo
         * -altrimenti -> non lo scrivo
         */
        for (var f = 0; f < format.length; f++) {
            switch (format[f]) {
                case "y":
                    if (((this.y.val != null) && (this.y.val != 0)) || (!is_first_format) || (format[format.length - 1] == format[f])) {
                        if (to_return != "") {
                            to_return += " ";
                        }
                        to_return += (+this.y.val.toFixed(2)) + GetTraduct(this.y.lang);
                        is_first_format = false
                    }
                    break;
                case "M":
                    if (((this.M.val != null) && (this.M.val != 0)) || (!is_first_format) || (format[format.length - 1] == format[f])) {
                        if (to_return != "") {
                            to_return += " ";
                        }
                        to_return += (+this.M.val.toFixed(2)) + GetTraduct(this.M.lang);
                        is_first_format = false;
                    }
                    break;
                case "d":
                    if (((this.d.val != null) && (this.d.val != 0)) || (!is_first_format) || (format[format.length - 1] == format[f])) {
                        if (to_return != "") {
                            to_return += " ";
                        }
                        to_return += (+this.d.val.toFixed(2)) + GetTraduct(this.d.lang);
                        is_first_format = false;
                    }
                    break;
                case "h":
                    if (((this.h.val != null) && (this.h.val != 0)) || (!is_first_format) || (format[format.length - 1] == format[f])) {
                        if (to_return != "") {
                            to_return += " ";
                        }
                        to_return += (+this.h.val.toFixed(2)) + GetTraduct(this.h.lang);
                        is_first_format = false;
                    }
                    break;
                case "m":
                    if (((this.m.val != null) && (this.m.val != 0)) || (!is_first_format) || (format[format.length - 1] == format[f])) {
                        if (to_return != "") {
                            to_return += " ";
                        }
                        to_return += (+this.m.val.toFixed(2)) + GetTraduct(this.m.lang);
                        is_first_format = false;
                    }
                    break;
                case "s":
                    if (((this.s.val != null) && (this.s.val != 0)) || (!is_first_format) || (format[format.length - 1] == format[f])) {
                        if (to_return != "") {
                            to_return += " ";
                        }
                        to_return += (+this.s.val.toFixed(2)) + GetTraduct(this.s.lang);
                        is_first_format = false;
                    }
                    break;
                default:
                    console.log("format code not found: >" + format[f] + "<");
                    break;
            }
        }
        return to_return;
    }
};

/**
 *  esegue la conversione dei secondi in ingresso nel formato data-time indicato
 * @param{integer}  value -   tempo espresso in secondi
 * @param{string}  formatDB  -   formato data-time in ingresso
 * @param{string}  formatASP  -   formato data-time in uscita
 * @return{string}  "value" espressi come indicato da "formatASP"
 **** Ipostesi
 *  l'ordine del formato non presenta buchi (Mdhm -> si, Mhm -> no)
 */
function ConvertUnitDBToFormatASP(value, formatDB, formatASP) {
    var to_return = "";
    var format_order = null;
    var format_temp = null;
    var val_temp = null;
    var val_conv = null;

    format_cod.Reset();
    format_order = format_cod.OrderFormat(formatASP);
    val_temp = value * format_cod[formatDB].from_sec;
    for (var f = format_order.length - 1; f >= 0; f--) {
        format_temp = format_order[f];
        if (f == format_order.length - 1) {    //prima operazione, elaboro dai secondi in ingresso
            val_conv = format_cod[format_temp].from_sec;
            val_temp = val_temp / val_conv;
        } else {    //vado avanti con l'elaborazione
            val_conv = format_cod[format_temp].conv_dw;
            val_temp = Math.floor(val_temp / val_conv);
        }
        format_cod[format_temp].val = val_temp;

        if (f < format_order.length - 1) {    //se non è la prima del formato
            format_cod[format_order[f + 1]].val -= val_temp * val_conv
        }
    }
    to_return = format_cod.GetOutput(formatASP);

    return to_return
}

/**
 *  -Imposta l'icona del filtro nella rispettiva th della dgRisultati
 * per ogni filtro dell'array, se esiste già lo nascondo
 * creo un elemento img con url del filtro e che aggiungo alla th come primo elemento
 *  -Imposta in grassetto la label nel dQuery dei filtri attivi
 *  -Se dQuery rimane nascosto, inserisce di fianco al button di apertura filtri l'indicatore di filtro attivo
 * @param{array}   filters  -   array dei filtri attivi
 */
function SetFilterIndicators(filters) {
    var column_th = null;
    var el = null;

    let field_td;
    let label_td;

    // Remove all icons
    $(".filtroattivo_img").remove();

    for (var f = 0; f < filters.length; f++) {
        column_th = document.querySelector("#UCView_dgRisultati th[name='" + filters[f] + "'] ");
        if (column_th != null) {
            el = column_th.querySelector("img");
            if (el != null) {
                el.style.display = "none";
            }
            el = document.createElement("img");
            el.className = "filtroattivo_img";
            el.src = "./image/filter.png?ts=" + + Number(new Date())
            el.id = filters[f] + "-column";
            column_th.insertBefore(el, column_th.firstChild)
        }

        // Sets the font weight of the corresponding label to "bold"
        field_td = $(`#UCView_dQuery [referencecolumn='${filters[f]}']`);
        if (field_td.length > 0) {
            label_td = field_td.siblings("td").find("span");

            if (label_td.length > 0) {
                label_td[0].style.fontWeight = "bold";
            }
        }
    }

    // Adds the active filter indicator next to the opening button
    let btnSearch = $("#UCView_btnViewSearch");
    let dQueryVisible = current_page.search_params.btn_search == "1";
    if (filters.length > 0 && !dQueryVisible && btnSearch.length > 0) {
        btnSearch.parent().append($("<img class='filtroattivo_img filtroattivo_img--top' src='./image/filter.png?ts=" + + Number(new Date()) + "'/>"));
    }
}

/** myScript.vb **/
UpdateRecipe = {
    /** myScript.vb
     *  aggiorna il valore all'elemento desiderato (input) in base al valore in ingresso
     * @param{string}   id_elem     -   id dell'elemento da aggiornare
     * @param{string}   value_strg  -   nuovo valore
     */
    value: function (id_elem, value_strg) {
        $('#' + id_elem).val(value_strg);
        UpdateRecipe.updateBG(id_elem);
    },
    /** myScript.vb
     *  aggiorna il valore all'elemento desiderato (checkbox) in base al valore in ingresso
     * @param{string}   id_elem     -   id dell'elemento da aggiornare
     * @param{boolean}  value_bool  -   nuovo valore
     */
    chkBox: function (id_elem, value_bool) {
        var value_strg = value_bool.toString();
        $('#' + id_elem).prop('checked', value_strg);
        UpdateRecipe.updateBG(id_elem);
    },
    /** myScript.vb
     *  aggiorna il valore all'elemento desiderato (radio) mettendolo a true
     * @param{string}   id_elem     -   id dell'elemento da aggiornare
     */
    radio: function (id_elem) {
        var sub_id = id_elem.substring(0, id_elem.length - 2);
        $('#' + id_elem).prop('checked', 'true');
        UpdateRecipe.updateBG(sub_id);
    },
    /** myScript.vb
     *  aggiunge all'elemento desiderato la classe "bgUpdatedValue"
     * @param{string}   id_elem     -   id dell'elemento da aggiornare
     */
    updateBG: function (id_elem) {
        $('#' + id_elem + '_td').addClass('bgUpdatedValue');
    },
    /** myScript.vb
     *  rimuove all'elemento desiderato la classe "bgUpdatedValue"
     * @param{string}   id_elem     -   id dell'elemento da aggiornare
     */
    cleanBG: function (id_elem) {
        $('#' + id_elem + '_td').removeClass('bgUpdatedValue');
    }
};

/***********************************************************************************************
 *                                         SESSION                                             *
 ***********************************************************************************************
 *  Funzioni per la gestione della sessione e delle risorse necessarie lato client             *
 ***********************************************************************************************/

/**
 * Handles the creation of the session ID and its concatenation to the end of the querystring if not already present.
 */
function checkSessionAndLocalStorage() {
    let queryParams = getCurrentQueryParams();
    let needResources = false;
    let needUpdateSessionID = false;

    if (Number(queryParams["session"]) !== -1) {
        // If something's missing and the page is not inside an IFrame (leave this check to the container page)
        if ((!localStorage.getItem("calendar") || !localStorage.getItem("languages") || !localStorage.getItem("managedCycleReports") || !localStorage.getItem("rootUrl")) && window.self === window.top) {
            needResources = true;
        }

        if (!sessionStorage.getItem("session")) {
            // Remove the session ID from the query string, the right one is always in the sessionStorage
            let newSessionId = getUniqueSessionIdentifier();
            sessionStorage.setItem("session", newSessionId);

            // Sets this last page, the one with the session ID not present, as invalid, so that it won't be used
            // by my_history in any case
            if (typeof my_history === "object") {
                if (my_history === null) {
                    MyHistory.GetHistory();
                }

                if (my_history.page_list[my_history.act_index]) {
                    my_history.page_list[my_history.act_index].isValid = false;
                }
            }

            needUpdateSessionID = true;
        } else {
            // Update the session ID in the query string of the current URL only if not already present
            if (!queryParams["session"]) {
                // Sets this last page, the one with the session ID not present, as invalid, so that it won't be used
                // by my_history in any case
                if (typeof my_history === "object") {
                    if (my_history === null) {
                        MyHistory.GetHistory();
                    }

                    if (my_history.page_list[my_history.act_index]) {
                        my_history.page_list[my_history.act_index].isValid = false;
                    }
                }

                needUpdateSessionID = true;
            } else if (Number(sessionStorage.getItem("session")) !== queryParams["session"]) {
                // Fix the session ID in the query string if the two differ
                needUpdateSessionID = true;

                // Sets this last page, the one with the session ID not present, as invalid, so that it won't be used
                // by my_history in any case
                if (typeof my_history === "object") {
                    if (my_history === null) {
                        MyHistory.GetHistory();
                    }

                    if (my_history.page_list[my_history.act_index]) {
                        my_history.page_list[my_history.act_index].isValid = false;
                    }
                }
            }
        }

        if (needResources) {
            queryParams["resources"] = "YES";
        }

        if (needUpdateSessionID) {
            queryParams["session"] = sessionStorage.getItem("session");
        }

        if (needResources || needUpdateSessionID) {
            // Load the new URL if necessary
            window.location.search = encodeQueryParams(queryParams);
        } else if (queryParams["resources"]) {
            if (typeof my_history === "object") {
                if (my_history === null) {
                    MyHistory.GetHistory();
                }
            }

            window.parent.location.href = removeResourceRequestFromQueryString(window.parent.location.href);
        }
    }
}

/**
 * Checks if the tab has been duplicated from another one (in that case the sessionStorage, therefore the session ID, would be copied by the original tab).
 * Clears the sessionStorage if that's the case.
 */
function checkTabDuplicationLock() {
    let retVal = false;

    // Remove the lock on unload
    window.addEventListener('beforeunload', () => {
        sessionStorage.removeItem('__tabDuplicationLock');
    });

    // If a lock was found, the tab has been duplicated, so clear the sessionStorage
    if (sessionStorage.getItem('__tabDuplicationLock')) {
        sessionStorage.clear();
        retVal = true;
    }

    // Set the lock on the current tab
    sessionStorage.setItem('__tabDuplicationLock', '1');

    return retVal;
}

/**
 * Return a unique session identifier by checking in the localStorage for duplicates.
 */
function getUniqueSessionIdentifier() {
    let lastSessionIdentifier = 0;

    if (localStorage.getItem("lastSessionIdentifier")) {
        lastSessionIdentifier = JSON.parse(localStorage.getItem("lastSessionIdentifier"));
    }

    // Resets the counter if the MAX was reached
    if (lastSessionIdentifier >= MAX_SESSION_IDENTIFIER_VALUE) {
        lastSessionIdentifier = 0;
    }

    newSessionId = ++lastSessionIdentifier;

    localStorage.setItem("lastSessionIdentifier", JSON.stringify(lastSessionIdentifier));

    return newSessionId;
}

/**
 * Parses the current query string from the URL into an object.
 */
function getCurrentQueryParams(url = window.location.search) {
    let retVal = {};
    let queryString = url.split('?')[1] || "";

    if (queryString.length > 0) {
        retVal = queryString.split('&').reduce(function (qs, query) {
            var chunks = query.split('=');
            var key = chunks[0];
            var value = decodeURIComponent(chunks.slice(1).join('=') || '');
            var valueLower = value.trim().toLowerCase();
            if (valueLower === 'true' || value === 'false') {
                value = Boolean(value);
            } else if (!isNaN(Number(value))) {
                value = Number(value);
            }
            return (qs[key] = value, qs);
        }, {});
    }

    return retVal
}

/**
 * Turns the object into query string format.
 * @param {any} queryParamsObj Object representing the query string to encode
 */
function encodeQueryParams(queryParamsObj) {
    let retVal = "";
    let prevChar = "";  // Becomes "&" after the first key-value pair converted

    for (let key of Object.keys(queryParamsObj)) {
        if (key) {
            retVal += prevChar + key + "=" + queryParamsObj[key];
            prevChar = "&";
        }
    }

    return retVal;
}

/**
 * Removes the "resources" key from the query string of the desired URL.
 * @param {any} url
 */
function removeResourceRequestFromQueryString(url) {
    let queryParams = getCurrentQueryParams(url);

    if (queryParams["resources"]) {
        delete queryParams["resources"];
    }

    return url.split("?")[0] + "?" + encodeQueryParams(queryParams);
}

function setSessionIdOnLinkClickListener() {
    $("a[href]").not("[href^='javascript:']").not("[href='#']").on("click", (e) => {
        let $elem = $(e.currentTarget);

        let queryParams = getCurrentQueryParams($(e.currentTarget).attr("href"));
        queryParams["session"] = sessionStorage.getItem("session");

        $elem.attr("href", $elem.attr("href").split("?")[0] + "?" + encodeQueryParams(queryParams))
    })
}

/**
 * Checks the new client update timestamp agains the old one, and reloads the resources if needed
 * @param {any} timestamp
 */
function checkLastClientUpdate(timestamp) {
    if (localStorage.getItem("lastClientUpdate") === null || localStorage.getItem("lastClientUpdate") !== timestamp) {
        localStorage.setItem("lastClientUpdate", timestamp);

        let queryParams = getCurrentQueryParams();

        queryParams["resources"] = "YES";
        window.location.search = encodeQueryParams(queryParams);
    }
}

/****************************************************************************
 *                              LANGUAGES                                   *
 ****************************************************************************
 *  Gestione delle traduzioni tramite sessionStorage.languages              *
 ****************************************************************************/

/**
 * Returns the string with every "[translation_key]" replaced by its translation.
 * @param {string} text Text to elaborate
 */
function getTranslatedText(text) {
    let retVal = text;
    let match;

    while ((match = retVal.match(/\[([^\]]+)\]/))) {
        retVal = retVal.replace(`[${match[1]}]`, getTranslation(match[1]));
    }

    return retVal;
}

/**
 * Returns the translation for the keyname in the current user language
 * @param {string} key Keyname of the desired translation
 */
function getTranslation(key) {
    let retVal;
    let languages = (JSON.parse(localStorage.getItem("languages")) || []).reduce((languages, translation) => {
        for (var key in translation) {
            if (Object.prototype.hasOwnProperty.call(translation, key)) {
                languages[key] = translation[key];
            }
        }
        return languages;
    }, {});

    if (isNullOrUndefined(languages) || isNullOrUndefined((retVal = languages[key]))) {
        retVal = `JS: Entry non trovata per il KeyName: ${key}`;

        console.error(`[getTranslation] Keyname "${key}" not found in dictionary.`);
    }

    return retVal;
}

/****************************************************************************
 *                              OTHERS                                      *
 ****************************************************************************
 *  Funzioni per la gestione di dimensioni dinamiche ed altro               *
 ****************************************************************************/

/**
 * Called on textarea input, adapts the size of the element to better suit the containing text
 * @param {JQuery<HTMLElement>} elem
 */
function adaptTextAreaSize(elem) {
    elem.style.whiteSpace = "pre";
    elem.style.width = $(elem).css("min-width") || "auto";
    elem.style.width = Math.min(
        Math.max(elem.scrollWidth - Number($(elem).css("padding-left").replace("px", "")) - Number($(elem).css("padding-right").replace("px", "")), Number($(elem).css("min-width").replace("px", ""))),
        $(elem).attr("maxWidth")
    ) + "px";

    elem.style.height = $(elem).css("min-height") || "auto";
    elem.style.height = Math.max(elem.scrollHeight - Number($(elem).css("padding-top").replace("px", "")) - Number($(elem).css("padding-bottom").replace("px", "")), Number($(elem).css("min-height").replace("px", ""))) + "px";

    if (elem.scrollLeftMax > 0 || elem.scrollTopMax > 0) {
        elem.style.whiteSpace = "pre-line";
        $(elem).height($(elem).height() + elem.scrollTopMax + 2);
    }
}

/**
 * Sets, for each textarea, the oninput listener, which calls the re-calculation of the size of the element
*/
function handleTextAreaSize() {
    $("textarea").each((idx, v) => {
        v.cols = 1;
        v.rows = 1;

        let $v = $(v);
        $v.width($v.css("max-width") || "100%");
        $v.attr("maxWidth", $v.width());

        adaptTextAreaSize(v);
    });

    $("textarea").off().on("input", (e) => { adaptTextAreaSize(e.target) });
}

/**
 * Checks if each select exceeds its parent width, and limits the length of the text inside its options accordingly
 */
function handleSelectMaxLength() {
    // Detects parent widths for every select
    $("select:not(.exclude-length-check)").each((idx, v) => {
        let $v = $(v);
        $v.hide(0);

        if (isNullOrUndefined($v.attr("parentWidth"))) {
            if ($v.is(":not(:visible)")) {
                let invisibleParents = $($v.parents().toArray().filter(elem => elem.style.display === "none"));
                invisibleParents.show(0);

                $v.parents("table").first().find("select").hide(0).each((idx, elem) => {
                    $(elem).attr("parentwidth", $(elem).parent().width());
                }).show(0);

                invisibleParents.hide(0);
                $v.hide(0);
            } else {
                $v.parents("table").first().find("select").hide(0).each((idx, elem) => {
                    $(elem).attr("parentWidth", $(elem).parent().width());
                }).show(0);
            }
        }
    });

    // Limits the length of options inside the selects
    $("select:not(.exclude-length-check)").each((idx, v) => {
        let $v = $(v);

        if ($v.attr("parentwidth") && Number($v.attr("parentwidth")) > 50) {
            let maxLength = Number($v.attr("parentwidth")) * 0.8 / (Number($v.css("font-size").replace("px", "")) / 2);

            $v.find("option").each((idx, opt) => {
                if (!!opt.innerHTML && opt.innerHTML.length > maxLength) {
                    opt.innerHTML = opt.innerHTML.substring(0, maxLength) + " ...";
                }
            });
        } else {
            console.warn("[handleSelectMaxLength] The parent is too thin or has no defined width.");
        }

        $v.show(0);
    });
}

function callCustomerHeaderDrawing() {
    let $header = $("#customerHeader");

    if ($header.length > 0 && !!drawCustomerHeader) {
        drawCustomerHeader();
    }
}

/**
 * Adds classes to the element according to the custom styles defined in its configuration.
 * @param {any} $elem Element jQuery object
 * @param {number[]} styles Array of CUSTOM_STYLES from the configuration of the element
 */
function setCustomStyles($elem, styles) {
    if ($elem && styles) {
        for (let style of styles) {
            $elem.addClass(`customstyle--${CUSTOM_STYLES[style].toLowerCase().replaceAll("-", "")}`);
        }
    }
}

/**
 * Returns whether the object passed in is not defined
 * @param {any} v Object to check
 */
function isNullOrUndefined(v) {
    return v === null || v === undefined;
}

/**
 * Sets the enter keypress listener for the UCView_div_query element
 */
function handleDivQueryEnterKey() {
    if ($("#UCView_div_query").length > 0) {
        $("#UCView_div_query").keypress((e) => {
            if (e.keyCode === 13 && $("#UCView_btnStartQuery").length > 0 && $(":focus").length > 0 && $(":focus")[0].tagName.toLowerCase() !== "textarea") {
                e.preventDefault();
                $("#UCView_btnStartQuery").click();
            }
        });
    }
}

/**
 * For PRODUCTION_REPORTS page, hides reports extraColumn when there's no report for the cycle
 */
function handleManagedCycleReports() {
    let managedCycles = JSON.parse(localStorage.getItem("managedCycleReports"));

    if (window.location.search.indexOf("pagename=PRODUCTION_REPORTS") !== -1) {
        if (managedCycles) {
            $("#div_risultati a[href^='ProductionReports.aspx']").each((idx, elem) => {
                if (elem.attributes["href"] && elem.attributes["href"].value.match(/CYC_ID=(\d+)/) && managedCycles.indexOf(Number(elem.attributes["href"].value.match(/CYC_ID=(\d+)/)[1])) === -1) {
                    $(elem).hide();
                }
            })
        } else {
            $("#div_risultati a[href^='Reports.aspx']").hide();
        }
    }
}

/**
 * Measures the width of the text via the canvas method
 * @param {string} text Text to measure
 * @param {string} font Font size string
 */
function getTextWidth(text, font) {
    var canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement("canvas"));
    var context = canvas.getContext("2d");
    context.font = font;
    var metrics = context.measureText(text);
    return metrics.width;
}

function handleDynamicHeightForScroll() {
    let $elem;
    let $containerDiv;

    /**
     * In order to use: set the "handleDynamicHeight" to the content element, the parent will become scrollable
     */
    function performCalculation() {
        // The calculations performed work with one element per page, 2 or more may compromise accuracy
        if ($(".handleDynamicHeight").length > 1) {
            console.warn(`[handleViewDynamicHeightForScroll] Ignoring 'handleDynamicHeight' for ${$(".handleDynamicHeight").length - 1} extra elements.`);
        }

        $elem = $(".handleDynamicHeight").first();
        $containerDiv = $elem.parent();

        // container height = window height - (mainSection offset + (mainSection height - element height))
        $containerDiv.css("height", "calc(100vh - " + (getTotalOffsetTopOfElement($(".mainSection")[0]) + ($(".mainSection")[0].scrollHeight - $elem.height())) + "px)");

        // Adds a class to the container making it scrollable
        $containerDiv.addClass("scrollableContainer");
    }

    if (current_page.aspx_name) {
        switch (current_page.aspx_name) {
            case "new.aspx":
            case "edit.aspx":
            case "viewcyclereports.aspx":
            case "viewrecipehistory.aspx":
                performCalculation();
                break;

            case "default.aspx":
                // If the page is in "showAll" mode
                if (!isNullOrUndefined($("#UCView_lnkPageShowAll").attr("disabled"))) {
                    performCalculation();
                    $containerDiv.addClass("scrollableContainerView");
                }
                break;
        }
    }
}

/**
 * Starts the onresize handler for the calculation of the chart size
 */
function startChartSizeCalculationHandler() {
    let timeoutId = null;
    calculateChartSize();

    $(window).on("resize", () => {
        clearTimeout(timeoutId);

        timeoutId = setTimeout(() => {
            calculateChartSize();
        }, 100);
    })
}

/**
 * Calculates the chart's dimensions and sets the values in the hidden fields
 */
function calculateChartSize() {
    let width = $(window).width() - 200 - 20;
    let height = $(window).height() - $(".navbarContainer").height() - 30;

    // Prevents ArgumentOutOfRangeException (on the Unit(Int32) constructor used by the chart library) and OverflowException (for negative values)
    if (width < 0 || width > 32000) {
        width = 1600;
    }
    if (height < 0 || height > 32000) {
        height = 800;
    }

    // Sets the hidden fields' values to allow the .aspx.vb to generate the chart with the proper sizing
    $("#ChartHeight").val(Math.floor(height));  // Floors to an integer
    $("#ChartWidth").val(Math.floor(width));  // Floors to an integer
}

/**
 * On click handler on the tree view panel, which updates the selected node and sets the selectedNode class to the td
 * @param {any} e
 */
function lotsTreeViewPanelOnClick(e) {
    let elem = e.target.tagName === "IMG" ? e.target.parentNode : e.target;

    if ($(elem).is("a[target=LotDetail]")) {
        $(e.currentTarget).find("td.selectedNode").removeClass("selectedNode");

        let idOfMainLink = elem.id[elem.id.length - 1] === 'i' ? elem.id.substring(0, elem.id.length - 1) : elem.id;
        $("#" + idOfMainLink).parent().addClass("selectedNode");

        WUClotsMenu_treeLots_Data.selectedNodeID.value = idOfMainLink;
    }
}

/**
 * Sets the height of the IFrame to the height of the contained #print_area
 */
function calculateLotDetailHeight() {
    window.parent.document.getElementById("IFrameLotDetail").setAttribute("height", window.document.getElementById("print_area").offsetHeight + "px");
}

/**
 * Gets the absolute distance from the top of the window of the element
 * @param {any} elem The element to analyse
 */
function getTotalOffsetTopOfElement(elem) {
    let currElem = elem;
    let offsetTop = currElem.offsetTop;

    while (currElem.offsetParent) {
        currElem = currElem.offsetParent;
        offsetTop += currElem.offsetTop;
    }

    return offsetTop;
}

/**
 * Removes the sessionExpired item in the URL to avoid showing the message again
 */
function removeSessionExpiredFromUrl() {
    let queryParams = getCurrentQueryParams();

    if (queryParams["sessionExpired"]) {
        delete queryParams["sessionExpired"];
    }

    window.location.search = encodeQueryParams(queryParams);
}

/**
 * Prevents scroll's default behaviour on numeric inputs (only when the field is focused), in order to prevent accidental value changes while scrolling
 */
function preventScrollFromChangingInputValue() {
    $('input[type=number]').on('focus', function () {
        $(this).on('wheel.disableScroll', function (e) {
            e.preventDefault()
        })
    })
    $('input[type=number]').on('blur', function () {
        $(this).off('wheel.disableScroll')
    })
}

/**
 * Extracts the contents of the iframe in order to print the page correctly
 */
function extractPrintDataFromIFrame() {
    let printArea = document.getElementById("IFrameLotDetail").contentDocument.getElementById("print_area");
    let clone = printArea.cloneNode(true);

    clone.classList.add("printOnly");

    $(".lothistoryContainer .mainSection").children("#print_area").remove()
    $(".lothistoryContainer .mainSection").append(clone);
}