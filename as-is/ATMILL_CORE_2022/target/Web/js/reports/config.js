﻿const UNITS_TRANSLATIONS = {
    WEIGHT_E_06_PER_HOUR: "g/h",
    WEIGHT_E_03_PER_HOUR: "kg/h",
    WEIGHT_PER_HOUR: "t/h",
    VOLUME_PER_HOUR: "L/h",
    POWER_PER_HOUR: "kWh",
    WEIGHT: "t",
    VOLUME: "L",
    POWER: "kW",
    POWER_PER_WEIGHT: "kW/t",
    POWER_HOUR_PER_WEIGHT: "kWh/t",
    PPM: "g/t",
    WEIGHT_E_03: "kg",
    WEIGHT_E_06: "g",
    PERCENTAGE: "%"
};

const DEFAULT_DATE_FORMAT = "dd-MM-yyyy HH:mm";
const DATE_FORMAT = (JSON.parse(localStorage.getItem("calendar"))?.DateTimeFormat || DEFAULT_DATE_FORMAT)
    .replace("dd", "DD").replace("yyyy", "YYYY").replace("HH", "hh");   // Converts the standard formatting sintax to the MomentJS specific one

const REPORT_ID = {
    1: 'TODO',
}

const DYNAMIC_WEIGHT_UNITS_CONFIG = {
    GRAMS_UNTIL: 0.0001,
    KGS_UNTIL: 0.1
};

const DYNAMIC_FLOWRATE_UNITS_CONFIG = {
    GRAMS_UNTIL: 0.0001,
    KGS_UNTIL: 0.1
};