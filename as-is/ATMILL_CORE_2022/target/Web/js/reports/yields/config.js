﻿// The 'content' within each report type can be an object (structured like {0: {element}, where the 0 is the index of the array in the generic configuration})
// or an array (in this case the number and order of the elements has to correspond to the generic configuration)

const SPECIFIC_CONFIGURATION = {};

// From here it's possible to toggle on/off the rendering of %/B1 and %/B0 columns, as well as LOSS/B1 and LOSS/B0 sections.
// The elements are shown if the necessary data is received, otherwise they remain hidden:
// - no B1 data in valueJSON.wheat => no %/B1 column and no LOSS/B1 section
// - no B0 data in valueJSON.wheat => no %/B0 column and no LOSS/B0 section
const B1_B0_CONFIGURATION = {
    PERC_ON_B0: false,
    PERC_ON_B1: true,
    LOSS_ON_B0: false,
    LOSS_ON_B1: true,
};

const SHOW_CAPACITTY_UTILIZATION = true;

/**
 * Hides/Shows the B0/B1 related columns according to the B1_B0_CONFIGURATION and to the data received.
 */
specificReportCode.SCALES_INSTANT = () => {
    let $cont = $("#reportsContainer");

    // %/B1
    if (!B1_B0_CONFIGURATION.PERC_ON_B1) {
        // Hide %/B1
        $cont.find("td[id$='.onB1'], td[data-column='onB1']").hide();
        $cont.find("th[data-column='onB1']").hide();
    }

    // LOSS/B1
    if (!B1_B0_CONFIGURATION.LOSS_ON_B1) {
        $(".reports--operation--loss_on_b1").remove();
    }

    // %/B0
    if (!B1_B0_CONFIGURATION.PERC_ON_B0) {
        // Hide %/B0
        $cont.find("td[id$='.onB0'], td[data-column='onB0']").hide();
        $cont.find("th[data-column='onB0']").hide();
    }

    // LOSS/B0
    if (!B1_B0_CONFIGURATION.LOSS_ON_B0) {
        $(".reports--operation--loss_on_b0").remove();
    }

    // CAPACITY_UTILIZATION
    if (!SHOW_CAPACITTY_UTILIZATION) {
        $("#scales-CAPACITY_UTILIZATION").hide();
        $("#scales-UTILIZATION_OVER_CAPACITY").hide();
    }
};

specificReportCode.SCALES_PERIODIC = specificReportCode.SCALES_INSTANT;
specificReportCode.SCALES_JOB = specificReportCode.SCALES_INSTANT;