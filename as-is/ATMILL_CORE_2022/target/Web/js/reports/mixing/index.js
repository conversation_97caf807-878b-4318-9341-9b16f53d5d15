﻿/**
 * Draws the panel for the mixing report
 * @param {any} elem Element to draw
 * @param {jQuery<HTMLElement>} parent Parent jQuery element
 */
SpecificElementDrawing.MIXING_PANEL = (elem, parent) => {
    let $elem = $("<div class='reports--mixing reports--mixing--extended'></div>");

    // Retrieve all data from the sessionStorage
    let valueBatch = getDataFromValueJSONViaPath("batch." + elem.parameters.contents);
    if (!isNullOrUndefined(valueBatch)) {
        valueBatch = valueBatch.elem;
    }

    let valueIngredients = getDataFromValueJSONViaPath("top.recipeIngredients.val");

    // Draw a row for each batch

    // Draw the first extra row for headings in collapsed view
    let $row = $(
        `<div class='reports--mixing--row reports--mixing--collapsed'>
            <div class='reports--mixing--row--header'>
              <div class= 'reports--mixing--row--datarow'>
                <div class='reports--mixing--row--datarow--expected'>${getTranslatedTextReport(elem.parameters.columns.expected.label)}</div>
                <div class='reports--mixing--row--datarow--actual'>${getTranslatedTextReport(elem.parameters.columns.actual.label)}</div>
                <div class='reports--mixing--row--datarow--diff'>${getTranslatedTextReport(elem.parameters.columns.difference.label)}</div>
              </div>
            </div>
          </div>`
    );
    $elem.append($row);

    let $ingredientRow;

    let actualValues;
    let requestedValues;
    let originsDescriptions;
    let dateStart;
    let dateStop;

    for (let batch of valueBatch) {
        [actualValues, requestedValues, originsDescriptions, dateStart, dateStop] = batch.val;

        $row = $(
            `<div class='reports--mixing--row'>
                <div class='reports--mixing--row--header'>
                  <div class='reports--mixing--row--header--title'>
                    <div class='reports--mixing--row--header--title--text'><span>${getTranslatedTextReport("[BATCH_REPETITION]")} ${batch.id}</span> (${datetimeToTime(dateStart)} / ${datetimeToTime(dateStop)})</div>
                  </div>
                  <div class= 'reports--mixing--row--datarow' >
                    <div class='reports--mixing--row--datarow--origin reports--mixing--extended'>${getTranslatedTextReport(elem.parameters.columns.originDescription.label)}</div>
                    <div class='reports--mixing--row--datarow--expected reports--mixing--extended'>${getTranslatedTextReport(elem.parameters.columns.expected.label)}</div>
                    <div class='reports--mixing--row--datarow--expected reports--mixing--row--datarow--nobold reports--mixing--collapsed'>${!requestedValues.some((v) => isNullOrUndefined(v)) ? formatValueWithDecimals(requestedValues.reduce((prev, next) => prev + next, 0), elem.parameters.columns.expected.decimals) : PLACEHOLDER_STRING} ${getTranslatedTextReport(`[${UNITS[UNITS.WEIGHT]}]`)}</div>
                    <div class='reports--mixing--row--datarow--actual reports--mixing--extended'>${getTranslatedTextReport(elem.parameters.columns.actual.label)}</div>
                    <div class='reports--mixing--row--datarow--actual reports--mixing--row--datarow--nobold reports--mixing--collapsed'>${!actualValues.some((v) => isNullOrUndefined(v)) ? formatValueWithDecimals(actualValues.reduce((prev, next) => prev + next, 0), elem.parameters.columns.expected.decimals) : PLACEHOLDER_STRING} ${getTranslatedTextReport(`[${UNITS[UNITS.WEIGHT]}]`)}</div>
                    <div class='reports--mixing--row--datarow--diff reports--mixing--extended'>${getTranslatedTextReport(elem.parameters.columns.difference.label)}</div>
                    <div class='reports--mixing--row--datarow--diff reports--mixing--row--datarow--nobold reports--mixing--collapsed'>
                        ${!requestedValues.some((v) => isNullOrUndefined(v)) & !actualValues.some((v) => isNullOrUndefined(v)) ? formatRelativeNumber(formatValueWithDecimals(((actualValues.reduce((prev, next) => prev + next, 0) / requestedValues.reduce((prev, next) => prev + next, 0)) - 1) * 100, elem.parameters.columns.difference.decimals, elem.parameters.columns.difference.decimalMode)) : PLACEHOLDER_STRING}${getTranslatedTextReport("[PERCENTAGE]")}
                    </div>
                  </div>
                </div>
                <div class='reports--mixing--row--ingredients reports--mixing--extended--anim'></div>
              </div>`
        );

        let $ingredients = $row.find(".reports--mixing--row--ingredients");
        let currIngredientType = null;

        // Sort ingredients according to the ingredient type
        valueIngredients = valueIngredients.sort((a, b) => MIXING_INGREDIENT_TYPES_ORDER[a.type] - MIXING_INGREDIENT_TYPES_ORDER[b.type]);

        for (let i = 0; i < valueIngredients.length; i++) {
            $ingredientRow = $(
                `<div class='reports--mixing--row--ingredientrow'>
                      <div class='reports--mixing--row--ingredientrow--name'>${valueIngredients[i].description}
                      </div>
                      <div class='reports--mixing--row--datarow' >
                        <div class='reports--mixing--row--datarow--origin'>${originsDescriptions[i]}</div>
                        <div class='reports--mixing--row--datarow--expected'>
                            ${!isNullOrUndefined(requestedValues[i]) ? formatDynamicUnitWeightMixing(formatValueWithDecimals(requestedValues[i], elem.parameters.columns.expected.decimals)) : "-- kg"}
                        </div>
                        <div class='reports--mixing--row--datarow--actual'>
                            ${!isNullOrUndefined(actualValues[i]) ? formatDynamicUnitWeightMixing(formatValueWithDecimals(actualValues[i], elem.parameters.columns.actual.decimals)) : "-- kg"}
                        </div>
                        <div class='reports--mixing--row--datarow--diff ${Math.abs(((actualValues[i] / requestedValues[i]) - 1) * 100) > valueIngredients[i].tolerance ? (requestedValues[i] > actualValues[i] ? "alarm-low" : "alarm-high") : (requestedValues[i] === actualValues[i] ? "" : "")}'>
                            ${MIXING_INGREDIENT_TYPES_SHOW_DIFF[MIXING_INGREDIENT_TYPES[valueIngredients[i].type]] ? formatRelativeNumber(formatValueWithDecimals(((actualValues[i] / requestedValues[i]) - 1) * 100, elem.parameters.columns.difference.decimals, elem.parameters.columns.difference.decimalMode)) + getTranslatedTextReport("[PERCENTAGE]") : ""}
                        </div>
                      </div>
                    </div>`
            );

            if (!MIXING_INGREDIENT_TYPES_SHOW_DIFF[MIXING_INGREDIENT_TYPES[valueIngredients[i].type]]) {
                $ingredientRow.find(".reports--mixing--row--datarow--diff").removeClass("alarm-low", "alarm-high").html("");
            } else if (isNullOrUndefined(requestedValues[i]) || isNullOrUndefined(actualValues[i])) {
                $ingredientRow.find(".reports--mixing--row--datarow--diff").removeClass("alarm-low", "alarm-high").html("-- " + getTranslatedTextReport("[PERCENTAGE]"));
            }

            // Adds the ingredient type label if this is the first ingredient of the type
            if (valueIngredients[i].type !== currIngredientType) {
                currIngredientType = valueIngredients[i].type;

                $ingredientRow.attr("ingredienttype", getTranslationReport(MIXING_INGREDIENT_TYPES_TRANSLATIONS[MIXING_INGREDIENT_TYPES[valueIngredients[i].type]]))
            }

            $ingredients.append($ingredientRow);
        }

        // Draw single repetition total
        $ingredientRow = $(
            `<div class='reports--mixing--row--ingredientrow reports--mixing--row--ingredientrow--total'>
                <div class='reports--mixing--row--ingredientrow--name'>${getTranslationReport("BATCH_REPETITION_TOTAL")}</div>
                <div class='reports--mixing--row--datarow' >
                    <div class='reports--mixing--row--datarow--origin'></div>
                    <div class='reports--mixing--row--datarow--expected' ${requestedValues.every(v => !isNullOrUndefined(v)) ? "" : "missing"}>
                        ${formatDynamicUnitWeightMixing(formatValueWithDecimals(requestedValues.reduce((p, n) => p + n), elem.parameters.columns.expected.decimals))}
                    </div>
                    <div class='reports--mixing--row--datarow--actual' ${actualValues.every(v => !isNullOrUndefined(v)) ? "" : "missing"}>
                        ${formatDynamicUnitWeightMixing(formatValueWithDecimals(actualValues.reduce((p, n) => p + n), elem.parameters.columns.actual.decimals))}
                    </div>
                    <div class='reports--mixing--row--datarow--diff'>
                        ${formatRelativeNumber(formatValueWithDecimals(((actualValues.reduce((p, n) => p + n) / requestedValues.reduce((p, n) => p + n)) - 1) * 100, elem.parameters.columns.difference.decimals, elem.parameters.columns.difference.decimalMode)) + getTranslatedTextReport("[PERCENTAGE]")}
                    </div>
                </div>
            </div>`
        );
        $ingredients.append($ingredientRow);

        // Append the row to the container
        $elem.append($row);
    }

    parent.append($elem);

    $elem.hide();

    return $elem;
};

/**
 * Draws the custom MIXING_TOTAL_PANEL operation
 * @param {jQuery<HTMLElement>} $elem Initialized jQuery element
 * @param {any} elem Element configuration object
 */
Operations.MIXING_TOTAL_PANEL = ($elem, elem) => {
    // Retrieve all data from the sessionStorage
    let valueBatch = getDataFromValueJSONViaPath("batch." + elem.parameters.contents);
    if (!isNullOrUndefined(valueBatch)) {
        valueBatch = valueBatch.elem;
    }

    let valueIngredients = getDataFromValueJSONViaPath("top.recipeIngredients.val");

    let $row = $(
        `<div class='reports--mixingtotal--row'>
                <div class='reports--mixingtotal--row--header'>
                  <div class='reports--mixing--row--ingredientrow--name'>${getTranslatedTextReport(elem.parameters.columns.productName.label)}</div>
                  <div class= 'reports--mixing--row--datarow' >
                    <div class='reports--mixing--row--datarow--expected'>${getTranslatedTextReport(elem.parameters.columns.expected.label)}</div>
                    <div class='reports--mixing--row--datarow--expectedkgs'>${getTranslatedTextReport(elem.parameters.columns.expectedKgs.label)}</div>
                    <div class='reports--mixing--row--datarow--actual'>${getTranslatedTextReport(elem.parameters.columns.actual.label)}</div>
                    <div class='reports--mixing--row--datarow--diff'>${getTranslatedTextReport(elem.parameters.columns.difference.label)}</div>
                  </div>
                </div>
                <div class='reports--mixing--row--ingredients'></div>
              </div>`
    );

    let $ingredients = $row.find(".reports--mixing--row--ingredients");

    let currIngredientType = null;

    // Sort ingredients according to the ingredient type
    valueIngredients = valueIngredients.sort((a, b) => MIXING_INGREDIENT_TYPES_ORDER[a.type] - MIXING_INGREDIENT_TYPES_ORDER[b.type]);

    for (let i = 0; i < valueIngredients.length; i++) {
        $ingredientRow = $(
            `<div class='reports--mixing--row--ingredientrow'>
                <div class='reports--mixing--row--ingredientrow--name'>${valueIngredients[i].description}
                </div>
                <div class='reports--mixing--row--datarow'>
                    <div class='reports--mixing--row--datarow--expected'>
                        ${formatValueWithDecimals(valueIngredients[i].quantity, elem.parameters.columns.expected.decimals) + " " + getTranslatedTextReport(`[${valueIngredients[i].unit}]`)}
                    </div>
                    <div class='reports--mixing--row--datarow--expectedkgs' ${valueBatch.some((v) => isNullOrUndefined(v.val[1][i])) ? "missing" : ""}>
                        ${formatDynamicUnitWeightMixing(valueBatch.reduce((prev, curr) => prev + curr.val[1][i], 0), elem.parameters.columns.expectedKgs.decimals, elem.parameters.columns.expectedKgs.decimalMode)}
                    </div>
                    <div class='reports--mixing--row--datarow--actual' ${valueBatch.some((v) => isNullOrUndefined(v.val[0][i])) ? "missing" : ""}>
                        ${formatDynamicUnitWeightMixing(valueBatch.reduce((prev, curr) => prev + curr.val[0][i], 0), elem.parameters.columns.expectedKgs.decimals, elem.parameters.columns.expectedKgs.decimalMode)}
                    </div>
                    <div class='reports--mixing--row--datarow--diff'>
                        ${MIXING_INGREDIENT_TYPES_SHOW_DIFF[MIXING_INGREDIENT_TYPES[valueIngredients[i].type]] ? formatRelativeNumber(formatValueWithDecimals(((valueBatch.reduce((prev, curr) => prev + curr.val[0][i], 0) / valueBatch.reduce((prev, curr) => prev + curr.val[1][i], 0)) - 1) * 100, elem.parameters.columns.difference.decimals, elem.parameters.columns.difference.decimalMode)) + getTranslatedTextReport("[PERCENTAGE]") : ""}
                    </div>
                </div>
            </div>`
        );

        // Adds the ingredient type label if this is the first ingredient of the type
        if (valueIngredients[i].type !== currIngredientType) {
            currIngredientType = valueIngredients[i].type;

            $ingredientRow.attr("ingredienttype", getTranslationReport(MIXING_INGREDIENT_TYPES_TRANSLATIONS[MIXING_INGREDIENT_TYPES[valueIngredients[i].type]]))
        }

        $ingredients.append($ingredientRow);
    }

    // Adds a total row
    $ingredientRow = $(
        `<div class='reports--mixing--row--ingredientrow reports--mixingtotal--row--ingredientrow--total'>
            <div class='reports--mixing--row--ingredientrow--name'>${getTranslationReport("YIELDS_TOT")}
            </div>
            <div class='reports--mixing--row--datarow'>
                <div class='reports--mixing--row--datarow--expected'></div>
                <div class='reports--mixing--row--datarow--expectedkgs'>
                    ${formatDynamicUnitWeightMixing(valueBatch.reduce((prev, curr) => prev + curr.val[1].reduce((p, c) => p + c, 0), 0), elem.parameters.columns.expectedKgs.decimals, elem.parameters.columns.expectedKgs.decimalMode)}
                </div>
                <div class='reports--mixing--row--datarow--actual'>
                    ${formatDynamicUnitWeightMixing(valueBatch.reduce((prev, curr) => prev + curr.val[0].reduce((p, c) => p + c, 0), 0), elem.parameters.columns.expectedKgs.decimals, elem.parameters.columns.expectedKgs.decimalMode)}
                </div>
                <div class='reports--mixing--row--datarow--diff'>
                    ${formatRelativeNumber(formatValueWithDecimals(((valueBatch.reduce((prev, curr) => prev + curr.val[0].reduce((p, c) => p + c, 0), 0) / valueBatch.reduce((prev, curr) => prev + curr.val[1].reduce((p, c) => p + c, 0), 0)) - 1) * 100, elem.parameters.columns.difference.decimals, elem.parameters.columns.difference.decimalMode)) + getTranslatedTextReport("[PERCENTAGE]")}
                </div>
            </div>
        </div>`
    );

    $ingredients.append($ingredientRow);

    $elem.append($row);

    // Show the "missing flow" warning note
    if ($ingredients.find("[missing]").length > 0) {
        $elem.append($(`<div class="reports--mixingtotal--missingflows">* ${getTranslationReport("REPORTS_MISSING_FLOWS")}</div>`))
    }
};

/**
 * Draws the custom MIXING_TOGGLE_DETAIL operation
 * @param {jQuery<HTMLElement>} $elem Initialized jQuery element
 * @param {any} elem Element configuration object
 */
Operations.MIXING_TOGGLE_DETAIL = ($elem, elem) => {
    // Draw the switch for viewing in the browser
    let $noPrintLabel = $(`<label> ${getTranslatedTextReport(elem.parameters.label)}</label> `).addClass("reports--noprint");
    let $switch = createSwitchComponent(`switch-${OPERATIONS[elem.parameters.operation].toLowerCase()
        }`, (e) => {
            let $target = $(`#${elem.parameters.target} `);

            if (e.currentTarget.checked) {
                $target.fadeIn(200);
            } else {
                $target.fadeOut(200);
            }
        }).addClass("reports--noprint");

    $elem.append($noPrintLabel);
    $elem.append($switch);
};

/**
 * Draws the custom MIXING_TOGGLE_DETAIL_EXPANSION operation
 * @param {jQuery<HTMLElement>} $elem Initialized jQuery element
 * @param {any} elem Element configuration object
 */
Operations.MIXING_TOGGLE_DETAIL_EXPANSION = ($elem, elem) => {
    // Draw the switch for viewing in the browser
    let $noPrintLabel = $(`<label> ${getTranslatedTextReport(elem.parameters.label)}</label> `).addClass("reports--noprint");
    let $switch = createSwitchComponent(`switch-${OPERATIONS[elem.parameters.operation].toLowerCase()
        }`, (e) => {
            let $target = $(`#${elem.parameters.target} `);

            if (e.currentTarget.checked) {
                $target.removeClass("reports--mixing--collapsed");
                $target.addClass("reports--mixing--extended");

                $target.find(".reports--mixing--extended--anim").slideDown();
                $target.find(".reports--mixing--collapsed--anim").slideUp();
            } else {
                $target.removeClass("reports--mixing--extended");
                $target.addClass("reports--mixing--collapsed");

                $target.find(".reports--mixing--extended--anim").slideUp();
                $target.find(".reports--mixing--collapsed--anim").slideDown();
            }
        }).addClass("reports--noprint");

    $elem.append($noPrintLabel);
    $elem.append($switch);
};

/**
 * A callback for handling the onchange event of the switch component
 * @callback onChangeCallback
 * @param {jQuery.Event} e Change event
 */
/**
 * Creates and returns a switch component
 * @param {string} id Id for the element
 * @param {onChangeCallback} onChangeCallback Callback
 */
function createSwitchComponent(id, onChangeCallback, defaultValue = false) {
    let $switch = $(`<label id = "${id}" class="reports--switch"></label> `);
    let $input = $(`<input type = 'checkbox' ${defaultValue ? "checked" : ""}> `);
    let $slider = $("<span></span>;");

    $switch.on("change", "input", onChangeCallback);

    $switch.append($input);
    $switch.append($slider);

    return $switch;
}

/**
 * Returns the relative representation of the number, always showing the plus/minus sign (when num !== 0)
 * @param {number | string} num
 */
function formatRelativeNumber(num) {
    let retVal;

    if (typeof (num) === "number") {
        retVal = num > 0 ? "+" + num : (num < 0 ? "-" + num : num);
    } else if (typeof (num) === "string" && !isNaN(Number(num))) {
        retVal = Number(num) > 0 ? "+" + num : num;
    }

    return retVal;
}

function formatDynamicUnitWeightMixing(weight, decimalPlaces = undefined, decimalMode = undefined) {
    let retVal = null;
    let value = Number(weight);

    if (!isNaN(value)) {
        if (value < DYNAMIC_WEIGHT_UNITS_MIXING_CONFIG.GRAMS_UNTIL) {
            retVal = formatValueWithDecimals(value, 0) + " " + UNITS_TRANSLATIONS.WEIGHT_E_06;
        }
        else if (value < DYNAMIC_WEIGHT_UNITS_MIXING_CONFIG.KGS_UNTIL) {
            retVal = formatValueWithDecimals(value / 1000, decimalPlaces, decimalMode) + " " + UNITS_TRANSLATIONS.WEIGHT_E_03;
        }
        else {
            retVal = formatValueWithDecimals(value / 1000000, 3, decimalMode) + " " + UNITS_TRANSLATIONS.WEIGHT;
        }
    } else {
        console.error(`[formatDynamicUnitWeightMixing] Function received non - numeric value.`);
    }

    return retVal;
}

function datetimeToTime(datetime) {
    let retVal;
    let calendarObj = localStorage.getItem("calendar");

    if (calendarObj) {
        retVal = moment(datetime, JSON.parse(calendarObj).DateTimeFormat).format("HH:mm:ss")
    }

    return retVal;
}