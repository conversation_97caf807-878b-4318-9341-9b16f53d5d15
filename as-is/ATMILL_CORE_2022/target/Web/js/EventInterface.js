﻿/********************************************************************************
 *                              INTERFACCIA EVENTI                              *
 ********************************************************************************
 *  InitPage        -> Azioni generiche al caricamento della pagina             *
 *  ButtonEvents    -> Gestione eventi sui pulsanti                             *
 *  DoneEvents      -> Eventi richiamati al completamento di azioni             *
 ********************************************************************************/

/**
 *  Azioni generiche al caricamento della pagina
 * diviso in:
 *  > apertura
 *      -add/reload pagina in my_history
 *      -visibilità pulsanti di navigazione
 *      -visibilità search
 *  > custom
 *      -specifico in base alla pagina aperta
 *  > chiusura
 *      -avvio rilevamento rotella per scorrimento verticale della pagina
 *      -aggiungo controllo numerico sui campi di input
 *      -chiudo loader
 */
function InitPage() {
    checkSessionAndLocalStorage();

    //init di apertura
    SetNavigationButtonVisibility();
    callCustomerHeaderDrawing();

    if (typeof my_history === "object") {
        GetNewPath();
        MyHistory.SetVisibilityOnPage();

        //init custom in base alla pagina
        switch (current_page.aspx_name) {
            case "export.aspx":
                SetWUCOperationSize();
                checkForPrintRequest();
                $(".rightSection").show();
                break;
            case "binlayers.aspx":
                SetWUCOperationSize();
                checkForPrintRequest();
                $(".rightSection").show();
                break;
            case "edit.aspx":
                SetWUCOperationSize();
                handleTextAreaSize();
                checkForPrintRequest();
                $(".rightSection").show();
                //handleDynamicHeightForScroll();
                break;
            case "graphics.aspx":
                SetWUCOperationSize();
                checkForPrintRequest();
                $(".rightSection").show();
                startChartSizeCalculationHandler();
                break;
            case "lot_history.aspx":
                SetWUCOperationSize();
                $(".rightSection").show();
                break;
            case "lothistorydetail.aspx":
                checkForPrintRequest();
                break;
            case "new.aspx":
                SetWUCOperationSize();
                handleTextAreaSize();
                $(".rightSection").show();
                //handleDynamicHeightForScroll();
                break;
            case "reports.aspx":
                SetWUCOperationSize();
                checkForPrintRequest();
                $(".rightSection").show();
                break;
            case "viewcyclereports.aspx":
                SetWUCOperationSize();
                handleTextAreaSize();
                checkForPrintRequest();
                $(".rightSection").show();
                //handleDynamicHeightForScroll();
                break;
            case "viewrecipehistory.aspx":
                SetWUCOperationSize();
                handleTextAreaSize();
                checkForPrintRequest();
                $(".rightSection").show();
                //handleDynamicHeightForScroll();
                break;
            default:  //"default.aspx":
                SetWUCOperationSize();
                handleTextAreaSize();
                checkForPrintRequest();
                $(".rightSection").show();
                handleDynamicHeightForScroll();
                break;
        }
    }

    //init di chiusura
    handleSelectMaxLength();
    SetCustomElement();
    SetCustomValidityInput();
    handleDivQueryEnterKey();
    handleManagedCycleReports();
    preventScrollFromChangingInputValue();
    setSessionIdOnLinkClickListener();

    if (typeof myLoader === "object") {
        myLoader.stop();
    }
    blanket.hide();
}

/**
 *  Gestione eventi sui pulsanti
 */
ButtonEvents = {
    // Pressione link di WUC_Operation
    Back: function () {
        MyHistory.Back();
    },
    Print: function () {
        CallPrint();
    },
    PrintReport: function () {
        PrintIframe('IFrameReport', ['jquery.mobile-1.4.2', 'reports/index', 'reports/print', 'reports/mixing/index', 'reports/mixing/print']);
    },
    PrintGraph: function () {
        PrintChart();
    },
    PrintIframe: function (iframe_id, stylesheets = ['jquery.mobile-1.4.2', 'default', 'print']) {
        PrintIframe(iframe_id, stylesheets);
    },
    PrintWhole: function () {
        PrintWhole();
    },
    RptMail: function (list_mail) {
        CallRptMail(list_mail);
    },

    // Pressione in WUC_New
    WUC_New: {
        Submit: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    case "STOCK_MANUAL_CORR_REQ":
                        MyHistory.Reload();
                        break;
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Submit();
            }
        },
        Cancel: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    case "STOCK_MANUAL_CORR_REQ":
                        MyHistory.Reload();
                        break;
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Cancel();
            }
        }
    },
    // Pressione in WUC_Edit
    WUC_Edit: {
        Submit: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Submit();
            }
        },
        Cancel: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Cancel();
            }
        }
    },
    // Pressione in WUC_Procexecution
    WUC_Procexecution: {
        Submit: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Submit();
            }
        },
        Cancel: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Cancel();
            }
        }
    },
    // Pressione in WUC_PlantConf
    WUC_PlantConf: {
        Cancel: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Reload();
            }
        }
    },

    // Eventi per cicli pianificati
    PlannedCycle: {
        StartPlanning: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                SetDataTable();
            }
        },
        StopPlanning: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                SetDataTable();
            }
        },
        PlanningMode: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                SetDataTable();
            }
        }
    },
    // Eventi per cicli controllati
    ControlledCycle: {
        StartJob: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                MyHistory.Reload();
            }
        },
        EndJob: function (pagename) {
            var b_default = false;
            if ((pagename != null) && (pagename != undefined) && (pagename != "")) {
                switch (pagename) {
                    default:
                        b_default = true;
                        break;
                }
            } else {
                b_default = true;
            }

            if (b_default) {
                // nothing to do
            }
        }
    },

    // Eventi per MessageBox.Confirm
    MessageBoxConfirm: {
        Submit: function () {
            // forzo la visibilità del blanket sulla pressione dell'OK (che potenzialmente crea una serie di reload da mascherare)
            sessionStorage.setItem('IsBlanketForced', 'True');
            __doPostBack();
        },
        Cancel: function () {
            if ((current_page.isReloaded) && (current_page.isValid)) { //la pagina attuale e quella corrente hanno lo stesso url -> l'attuale è una pagina riconosciuta
                //nothing
            } else {
                MyHistory.Cancel();
            }
        }
    },
    // Eventi per MessageBox.Prompt
    MessageBoxPrompt: {
        Submit: function () {
            // forzo la visibilità del blanket sulla pressione dell'OK (che potenzialmente crea una serie di reload da mascherare)
            sessionStorage.setItem('IsBlanketForced', 'True');
            __doPostBack();
        },
        Cancel: function () {
            if ((current_page.isReloaded) && (current_page.isValid)) { //la pagina attuale e quella corrente hanno lo stesso url -> l'attuale è una pagina riconosciuta
                //nothing
            } else {
                MyHistory.Cancel();
            }
        }
    },
    // Eventi per MessageBox.Alert
    MessageBoxAlert: {
        Submit: function () {
            if ((current_page.isReloaded) && (current_page.isValid)) { //la pagina attuale e quella corrente hanno lo stesso url -> l'attuale è una pagina riconosciuta
                //nothing
            } else {
                MyHistory.Cancel();
            }
        }
    }
};

/**
 *  Eventi richiamati al completamento di azioni
 */
DoneEvents = {
    // Login verificato
    Login: function () {
        MyHistory.Reload();
    },

    // In seguito ad un'azione derivata dall'url (torno alla stessa pagina senza "xxxx = yes" oppure "operation = xxx" dall'url stesso)
    ActionURL: function () {
        MyHistory.Submit();
    },

    // In seguito ad un'azione generica non deriata dall'url (ricarico lo stesso url)
    ActionGeneric: function () {
        MyHistory.Reload();
    }
};