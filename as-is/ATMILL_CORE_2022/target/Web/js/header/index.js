﻿/**
 * Draws elements inside of the costumer header as configured
 * @param {any} config Configuration for the customer header
 */
function drawCustomerHeader(config = HEADER_CONFIGURATION) {
    let $container = $("#customerHeader");

    if ($container.length > 0) {
        // Setup grid layout
        $container.css("grid-template-columns", `repeat(${config.grid.x}, 1fr)`);
        $container.css("grid-template-rows", `repeat(${config.grid.y}, auto)`);

        // Draw each element
        for (let elem of config.content) {
            if (checkMandatoryHeaderParameters(elem.type, elem.parameters)) {
                let $elem;

                switch (elem.type) {
                    case HEADER_ELEMENT_TYPES.IMG:
                        $elem = $('<img class="customerheader--elem customerheader--elem--img"/>');
                        $elem.css("height", elem.parameters.height);
                        $elem.attr("src", elem.parameters.src);
                        break;

                    case HEADER_ELEMENT_TYPES.TEXT:
                        $elem = $('<div class="customerheader--elem customerheader--elem--text"></div>');
                        $elem.html(getTranslatedText(elem.parameters.text));
                        break;

                    default:
                        console.error(`[drawCustomerHeader] Unknown header element type: "${elem.type}".`);
                        console.error("[drawCustomerHeader] Element: ", elem);
                        break;
                }

                $elem.css("grid-column", `${elem.position.x} / span ${elem.size.x}`);
                $elem.css("grid-row", `${elem.position.y} / span ${elem.size.y}`);

                // Manage custom styles
                setCustomStyles($elem, elem.parameters.styles);

                $container.append($elem);
            } else {
                console.error(
                    `[drawCustomerHeader] Missing parameters for type ${HEADER_ELEMENT_TYPES[elem.type]} (expected "${MANDATORY_HEADER_ELEMENT_PARAMETERS[HEADER_ELEMENT_TYPES[elem.type]].join('","')}").`
                );
                console.error("[drawCustomerHeader] Element: ", elem);
            }
        }
    }
}

/**
 * Checks whether the mandatory parameters for the current type are present on the object (customer header).
 * @param {number} type Element type number
 * @param {any} obj Parameters object
 */
function checkMandatoryHeaderParameters(type, obj) {
    return MANDATORY_HEADER_ELEMENT_PARAMETERS[HEADER_ELEMENT_TYPES[type]].every((k) => k in obj);
}