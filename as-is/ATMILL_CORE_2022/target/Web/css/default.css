:root {
    --sm: 5px;
    --md: 10px;
    --lg: 15px;
    --navbar-height: 90px;
    --ocrim-color: #ec2a3d;
}

@media screen {
    .printOnly {
        display: none;
    }
}

html {
    height: 100%;
}

.blanket {
    background-color: #FFFFFF;
    /*height: 100px;*/
    width: 100.1%;
    position: absolute;
    /*top: 0px;*/
    left: 0px;
    z-index: 999;
    opacity: 1;
}

.navbarContainer {
    height: var(--navbar-height);
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%);
    border-top: 1px solid #aaaaaa;
    border-bottom: 1px solid #aaaaaa;
    position: relative;
    justify-content: space-between;
}

.ocrimLink {
    display: inline-block;
}

    .ocrimLink img {
        height: 45px;
        width: 140px;
    }

#loginTopCont + #topMenuContainer {
    border-top: 1px solid #aaaaaa;
}

.verticalSpace {
    height: 10px;
    width: 100%;
}

.hoverHand {
    cursor: pointer;
}

#ruler {
    visibility: hidden;
    white-space: nowrap;
}

input[type='number'] {
    -moz-appearance: textfield;
    -webkit-appearance: textfield;
    margin: 0;
}

input[type=image] {
    width: auto;
}

input[type=checkbox] {
    width: auto;
}

input.DateTimePicker_jq {
    width: 200px;
}

.tdWuc input {
    width: auto;
}

.tdWuc select {
    width: 100%;
}

    .tdWuc select[name$="DateH"] {
        width: auto;
    }

.menuTOP {
    height: 20px;
    border-radius: 20px;
    background-color: #65aac0;
    line-height: 20px;
    letter-spacing: 1px;
    margin-top: 10px;
}

.menuMID {
    margin: 0px 9px;
    padding: 2px 5px 3px 5px;
    border-left: 3px solid #65aac0;
    border-right: 3px solid #65aac0;
    border-bottom: 8px solid #65aac0;
    border-radius: 0px 0px 20px 20px;
}

.midOperation {
    height: 1px;
}
/****** ASPETTO DEI TESTI ******/
/*elenco elementi: in grassetto*/
.label-text-section, .title-text, .btnEdit, .btnNew, .btnPlanningMode, .btnCycleStart, .btnCycleStop, .btnRefresh,
.btnStampa, .txtBold, .label1, .txt2, .txtPagerStyle td table tbody tr td span {
    font-weight: bold;
}

.lblError {
    font-weight: normal;
}
/*elenco elementi: dimensione testo*/
.txtSmall, body, .label-text, .label-text-section, .label-text-login, .ddl-text, .rb, .TEXT, .text, .calendar-text, .textbox-text,
.textbox-text-right, .text-right, .btnStampa, .label1 {
    font-size: 11px;
}

.btnCycleStop, .btnCycleStart, .btnPlanningMode, .btnNew, .btnEdit, .btnLogin, .btnGeneral, .btnRefresh {
    font-size: 12px;
}

.title-text {
    font-size: 16px;
}

/*elenco elementi: tipo font*/
body, .label-text, .rb, .TEXT, .text, .ddl-text, .calendar-text, .textbox-text, .textbox-text-right, .text-right, .btnLogin, .btnGeneral,
.title-text, .label-text-login, .btnStampa, .label1 {
    font-family: "Verdana", "Georgia", "Arial", "Helvetica", "Sans-serif";
}

font {
    font-family: Tahoma;
}

/*elenco elementi: tipo di allineamento */
.textbox-text-right, .text-right, .btnCycleStop, .btnPlanningMode, .btnCycleStart, .btnRefresh, .txt1, .txtRight {
    text-align: right;
}

.txtLeft {
    text-align: left;
}

.txtCenter {
    text-align: center;
}

.textbox-text, .btnLogin, .floatLeft, table#WebOperation_tblPrint tbody tr {
    float: left;
}

/*elenco elementi: decorazione testo*/
a:link, a:visited {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/*elenco elementi: colore testo*/
a:link, a:visited, .events:link, .btnCycleStop, .btnPlanningMode, .btnCycleStart, .btnRefresh, .fontBlack {
    color: #000000;
}

.title-text, .btnEdit, .btnNew, .txtWhite, .btnStampa, .txt2 {
    color: #FFFFFF;
}

a.lblError, .txtRed, a.home:hover, a.events:hover {
    color: #FF3300;
}

a:hover {
    color: #99CCFF;
}

/*elenco elementi: larghezza elementi*/
tr.txtPagerStyle td table, table#UCView_tblSum {
    width: inherit;
}

    table#UCView_tblSum .tblSumSeparator {
        width: 50px;
    }

.btnLogin {
    width: 60px;
}

.textbox-text:not(textarea), .textbox-text-right {
    width: 80px !important;
}

textarea.textbox-text {
    max-width: 90% !important;
    min-height: 1.2em;
    min-width: 200px;
    line-height: 1.2em;
}

.btnGeneral {
    width: 100px;
}

.tabDimRid1 {
    width: 100%;
}

.bgImg2 {
    width: 20%;
}

.imgContainer {
    height: calc(100vh - 90px);
    overflow: hidden;
}

.imgExpanse, .tabBground1, .menuWucTOP {
    width: 100%;
}

.tdBin {
    width: 200px;
}

.tdLegenda {
    padding: 0 var(--lg);
}

    .tdLegenda table {
        max-width: 600px;
    }

        .tdLegenda table td:first-child {
            position: relative;
        }

        .tdLegenda table .legend--color {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 70%;
            height: 70%;
            border: 1px solid black;
            box-sizing: border-box;
        }

/*elenco elementi: caratteristiche bordi*/
.btnLogin, .btnGeneral, .btnCycleStop, .btnCycleStart, .btnPlanningMode, .btnCycleStop, .btnCycleStart, .btnRefresh, .bord1px {
    border: solid 1px;
}

.btnLogin, .btnGeneral, table .tabBorder1, .btnCycleStop, .btnCycleStart, .btnPlanningMode, .btnCycleStop, .btnCycleStart, .btnRefresh {
    border-color: #000000;
}

.btnCycleStop, .btnCycleStart, .btnPlanningMode, .btnRefresh {
    border-radius: 3px;
}

.btnEdit, .btnNew {
    border: 0px;
}

.rb {
    border-style: none;
}

.brdDes {
    border-style: dashed;
}

.btnStampa {
    border: solid 1px #000000 inset;
}

.tblBin {
    border: solid 3px #000000;
    width: 200px;
    height: 350px;
}

/*elenco elementi: spaziature -> margini*/
body {
    margin: 0;
}

.div1 {
    margin-left: -250px;
    margin-top: -300px;
}

.tabMrg3 {
    margin-top: 2px;
    margin-left: 0px;
    margin-right: 5px;
}

.tabMrg3 {
    border-collapse: collapse;
}

    .tabMrg3 tr td.txtBold {
        padding-top: 3px;
    }

.bgSet1 {
    margin-top: 0px;
}

.tabMrg3Top {
    margin-top: 5px;
}

a.home {
    margin-left: 30px;
}

.margin1 {
    margin-left: 2px;
}

table .tabMrg1 {
    margin-left: 25px;
}

.tabMrg2, div #dView, div #dPeriodic, div #dLots, div #dView {
    margin-left: 3px;
}

.divMrg1, .mrgBot {
    margin-bottom: 5px;
}
/*elenco elementi: spaziature -> padding*/
.label-text-login {
    padding-left: 8px;
}

.menuWarningLeft {
    padding-left: 5px;
}

.menuWarningRight {
    padding-right: 7px;
}

/*elenco elementi: colore sfondo*/
body, .btnLogin, .btnGeneral, .btnCycleStop, .btnPlanningMode, .btnCycleStart, .btnRefresh, .bgWhite, #warnings {
    background-color: #FFFFFF;
}

    .btnLogin:hover, .btnGeneral:hover, .btnCycleStop:hover, .btnCycleStart:hover, .btnRefresh:hover, .btnPlanningMode:hover, .bgCol1 {
        background-color: #F0F0F0; /*TODO*/
    }

table #tblNew {
    background-color: #000000;
}

.btnEdit, .btnNew, body.body-time {
    background-color: transparent;
}

/*elenco elementi: proprietà background-repeat*/

.tabBground1 {
    background-repeat: repeat-x;
}

.btnEdit, .btnNew, .btnPlanningMode, .btnCycleStop, .btnCycleStart, .btnRefresh, .bgImg2, .menuWucTOP {
    background-repeat: no-repeat;
}

.menuWucTOP, .menuLEFT, .menuRIGHT {
    background-repeat: repeat-y;
}

/*elenco elementi: proprietà background-position*/
.title_center, .btnEdit, .btnNew, .bgCenter {
    background-position: center;
}

.btnCycleStop, .btnPlanningMode, .btnCycleStart, .btnRefresh, .menuWucTOP {
    background-position: left;
}

.bgSet1 {
    background-position: center top;
}

/*elenco elementi: proprietà background-size*/
.menuLEFT, .menuRIGHT {
    background-size: contain;
}
/*elenco elementi: background con img*/
.tabBground1, .bgImg2 {
    border-bottom: 2px solid #65aac0;
    padding-bottom: 0px;
    padding-top: 0px;
    height: 14px;
}

.bgImg2 {
    background-color: #65aac0;
    border-radius: 0px 20px 0px 0px;
}

/*elenco elementi: tipo di cursore*/
.link_hover {
    cursor: pointer;
}

.btnNew, .btnPlanningMode, .btnCycleStart, .btnRefresh, .btnCycleStop, .btnEdit, .btnGeneral, .btnLogin {
    cursor: default;
}

/*modifiche css ad img*/
.botImg {
    vertical-align: bottom;
}

.txt2 {
    vertical-align: middle;
}

.tdExpanse, .vAlgTop {
    vertical-align: top;
}

/****** personalizzati ******/
table {
    width: 100%;
    border: 0px;
    text-align: left;
    border-collapse: collapse;
    border-spacing: 0;
}

    table[id$="tblHeader"] {
        min-width: 400px;
        margin: 0.5rem 0;
    }

        table[id$="tblHeader"] + table, table#tblHeader + table {
            margin-top: 0.5rem;
        }

        table[id$="tblHeader"] + hr {
            display: block !important;
        }

    table.tableHeight tr {
        height: 25px;
    }

    table[id$="dgRisultati"] tbody tr {
        height: 27px !important;
    }

.tdBin table {
    border-collapse: initial;
}

div[id$="dView"] table tbody tr, div[id$="dQuery"] table tbody tr {
    height: 25px;
}

    div[id$="dQuery"] table tbody tr td:first-child {
        width: 25%;
    }

div[id$="dView"] table.tblBin tbody tr, div[id$="dQuery"] table.tblBin tbody tr {
    height: auto;
}

table td span.rb {
    padding-right: 20px;
}

.bgSet1 {
    width: 50%;
    z-index: auto;
}

.txt2 {
    height: 30px;
}

.style1 {
    z-index: 101;
    left: 120px;
    position: absolute;
    top: 184px;
    width: 96px;
    height: 32px;
}

.style2 {
    z-index: 102;
    left: 256px;
    position: absolute;
    top: 176px;
    width: 256px;
    height: 40px;
}

.style3 {
    z-index: 103;
    left: 536px;
    position: absolute;
    top: 184px;
}

.div1 {
    margin-left: -250px;
    margin-top: -300px;
    position: absolute;
    top: 50%;
    left: 50%;
}

div.rightSection, div.UCCont, .elementHide {
    display: none;
}

    div.UCCont > div {
        overflow: hidden;
    }

.uploadProgressDiv {
    text-align: center;
    padding-top: 5px;
}

div #div1, .bgSet1 {
    position: absolute;
}

div #div1 {
    margin-left: -250px;
    margin-top: -300px;
    top: 50%;
    left: 50%;
}

.btnCycleStop {
    background-image: url('../image/btnStop.png');
    height: 30px;
    min-width: 160px;
}

.btnCycleStart {
    background-image: url('../image/btnStart.png');
    height: 30px;
    min-width: 160px;
}

.btnRefresh {
    background-image: url('../image/btnRefresh.png');
    height: 30px;
    min-width: 160px;
}

.btnPlanningMode {
    background-image: url('../image/btnPlanningMode.png');
    height: 30px;
    min-width: 235px;
}

.btnNew {
    background-image: url('../image/button1.png');
    height: 25px;
    width: 100px;
}

.buttonGreen {
    width: 30%;
    padding: 0% 10%;
}

.txtButtonGreen {
    width: 94%;
    text-align: left;
    vertical-align: bottom;
}

#warnings {
    border: 0px solid #000000;
    height: 135px;
}

.btnCycleStart:disabled, .btnCycleStop:disabled {
    background-color: #CCCCCC;
    opacity: 0.4;
}

.btnCycleStart, .btnRefresh, .btnCycleStop, .btnPlanningMode {
    background-position: 3px center;
}

textarea {
    resize: none;
    overflow: hidden;
}

/****** MYCALENDAR ******/
/*bordi*/
.MyCalendar .ajax__calendar_container, .MyCalendar .ajax__calendar_body {
    border: solid 1px #cccccc;
}
/*background*/
.MyCalendar .ajax__calendar_container {
    background-color: #e2e2e2;
}

.MyCalendar .ajax__calendar_body {
    background-color: #e9e9e9;
}

.MyCalendar .ajax__calendar_header, .MyCalendar .ajax__calendar_hover .ajax__calendar_day, .MyCalendar .ajax__calendar_hover .ajax__calendar_month,
.MyCalendar .ajax__calendar_hover .ajax__calendar_year, .MyCalendar .ajax__calendar_active {
    background-color: #ffffff;
}
/*spaziature*/
.MyCalendar .ajax__calendar_header {
    margin-bottom: 4px;
}

.MyCalendar .ajax__calendar_title, .MyCalendar .ajax__calendar_next, .MyCalendar .ajax__calendar_prev {
    padding-top: 3px;
}

.MyCalendar .ajax__calendar_dayname {
    margin-bottom: 4px;
    margin-top: 2px;
}
/*testo*/
.MyCalendar .ajax__calendar_dayname, .MyCalendar .ajax__calendar_day {
    text-align: center;
}

.MyCalendar .ajax__calendar_dayname, .MyCalendar .ajax__calendar_today, .MyCalendar .ajax__calendar_hover .ajax__calendar_day,
.MyCalendar .ajax__calendar_hover .ajax__calendar_month, .MyCalendar .ajax__calendar_hover .ajax__calendar_year, .MyCalendar .ajax__calendar_active {
    font-weight: bold;
}

.MyCalendar .ajax__calendar_title, .MyCalendar .ajax__calendar_next, .MyCalendar .ajax__calendar_prev, .MyCalendar .ajax__calendar_hover .ajax__calendar_day, .MyCalendar .ajax__calendar_hover .ajax__calendar_month, .MyCalendar .ajax__calendar_hover .ajax__calendar_year, .MyCalendar .ajax__calendar_active {
    color: #004080;
}

.MyCalendar .ajax__calendar_other, .MyCalendar .ajax__calendar_hover .ajax__calendar_today, .MyCalendar .ajax__calendar_hover .ajax__calendar_title {
    color: #bbbbbb;
}

/*immagine button margin*/
.MyCalendarImgMargin {
    margin-left: 2px;
    margin-right: 5px;
}

.DivPopMessage {
    display: none;
    position: absolute;
    top: 20%;
    right: 15%;
    background-color: #F0F0F0;
    font-size: 13px;
    padding: 10px 10px 10px 10px;
}

.lotLink {
    font-style: oblique;
}

.rowSection {
    background-color: #F2D1C4;
}

.rowSection_LightGreen {
    background-color: #9cfc95;
}

.rowSection_LightBlue {
    background-color: #95f0fc;
}

.rowSection_LightRed {
    background-color: #ff9696;
}

.rowEven {
    background-color: #E1F0F9;
}

.rowOdd {
    background-color: #FFFFFF;
}

.bgUpdatedValue {
    background-color: #F4DA70;
}

.tbVersionInfo {
    width: 50%;
}

.tblLanguage, .tblTime {
    width: 50%;
    margin: auto;
}

    .tblLanguage tr td {
        text-align: center;
    }

.tblAlarms {
    border-collapse: collapse;
}

    .tblAlarms tbody tr:first-child td {
        color: #FFFFFF;
        text-align: center;
        font-weight: bold;
    }

input.textbox-text-right {
    margin-right: 5px;
}

/* myMessageBox */
.overlay_ {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99999;
    background: rgba(0, 0, 0, .8);
}

.MessageBox {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 20%;
    margin: 15% auto;
    background: #EEE;
    z-index: 99999;
    border-radius: 5px;
}

.MessageBox__wrapper {
    overflow: auto;
    height: 100%;
}

.MessageBox__content {
    margin-top: 4px;
    padding: 0.5em;
    text-align: center;
    font-size: 14px;
    overflow-y: auto;
}

.MessageBox__btn__content {
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 40px;
    background: rgb(201, 199, 199);
    border-radius: 0px 0px 5px 5px;
    -moz-border-radius: 0px 0px 5px 5px;
    -webkit-border-radius: 0px 0px 5px 5px;
    margin-top: 5px;
}

.MessageBox__close {
    position: relative;
    width: 80px;
    height: 25px;
    margin-top: 7.5px;
}

.MessageBox__install {
    position: relative;
    width: 80px;
    height: 25px;
    margin-top: 7.5px;
}

    .MessageBox__install.MessageBox__btn__double {
        margin-right: 4%;
        margin-left: 38%;
    }

    .MessageBox__install.MessageBox__btn__solo {
        margin-right: 0%;
        margin-left: 45%;
    }

.MessageBox__input {
    width: 79.5%;
    margin-left: 10%;
    height: 25px;
}

.myPagerTable {
    width: auto;
}

    .myPagerTable a {
        padding: 2px 5px;
    }

        .myPagerTable a[disabled="disabled"] {
            font-weight: bold;
        }

#UCSystemGroupsRights_tblGroupsRights td {
    width: 11.5%;
}

    #UCSystemGroupsRights_tblGroupsRights td:first-child {
        width: auto;
    }

.greenLed {
    background-image: url("../image/greenled.png");
}

.yellowLed {
    background-image: url("../image/yellowled.png");
}

.redLed {
    background-image: url("../image/redled.png");
}
/* report mixing */
.totalRow {
    background-color: #dddfe3;
    font-weight: bold;
}

    .totalRow td {
        height: 20px;
        line-height: 20px;
        border-bottom: solid 2px #000000;
        border-top: solid 2px #000000;
    }

        .totalRow td:first-child {
            border-left: solid 2px #000000;
        }

        .totalRow td:last-child {
            border-right: solid 2px #000000;
        }

.batchRow {
    background-color: #F0F0F0;
    height: 25px;
}

.mixingLed {
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 20px;
}

.mixNodeSelected {
    font-weight: bold;
}

#ddlJob {
    direction: ltr;
    width: 100%;
}

    #ddlJob option {
        direction: ltr;
        text-align: left;
    }

.input-wide {
    width: 200px;
}

.btnLeftIcon {
    width: auto;
    padding-left: 30px;
}

/** LOADER **/
.loaderAjaxBG {
    background-color: #FFFFFF;
    width: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1000;
    opacity: 0.75;
}

.loaderAjax {
    position: fixed;
    z-index: 1001;
    text-align: center;
    color: #000000;
    top: 24%;
    border-radius: 20pt;
    width: 100%;
}

    .loaderAjax p {
        font-size: 20pt;
    }

.filtroattivo_img {
    width: 17px;
    height: 17px;
}

.filtroattivo_img--top {
    margin-left: 0.5rem;
}

#UCView_dgRisultati th {
    vertical-align: middle;
    position: sticky;
    top: 0;
    color: inherit;
    background-color: inherit;
}

    #UCView_dgRisultati th.icon-col {
        width: 22px;
    }

    #UCView_dgRisultati th a {
        vertical-align: super;
    }

.scrollableContainer {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
}

    .scrollableContainer table {
        border: none !important
    }

.scrollableContainerView {
    border: 1px solid black;
    border-bottom: none;
}

td div[name='div_rb'] {
    float: left;
}

select:disabled, input[type="text"]:disabled, input[type="number"]:disabled, textarea:disabled {
    background-color: rgb(236, 236, 236);
    color: black;
    opacity: 1;
    border: 1px solid gray;
    padding: 2px;
}

/* Adapts in order to always show the Plot button in graphics.aspx */
@media screen {
    #Chart1 {
        max-width: calc(100vw - 210px);
    }
}

#customerHeader {
    display: none;
    width: 98%;
    height: 100px;
    border: double 3px #000000;
    margin: 10px auto 0px auto;
}

.customerheader--elem--img {
    background-repeat: no-repeat;
    background-size: contain;
    background-position-y: center;
    background-position-x: center;
}

.center-y {
    display: flex;
    align-items: center;
}

.center-x {
    display: flex;
    flex-direction: column;
    align-items: center;
}

input[type="text"], input[type="password"], input[type="number"], textarea {
    padding: 2px;
}

select {
    padding: 3px;
}

div.hr {
    height: 1px;
    width: 100%;
    position: relative;
    padding-top: var(--md) !important;
    padding-bottom: var(--md) !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

    div.hr:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        height: 1px;
        width: 100%;
        transform: translateY(-50%);
        background-color: lightgray;
    }

input:invalid:not(:focus), select:invalid:not(:focus) {
    outline: 1px solid red;
    outline-offset: -1px;
}

.scrollable {
    overflow-y: auto;
    scrollbar-width: thin;
}

span.span-sigma {
    font-size: 40px;
    font-family: Times New Roman;
    margin-left: var(--md)
}

/* Time.aspx */
.body-time, #form_Time {
    height: 100%;
    padding: 0 10px;
}

    #form_Time .txt1 {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

/* Lot_History.aspx */
.lothistory--settings {
    width: 95%;
    margin: 5px auto 0 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 10px 5px;
}

    .lothistory--settings .lothistory--actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
        padding: 0 10px;
    }

    .lothistory--settings .lothistory--mode {
        display: grid;
        grid-template-rows: repeat(auto, 3);
        grid-template-columns: 1fr 2fr;
        grid-template-areas: 'span span' 'input1 label1' 'input2 label2';
        gap: 10px 0;
    }

        .lothistory--settings .lothistory--mode span {
            grid-area: span;
            font-weight: bold;
        }

        .lothistory--settings .lothistory--mode input#WUClotsMenu_rbRinTracciabilita {
            grid-area: input1;
            margin: 0;
        }

        .lothistory--settings .lothistory--mode input#WUClotsMenu_rbTracciabilita {
            grid-area: input2;
            margin: 0;
        }

        .lothistory--settings .lothistory--mode label[for=WUClotsMenu_rbRinTracciabilita] {
            grid-area: label1;
        }

        .lothistory--settings .lothistory--mode label[for=WUClotsMenu_rbTracciabilita] {
            grid-area: label2;
        }

    .lothistory--settings .lothistory--comp {
        display: grid;
        grid-template-rows: auto;
        grid-template-columns: auto 1fr;
        grid-template-areas: 'label input';
    }

        .lothistory--settings .lothistory--comp input {
            grid-area: input;
            width: min-content;
            margin: 0 10px;
        }

        .lothistory--settings .lothistory--comp label {
            grid-area: label;
            font-weight: bold;
            display: flex;
            align-items: center;
        }

.lothistory--comptree {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: auto 1fr;
    grid-template-areas: 'label input';
    padding: 0 var(--sm);
    margin-bottom: var(--lg);
}

    .lothistory--comptree input {
        grid-area: input;
        width: min-content;
        margin: 0 10px;
    }

    .lothistory--comptree label {
        grid-area: label;
        font-weight: bold;
        display: flex;
        align-items: center;
    }

.lothistory--printtree {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: auto 1fr;
    grid-template-areas: 'label input';
    padding: 0 var(--sm);
    margin-bottom: var(--lg);
}

    .lothistory--printtree input {
        grid-area: input;
        width: min-content;
        margin: 0 10px;
    }

    .lothistory--printtree label {
        grid-area: label;
        font-weight: bold;
        display: flex;
        align-items: center;
    }

[id$="treeLots"] td:not(:last-child) {
    width: 1%;
}

[id$="treeLots"] a[target="lotDetail"] > img {
    padding: 0 var(--sm);
}

[id$="treeLots"] td > div {
    height: 100% !important;
}

#UpdatePanelIFrame {
    height: max(80vh, 100%);
    width: 95%;
    margin: auto;
}

.lothistoryContainer {
    display: flex;
    height: calc(100vh - var(--navbar-height));
}

    .lothistoryContainer .leftSection {
        min-width: 15%;
        max-width: 25%;
        box-shadow: inset lightgray -5px 0px 3px -5px;
    }

    .lothistoryContainer .mainSection {
        width: 80%;
    }

.lotsMenuCont {
    max-height: calc(100vh - var(--navbar-height));
    overflow-y: scroll;
    scrollbar-width: none;
}

    .lotsMenuCont #WUClotsMenu_UpdatePanelTreeView {
        padding-bottom: var(--sm);
        overflow-x: auto;
        scrollbar-width: thin;
    }

        .lotsMenuCont #WUClotsMenu_UpdatePanelTreeView > table a {
            margin-right: var(--lg);
        }

            /* Only selects the nodes with a title attribute (which contains the USER_NOTES) and without a tabindex of -1 (as set for the images)*/
            .lotsMenuCont #WUClotsMenu_UpdatePanelTreeView > table a[title]:not([tabindex="-1"]) {
                position: relative;
            }

                .lotsMenuCont #WUClotsMenu_UpdatePanelTreeView > table a[title]:not([tabindex="-1"]):after {
                    content: "*";
                    position: absolute;
                }

        .lotsMenuCont #WUClotsMenu_UpdatePanelTreeView td.selectedNode {
            text-shadow: -0.5px 0 #000, 0.5px 0 #000;
        }

            .lotsMenuCont #WUClotsMenu_UpdatePanelTreeView td.selectedNode a:hover {
                text-shadow: -0.5px 0 #99CCFF, 0.5px 0 #99CCFF;
            }

#IFrameLotDetail {
    width: 100%;
}

/* Login section */
.tdLogin {
    width: 250px;
    min-width: 160px;
}

    .tdLogin #Login_Div_NoLoginTop {
        height: 100%;
        width: 100%;
        display: grid;
        grid-template-rows: 1fr 1fr;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-areas: 'lbl1 input1 login' 'lbl2 input2 login';
        gap: calc(var(--sm) / 2);
    }

        .tdLogin #Login_Div_NoLoginTop label {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

            .tdLogin #Login_Div_NoLoginTop label[for=Login_txtUserName] {
                grid-area: lbl1;
            }

            .tdLogin #Login_Div_NoLoginTop label[for=Login_txtPassword] {
                grid-area: lbl2;
            }

        .tdLogin #Login_Div_NoLoginTop #Login_txtUserName {
            grid-area: input1;
        }

        .tdLogin #Login_Div_NoLoginTop #Login_txtPassword {
            grid-area: input2;
        }

        .tdLogin #Login_Div_NoLoginTop #Login_btnLogin {
            grid-area: login;
        }

    .tdLogin #Login_Div_YesLoginTop {
        height: 100%;
        width: 100%;
        display: grid;
        grid-template-rows: 1fr 1fr;
        grid-template-columns: 1fr auto 1fr;
        grid-template-areas: '. lbl1 .' '. lbl2 .';
        gap: var(--sm);
    }

        .tdLogin #Login_Div_YesLoginTop .loggedUser {
            grid-area: lbl1;
        }

        .tdLogin #Login_Div_YesLoginTop #IdLogoutTop {
            grid-area: lbl2;
            text-align: right;
        }

.tdTime {
    width: 220px;
    border-right: 1px solid #aaaaaa;
}

    .tdTime .iframeTime {
        grid-area: time;
        height: 40px;
        width: 240px;
        margin-top: var(--sm) 0;
        float: right;
    }

/* Custom styles applied by JS */
.customstyle--border {
    border: solid 2px black;
}

.customstyle--bordertop {
    border-top: solid 2px black;
}

.customstyle--borderright {
    border-right: solid 2px black;
}

.customstyle--borderbottom {
    border-bottom: solid 2px black;
}

.customstyle--borderleft {
    border-left: solid 2px black;
}

.customstyle--borderx {
    border-right: solid 2px black;
    border-left: solid 2px black;
}

.customstyle--bordery {
    border-top: solid 2px black;
    border-bottom: solid 2px black;
}

.customstyle--bold {
    font-weight: bold;
}

.customstyle--boldvalue > span {
    font-weight: bold;
}

.customstyle--boldlabel > label {
    font-weight: bold;
}

.customstyle--bggray {
    background-color: #dddfe3;
}

.customstyle--marginauto {
    margin: auto;
}

.customstyle--margintopauto {
    margin-top: auto;
}

.customstyle--marginrightauto {
    margin-right: auto;
}

.customstyle--marginbottomauto {
    margin-bottom: auto;
}

.customstyle--marginleftauto {
    margin-left: auto;
}

.customstyle--marginxauto {
    margin-right: auto;
    margin-left: auto;
}

.customstyle--marginyauto {
    margin-top: auto;
    margin-bottom: auto;
}

.customstyle--textcenter {
    display: flex;
    justify-content: center;
}

.customstyle--textleft {
    display: flex;
    justify-content: flex-start;
}

.customstyle--textright {
    display: flex;
    justify-content: flex-end;
}

.customstyle--texttop {
    display: flex;
    align-items: flex-start;
}

.customstyle--textmiddle {
    display: flex;
    align-items: center;
}

.customstyle--textbottom {
    display: flex;
    align-items: flex-end;
}

.customstyle--bgpositionytop {
    background-position-y: top;
}

.customstyle--bgpositionycenter {
    background-position-y: center;
}

.customstyle--bgpositionybottom {
    background-position-y: bottom;
}

.customstyle--bgpositionxleft {
    background-position-x: left;
}

.customstyle--bgpositionxcenter {
    background-position-x: center;
}

.customstyle--bgpositionxright {
    background-position-x: right;
}

.customstyle--labeltextcenter label {
    text-align: center !important;
}

.customstyle--labeltextleft label {
    text-align: left !important;
}

.customstyle--labeltextright label {
    text-align: right !important;
}

.customstyle--valuetextcenter span {
    text-align: center !important;
}

.customstyle--valuetextleft span {
    text-align: left !important;
}

.customstyle--valuetextright span {
    text-align: right !important;
}

.customstyle--labelvaluewidthadapt label,
.customstyle--labelvaluewidthadapt span {
    width: auto;
    flex: 1 1 auto;
}

.customstyle--labelvalue2575 label {
    width: 25%;
}

.customstyle--labelvalue2575 span {
    width: 75%;
}

.customstyle--labelvalue7525 label {
    width: 75%;
}

.customstyle--labelvalue7525 span {
    width: 25%;
}

.customstyle--paddingtopsm {
    padding-top: var(--sm);
}

.customstyle--paddingtopmd {
    padding-top: var(--md);
}

.customstyle--paddingtoplg {
    padding-top: var(--lg);
}

.customstyle--paddingrightsm {
    padding-right: var(--sm);
}

.customstyle--paddingrightmd {
    padding-right: var(--md);
}

.customstyle--paddingrightlg {
    padding-right: var(--lg);
}

.customstyle--paddingbottomsm {
    padding-bottom: var(--sm);
}

.customstyle--paddingbottommd {
    padding-bottom: var(--md);
}

.customstyle--paddingbottomlg {
    padding-bottom: var(--lg);
}

.customstyle--paddingleftsm {
    padding-left: var(--sm);
}

.customstyle--paddingleftmd {
    padding-left: var(--md);
}

.customstyle--paddingleftlg {
    padding-left: var(--lg);
}

.customstyle--paddingxsm {
    padding-left: var(--sm);
    padding-right: var(--sm);
}

.customstyle--paddingxmd {
    padding-left: var(--md);
    padding-right: var(--md);
}

.customstyle--paddingxlg {
    padding-left: var(--lg);
    padding-right: var(--lg);
}

.customstyle--paddingysm {
    padding-top: var(--sm);
    padding-bottom: var(--sm);
}

.customstyle--paddingymd {
    padding-top: var(--md);
    padding-bottom: var(--md);
}

.customstyle--paddingylg {
    padding-top: var(--lg);
    padding-bottom: var(--lg);
}

.customstyle--labelvaluevertical {
    flex-direction: column;
    justify-content: space-evenly;
}

/* Top Menu */
#topMenuContainer {
    display: block;
    overflow: hidden;
}

    #topMenuContainer > #dhtmlgoodies_menu {
        max-width: 100vw;
        overflow-y: auto;
        height: 40px;
        scrollbar-width: thin;
        scroll-snap-type: y mandatory;
    }

        #topMenuContainer > #dhtmlgoodies_menu > ul {
            max-width: 100vw;
        }

            #topMenuContainer > #dhtmlgoodies_menu > ul > li {
                scroll-snap-align: start;
                box-sizing: border-box;
                padding: 5px 0;
            }

                #topMenuContainer > #dhtmlgoodies_menu > ul > li > a {
                    padding-left: calc(5px + 0.5vw);
                    padding-right: calc(5px + 0.5vw);
                }

/* Unscrollable website */
html {
    overflow: hidden;
}

body, body > form, body > form {
    height: 100%;
}

.mainContainer {
    display: flex;
    max-height: calc(100vh - var(--navbar-height));
}

.mainSection {
    flex-grow: 1;
    overflow: auto;
    scrollbar-width: none;
}

.rightSection {
    width: 200px;
    min-width: 200px;
    overflow: auto;
    scrollbar-width: none;
}

    .rightSection input {
        width: auto;
    }

/* Div periodic */
.divPeriodicByDate {
    display: grid;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr;
    grid-template-areas: 'startDate' 'stopDate';
    gap: var(--sm);
    padding: var(--sm) 0;
}

    .divPeriodicByDate .labelStart, .divPeriodicByDate .labelStop {
        display: none;
    }

    .divPeriodicByDate .inputStart {
        grid-area: startDate;
    }

    .divPeriodicByDate .inputStop {
        grid-area: stopDate;
    }

/* Div select */
.divSelect .graphIndicators {
    display: flex;
    flex-direction: column;
    gap: var(--sm);
    margin-top: var(--md);
}

/* HttpError.aspx */
.exceptionBody {
    padding: var(--lg);
    "Verdana", "Georgia", "Arial", "Helvetica", "Sans-serif";
}

.section__exception--cont {
    -moz-box-shadow: 0px 0px 3px 1px rgba(50, 50, 50, 0.75);
    -webkit-box-shadow: 0px 0px 3px 1px rgba(50, 50, 50, 0.75);
    box-shadow: 0px 0px 3px 1px rgba(50, 50, 50, 0.75);
    border-radius: 0;
    padding: .5rem;
    margin-top: 1rem;
}

.section__exception--title {
    -moz-box-shadow: 0px 0px 3px 1px rgba(50, 50, 50, 0.75);
    -webkit-box-shadow: 0px 0px 3px 1px rgba(50, 50, 50, 0.75);
    box-shadow: 0px 0px 3px 1px rgba(50, 50, 50, 0.75);
    font-size: 16px;
    font-variant: small-caps;
    display: flex;
    line-height: 16px;
    padding: 11px;
    color: white;
    background-color: #ec2a3d;
}

    .section__exception--title label {
        flex: 1;
    }

    .section__exception--title .exception_datetime {
        flex: 1;
        text-align: right;
        font-size: 12px;
    }

.section__exception--subtitle {
    font-size: 14px;
    font-variant: small-caps;
    color: #ec2a3d;
}

.section__exception--data {
    padding: 1rem 0;
    font-size: 11px;
}

    .section__exception--data > div {
        padding: 0 1rem;
    }

        .section__exception--data > div .section__exception--value.stackTrace--value {
            font-size: 11px;
            line-height: 1.3;
            word-break: break-all;
        }

.section__exception--label {
    font-weight: 600;
}

.div__datetime--cont {
    text-align: right;
}

/* WucView pager */
.div_pager a {
    cursor: pointer;
}

/* Printing */
.showRowOnlyOnPrint {
    display: none;
}

/* PDF */
.pdfView {
    width: 100%;
}