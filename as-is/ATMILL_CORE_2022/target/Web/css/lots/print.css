﻿html {
    overflow: visible !important;
}

.rowSection {
    background-color: transparent;
}

#printContainer > div > table tr:not(:first-child) {
    background-color: transparent !important;
}

a, a:link, a:visited {
    text-decoration: none !important;
    font-style: normal !important;
}

#printContainer {
    font-size: 8px;
    margin: auto;
}

    #printContainer .lotSection:not(:last-child) {
        border-bottom: none;
    }

    #printContainer .lotSection > span:first-child:not(:only-child) {
        font-size: 12px;
    }

    #printContainer table td {
        padding-top: 5px;
        padding-bottom: 5px;
    }

.userNote_td input {
    display: none !important;
}

.userNote_td #userNote_textarea {
    border: 0;
    outline: none;
    font-size: 8px;
}

table {
    margin-left: 0 !important;
    width: 100%;
}