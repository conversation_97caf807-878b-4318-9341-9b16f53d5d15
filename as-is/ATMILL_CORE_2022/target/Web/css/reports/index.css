html {
    overflow: hidden;
}

body {
    height: 100%;
    font-family: "Verdana", "Georgia", "Arial", "Helvetica", "Sans-serif";
    font-size: 11px;
    margin: 2px;
}

table {
    border-spacing: inherit;
    text-align: center;
    width: 98%;
}

div#print_area.print_area {
    width: 100%;
    padding-top: 0px;
    background-color: #ffffff;
}

/* NEW */
:root {
    --sm: 5px;
    --md: 10px;
    --lg: 15px;
    --switch-height: 15px;
    --switch-width: 35px;
}

#customerHeader {
    display: none;
    width: 98%;
    height: 100px;
    border: double 3px #000000;
    margin: 10px auto 0px auto;
}

.customerheader--elem--img {
    background-repeat: no-repeat;
    background-size: contain;
    background-position-y: center;
    background-position-x: center;
}

#reportHeader {
    height: 30px;
    width: 98%;
    margin-left: 1%;
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 20px;
    border-bottom: solid 2px #000000;
    display: flex;
    justify-content: center;
    align-items: center;
}

#reportsContainer {
    display: grid;
    margin: var(--lg);
    margin-bottom: 0;
}

.reports--grid {
    display: grid;
    width: 98%;
    margin: 0;
    margin-bottom: auto;
}

    .reports--grid > * {
        margin: 2.5px;
    }

.reports--table {
    width: 98%;
    margin: 0;
    table-layout: fixed;
    margin-bottom: auto;
}

    .reports--table > thead > tr > th {
        font-weight: bold;
        border-bottom: solid 2px black;
    }

    .reports--table > tbody td:not(:empty):after {
        content: attr(data-unit);
        margin-left: 5px;
    }

    .reports--table > tbody td {
        text-align: right;
        padding-right: 5px;
    }

span.alarm-low,
span.alarm-high,
span.alarm-low:before,
span.alarm-high:before {
    color: red;
    font-weight: bold;
}

    span.alarm-low:before {
        content: "\25bc";
    }

    span.alarm-high:before {
        content: "\25b2";
    }

.reports--table > tbody td.alarm-low,
.reports--table > tbody td.alarm-high {
    color: red;
    font-weight: bold;
}

    .reports--table > tbody td.alarm-low:before,
    .reports--table > tbody td.alarm-high:before {
        color: red;
        font-weight: bold;
    }

    .reports--table > tbody td.alarm-low:before {
        content: "\25bc";
    }

    .reports--table > tbody td.alarm-high:before {
        content: "\25b2";
    }

.reports--table .reports--table--row--spacing td {
    padding: 1mm;
    line-height: 0;
}

.reports--table .reports--table--row--hr td {
    padding: 0.5px;
    line-height: 0;
    background-color: black;
}

.reports--table .reports--table--row td {
    height: 30px;
}

.reports--table .reports--table--row:nth-child(2n+1) td {
    background-color: #E1F0F9;
}

.reports--table--row[data-total-type] ~ .reports--table--row:nth-child(2n) td {
    background-color: #E1F0F9;
}

.reports--table--row[data-total-type] ~ .reports--table--row:nth-child(2n + 1) td {
    background-color: transparent;
}

.reports--table > tbody td:first-child {
    text-align: left;
    padding-left: 5px;
}

.reports--table .reports--table--row[data-total-type="sub_total"] td {
    border-top: solid 1px black;
    border-bottom: solid 1px black;
    background-color: #dddfe3 !important;
    font-weight: bold;
}

    .reports--table .reports--table--row[data-total-type="sub_total"] td:first-child {
        border-left: solid 1px black;
    }

    .reports--table .reports--table--row[data-total-type="sub_total"] td:last-child {
        border-right: solid 1px black;
    }

.reports--table .reports--table--row[data-total-type="total"] td {
    border-top: solid 1px black;
    border-bottom: solid 1px black;
    background-color: #dddfe3 !important;
    font-weight: bold;
}

    .reports--table .reports--table--row[data-total-type="total"] td:first-child {
        border-left: solid 1px black;
    }

    .reports--table .reports--table--row[data-total-type="total"] td:last-child {
        border-right: solid 1px black;
    }

.reports--table .reports--table--row[data-total-type]:not([hiddenTotal]) + .reports--table--row[data-total-type]:not([hiddenTotal]) td {
    border-top: none;
}

.reports--table .reports--table--row--ruler {
    line-height: 0;
    font-size: 0;
    padding: 0;
    height: 0;
}

    .reports--table .reports--table--row--ruler td {
        line-height: 0;
        font-size: 0;
        padding: 0;
        height: 0;
    }

.reports--operation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px;
}

    .reports--operation label,
    .reports--operation span {
        text-align: left;
        width: 50%;
    }

    .reports--operation > *:only-child {
        text-align: center;
        margin: auto;
    }

.reports--operation--label_value {
    display: flex;
    align-items: center;
}

.reports--operation--loss_on_b1, .reports--operation--loss_on_b0 {
    padding: 10px;
    margin-top: var(--md)
}

.reports--operation--work_time_perc_over_period span {
    width: 100%;
}

.reports--operation--capacity_perc_over_plant span {
    width: 100%;
}

a:link, a:visited {
    color: #000000;
    text-decoration: underline;
    font-style: italic;
}

a:hover {
    color: #99CCFF;
    text-decoration: underline;
}

@media screen {
    #reportsContainer {
        margin-bottom: 2rem;
    }
}

.customstyle--border {
    border: solid 1px black;
}

.customstyle--bordertop {
    border-top: solid 1px black;
}

.customstyle--borderright {
    border-right: solid 1px black;
}

.customstyle--borderbottom {
    border-bottom: solid 1px black;
}

.customstyle--borderleft {
    border-left: solid 1px black;
}

.customstyle--borderx {
    border-right: solid 1px black;
    border-left: solid 1px black;
}

.customstyle--bordery {
    border-top: solid 1px black;
    border-bottom: solid 1px black;
}

.customstyle--bold {
    font-weight: bold;
}

.customstyle--boldvalue > span {
    font-weight: bold;
}

.customstyle--boldlabel > label {
    font-weight: bold;
}

.customstyle--bggray {
    background-color: #dddfe3;
}

.customstyle--marginauto {
    margin: auto;
}

.customstyle--margintopauto {
    margin-top: auto;
}

.customstyle--marginrightauto {
    margin-right: auto;
}

.customstyle--marginbottomauto {
    margin-bottom: auto;
}

.customstyle--marginleftauto {
    margin-left: auto;
}

.customstyle--marginxauto {
    margin-right: auto;
    margin-left: auto;
}

.customstyle--marginyauto {
    margin-top: auto;
    margin-bottom: auto;
}

.customstyle--textcenter {
    display: flex;
    justify-content: center;
}

.customstyle--textleft {
    display: flex;
    justify-content: flex-start;
}

.customstyle--textright {
    display: flex;
    justify-content: flex-end;
}

.customstyle--texttop {
    display: flex;
    align-items: flex-start;
}

.customstyle--textmiddle {
    display: flex;
    align-items: center;
}

.customstyle--textbottom {
    display: flex;
    align-items: flex-end;
}

.customstyle--bgpositionytop {
    background-position-y: top;
}

.customstyle--bgpositionycenter {
    background-position-y: center;
}

.customstyle--bgpositionybottom {
    background-position-y: bottom;
}

.customstyle--bgpositionxleft {
    background-position-x: left;
}

.customstyle--bgpositionxcenter {
    background-position-x: center;
}

.customstyle--bgpositionxright {
    background-position-x: right;
}

.customstyle--labeltextcenter label {
    text-align: center !important;
}

.customstyle--labeltextleft label {
    text-align: left !important;
}

.customstyle--labeltextright label {
    text-align: right !important;
}

.customstyle--valuetextcenter span {
    text-align: center !important;
}

.customstyle--valuetextleft span {
    text-align: left !important;
}

.customstyle--valuetextright span {
    text-align: right !important;
}

.customstyle--labelvaluewidthadapt {
    gap: var(--md)
}

    .customstyle--labelvaluewidthadapt label,
    .customstyle--labelvaluewidthadapt span {
        width: auto;
    }

    .customstyle--labelvaluewidthadapt span {
        flex-grow: 1;
    }

.customstyle--labelvalue2575 label {
    width: 25%;
}

.customstyle--labelvalue2575 span {
    width: 75%;
}

.customstyle--labelvalue7525 label {
    width: 75%;
}

.customstyle--labelvalue7525 span {
    width: 25%;
}

.customstyle--paddingtopsm {
    padding-top: var(--sm);
}

.customstyle--paddingtopmd {
    padding-top: var(--md);
}

.customstyle--paddingtoplg {
    padding-top: var(--lg);
}

.customstyle--paddingrightsm {
    padding-right: var(--sm);
}

.customstyle--paddingrightmd {
    padding-right: var(--md);
}

.customstyle--paddingrightlg {
    padding-right: var(--lg);
}

.customstyle--paddingbottomsm {
    padding-bottom: var(--sm);
}

.customstyle--paddingbottommd {
    padding-bottom: var(--md);
}

.customstyle--paddingbottomlg {
    padding-bottom: var(--lg);
}

.customstyle--paddingleftsm {
    padding-left: var(--sm);
}

.customstyle--paddingleftmd {
    padding-left: var(--md);
}

.customstyle--paddingleftlg {
    padding-left: var(--lg);
}

.customstyle--paddingxsm {
    padding-left: var(--sm);
    padding-right: var(--sm);
}

.customstyle--paddingxmd {
    padding-left: var(--md);
    padding-right: var(--md);
}

.customstyle--paddingxlg {
    padding-left: var(--lg);
    padding-right: var(--lg);
}

.customstyle--paddingysm {
    padding-top: var(--sm);
    padding-bottom: var(--sm);
}

.customstyle--paddingymd {
    padding-top: var(--md);
    padding-bottom: var(--md);
}

.customstyle--paddingylg {
    padding-top: var(--lg);
    padding-bottom: var(--lg);
}

.customstyle--labelvaluevertical {
    flex-direction: column;
    justify-content: space-evenly;
}

@media screen {
    .reports--print {
        display: none;
    }
}

.reports--operation--mixing_toggle_detail {
    display: flex;
    align-items: center;
    justify-content: center;
}

.reports--switch {
    position: relative;
    display: inline-block;
    height: var(--switch-height);
    width: var(--switch-width) !important;
}

    .reports--switch input {
        visibility: hidden;
    }

    .reports--switch span {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        height: 100%;
        width: 100%;
        border-radius: calc(var(--switch-width) / 2);
        background-color: lightgray;
        transition: 300ms;
        cursor: pointer;
    }

        .reports--switch span:before {
            position: absolute;
            content: "";
            height: calc(var(--switch-height) - var(--sm));
            width: calc(var(--switch-height) - var(--sm));
            top: calc(var(--sm) / 2);
            left: calc(var(--sm) / 2);
            background-color: white;
            transition: 300ms;
            border-radius: 100%;
        }

    .reports--switch input:checked + span {
        background-color: #65aac0;
    }

        .reports--switch input:checked + span:before {
            left: calc(var(--sm) / 2 + var(--switch-width) - var(--switch-height));
        }

.tableship {
    text-align: left !important;
    border-spacing: 1px;
}

    .tableship td {
        padding: 3px;
    }

        .tableship td.odd {
            font-weight: bold;
        }

        .tableship td.sodd {
            background-color: #F0F0F0;
            font-weight: bold;
        }

.contship {
    width: 500px;
    border: 1px solid;
    padding: 10px;
    box-shadow: 2px 2px;
        }