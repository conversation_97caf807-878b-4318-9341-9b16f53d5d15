* {
    transition: none !important
}

#customerHeader {
    display: grid;
    width: 98%;
    height: 70pt;
    border: double 3pt #000000;
    margin: 5pt auto 0pt auto;
}

    #customerHeader .customerheader--elem--img {
        margin: auto 0;
    }

#reportHeader {
    font-size: 16pt;
    border-bottom: solid 1.5px #000000;
}

.reports--table > tbody td.alarm-low,
.reports--table > tbody td.alarm-high,
.reports--table > tbody td.alarm-low:before,
.reports--table > tbody td.alarm-high:before {
    color: black;
}

span.alarm-low,
span.alarm-high,
span.alarm-low:before,
span.alarm-high:before {
    color: black;
}

.reports--table--row:not([data-total-type]) td {
    background-color: transparent !important;
}

.reports--table > thead > tr > th {
    font-weight: bold;
    border-bottom: solid 1px black;
}

.reports--table .reports--table--row[data-total-type="sub_total"] td {
    border-top: solid 1px black;
    border-bottom: solid 1px black;
}

    .reports--table .reports--table--row[data-total-type="sub_total"] td:first-child {
        border-left: solid 1px black;
    }

    .reports--table .reports--table--row[data-total-type="sub_total"] td:last-child {
        border-right: solid 1px black;
    }

.reports--table .reports--table--row[data-total-type="total"] td {
    border-top: solid 1px black;
    border-bottom: solid 1px black;
}

    .reports--table .reports--table--row[data-total-type="total"] td:first-child {
        border-left: solid 1px black;
    }

    .reports--table .reports--table--row[data-total-type="total"] td:last-child {
        border-right: solid 1px black;
    }

.reports--table .reports--table--row:not([data-total-type]) + .reports--table--row:not([data-total-type]) td {
    border-top: 1px solid rgba(128, 128, 128, 0.25);
}

a:link, a:visited {
    text-decoration: none;
    font-style: normal;
}

.customstyle--border {
    border: solid 1px black;
}

.customstyle--bordertop {
    border-top: solid 1px black;
}

.customstyle--borderright {
    border-right: solid 1px black;
}

.customstyle--borderbottom {
    border-bottom: solid 1px black;
}

.customstyle--borderleft {
    border-left: solid 1px black;
}

.customstyle--borderx {
    border-right: solid 1px black;
    border-left: solid 1px black;
}

.customstyle--bordery {
    border-top: solid 1px black;
    border-bottom: solid 1px black;
}

.customstyle--border,
.customstyle--bordertop,
.customstyle--borderright,
.customstyle--borderbottom,
.customstyle--borderleft,
.customstyle--borderx,
.customstyle--bordery,
.reports--table .reports--table--row[data-total-type] td {
    border-width: 1px !important;
}

.reports--noprint {
    display: none;
}