﻿body {
    font-size: 10px;
}

.alarm-low,
.alarm-low:after,
.alarm-high,
.alarm-high:after {
    color: black !important;
}

.reports--mixing--row--ingredients {
    font-size: 10px;
}

.reports--mixing--row--header .reports--mixing--row--header--title {
    font-size: 11px;
}

.reports--mixing--row {
    break-inside: avoid;
}

.reports--mixing--row--ingredientrow .reports--mixing--row--datarow,
.reports--mixing--row--ingredientrow .reports--mixing--row--ingredientrow--name {
    background-color: transparent !important;
}

.reports--mixing--row--ingredientrow:not(:last-child) .reports--mixing--row--datarow,
.reports--mixing--row--ingredientrow:not(:last-child) .reports--mixing--row--ingredientrow--name {
    border-bottom: 1px solid rgba(128, 128, 128, 0.25);
}

.reports--mixingtotal--row .reports--mixingtotal--row--ingredientrow--total,
.reports--mixing--row--ingredientrow.reports--mixing--row--ingredientrow--total {
    border: solid 1px black;
    background-color: #dddfe3;
}

.reports--mixing--row--ingredientrow[ingredienttype] {
    background-color: transparent !important;
}

.reports--mixing--row:not(:first-child) {
    padding-top: 2rem;
    border-top: none;
}

.reports--mixing--row--ingredientrow[ingredienttype]:before {
    font-weight: bold;
}

.reports--mixing--row--datarow > * {
    width: 22.5%;
}

.reports--mixing--row--datarow .reports--mixing--row--datarow--origin {
    width: 32.5%;
}

.reports--mixing--row:not(:first-child):not(:nth-child(2)) {
    border-top: none;
}