.ui-widget-content {
    /*INIZIO PARTE PERSONALIZZATA*/
    background: #c9c9c9; /* Old browsers */
    background: -moz-linear-gradient(left, #c9c9c9 0%, #e7e7e7 49%, #ffffff 73%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%,#c9c9c9), color-stop(49%,#e7e7e7), color-stop(73%,#ffffff), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(left, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(left, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(left, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* IE10+ */
    background: linear-gradient(to right, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* W3C */
    /*FINE PARTE PERSONALIZZATA*/
}

    .ui-widget-content table {
        border-collapse: unset;
    }

/*** DEFAULT COLORS/STYLES ***/
/* Root menu */
.dropdown-menu {
    background: #c9c9c9; /* Old browsers */
    background: -moz-linear-gradient(left, #c9c9c9 0%, #e7e7e7 49%, #ffffff 73%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%,#c9c9c9), color-stop(49%,#e7e7e7), color-stop(73%,#ffffff), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(left, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(left, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(left, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* IE10+ */
    background: linear-gradient(to right, #c9c9c9 0%,#e7e7e7 49%,#ffffff 73%,#ffffff 100%); /* W3C */
    width: 99.9%;
}
    /* Root links */
    .dropdown-menu a,
    .dropdown-menu a:link,
    .dropdown-menu a:visited {
        text-decoration: none;
        color: #393939;
        padding: 3px 9px;
    }
        /* Root link hover */
        .dropdown-menu a:hover,
        .dropdown-menu li.dropdown-menu-hover > a {
            color: #000;
        }
    /* Root menu item hover */
    .dropdown-menu li:hover,
    .dropdown-menu li.dropdown-menu-hover {
        background: #e0e2e2;
    }
    /* Submenus */
    .dropdown-menu ul {
        border: 1px solid #c4c7c8;
        background: #e0e2e2;
    }
        /* Submenu item hover */
        .dropdown-menu ul li:hover,
        .dropdown-menu ul li.dropdown-menu-hover {
            background-color: #c4c7c8;
        }

    .dropdown-menu li ul li {
        float: none;
        padding: 0;
    }

        .dropdown-menu li ul li a {
            padding: 10px 20px !important;
        }

/* Optional submenu drop shadow */
.dropdown-menu-shadow {
    -webkit-box-shadow: 3px 3px 2px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 3px 3px 2px rgba(0, 0, 0, 0.3);
    -ms-box-shadow: 3px 3px 2px rgba(0, 0, 0, 0.3);
    -o-box-shadow: 3px 3px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 3px 3px 2px rgba(0, 0, 0, 0.3);
}

/*** ESSENTIAL STYLES ***/
.dropdown-menu {
    white-space: nowrap;
    display: block;
    /* IE7 inline-block fix */
    *display: inline;
    *zoom: 1;
}

    .dropdown-menu,
    .dropdown-menu ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }

        .dropdown-menu ul {
            display: none;
            position: absolute;
            z-index: 1000000;
            top: var(--navbar-height);
        }

            .dropdown-menu ul ul {
                top: 0;
                left: 100%;
            }

        .dropdown-menu > li {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            cursor: pointer;
        }

        .dropdown-menu li {
            margin: 0;
            padding: 7.5px 10px;
            display: inline-block;
            font-size: 13px;
            float: left;
            /* IE7 inline-block and padding fix */
            *display: inline;
            *zoom: 1;
            *vertical-align: bottom;
        }

            .dropdown-menu li a {
                display: block;
            }

                .dropdown-menu li a img {
                    height: 34px;
                }

        .dropdown-menu ul li {
            position: relative;
            display: block;
        }

        .dropdown-menu li.dropdown-menu-sub-indicator > a {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        /* Submenu arrow indicators */
        .dropdown-menu ul li.dropdown-menu-sub-indicator {
            background-image: url('../image/chevron-right.svg');
            background-repeat: no-repeat;
            background-position: calc(100% - 10px) center;
            background-size: 10px;
            transition-property: background-position;
            transition-duration: 0.3s;
        }

            /* Add space for the arrow */
            .dropdown-menu ul li.dropdown-menu-sub-indicator > a {
                padding-right: 25px !important;
            }

            /* Hover arrow animation (+-10px)*/
            .dropdown-menu ul li.dropdown-menu-sub-indicator:hover {
                background-position-x: 100%;
            }