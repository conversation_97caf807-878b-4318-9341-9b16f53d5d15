﻿Imports System.Net
Imports System.ServiceModel
Imports System.ServiceModel.Web
Imports UsersGUI

Partial Class Export
    Inherits myWebPage

    Protected m_config As config
    Public mPageName As String = String.Empty
    Public mMenuName As String = String.Empty
    Private mScreen As Screen
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Private msgExecuteWithError As New myAlert

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        m_config = CType(Application("Config"), config)
        If m_config Is Nothing Then
            Exit Sub
        End If

        If Not WebTools.tools.GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
            Exit Sub
        End If

        ' Controlla se è necessario fare un update delle risorse del JS
        myScript.UpdateJSResources(m_config)

        WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
        If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
            lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
            Me.dView.Visible = False
            Me.dError.Visible = True
            Exit Sub
        End If

        msgExecuteWithError.Init(Me.Page, myMessageBoxParam.NoParameter)

        txtFromData.Attributes.Add("type", "text")
        txtFromData.Attributes.Add("datetimepicker", "step:10;")

        txtToData.Attributes.Add("type", "text")
        txtToData.Attributes.Add("datetimepicker", "step:10;")

        Me.lblFromData.Text = m_config.GetEntryByKeyName("FROM_DATE").GetValue()
        Me.lblToData.Text = m_config.GetEntryByKeyName("TO_DATE").GetValue()

        Me.rbIntake.Text = m_config.GetEntryByKeyName("INTAKE_DATA").GetValue()
        Me.rbBagging.Text = m_config.GetEntryByKeyName("BAGGING_DATA").GetValue()
        Me.rbLoadout.Text = m_config.GetEntryByKeyName("LOADOUT_DATA").GetValue()
        Me.rbShip.Text = m_config.GetEntryByKeyName("SHIPMENTS_DATA").GetValue()
        Me.rbFlows.Text = m_config.GetEntryByKeyName("FLOWLOGS_DATA").GetValue()
        Me.rbProd.Text = m_config.GetEntryByKeyName("PRODUCTION_DATA").GetValue()

        HiddenWcfPath.Value = System.Configuration.ConfigurationManager.AppSettings("Relative.OWSAddress").ToString()
        HiddenExportResultOk.Value = m_config.GetEntryByKeyName("ExportResultOk").GetValue()
        HiddenExportResultFailedFromDate.Value = m_config.GetEntryByKeyName("ExportResultFailedFromDate").GetValue()
        HiddenExportResultFailedToDate.Value = m_config.GetEntryByKeyName("ExportResultFailedToDate").GetValue()

        DeleteOldFile()

        myScript.InvokeJS(Me.Page, "ShowUCCont();")
    End Sub

    Private Function CanFindData(ByRef s_warning As String) As Boolean

        If (txtFromData.Text = String.Empty) Then
            s_warning = String.Format(m_config.GetEntryByKeyName("EXPORT_DATA_FIELD_NULL").GetValue(), m_config.GetEntryByKeyName("FROM_DATA").GetValue())
            Return False
        End If

        If (txtToData.Text = String.Empty) Then
            s_warning = String.Format(m_config.GetEntryByKeyName("EXPORT_DATA_FIELD_NULL").GetValue(), m_config.GetEntryByKeyName("TO_DATA").GetValue())
            Return False
        End If

        If (Not IsDate(txtFromData.Text)) Then
            s_warning = String.Format(m_config.GetEntryByKeyName("EXPORT_DATA_FIELD_ERROR").GetValue(), m_config.GetEntryByKeyName("FROM_DATA").GetValue())
            Return False
        End If

        If (Not IsDate(txtToData.Text)) Then
            s_warning = String.Format(m_config.GetEntryByKeyName("EXPORT_DATA_FIELD_ERROR").GetValue(), m_config.GetEntryByKeyName("TO_DATA").GetValue())
            Return False
        End If

        Return True
    End Function

    Private Sub DeleteOldFile()
        Dim s_dir As New System.IO.DirectoryInfo(Server.MapPath("./documents/export/"))
        If (s_dir.Exists) Then
            For Each s_file As System.IO.FileInfo In s_dir.GetFiles()
                If (s_file.CreationTime < DateTime.Now.AddDays(-1 * CommonDefines.Defines.DELETE_FILE_EXPORT_DATA_AFTER_DAY)) Then
                    s_file.Delete()
                End If
            Next
        End If
    End Sub

    Private Sub DownloadFile(file_name As String)
        Response.Clear()
        Response.ContentType = "application/octet-stream"
        Response.AddHeader("Content-Disposition", "attachment; filename=""" & file_name & """")
        Response.WriteFile(Server.MapPath("~/documents/export/" & file_name & ""))
        Response.End()

    End Sub

    Protected Sub hLink_Click(sender As Object, e As EventArgs)
        DownloadFile(HiddenExportPath.Value)
    End Sub

End Class