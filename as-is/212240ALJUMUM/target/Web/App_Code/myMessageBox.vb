﻿Option Strict On

' query parameter sui quali le classi myAlert e myConfirm lavorano.
' Se non viene usato nessun query parameter, inizializzare con NoParameter
Public Enum myMessageBoxParam
    NoParameter = 0 ' da usare solo se non serve la myConfirm
    Delete = 1
    AbortCycle = 2
    ShowControls = 3
    ShowRecipeExecutable = 4
    CloneCycle = 5
    CloneRecipe = 6
    ForceCompleteOrder = 7
    DeleteAll = 8
End Enum

Public Class myAlert
    Protected m_page As Page
    Protected m_query_parameter As String

    Public Sub New()
        m_page = Nothing
        m_query_parameter = String.Empty
    End Sub

    ' aggiungo il query parameter per rimuoverlo sull'eventuale messaggio di errore (e.g. foreign key che impedisce la cancellazione) e refresh della pagina
    ' altrimenti mi continua a rimanere 'deleted=yes' nell'url e non ne esco. Andrebbe generalizzato per il caso messaggio di errore dopo
    ' risposta positiva a myConfirm (essendo che lavorano sullo stesso parametro)
    Public Sub Init(ByVal page As Page, ByVal query_parameter_enum As myMessageBoxParam)
        Me.m_page = page
        SetQueryParameterFromEnum(query_parameter_enum)
    End Sub

    Public Overridable Sub Show(ByVal str_message As String)
        Dim script As String = String.Empty

        str_message = str_message.Replace("'", "\'")
        If Not Me.m_page Is Nothing Then
            script = "myMessageBox.alert('" & str_message & "', function() {ButtonEvents.MessageBoxAlert.Submit();});"

            myScript.InvokeJS(Me.m_page, script)
        End If
    End Sub

    Public Overridable Sub Show(ByVal str_message As String, ByVal str_callback As String)
        Dim script As String = String.Empty

        str_message = str_message.Replace("'", "\'")
        If Not Me.m_page Is Nothing Then
            script = "myMessageBox.alert('" & str_message & "', " & str_callback & ");"

            myScript.InvokeJS(Me.m_page, script)
        End If
    End Sub

    Protected Sub SetQueryParameterFromEnum(ByVal param_enum As myMessageBoxParam)
        Select Case param_enum
            Case myMessageBoxParam.NoParameter
                m_query_parameter = String.Empty
            Case myMessageBoxParam.Delete
                m_query_parameter = "deleted"
            Case myMessageBoxParam.AbortCycle
                m_query_parameter = "AbortCycle"
            Case myMessageBoxParam.ShowControls
                m_query_parameter = "ShowControls"
            Case myMessageBoxParam.CloneCycle
                m_query_parameter = "CloneCycle"
            Case myMessageBoxParam.CloneRecipe
                m_query_parameter = "CloneRecipe"
            Case myMessageBoxParam.ForceCompleteOrder
                m_query_parameter = "ForceCompleteOrder"
            Case myMessageBoxParam.DeleteAll
                m_query_parameter = "DeleteAll"
            Case Else
                m_query_parameter = String.Empty
        End Select
    End Sub

End Class

Public Class myConfirm
    Inherits myAlert

    Protected m_answer_field As HiddenField

    Public Sub New()
        MyBase.New()
        Me.m_answer_field = Nothing
    End Sub

    ' il terzo parametro serve per indicare allo script il query parameter sul quale agire
    ' se si usa il query parameter, nell'url serve la struttura m_query_parameter=yes per funzionare correttamente
    Public Overloads Sub Init(ByVal page As Page, ByVal reply As HiddenField, ByVal query_parameter_enum As myMessageBoxParam)
        MyBase.m_page = page
        SetQueryParameterFromEnum(query_parameter_enum)
        Me.m_answer_field = reply
    End Sub

    Public Overrides Sub Show(ByVal str_message As String)
        Dim script As String = String.Empty

        str_message = str_message.Replace("'", "\'")
        If Not Me.m_page Is Nothing Then
            If Not Me.m_answer_field Is Nothing Then
                script = "myMessageBox.confirm('" & str_message & "', " &
                            "function(){ document.getElementById('" + Me.m_answer_field.ClientID + "').value = true; ButtonEvents.MessageBoxConfirm.Submit(); }, " &
                            "function(){ document.getElementById('" + Me.m_answer_field.ClientID + "').value = false; ButtonEvents.MessageBoxConfirm.Cancel(); });"
                myScript.InvokeJS(Me.m_page, script)
            Else
                ' caso hidden field inizializzato senza valore di ritorno
                myScript.InvokeJS(Me.m_page, "myMessageBox.alert('hidden_field not initialized', function() {ButtonEvents.MessageBoxAlert.Submit();});")
            End If
        End If
    End Sub

    Public Function GetAnswer() As Boolean
        Dim ret_val = False

        If Me.m_answer_field.Value.ToLower = "true" Then
            Me.m_answer_field.Value = "false"
            ret_val = True
        End If

        Return ret_val
    End Function

End Class

Public Class myPrompt
    Inherits myConfirm

    Private m_input_text_field As HiddenField

    Public Sub New()
        MyBase.New()
        Me.m_input_text_field = Nothing
    End Sub

    ' il quarto parametro serve per indicare allo script il query parameter sul quale agire
    ' se si usa il query parameter, nell'url serve la struttura m_query_parameter=yes per funzionare correttamente
    Public Overloads Sub Init(ByVal page As Page, ByVal reply As HiddenField, ByVal input_text As HiddenField, ByVal query_parameter_enum As myMessageBoxParam)
        MyBase.m_page = page
        SetQueryParameterFromEnum(query_parameter_enum)
        MyBase.m_answer_field = reply
        Me.m_input_text_field = input_text
    End Sub

    Public Overrides Sub Show(ByVal str_message As String)
        Show(str_message, String.Empty)
    End Sub

    Public Overloads Sub Show(ByVal str_message As String, ByVal str_default_value As String)
        Dim script As String = String.Empty

        str_message = str_message.Replace("'", "\'")
        If Not Me.m_page Is Nothing Then
            If Not Me.m_answer_field Is Nothing AndAlso Not Me.m_input_text_field Is Nothing Then
                script = "myMessageBox.prompt('" & str_message & "', null, '" & str_default_value & "', " &
                            "function() { " &
                                "document.getElementById('" + Me.m_answer_field.ClientID + "').value = true; " &
                                "document.getElementById('" + Me.m_input_text_field.ClientID + "').value = temp_return_value; " &
                                "ButtonEvents.MessageBoxPrompt.Submit(); " &
                            "}, " &
                            "function() { " &
                                "document.getElementById('" + Me.m_answer_field.ClientID + "').value = false;" &
                                "document.getElementById('" + Me.m_input_text_field.ClientID + "').value = ''; " &
                                "ButtonEvents.MessageBoxPrompt.Cancel();" &
                            "});"
                myScript.InvokeJS(Me.m_page, script)
            Else
                ' caso hidden field inizializzato senza valore di ritorno
                myScript.InvokeJS(Me.m_page, "myMessageBox.alert('hidden_field not initialized',function() {ButtonEvents.MessageBoxAlert.Submit();});")
            End If
        End If
    End Sub

    Public Function GetInputText() As String
        Return m_input_text_field.Value
    End Function

End Class