<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Graphics.aspx.vb" Inherits="Graphics" %>

<%@ Register Assembly="System.Web.DataVisualization, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Namespace="System.Web.UI.DataVisualization.Charting" TagPrefix="asp" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_View.ascx" TagPrefix="WUCView" TagName="View" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<%@ Register Src="~/UserControl/WUC_PlantConf.ascx" TagPrefix="WUCPlantConf" TagName="ePlantConf" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta http-equiv="X-UA-Compatibile" content="IE=8" />
</head>
<body onload="InitPage()">
    <form id="form_Graphics" method="post" runat="server">
        <asp:Label ID="lbl_exception" CssClass="lbl_exception" runat="server"></asp:Label>
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true">
        </asp:ScriptManager>
        <asp:HiddenField ID="ChartHeight" runat="server" />
        <asp:HiddenField ID="ChartWidth" runat="server" />
        <asp:Timer ID="Timer1" runat="server" Interval="30000">
        </asp:Timer>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
                <div id="dView" runat="server" visible="true">
                    <table border="0" class="tabMrg1">
                        <tr>
                            <td class="tdExpanse txtCenter">
                                <asp:Label ID="lblInfo" runat="server" CssClass="label-text-login"></asp:Label>
                            </td>
                        </tr>
                    </table>
                    <div id="print_area">
                        <div id="customerHeader"></div>
                        <div id="dvTitle" runat="server" visible="true" style="text-align: center;">
                            <asp:Label ID="lblTile" runat="server" Font-Size="X-Large"></asp:Label>
                            <hr />
                        </div>
                        <table id="tbl_instant_val" runat="server"></table>
                        <asp:Chart ID="Chart1" runat="server">
                            <Series>
                                <asp:Series ChartType="Area" Palette="EarthTones" ChartArea="ChartArea1">
                                </asp:Series>
                            </Series>
                            <ChartAreas>
                                <asp:ChartArea Name="ChartArea1">
                                </asp:ChartArea>
                            </ChartAreas>
                        </asp:Chart>
                    </div>
                </div>
                <div id="dError" runat="server" visible="false" class="margin1">
                    <table id="Table1" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="rightSection">
                <!-- Operazioni.... inizio -->
                <WUCOperation:eOperation ID="WebOperation" runat="server" />
                <!-- Operazioni.... fine-->

                <!-- Extra.... inizio -->
                <div id="divExtra" runat="server">
                    <div class="menuTOP">
                        <div class="txtCenter txtWhite txtBold">
                            <%= m_config.GetEntryByKeyName("Selections").GetValue%>
                        </div>
                    </div>
                    <div id="divSelect" class="menuMID" runat="server">
                        <div id="divPeriodic" runat="server">
                            <asp:RadioButtonList ID="rbl_choose_type" runat="server" AutoPostBack="true"></asp:RadioButtonList>
                            <div id="tblByDate" class="divPeriodicByDate bgWhite" runat="server">
                                <asp:Label ID="lblYieldsStartDate" CssClass="labelStart" runat="server"></asp:Label>

                                <asp:TextBox ID="start_date" runat="server" CssClass="text DateTimePicker_jq inputStart"
                                    datetimepicker="step:60;" type="text"></asp:TextBox>

                                <asp:Label ID="lblYieldsStopDate" CssClass="labelStop" runat="server"></asp:Label>

                                <asp:TextBox ID="stop_date" runat="server" CssClass="text DateTimePicker_jq inputStop"
                                    datetimepicker="step:60;" type="text"></asp:TextBox>
                            </div>
                            <table id="tblByLastHours" runat="server" class="bgWhite">
                                <tr>
                                    <td>
                                        <%=m_config.GetEntryByKeyName("GRAPH_PERIOD").GetValue()%>
                                    </td>
                                    <td>
                                        <asp:TextBox ID="txt_hours" runat="server" Font-Size="Smaller" Width="30px"></asp:TextBox>
                                    </td>
                                    <td>
                                        <asp:RadioButtonList ID="rbl_hours_days" runat="server" AutoPostBack="true"></asp:RadioButtonList>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="graphIndicators">
                            <span class="graphIndicators--indicators"><%=m_config.GetEntryByKeyName("GRAPH_INDICATOR").GetValue()%></span>
                            <asp:CheckBoxList ID="cbl_graph_indicator" CssClass="graphIndicators--indicatorsTable" AutoPostBack="false" runat="server" CellPadding="0"
                                CellSpacing="0" Font-Size="Smaller">
                            </asp:CheckBoxList>
                            <asp:DropDownList ID="ddl_graph_indicator" runat="server" Font-Size="Smaller" AutoPostBack="true"
                                OnSelectedIndexChanged="ddl_graph_indicator_SelectedIndexChanged" CssClass="exclude-length-check">
                            </asp:DropDownList><br />
                            <br />
                            <div id="dvMinYAxis" runat="server" visible="true">
                                <span class="graphIndicators--indicators" style="text-align: center"><%=m_config.GetEntryByKeyName("GRAPH_MIN_Y_AXIS").GetValue()%></span>
                                <asp:TextBox ID="txt_y_axis_offset" runat="server" Font-Size="Smaller" Width="30px"></asp:TextBox>
                                <br />
                                <br />
                            </div>
                            <div id="DivScales" runat="server" visible="true">
                                <span class="graphIndicators--scales"><%=m_config.GetEntryByKeyName("SCALES").GetValue()%></span>
                                <asp:CheckBoxList ID="cbl_scale" CssClass="graphIndicators--scalesTable" AutoPostBack="false" runat="server" Font-Size="Smaller">
                                </asp:CheckBoxList>
                            </div>
                        </div>
                        <br />
                        <div class="txtCenter">
                            <asp:Button ID="btnDraw" runat="server" class="btnGeneral" />
                        </div>
                    </div>
                    <div class="menuBOT">
                        <div>&nbsp;</div>
                    </div>
                </div>
                <!-- Extra.... fine-->

                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->
            </div>
        </div>
    </form>
</body>
</html>