<?xml version="1.0"?>
<!--
    Nota: come alternativa alla modifica manuale del file, è possibile utilizzare lo
    strumento di amministrazione Web per configurare le impostazioni dell'applicazione. Utilizzare il comando
    Configurazione ASP.NET del menu Sito Web di Visual Studio.
    Un elenco completo di impostazioni e commenti è disponibile nel file
    machine.config.comments che si trova in genere in
    \Windows\Microsoft.Net\Framework\v2.x\Config
-->
<configuration>
  <appSettings>
    <add key="IstanzaDataBase" value=".\SQLEXPRESS"/>
    <add key="DataBaseName" value="212240ALJUMUM"/>
    <add key="TypeDb" value="8"/>
    <add key="root" value="/212240ALJUMUM/"/>
    <add key="ChartImageHandler" value="storage=file;timeout=20;dir=c:\TempImageFiles\;deleteAfterServicing=false;"/>
    <!-- Testo per la scritta in alto a sx delle pagine web-->
    <add key="SiteName" value="Management@mill Al Jumum - Kingdom of Saudi Arabia"/>
    <add key="PrintWithWcf" value="false"/>
    <add key="Relative.OWSAddress" value="AtMillWcfServices/Service.svc"/>
    <!-- Se true usa il sql server reporting services, se false usa javascript -->
    <add key="UseSSRSReport" value="true"/>
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <httpRuntime maxRequestLength="1048576" executionTimeout="3600"/>
    <!--
            Impostare compilation debug="true" per inserire i
            simboli di debug nella pagina compilata. Poiché tale operazione ha effetto
            sulle prestazioni, impostare questo valore su true
            solo durante lo sviluppo.

            L'impostazione compilation debug="true" abilita inoltre LogTools alla scrittura del file diag\web.debug.log,
            dove vengono archiviate le chiamate a LogTools.Tools.LogDebug.

            Opzioni di Visual Basic:
            Impostare strict="true" per impedire qualsiasi conversione di tipi di dati
            in caso di possibile perdita di dati.
            Impostare explicit="true" per imporre la dichiarazione di tutte le variabili.
        -->
    <compilation explicit="true" strict="false" targetFramework="4.8" debug="true">
      <assemblies>
        <!-- per il prossimo assembly sono necessari MSChart.exe ed MSChart_VisualStudioAddOn -->
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Data.Services, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Data.Services.Client, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Security, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Data.Entity.Design, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Extensions.Design, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="Accessibility, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Web.DynamicData, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Routing, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="Microsoft.SqlServer.Types, Version=1*******, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
        <add assembly="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
        <add assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
      </assemblies>
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
      </buildProviders>
    </compilation>
    <httpHandlers>
      <add path="ChartImg.axd" verb="GET,HEAD" type="System.Web.UI.DataVisualization.Charting.ChartHttpHandler, System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" validate="false"/>
    </httpHandlers>
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID">
      <namespaces>
        <clear/>
        <add namespace="System"/>
        <add namespace="System.Collections"/>
        <add namespace="System.Collections.Generic"/>
        <add namespace="System.Collections.Specialized"/>
        <add namespace="System.Configuration"/>
        <add namespace="System.Text"/>
        <add namespace="System.Text.RegularExpressions"/>
        <add namespace="System.Linq"/>
        <add namespace="System.Xml.Linq"/>
        <add namespace="System.Web"/>
        <add namespace="System.Web.Caching"/>
        <add namespace="System.Web.SessionState"/>
        <add namespace="System.Web.Security"/>
        <add namespace="System.Web.Profile"/>
        <add namespace="System.Web.UI"/>
        <add namespace="System.Web.UI.WebControls"/>
        <add namespace="System.Web.UI.WebControls.WebParts"/>
        <add namespace="System.Web.UI.HtmlControls"/>
        <add namespace="System.Web.HttpContext"/>
        <add namespace="CommonDefines.Defines"/>
      </namespaces>
      <controls>
        <add tagPrefix="asp" namespace="System.Web.UI.DataVisualization.Charting" assembly="System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      </controls>
    </pages>
    <!--
            La sezione <authentication> consente di configurare
            la modalità di autenticazione della sicurezza utilizzata da
            ASP.NET per identificare un utente in ingresso.
        -->
    <authentication mode="Windows"/>
    <!--
            La sezione <customErrors> consente di configurare
            l'operazione da eseguire in caso di errore non gestito
            durante l'esecuzione di una richiesta. In particolare,
            consente agli sviluppatori di configurare le pagine di errore HTML
            in modo che vengano visualizzate al posto dell'analisi dello stack dell'errore.

        <customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
            <error statusCode="403" redirect="NoAccess.htm" />
            <error statusCode="404" redirect="FileNotFound.htm" />
        </customErrors>
        -->
    <customErrors mode="Off"/>
  </system.web>
  <!--
        La sezione system.webServer è richiesta per eseguire ASP.NET AJAX in Internet
        Information Services 7.0. Non è necessaria per la versione precedente di IIS.
    -->
  <system.serviceModel>
    <serviceHostingEnvironment aspNetCompatibilityEnabled="true"/>
  </system.serviceModel>
  <system.webServer>
    <handlers>
      <remove name="ChartImageHandler"/>
      <add name="ChartImageHandler" preCondition="integratedMode" verb="GET,HEAD,POST" path="ChartImg.axd" type="System.Web.UI.DataVisualization.Charting.ChartHttpHandler, System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit"/>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit"/>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0"/>
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0"/>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0"/>
      <add name="ReportViewerWebControlHandler" verb="*" path="Reserved.ReportViewerWebControl.axd" preCondition="integratedMode" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
    </handlers>
    <validation validateIntegratedModeConfiguration="false"/>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="1073741824"/>
      </requestFiltering>
    </security>
    <modules runAllManagedModulesForAllRequests="true"/>
  </system.webServer>
</configuration>