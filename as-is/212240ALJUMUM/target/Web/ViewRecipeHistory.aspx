﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ViewRecipeHistory.aspx.vb" Inherits="ViewRecipeHistory" %>

<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_Edit.ascx" TagPrefix="WUCEdit" TagName="eEdit" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
</head>
<body onload="InitPage()">
    <form id="form_ViewRecipeHistory" method="post" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true">
        </asp:ScriptManager>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div class="mainSection">
                <div id="print_area" class="tabMrg3 tabMrg3Top">
                    <asp:HiddenField ID="HiddenMyConfirmReply" runat="server" />
                    <div id="customerHeader"></div>
                    <table id="titleTable" class="tabMrg3">
                        <tr class="noPrint">
                            <td class="bgImg2">&nbsp;
                            </td>
                            <td class="tabBground1">&nbsp;
                            </td>
                        </tr>
                        <tr>
                            <td id="pageTitle" colspan="2" class="txtLeft txtBold">
                                <%= If(mPageName IsNot Nothing, m_config.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue(), "")%>
                            </td>
                        </tr>
                    </table>
                    <table class="tabDimRid1">
                        <tr>
                            <td>
                                <div id="dView" runat="server" visible="true">
                                    <table id="tblHeader" runat="server" visible="true"></table>
                                    <hr style="display: none" />
                                    <table id="tblEdit" class="txtLeft" runat="server">
                                    </table>
                                </div>
                                <div id="dError" runat="server" visible="false">
                                    <table id="Table1" runat="server">
                                        <tr>
                                            <td>
                                                <asp:Label runat="server" ID="lblError" class="lblError"></asp:Label>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="rightSection">
                <!-- Operazioni.... inizio -->
                <div id="DivOperation" style="display: none;">
                    <asp:HiddenField ID="HiddenDeleteAll" runat="server" />
                    <div class="menuTOP">
                        <div class="txtCenter txtWhite txtBold">
                            <%= m_config.GetEntryByKeyName("Operations").GetValue%>
                        </div>
                    </div>
                    <div id="DivElements" class="menuMID midOperation">
                        <table id="tblOperation" runat="server">
                        </table>
                    </div>
                </div>
                <!-- Operazioni.... fine-->

                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->
            </div>
        </div>
    </form>

    <div id="div_blanket" runat="server" class="blanket" blanket></div>

    <script type="text/javascript">
        blanket.show();
    </script>
</body>
</html>