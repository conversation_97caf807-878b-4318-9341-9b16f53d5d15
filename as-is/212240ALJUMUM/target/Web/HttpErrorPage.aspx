<%@ Page Language="VB" AutoEventWireup="false" CodeFile="HttpErrorPage.aspx.vb" Inherits="_HttpErrorPage" %>

<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta http-equiv="X-UA-Compatibile" content="IE=8" />

    <link type="text/css" href="./css/jquery.mobile-1.4.2.css" rel="stylesheet" />
    <link id="report_css" type="text/css" href="<%= "./css/default.css" & myScript.GetQueryClientUpdate() %>" rel="stylesheet" />
    <link id="report_css_print" type="text/css" href="<%= "./css/print.css" & myScript.GetQueryClientUpdate() %>" rel="stylesheet" media="print" />

    <script type="text/javascript" src="./js/jquery-1.9.1.js"></script>
</head>
<body onload="InitPage();">
    <form id="form_Default" method="post" runat="server">
        <div class="navbarContainer" <%= If(Current.Request("hideNavigation") IsNot Nothing AndAlso Current.Request("hideNavigation") = UsersGUI.costanti.m_StringYes, "style='display:none;'", "") %>>
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer" runat="server">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
                <article id="print_area" class="exceptionBody">
                    <div class="container">

                        <div class="section__exception--title">
                            <label>Exception</label>
                            <label class="exception_datetime"><% Response.Write(DateTime.Now.ToString(UsersGUI.costanti.m_ODBCSqlDateTimeFormat)) %></label>
                        </div>

                        <% For Each exc As Exception In exceptions %>

                        <section class="section__exception--cont card card-body">

                            <div class="section__exception--subtitle">
                                <label>Exception details</label>
                            </div>

                            <div class="section__exception--data">

                                <div class="row">
                                    <div class="col-2">
                                        <label class="section__exception--label">Type</label>
                                    </div>
                                    <div class="col-10">
                                        <label class="section__exception--value"><% Response.Write(exc.GetType.ToString) %></label>
                                    </div>
                                </div>

                                <hr />

                                <div class="row">
                                    <div class="col-2">
                                        <label class="section__exception--label">Message</label>
                                    </div>
                                    <div class="col-10">
                                        <label class="section__exception--value"><% Response.Write(exc.Message) %></label>
                                    </div>
                                </div>

                                <hr />

                                <div class="row">
                                    <div class="col-2">
                                        <label class="section__exception--label">Source</label>
                                    </div>
                                    <div class="col-10">
                                        <label class="section__exception--value"><% Response.Write(exc.Source) %></label>
                                    </div>
                                </div>

                                <% If exc.StackTrace IsNot Nothing Then %>
                                <hr />
                                <div class="row">
                                    <div class="col-2">
                                        <label class="section__exception--label">Stack trace</label>
                                    </div>
                                    <div class="col-10">
                                        <div class="card card-body bg-light">
                                            <span class="section__exception--value stackTrace--value"><% Response.Write(exc.StackTrace.Replace(vbNewLine, "<br>")) %></span>
                                        </div>
                                    </div>
                                </div>
                                <% End If%>
                            </div>
                        </section>

                        <% Next %>
                    </div>
                </article>
            </div>
            <div class="rightSection">
                <!-- Operazioni.... inizio -->
                <WUCOperation:eOperation ID="WebOperation" runat="server" />
                <!-- Operazioni.... fine-->
            </div>
        </div>
    </form>
</body>
</html>