﻿Option Strict On

Imports UsersGUI
Imports UsersGUI.costanti
Imports WebDataBaseLayer
Imports UnitsTools

Partial Class BinLayers
    Inherits myWebPage

    Protected Const TRACING_MODEL_LIQUID As Integer = 1
    Protected Const TRACING_MODEL_LAYER As Integer = 2
    Protected Const TRACING_MODEL_CARICOLIQUIDO_SCARICOSTRATI As Integer = 3
    Protected Const TRACING_MODEL_CARICOSTRATI_SCARICOLIQUIDO As Integer = 4

    Protected m_ListColor As New Generic.List(Of String)

    Protected m_CelId As Long = m_InvalidId
    Protected m_DrawFlow As Boolean = False
    Protected m_TypeView As String = String.Empty
    Protected m_TotaleCella As Double = m_InvalidDblValue
    Protected m_b_jobs As Boolean = False
    Protected m_b_lots As Boolean = False
    Protected m_b_stocks As Boolean = False
    Protected m_TracingModel As Integer = m_IntegerZero

    Protected m_NomeCella As String = String.Empty
    Protected m_HVuoto As Double = m_InvalidDblValue

    Protected m_TotalHighJob As Long = 0
    Protected m_TotalWidthJob As Long = 0
    Protected m_HighJob As Long = 0
    Protected m_WidthJob As Long = 0

    Protected m_config As config
    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing

    Protected dt As Data.DataTable = Nothing

    Private keyColorMap As Dictionary(Of String, String) = Nothing
    Private nextColorIndex As Integer = 0

    ' configurazione unità di misura a DB ed ad interfaccia utente
    Private Class CfgMeasurementUnit

        Public Class Database
            Public Const EmptyAmount As MeasurementUnit = MeasurementUnit.Kg
            Public Const OtherAmounts As MeasurementUnit = MeasurementUnit.Kg
        End Class

        Public Class UserInterface
            Public Const EmptyAmount As MeasurementUnit = MeasurementUnit.Ton
            Public Const OtherAmounts As MeasurementUnit = MeasurementUnit.Ton
        End Class

    End Class

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        m_config = CType(Application("Config"), config)
        If m_config Is Nothing Then
            Exit Sub
        End If

        ' Controlla se è necessario fare un update delle risorse del JS
        myScript.UpdateJSResources(m_config)

        Me.AddColor()
        Me.GetInfo()
        Me.DrawFlows()
        Me.GetData()
        Me.DrawBin()
        Me.DrawDetails()
        Me.DrawHeader()

        myScript.InvokeJS(Me.Page, "HideRows('WebEdit_tblHeader');")
        myScript.InvokeJS(Me.Page, "SetTdColspan('WebEdit_tblHeader');")
        myScript.InvokeJS(Me.Page, "ShowUCCont();")
        myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('WebEdit_tblHeader');")
    End Sub

    Private Sub AddColor()
        m_ListColor.Add("#e6194B") ' Red
        m_ListColor.Add("#4363d8") ' Blue
        m_ListColor.Add("#3cb44b") ' Green
        m_ListColor.Add("#ffe119") ' Yellow
        m_ListColor.Add("#f58231") ' Orange
        m_ListColor.Add("#469990") ' Teal
        m_ListColor.Add("#800000") ' Maroon
        m_ListColor.Add("#f032e6") ' Magenta
        m_ListColor.Add("#42d4f4") ' Cyan
        m_ListColor.Add("#a9a9a9") ' Grey
        m_ListColor.Add("#fabed4") ' Pink
        m_ListColor.Add("#aaffc3") ' Mint
        m_ListColor.Add("#9A6324") ' Brown
        m_ListColor.Add("#dcbeff") ' Lavander
        m_ListColor.Add("#000075") ' Navy
        m_ListColor.Add("#fffac8") ' Beige
    End Sub

    Private Sub GetInfo()
        If Current.Request.QueryString("CEL_ID") IsNot Nothing AndAlso IsNumeric(Current.Request.QueryString("CEL_ID").ToString) Then
            m_CelId = Long.Parse(Request.QueryString("CEL_ID").ToString)
            m_TotaleCella = WebTools.tools.GetBinCapacity(m_CelId)
        End If

        If Current.Request.QueryString("DRAWFLOW") IsNot Nothing AndAlso Current.Request.QueryString("DRAWFLOW").ToString <> String.Empty Then
            m_DrawFlow = CBool(Current.Request("DRAWFLOW").ToString)
        End If

        If Current.Request.QueryString("TYPEVIEW") IsNot Nothing AndAlso Current.Request.QueryString("TYPEVIEW").ToString <> String.Empty Then
            m_TypeView = Current.Request("TYPEVIEW").ToString

            Select Case UCase(m_TypeView)
                Case "JOBS"
                    m_b_jobs = True
                Case "LOTS"
                    m_b_lots = True
                Case "STOCKS"
                    m_b_stocks = True
            End Select
        End If

        If m_b_jobs Then
            m_TracingModel = TRACING_MODEL_LAYER
        ElseIf m_b_lots Then
            If Current.Request.QueryString("TRACING_MODEL") IsNot Nothing AndAlso IsNumeric(Current.Request.QueryString("TRACING_MODEL").ToString) Then
                m_TracingModel = Integer.Parse(Current.Request("TRACING_MODEL").ToString)
            End If
        ElseIf m_b_stocks Then
            m_TracingModel = TRACING_MODEL_LAYER
        End If

    End Sub

    Private Sub GetData()
        Dim sSelect As String = String.Empty

        If m_b_jobs Then
            sSelect = "SELECT JOB_TO_CEL.ID, JOB_TO_CEL.JOB_NUMBER, JOB_TO_CEL.CEL_ID, CELLS.DESCRIPTION, JOB_TO_CEL.JOB_NUMBER, JOB_TO_CEL.READY_DATE " &
                "FROM JOB_TO_CEL, CELLS WHERE JOB_TO_CEL.CEL_ID = CELLS.ID AND JOB_TO_CEL.CEL_ID = " & m_CelId & " " &
                "ORDER BY JOB_TO_CEL.INSERT_DATE DESC"

        ElseIf m_b_lots Then
            sSelect = "SELECT LO_ID, AMOUNT, CEL_ID, CEL_NAME, LO_NAME, NAME, CONTINUOUS_LEVEL_AMOUNT " &
                "FROM VIEW_LOTS_TO_CELL " &
                "WHERE CEL_ID = " & m_CelId & " " &
                "ORDER BY INSERT_DATE DESC"

        ElseIf m_b_stocks Then
            sSelect = "SELECT CELLS.ID, CELLS.PRO_ID, CELLS.CURRENT_AMOUNT, PRODUCTS.ID, CELLS.DESCRIPTION, PRODUCTS.NAME " &
                "FROM CELLS INNER JOIN PRODUCTS ON CELLS.PRO_ID = PRODUCTS.ID " &
                "WHERE CELLS.ID = " & m_CelId
        End If

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        ' calcolo la quantità "vuota" di cella
        m_HVuoto = m_TotaleCella

        If m_b_jobs Then
            sSelect = "SELECT CURRENT_AMOUNT FROM CELLS WHERE ID = '" & m_CelId & "'"
            Dim dt2 = DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each dr2 As Data.DataRow In dt2.Rows
                m_HVuoto -= CDbl(dr2.Item(0).ToString) 'Amount in cella
            Next

        ElseIf m_b_lots Then

            For Each dr As Data.DataRow In dt.Rows
                m_HVuoto -= CDbl(dr.Item(1).ToString) 'Amount
            Next

        ElseIf m_b_stocks Then

            For Each dr As Data.DataRow In dt.Rows
                m_HVuoto -= CDbl(dr.Item(2).ToString) 'Amount
            Next
        End If
    End Sub

    Private Sub DrawFlows()

        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable

        If m_DrawFlow Then

            ' flussi in ingresso alla cella
            sSelect = "SELECT FLOWS.SOURCE_CELL, CELLS.DESCRIPTION, FLOWS.PREVIOUS_WEIGHT, DATEDIFF(s, FLOWS.START_TIME, GETDATE()) AS DURATA_S FROM FLOWS, CELLS " &
                        "WHERE FLOWS.SOURCE_CELL = CELLS.ID AND FLOWS.DEST_CELL = '" & m_CelId & "' AND FLOWS.STATUS = 'ON'"
            dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count > 0 Then
                Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
                Dim row2 As New System.Web.UI.HtmlControls.HtmlTableRow
                tblFlowIn.Rows.Add(row)
                tblFlowIn.Rows.Add(row2)
            End If

            For Each dr As Data.DataRow In dt.Rows
                Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = dr.Item("DESCRIPTION").ToString()
                tc.Align = "center"
                tblFlowIn.Rows(0).Cells.Add(tc)

                Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                Dim img As New System.Web.UI.HtmlControls.HtmlImage

                img.Src = "~/image/DownArrow.png"
                tc2.Align = "center"
                tc2.Controls.Add(img)
                tblFlowIn.Rows(1).Cells.Add(tc2)
            Next

            ' flussi in uscita dalla cella
            sSelect = "SELECT FLOWS.DEST_CELL, CELLS.DESCRIPTION, FLOWS.PREVIOUS_WEIGHT, DATEDIFF(s, FLOWS.START_TIME, GETDATE()) AS DURATA_S FROM FLOWS, CELLS " &
                        "WHERE FLOWS.DEST_CELL = CELLS.ID AND  FLOWS.SOURCE_CELL = '" & m_CelId & "' AND FLOWS.STATUS = 'ON'"
            dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count > 0 Then
                Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
                Dim row2 As New System.Web.UI.HtmlControls.HtmlTableRow
                tblFlowOut.Rows.Add(row)
                tblFlowOut.Rows.Add(row2)
            End If

            For Each dr As Data.DataRow In dt.Rows
                Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = dr.Item("DESCRIPTION").ToString()
                tc.Align = "center"
                tblFlowOut.Rows(1).Cells.Add(tc)

                Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                Dim img As New System.Web.UI.HtmlControls.HtmlImage

                img.Src = "~/image/DownArrow.png"
                tc2.Align = "center"
                tc2.Controls.Add(img)
                tblFlowOut.Rows(0).Cells.Add(tc2)
            Next
        End If

    End Sub

    Protected Sub DrawBinWithModelLoadLiquidUnloadLayers(empty_height_perc As Double)

        Dim dr_amount As Long
        Dim temp_val As Integer = 0
        Dim row_height As Double = 0.0

        If m_b_lots Then
            For Each dr As Data.DataRow In dt.Rows
                m_TotalWidthJob += CLng(dr.Item(1).ToString) ' Amount
            Next
        Else
            For Each dr As Data.DataRow In dt.Rows
                m_TotalWidthJob += CLng(dr.Item(2).ToString) ' Amount
            Next

        End If

        ' devo evitare la divisione per 0
        If m_TotalWidthJob = 0 Then
            m_TotalWidthJob = 1
        End If

        Dim tableRowsReversed As List(Of Data.DataRow) = dt.Rows.Cast(Of Data.DataRow).ToList

        Dim max_hight_first_lot As Long = 0

        If m_b_lots Then
            max_hight_first_lot = CLng(tableRowsReversed.Last().Item(1).ToString())
        Else
            max_hight_first_lot = CLng(tableRowsReversed.Last().Item(2).ToString())
        End If

        If m_HVuoto > 0 Then
            max_hight_first_lot = CInt(Math.Round((max_hight_first_lot / m_TotalWidthJob) * 100 * ((100 - empty_height_perc) / 100)))
        Else
            max_hight_first_lot = CInt(Math.Round((max_hight_first_lot / m_TotalWidthJob) * 100 * ((100 - (100 - empty_height_perc)) / 100)))
        End If

        Dim row2 As New System.Web.UI.HtmlControls.HtmlTableRow

        If Math.Abs(m_HVuoto) < m_TotaleCella Then
            row_height = Math.Round(((m_TotaleCella - m_HVuoto) / m_TotaleCella) * 100)
        Else
            row_height = 0
            End If

        If row_height < 1 Then
            row_height = 1
        End If

        row_height = (row_height - max_hight_first_lot)

        row2.Height = row_height & "%"

        Dim n_lot As Integer = 1

        For Each dr As Data.DataRow In tableRowsReversed

            If m_b_lots Then
                dr_amount = CLng(dr.Item(1).ToString)
        Else
                dr_amount = CLng(dr.Item(2).ToString)
        End If

            If (n_lot >= tableRowsReversed.Count) Then
                m_HighJob = dr_amount

                Dim row3 As New System.Web.UI.HtmlControls.HtmlTableRow
        If m_HVuoto > 0 Then
                    temp_val = CInt(Math.Round((m_HighJob / m_TotalWidthJob) * 100 * ((100 - empty_height_perc) / 100)))
                Else
                    temp_val = CInt(Math.Round((m_HighJob / m_TotalWidthJob) * 100 * ((100 - (100 - empty_height_perc)) / 100)))
                End If

                ' devo evitare righe con altezza 0%
                If temp_val > 1 Then
                    row3.Height = temp_val.ToString & "%"
                Else
                    row3.Height = "1%"
                End If

                Dim tc3 As New System.Web.UI.HtmlControls.HtmlTableCell

                If m_b_stocks Then
                    tc3.BgColor = GetColorForKey(dr.Item(1).ToString()) ' PRO_ID
                ElseIf m_b_lots Then
                    tc3.BgColor = GetColorForKey(dr.Item(0).ToString()) ' LO_ID
                ElseIf m_b_jobs Then
                    tc3.BgColor = GetColorForKey(dr.Item(1).ToString()) ' JOB_NUMBER
            End If

                tc3.Height = row3.Height

                tc3.ColSpan = tableRowsReversed.Count - 1
                row3.Cells.Insert(0, tc3)

                tblBin.Rows.Add(row3)

                n_lot = n_lot + 1
        Else
                If (n_lot = 1) Then
                    tblBin.Rows.Add(row2)
                End If
                n_lot = n_lot + 1

                Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                Dim currWidth As Integer = CInt(Math.Round((dr_amount / m_TotalWidthJob) * 100))

                If currWidth < 1 Then
                    currWidth = 1
                End If

                tc2.Width = currWidth.ToString & "%"
                tc2.Height = (Math.Round(((m_TotaleCella - m_HVuoto) / m_TotaleCella) * 100) - max_hight_first_lot).ToString() & "%"

                If m_b_stocks Then
                    tc2.BgColor = GetColorForKey(dr.Item(1).ToString()) ' PRO_ID
                ElseIf m_b_lots Then
                    tc2.BgColor = GetColorForKey(dr.Item(0).ToString()) ' LO_ID
                ElseIf m_b_jobs Then
                    tc2.BgColor = GetColorForKey(dr.Item(1).ToString()) ' JOB_NUMBER
                End If

                ' Aggiungo le celle a sinistra, in modo che la più vecchia si depositi a destra
                tblBin.Rows(tblBin.Rows.Count - 1).Cells.Insert(0, tc2)
        End If

        Next
    End Sub

    Protected Sub DrawBinWithModelLoadLayersUnloadLiquid(empty_height_perc As Double)
        Dim dr_amount As Long
        Dim temp_val As Integer = 0
        Dim row_height As Double = 0.0

                If m_b_lots Then
                    For Each dr As Data.DataRow In dt.Rows
                        m_TotalWidthJob += CLng(dr.Item(1).ToString) ' Amount
                    Next
                Else
                    For Each dr As Data.DataRow In dt.Rows
                        m_TotalWidthJob += CLng(dr.Item(2).ToString) ' Amount
                    Next

                End If

                ' devo evitare la divisione per 0
                If m_TotalWidthJob = 0 Then
                    m_TotalWidthJob = 1
                End If

        ' Rigiro l'ordine delle righe in modo da assegnare i colori nel modo più sensato (il primo colore all'ultimo strato e così via)
        Dim tableRowsReversed As List(Of Data.DataRow) = dt.Rows.Cast(Of Data.DataRow).ToList
        tableRowsReversed.Reverse()

                Dim row2 As New System.Web.UI.HtmlControls.HtmlTableRow

                If Math.Abs(m_HVuoto) < m_TotaleCella Then
                    row_height = Math.Round(((m_TotaleCella - m_HVuoto) / m_TotaleCella) * 100)
                Else
                    row_height = 0
                End If

                If row_height < 1 Then
                    row_height = 1
                End If

                row2.Height = row_height & "%"

        Dim n_lot As Integer = 1
                For Each dr As Data.DataRow In tableRowsReversed

                    If m_b_lots Then
                        dr_amount = CLng(dr.Item(1).ToString)
                    Else
                        dr_amount = CLng(dr.Item(2).ToString)
                    End If

            If (n_lot = 1) Then
                m_HighJob = dr_amount

                Dim row3 As New System.Web.UI.HtmlControls.HtmlTableRow
                temp_val = CInt(Math.Round((m_HighJob / m_TotalWidthJob) * 100 * ((100 - empty_height_perc) / 100)))

                ' devo evitare righe con altezza 0%
                If temp_val > 1 Then
                    row3.Height = temp_val.ToString & "%"
                Else
                    row3.Height = "1%"
                End If

                Dim tc3 As New System.Web.UI.HtmlControls.HtmlTableCell

                If m_b_stocks Then
                    tc3.BgColor = GetColorForKey(dr.Item(1).ToString()) ' PRO_ID
                ElseIf m_b_lots Then
                    tc3.BgColor = GetColorForKey(dr.Item(0).ToString()) ' LO_ID
                ElseIf m_b_jobs Then
                    tc3.BgColor = GetColorForKey(dr.Item(1).ToString()) ' JOB_NUMBER
                End If

                tc3.Height = row3.Height

                tc3.ColSpan = tableRowsReversed.Count - 1
                row3.Cells.Insert(0, tc3)

                ' Aggiungo le righe in alto, in modo da tornare all'ordine giusto, alterato in partenza dal reverse
                If m_HVuoto > 0 Then
                    tblBin.Rows.Insert(1, row3)
                Else
                    tblBin.Rows.Insert(0, row3)
                End If

                n_lot = n_lot + 1
            Else
                If (n_lot = 2) Then
                    tblBin.Rows.Add(row2)
                End If
                n_lot = n_lot + 1

                    Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim currWidth As Integer = CInt(Math.Round((dr_amount / m_TotalWidthJob) * 100))

                    If currWidth < 1 Then
                        currWidth = 1
                    End If

                    tc2.Width = currWidth.ToString & "%"
                    tc2.Height = row2.Height

                    If m_b_stocks Then
                        tc2.BgColor = GetColorForKey(dr.Item(1).ToString()) ' PRO_ID
                    ElseIf m_b_lots Then
                        tc2.BgColor = GetColorForKey(dr.Item(0).ToString()) ' LO_ID
                    ElseIf m_b_jobs Then
                        tc2.BgColor = GetColorForKey(dr.Item(1).ToString()) ' JOB_NUMBER
                    End If

                ' Aggiungo le celle a sinistra, in modo che la più vecchia si depositi a destra
                    tblBin.Rows(tblBin.Rows.Count - 1).Cells.Insert(0, tc2)
            End If

                Next
    End Sub

    Protected Sub DrawBinWithModelLayers(empty_height_perc As Double)
        Dim dr_amount As Long
        Dim temp_val As Integer = 0
        Dim row_height As Double = 0.0

                m_TotalHighJob = 0

                If m_b_lots Then
                    For Each dr As Data.DataRow In dt.Rows
                        m_TotalHighJob += CLng(dr.Item(1).ToString) ' Amount
                    Next
                Else
                    For Each dr As Data.DataRow In dt.Rows
                        m_TotalHighJob += CLng(dr.Item(2).ToString) ' Amount
                    Next
                End If

                ' devo evitare la divisione per 0
                If m_TotalHighJob = 0 Then
                    m_TotalHighJob = 1
                End If

                ' Rigiro l'ordine delle righe in modo da assegnare i colori nel modo più sensato (il primo colore all'ultimo strato e così via)
                Dim tableRowsReversed As List(Of Data.DataRow) = dt.Rows.Cast(Of Data.DataRow).ToList
                tableRowsReversed.Reverse()

                For Each dr As Data.DataRow In tableRowsReversed

                    If m_b_lots Then
                        dr_amount = CLng(dr.Item(1).ToString)
                    Else
                        dr_amount = CLng(dr.Item(2).ToString)
                    End If

                    m_HighJob = dr_amount

                    Dim row3 As New System.Web.UI.HtmlControls.HtmlTableRow
                    temp_val = CInt(Math.Round((m_HighJob / m_TotalHighJob) * 100 * ((100 - empty_height_perc) / 100)))

                    ' devo evitare righe con altezza 0%
                    If temp_val > 1 Then
                        row3.Height = temp_val.ToString & "%"
                    Else
                        row3.Height = "1%"
                    End If

                    Dim tc3 As New System.Web.UI.HtmlControls.HtmlTableCell

                    If m_b_stocks Then
                        tc3.BgColor = GetColorForKey(dr.Item(1).ToString()) ' PRO_ID
                    ElseIf m_b_lots Then
                        tc3.BgColor = GetColorForKey(dr.Item(0).ToString()) ' LO_ID
                    ElseIf m_b_jobs Then
                        tc3.BgColor = GetColorForKey(dr.Item(1).ToString()) ' JOB_NUMBER
                    End If

                    tc3.Height = row3.Height

                    row3.Cells.Add(tc3)

                    ' Aggiungo le righe in alto, in modo da tornare all'ordine giusto, alterato in partenza dal reverse
                    If m_HVuoto > 0 Then
                        tblBin.Rows.Insert(1, row3)
                    Else
                        tblBin.Rows.Insert(0, row3)
                    End If
                Next
    End Sub

    Protected Sub DrawBinWithModelLiquid(empty_height_perc As Double)
        Dim dr_amount As Long
        Dim temp_val As Integer = 0
        Dim row_height As Double = 0.0

        If m_b_lots Then
            For Each dr As Data.DataRow In dt.Rows
                m_TotalWidthJob += CLng(dr.Item(1).ToString) ' Amount
            Next
        Else
            For Each dr As Data.DataRow In dt.Rows
                m_TotalWidthJob += CLng(dr.Item(2).ToString) ' Amount
            Next

        End If

        ' devo evitare la divisione per 0
        If m_TotalWidthJob = 0 Then
            m_TotalWidthJob = 1
        End If

        Dim row2 As New System.Web.UI.HtmlControls.HtmlTableRow

        If Math.Abs(m_HVuoto) < m_TotaleCella Then
            row_height = Math.Round(((m_TotaleCella - m_HVuoto) / m_TotaleCella) * 100)
        Else
            row_height = 0
        End If

        If row_height < 1 Then
            row_height = 1
        End If

        row2.Height = row_height & "%"

        tblBin.Rows.Add(row2)

        ' Rigiro l'ordine delle righe in modo da assegnare i colori nel modo più sensato (il primo colore all'ultimo strato e così via)
        Dim tableRowsReversed As List(Of Data.DataRow) = dt.Rows.Cast(Of Data.DataRow).ToList
        tableRowsReversed.Reverse()

        For Each dr As Data.DataRow In tableRowsReversed

            If m_b_lots Then
                dr_amount = CLng(dr.Item(1).ToString)
            Else
                dr_amount = CLng(dr.Item(2).ToString)
            End If

            Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
            Dim currWidth As Integer = CInt(Math.Round((dr_amount / m_TotalWidthJob) * 100))

            If currWidth < 1 Then
                currWidth = 1
            End If

            tc2.Width = currWidth.ToString & "%"
            tc2.Height = row2.Height

            If m_b_stocks Then
                tc2.BgColor = GetColorForKey(dr.Item(1).ToString()) ' PRO_ID
            ElseIf m_b_lots Then
                tc2.BgColor = GetColorForKey(dr.Item(0).ToString()) ' LO_ID
            ElseIf m_b_jobs Then
                tc2.BgColor = GetColorForKey(dr.Item(1).ToString()) ' JOB_NUMBER
            End If

            ' Aggiungo le celle a sinistra, in modo che la più vecchia si depositi a destra
            tblBin.Rows(tblBin.Rows.Count - 1).Cells.Insert(0, tc2)
        Next
    End Sub

    Protected Sub DrawBin()

        Dim empty_height_perc As Double = 0

        If dt Is Nothing Then
            Exit Sub
        End If

        If m_HVuoto > 0 Then
            empty_height_perc = Math.Round((m_HVuoto / m_TotaleCella) * 100)
            Dim row As New System.Web.UI.HtmlControls.HtmlTableRow

            If empty_height_perc = 100 AndAlso m_HVuoto <> m_TotaleCella Then
                empty_height_perc = 99 ' caso particolare (cella quasi vuota)
            End If

            row.Height = empty_height_perc & "%"
            tblBin.Rows.Add(row)
        Else
            empty_height_perc = 100 ' caso particolare (significa che ho tutta la cella piena)
        End If

        Select Case m_TracingModel
            Case TRACING_MODEL_LIQUID

                DrawBinWithModelLiquid(empty_height_perc)

            Case TRACING_MODEL_LAYER

                DrawBinWithModelLayers(empty_height_perc)

            Case TRACING_MODEL_CARICOLIQUIDO_SCARICOSTRATI

                DrawBinWithModelLoadLiquidUnloadLayers(empty_height_perc)

            Case TRACING_MODEL_CARICOSTRATI_SCARICOLIQUIDO

                DrawBinWithModelLoadLayersUnloadLiquid(empty_height_perc)

        End Select

    End Sub

    Protected Sub DrawDetails()
        Dim m_strValue As String = String.Empty
        Dim m_destURL As String = String.Empty

        Dim dr_lo_id As Integer
        Dim dr_amount As Long
        Dim dr_description As String

        Dim tot_amount As Long = 0

        If dt Is Nothing Then
            Exit Sub
        End If

        Dim row As System.Web.UI.HtmlControls.HtmlTableRow
        Dim tc As System.Web.UI.HtmlControls.HtmlTableCell

        ' header
        row = New System.Web.UI.HtmlControls.HtmlTableRow
        row.Attributes("class") = "rowSection"
        tblLegend.Rows.Add(row)

        tc = New System.Web.UI.HtmlControls.HtmlTableCell
        tc.InnerText = m_config.GetEntryByKeyName("VIEWDETAILS").GetValue & ":"
        tc.ColSpan = 4
        tc.Attributes("class") = "txtBold"
        tblLegend.Rows(tblLegend.Rows.Count - 1).Cells.Add(tc)

        ' empty amount
        If m_b_stocks OrElse m_b_lots Then
            If m_HVuoto >= 0 Then
                row = New System.Web.UI.HtmlControls.HtmlTableRow
                If tblLegend.Rows.Count Mod 2 = 0 Then
                    row.Attributes("class") = "rowEven"
                Else
                    row.Attributes("class") = "rowOdd"
                End If
                tblLegend.Rows.Add(row)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Width = "20px"
                tc.InnerHtml = "<div class=""legend--color"" style=""background-color: white;""></div>"
                tblLegend.Rows(tblLegend.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = m_config.GetEntryByKeyName("EMPTY").GetValue
                tblLegend.Rows(tblLegend.Rows.Count - 1).Controls.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                If m_b_lots Then
                    tc.InnerText = m_config.GetEntryByKeyName("NO_PRODUCT").GetValue
                Else
                    tc.InnerText = ""
                End If
                tblLegend.Rows(tblLegend.Rows.Count - 1).Controls.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = Math.Round(
                    m_HVuoto *
                    UsersGUI.tools.GetConversionFactorToASP(
                        GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.EmptyAmount),
                        GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)
                    ),
                    GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)
                ) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)

                tblLegend.Rows(tblLegend.Rows.Count - 1).Controls.Add(tc)
            End If
        End If

        ' other amounts
        For Each dr As Data.DataRow In dt.Rows
            If m_b_lots Then
                dr_lo_id = CInt(dr.Item(0).ToString)
            Else
                dr_lo_id = m_InvalidId
            End If

            row = New System.Web.UI.HtmlControls.HtmlTableRow
            If tblLegend.Rows.Count Mod 2 = 0 Then
                row.Attributes("class") = "rowEven"
            Else
                row.Attributes("class") = "rowOdd"
            End If
            tblLegend.Rows.Add(row)

            tc = New System.Web.UI.HtmlControls.HtmlTableCell
            tc.Width = "20px"

            Dim color As String = String.Empty
            If m_b_stocks Then
                color = GetColorForKey(dr.Item(1).ToString()) ' PRO_ID
            ElseIf m_b_lots Then
                color = GetColorForKey(dr.Item(0).ToString()) ' LO_ID
            ElseIf m_b_jobs Then
                color = GetColorForKey(dr.Item(1).ToString()) ' JOB_NUMBER
            End If
            tc.InnerHtml = "<div class=""legend--color"" style=""background-color: " & color & ";""></div>"
            tblLegend.Rows(tblLegend.Rows.Count - 1).Cells.Add(tc)

            tc = New System.Web.UI.HtmlControls.HtmlTableCell

            If m_b_stocks Then
                dr_amount = CLng(dr.Item(2).ToString)

                tc.InnerText = dr.Item(5).ToString ' nome prodotto
            ElseIf m_b_lots Then

                dr_amount = CLng(dr.Item(1).ToString)
                dr_description = dr.Item(4).ToString

                'Se si stanno visualizzando i lotti crea un collegamento adeguato
                Dim link As New HyperLink
                link.Text = dr_description
                link.NavigateUrl = myFunction.GetRoot & "Lot_History.aspx?control=lothistory&pagename=lothistory&menuname=MENU_LOTS&sql_where=WHERE 1=1 AND ID = '" & dr_lo_id & "'&ID=" & dr_lo_id
                tc.Controls.Add(link)
            ElseIf m_b_jobs Then
                tc.InnerText = dr.Item(1).ToString
            End If

            tot_amount += dr_amount

            tblLegend.Rows(tblLegend.Rows.Count - 1).Controls.Add(tc)

            tc = New System.Web.UI.HtmlControls.HtmlTableCell
            If m_b_lots Then
                tc.InnerText = dr.Item(5).ToString
            Else
                tc.InnerText = ""
            End If
            tblLegend.Rows(tblLegend.Rows.Count - 1).Controls.Add(tc)

            tc = New System.Web.UI.HtmlControls.HtmlTableCell
            If m_b_jobs Then
                tc.InnerText = dr.Item(5).ToString()
            Else
                tc.InnerText = Math.Round(
                    dr_amount *
                    UsersGUI.tools.GetConversionFactorToASP(
                        GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.EmptyAmount),
                        GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)
                    ),
                    GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)
                ) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)
            End If

            tblLegend.Rows(tblLegend.Rows.Count - 1).Controls.Add(tc)
        Next

        ' bottom line for total
        row = New System.Web.UI.HtmlControls.HtmlTableRow
        row.Attributes("class") = "rowSection"
        tblLegend.Rows.Add(row)

        tc = New System.Web.UI.HtmlControls.HtmlTableCell
        tc.InnerText = m_config.GetEntryByKeyName("Total amount").GetValue & ":"
        tc.ColSpan = 2
        tc.Attributes("class") = "txtBold"
        tblLegend.Rows(tblLegend.Rows.Count - 1).Cells.Add(tc)

        tc = New System.Web.UI.HtmlControls.HtmlTableCell
        tblLegend.Rows(tblLegend.Rows.Count - 1).Cells.Add(tc)

        tc = New System.Web.UI.HtmlControls.HtmlTableCell
        tc.InnerText = Math.Round(
            tot_amount *
            UsersGUI.tools.GetConversionFactorToASP(
                GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.EmptyAmount),
                GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)
            ),
            GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)
        ) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.EmptyAmount)

        tblLegend.Rows(tblLegend.Rows.Count - 1).Controls.Add(tc)

    End Sub

    Private Sub DrawHeader()
        Dim header = New Header()
        header.MasterDBName = "VIEW_CELLS"
        header.ParentMenu = mScreen
        header.FilterColumns = New List(Of HeaderFilter) From {
            New HeaderFilter() With {
                .ColumnName = "ID",
                .FilterValueParamName = "CEL_ID"
            }
        }

        header.EditFieldList = New List(Of Field) From {
            New Field("") With {
                .FieldDb = "ID",
                .FieldType = "Number",
                .FieldName = "Code"
            },
            New Field("") With {
                .FieldDb = "DESCRIPTION",
                .FieldType = "String",
                .FieldName = "Bin"
            },
            New Field("") With {
                .FieldDb = "NAME",
                .FieldType = "String",
                .FieldName = "Product"
            },
            New Field("") With {
                .FieldDb = "PRO_TYPE",
                .FieldType = "String",
                .FieldName = "Product type"
            },
            New Field("") With {
                .FieldDb = "CURRENT_AMOUNT",
                .FieldType = "Number",
                .FieldName = "Amount in kg"
            },
            New Field("") With {
                .FieldDb = "CONTINUOUS_LEVEL_AMOUNT",
                .FieldType = "Number",
                .FieldName = "CONTINUOUS_LEVEL_AMOUNT"
            }
        }

        WebTools.drawings.DrawHeaderFields(tblHeader, header, m_config, True)

        If tblHeader.Rows.Count <= 0 Then
            tblHeader.Visible = False
        End If
    End Sub

    ''' <summary>
    ''' Handles the assignement of colors to keys (lots/products/jobs)
    ''' </summary>
    ''' <param name="key"></param>
    Private Function GetColorForKey(key As String) As String
        Dim retVal As String

        If keyColorMap Is Nothing Then
            keyColorMap = New Dictionary(Of String, String)
        End If

        If keyColorMap.ContainsKey(key) Then
            retVal = keyColorMap(key)
        Else
            retVal = m_ListColor(nextColorIndex)
            keyColorMap.Add(key, retVal)
            nextColorIndex = (nextColorIndex + 1) Mod m_ListColor.Count
        End If

        Return retVal
    End Function

End Class