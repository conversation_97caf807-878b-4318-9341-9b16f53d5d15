﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_SystemGroupsRights.ascx.vb" Inherits="WUC_WUC_SystemGroupsRights" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>

<div class="tabMrg3 tabMrg3Top">
    <table id="tabTitle" class="tabMrg3">
        <tr>
            <td class="bgImg2">&nbsp;
            </td>
            <td class="tabBground1">&nbsp;
            </td>
        </tr>
        <tr>
            <td colspan="2" class="txtLeft txtBold">
                <%= If(mPageName IsNot Nothing, m_config.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue(), "")%>
            </td>
        </tr>
    </table>
    <table class="tabMrg3Top">
        <tr>
            <td>
                <div id="dView" class="divExpanse" runat="server" visible="true">
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <asp:Label runat="server" ID="lblPageName" class="lblPageName"></asp:Label>
                                </td>
                                <td colspan="3">
                                    <asp:DropDownList ID="ddl_pagename" runat="server" AutoPostBack="true"
                                        OnSelectedIndexChanged="ddl_pagename_SelectedIndexChanged">
                                    </asp:DropDownList>
                                </td>
                            </tr>
                        </tbody>
                        <thead>
                            <tr>
                                <th style="width: 25%"></th>
                                <th style="width: 25%"></th>
                                <th style="width: 25%"></th>
                                <th style="width: 25%"></th>
                            </tr>
                        </thead>
                    </table>
                    <br />
                    <table id="tblGroupsRights" runat="server" class="bord1px tabBorderCollapse">
                    </table>
                    <br />
                    <table id="tblButtons" runat="server">
                        <tr>
                            <td class="txtLeft">
                                <asp:Button ID="btnSubmit" runat="server" OnClientClick="myLoader.start()" CssClass="btnGeneral" Visible="false" />&nbsp;
                                <asp:Button ID="btnEdit" runat="server" OnClientClick="myLoader.start()" CssClass="btnGeneral" Visible="false" />&nbsp;
                                <asp:Button ID="btnCancel" runat="server" CssClass="btnGeneral" Visible="false" />
                            </td>
                        </tr>
                    </table>
                </div>
                <div id="dError" runat="server" visible="false">
                    <table id="Table1" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" class="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</div>