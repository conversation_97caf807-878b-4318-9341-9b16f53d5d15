﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_Pdf.ascx.vb" Inherits="WUC_WUC_Pdf" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>

<div class="tabMrg3 tabMrg3Top">
    <table id="tabTitle" class="tabMrg3">
        <tr>
            <td class="bgImg2">&nbsp;
            </td>
            <td class="tabBground1">&nbsp;
            </td>
        </tr>
        <tr>
            <td colspan="2" class="txtLeft txtBold">
                <%= If(mPageName IsNot Nothing, m_config.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue(), "")%>
            </td>
        </tr>
    </table>
    <table class="tabMrg3Top">
        <tr>
            <td>
                <div id="dView" class="divExpanse" runat="server" visible="true">
                    <% If Current.Request.QueryString("link") IsNot Nothing Then %>
                    <object class="pdfView" data="<%= myFunction.GetRoot & Current.Request.QueryString("link") %>" type="application/pdf">
                        <div>PDF file "<%= myFunction.GetRoot & Current.Request.QueryString("link") %>" is missing</div>
                    </object>
                    <% Else %>
                    <div>No PDF file provided</div>
                    <% End If %>
                </div>
                <div id="dError" runat="server" visible="false">
                    <table id="Table1" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" class="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</div>