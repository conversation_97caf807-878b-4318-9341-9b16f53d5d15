﻿Imports UsersGUI
Imports WebTools.tools

Partial Class UserControl_WUC_PLCStatus
    Inherits System.Web.UI.UserControl

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing

    Public mMenuName As String = String.Empty
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty

    Public _ListPLC As New Generic.List(Of PLC)
    Public b_reacheble As Boolean = True
    Public b_run As Boolean = True
    Public b_stop As Boolean = False
    Public b_starting As Boolean = False

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then

        End If

        Dim sSelect As String = "SELECT * FROM SYSTEM_ZBUS"
        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)
        If (dt IsNot Nothing AndAlso dt.Rows.Count > 0) Then
            For Each dr As Data.DataRow In dt.Rows
                Dim _plc As New PLC
                _plc.ID = Integer.Parse(dr("ID").ToString())
                _plc.DESCRIPTION = m_config.GetEntryByKeyName(dr("DESCRIPTION").ToString().Trim()).GetValue()
                _plc.STATUS = dr("STATUS").ToString().Trim()
                If (dr("PLC_REACHABLE") IsNot Nothing) Then
                    _plc.REACHABLE = dr("PLC_REACHABLE").ToString().Trim()
                End If

                _plc.IN_START = "NO"
                Dim sSelectIfStarting = "select TOP 1 * from COMMAND_INTERFACE WHERE REQUEST_ID=22 And (DATE_DONE Is null Or DATE_DONE>= DATEADD(MI,-3, getdate())) ORDER BY ID DESC"
                Dim dtIfStarting As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelectIfStarting, False)
                If (dtIfStarting IsNot Nothing AndAlso dtIfStarting.Rows.Count > 0) Then
                    _plc.IN_START = "YES"
                End If

                _ListPLC.Add(_plc)

            Next
        End If

    End Sub

    Public Class PLC
        Public ID As Integer = 0
        Public DESCRIPTION As String = ""
        Public STATUS As String = ""
        Public REACHABLE As String = ""
        Public IN_START As String = ""

    End Class

End Class