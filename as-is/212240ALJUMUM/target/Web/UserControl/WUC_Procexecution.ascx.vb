﻿Imports UsersGUI
Imports WebDataBaseLayer
Imports WebTools.tools

Partial Class WUC_WUC_Procexecution
    Inherits UserControl

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing

    Private mMenuName As String = String.Empty
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Private mId As Long = m_InvalidId
    Private sSelect As String = String.Empty
    Private sAccess As String = String.Empty
    Private from_page As String = String.Empty
    Private dt As Data.DataTable = Nothing
    Private b_access_denied As Boolean = False
    Private msgGeneric As New myAlert

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded
        If Me.Visible Then
            m_config = CType(Application("Config"), config)

            If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            ' message init
            msgGeneric.Init(Me.<PERSON>, myMessageBoxParam.NoParameter)

            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Adding) Then
                lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue
                Me.status.Visible = False
                Me.dViewError.Visible = True
                Exit Sub
            End If

            If Current.Request.QueryString("ID") IsNot Nothing AndAlso Current.Request.QueryString("ID").ToString <> String.Empty AndAlso Current.Request.QueryString("ID").ToString <> "-1" Then
                mId = Long.Parse(Current.Request.QueryString("ID").ToString)
            End If

            from_page = Current.Request.QueryString("from")

            If Not from_page Is Nothing AndAlso from_page <> String.Empty Then
                Select Case from_page
                    Case "MAINTENANCE_PROCEDURES_ASSIGNMENT"
                        sAccess = "SELECT * FROM " & mPageName & " WHERE MPA_ID='" & mId & "'" 'MAINT_PROC_EXEC_REQ
                        sSelect = "SELECT EDESC, PROCCODE, PDESC FROM VIEW_ASSIGNEMENTS "
                    Case "MAINTENANCE_PLANNING"
                        sAccess = "SELECT * FROM " & mPageName & " WHERE MPL_ID='" & mId & "'" 'EXECUTED_MAINT_PROC
                        sSelect = "SELECT EQDESC, CODE, PDESC FROM VIEW_MAINTENANCE_PLANNING "
                    Case Else
                        sAccess = String.Empty
                        sSelect = String.Empty

                        Exit Sub
                End Select

                ' controllo che la mpa non sia già stata registrata (in attesa del c++)
                If sAccess <> String.Empty Then
                    dt = DataBase.ExecuteSQL_DataTable(sAccess, False)

                    If dt Is Nothing OrElse dt.Rows.Count > 0 Then
                        b_access_denied = True
                    End If
                Else
                    b_access_denied = True
                End If

                If b_access_denied Then
                    Me.status.Visible = False
                    msgGeneric.Show(m_config.GetEntryByKeyName("ASSIGNMENT_IN_PROCESS").GetValue)
                    Return
                End If

                If sSelect <> String.Empty Then
                    sSelect &= " WHERE ID = '" & mId & "'"
                    dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

                    If Not dt Is Nothing AndAlso dt.Rows.Count > 0 Then
                        For Each dr As Data.DataRow In dt.Rows
                            Me.lblEquipment.Text = m_config.GetEntryByKeyName("Item").GetValue & ":"
                            Me.txtEquipment.Text = dr(0).ToString
                            Me.lblProcedure.Text = m_config.GetEntryByKeyName("Code").GetValue & ":"
                            Me.txtProcedure.Text = dr(1).ToString
                            Me.lblDescription.Text = m_config.GetEntryByKeyName("Procedure").GetValue & ":"
                            Me.txtDescription.Text = dr(2).ToString
                        Next
                    End If
                End If

            End If

            Me.lblInterventionReport.Text = m_config.GetEntryByKeyName("INTERVENTION_REPORT").GetValue & ":"
            Me.btnAnnulla.Text = m_config.GetEntryByKeyName("CANCEL_BUTTON").GetValue
            Me.btnConferma.Text = m_config.GetEntryByKeyName("SUBMIT_BUTTON").GetValue

            myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('UCProcexecution_tblProcExecution');")
        End If
    End Sub

    Protected Sub btnAnnulla_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnAnnulla.Click
        myScript.InvokeJS(Me.Page, "ButtonEvents.WUC_Procexecution.Cancel('" & mPageName & "');")
    End Sub

    Protected Sub btnConferma_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnConferma.Click
        If Not (Page.IsValid) Then
            Exit Sub
        End If

        If Me.txtInterventionReport.Text = String.Empty Then
            msgGeneric.Show(m_config.GetEntryByKeyName("INTERVENTION_REPORT").GetValue & ": " & m_config.GetEntryByKeyName("EMPTY_FIELD_FOUND").GetValue())
            Exit Sub
        End If

        If Me.InsertReq() Then
            myScript.InvokeJS(Me.Page, "ButtonEvents.WUC_Procexecution.Submit('" & mPageName & "');")
        End If
    End Sub

    Private Function InsertReq() As Boolean
        Dim su_id As Integer = m_InvalidId
        Dim ret_val As Boolean = False

        If Current.Session("UserID") IsNot Nothing AndAlso Current.Session("UserID").ToString <> String.Empty Then
            su_id = Current.Session("UserID")
        Else
            ' gestisco il caso che la pagina abbia accesso guest
            su_id = m_SystemIdUser
        End If

        If Not from_page Is Nothing AndAlso from_page <> String.Empty Then
            Select Case from_page
                Case "MAINTENANCE_PLANNING" ' attended maint
                    Dim emp As New ExecutedMaintProc(su_id, mId, Me.txtInterventionReport.Text, m_StringNo)
                    emp.Save()
                    ret_val = True

                Case "MAINTENANCE_PROCEDURES_ASSIGNMENT" ' unattended maint
                    Dim mper As New MaintProcExecReq(su_id, mId, Me.txtInterventionReport.Text)
                    mper.Save()
                    ret_val = True
            End Select
        End If

        Return ret_val
    End Function

End Class