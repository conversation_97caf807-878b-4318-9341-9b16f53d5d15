﻿Imports System.Data
Imports UsersGUI
Imports WebDataBaseLayer
Imports WebTools.tools

Partial Class UserControl_WUC_RecipeUpload
    Inherits UserControl

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing

    Public mMenuName As String = String.Empty
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty

    Private m_rec_id As Integer = m_InvalidId
    Private m_mtr_id As Integer = m_InvalidId
    Private m_mrpt_id_upload_section As Integer = m_InvalidId

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
            Exit Sub
        End If

        If Current.Request.QueryString("id") IsNot Nothing AndAlso Current.Request.QueryString("id").ToString <> String.Empty Then
            m_rec_id = Current.Request.QueryString("id")
        End If

        If Current.Request.QueryString("MTR_ID") IsNot Nothing AndAlso Current.Request.QueryString("MTR_ID").ToString <> String.Empty Then
            m_mtr_id = Current.Request.QueryString("MTR_ID")
        End If

        If Not Page.IsPostBack Then
            Me.btnUpload.Text = m_config.GetEntryByKeyName("RCP_UPLOAD").GetValue()

            DrawUploadSection()
        End If
    End Sub

    ' disegno nella tendina i mrpt referenziati da upload tag aggiungendo "All" per permettere l'upload di tutti i parametri contemporaneamente.
    ' Se non ci sono MRP con upload tag nascondo la sezione di upload
    Protected Sub DrawUploadSection()

        If WebDataBaseLayer.DataBase.TableExist("META_RECIPE_PARAMS") AndAlso
            WebDataBaseLayer.DataBase.TableExist("META_RECIPE_PARAM_TYPE") Then

            Dim sSelect As String = "SELECT DISTINCT(MRPT_ID), DESCRIPTION FROM META_RECIPE_PARAMS INNER JOIN META_RECIPE_PARAM_TYPE " &
                "ON META_RECIPE_PARAMS.MRPT_ID = META_RECIPE_PARAM_TYPE.ID WHERE UPLOAD_TAG IS NOT NULL AND MTR_ID = " & m_mtr_id

            Dim dt As Data.DataTable
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            Dim dt2 As New Data.DataTable

            dt2.Columns.Add("ID")
            dt2.Columns.Add("DESCRIPTION")

            If dt.Rows.Count > 1 Then
                Dim dr_all As Data.DataRow
                dr_all = dt2.NewRow
                dr_all("ID") = 0
                dr_all("DESCRIPTION") = m_config.GetEntryByKeyName("RCP_ALL_PARAMS").GetValue

                dt2.Rows.Add(dr_all)
            End If

            For Each dr As DataRow In dt.Rows
                Dim dr2 As Data.DataRow
                dr2 = dt2.NewRow
                dr2("ID") = dr("MRPT_ID")
                dr2("DESCRIPTION") = m_config.GetEntryByKeyName(dr("DESCRIPTION")).GetValue

                dt2.Rows.Add(dr2)
            Next

            Me.ddl_mrpt.DataSource = dt2
            Me.ddl_mrpt.DataTextField = "DESCRIPTION"
            Me.ddl_mrpt.DataValueField = "ID"
            Me.ddl_mrpt.DataBind()
        End If
    End Sub

    Protected Sub btnUpload_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnUpload.Click
        Dim sDelete As String = String.Empty

        m_mrpt_id_upload_section = ddl_mrpt.SelectedValue

        Dim req As New CommandInterface(EnumRequest.UploadRecipeParameters, m_rec_id, m_mrpt_id_upload_section)
        req.PostUniqueRequest()

        CleanOldUpdatedValueBackground()

        Dim bOk As Boolean = False
        While Not bOk
            bOk = myFunction.WaitCompleted(mScreen, EnumWaitOperation.Upload)
        End While

        ShowUpdatedValues()
    End Sub

    Private Sub CleanOldUpdatedValueBackground()
        Dim ctrl As Control
        Dim m_script As String = String.Empty

        For Each f As Field In mScreen.EditFields
            ctrl = Nothing

            ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb)
            If ctrl IsNot Nothing Then
                m_script &= "UpdateRecipe.cleanBG('" & ctrl.ClientID & "');"
            End If
        Next

        myScript.InvokeJS(Me.Page, m_script)
    End Sub

    Private Sub ShowUpdatedValues()
        Dim ctrl As Control
        Dim m_text As myTextBox = Nothing
        Dim m_ddl As myDropDownList = Nothing
        Dim m_chk As myCheckBox = Nothing
        Dim m_radio As myRadioButton = Nothing
        Dim sSelect As String = String.Empty
        Dim value As String = String.Empty
        Dim value_upl As String = String.Empty
        Dim rcp_par_name As String = String.Empty
        Dim m_script As String = String.Empty

        Dim temp_str As String = String.Empty
        Dim dt As Data.DataTable

        sSelect = "SELECT ASP_NAME, FIELD_VALUE FROM VIEW_RECIPE_PARAM_UPLOAD_VALUES WHERE REC_ID = '" & m_rec_id & "'"

        If (m_mrpt_id_upload_section <> META_RECIPE_PARAMETER_TYPE_ALL) Then
            sSelect &= " AND MRPT_ID = '" & m_mrpt_id_upload_section & "'"
        End If

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

            For Each dr As Data.DataRow In dt.Rows
                rcp_par_name = dr.Item(0).ToString
                value_upl = dr.Item(1).ToString

                If value_upl <> String.Empty Then

                    For Each f As Field In mScreen.EditFields

                        If rcp_par_name = f.FieldDb Then

                            ' retrieve control in page
                            Select Case f.GetFieldType
                                Case EnumFieldType.FieldNumber
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb)
                                    If ctrl IsNot Nothing Then
                                        m_text = CType(ctrl, myTextBox)

                                        If m_text.Text <> String.Empty Then
                                            value = m_text.Text.Trim
                                        Else
                                            value = "-1" 'invalido
                                        End If

                                        If CDbl(value) <> CDbl(value_upl.ToString) Then
                                            m_script &= "UpdateRecipe.value('" & m_text.ClientID & "', '" & CDbl(value_upl.ToString) & "');"
                                        End If
                                    End If

                                Case EnumFieldType.FieldString
                                    ' non entra perchè c++ non ha una TagReadStrErr
                                    'ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb)
                                    'If ctrl IsNot Nothing Then
                                    '    m_text = CType(ctrl, myTextBox)

                                    '    If m_text.Text <> value.ToString Then
                                    '        m_script &= "UpdateRecipe.value('" & m_text.ClientID & "', '" & value & "');"
                                    '    End If
                                    'End If

                                Case EnumFieldType.FieldList
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb)
                                    If ctrl IsNot Nothing Then
                                        m_ddl = CType(ctrl, myDropDownList)

                                        If m_ddl.SelectedIndex <> value_upl.ToString Then
                                            m_script &= "UpdateRecipe.value('" & m_ddl.ClientID & "', '" & value_upl & "');"
                                        End If
                                    End If

                                Case EnumFieldType.FieldCheckBoxOnOff
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb)
                                    If ctrl IsNot Nothing Then
                                        m_chk = CType(ctrl, myCheckBox)

                                        If m_chk.Checked And value_upl.ToString = m_StringOff Then
                                            m_script &= "UpdateRecipe.chkBox('" & m_chk.ClientID & "', false);"
                                        ElseIf Not m_chk.Checked And value_upl.ToString = m_StringOn Then
                                            m_script &= "UpdateRecipe.chkBox('" & m_chk.ClientID & "', true);"
                                        End If
                                    End If
                                Case EnumFieldType.FieldCheckBoxOneZero
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb)
                                    If ctrl IsNot Nothing Then
                                        m_chk = CType(ctrl, myCheckBox)

                                        If m_chk.Checked And value_upl.ToString = m_StringOff Then
                                            m_script &= "UpdateRecipe.chkBox('" & m_chk.ClientID & "', false);"
                                        ElseIf Not m_chk.Checked And value_upl.ToString = m_StringOn Then
                                            m_script &= "UpdateRecipe.chkBox('" & m_chk.ClientID & "', true);"
                                        End If
                                    End If
                                Case EnumFieldType.FieldCheckBoxYesNo
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb)
                                    If ctrl IsNot Nothing Then
                                        m_chk = CType(ctrl, myCheckBox)

                                        If m_chk.Checked And value_upl.ToString = m_StringOff Then
                                            m_script &= "UpdateRecipe.chkBox('" & m_chk.ClientID & "', false);"
                                        ElseIf Not m_chk.Checked And value_upl.ToString = m_StringOn Then
                                            m_script &= "UpdateRecipe.chkBox('" & m_chk.ClientID & "', true);"
                                        End If
                                    End If
                                Case EnumFieldType.FieldRadioButton
                                    ' check A position
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb & "_1")
                                    If ctrl IsNot Nothing Then
                                        m_radio = CType(ctrl, myRadioButton)

                                        If Not m_radio.Checked And value_upl.ToString = "1" Then
                                            m_script &= "UpdateRecipe.radio('" & m_radio.ClientID & "');"
                                            Continue For
                                        End If

                                    End If

                                    ' check B position
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb & "_2")
                                    If ctrl IsNot Nothing Then
                                        m_radio = CType(ctrl, myRadioButton)

                                        If Not m_radio.Checked And value_upl.ToString = "2" Then
                                            m_script &= "UpdateRecipe.radio('" & m_radio.ClientID & "');"
                                            Continue For
                                        End If

                                    End If

                                    ' check C position
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb & "_3")
                                    If ctrl IsNot Nothing Then
                                        m_radio = CType(ctrl, myRadioButton)

                                        If Not m_radio.Checked And value_upl.ToString = "3" Then
                                            m_script &= "UpdateRecipe.radio('" & m_radio.ClientID & "');"
                                            Continue For
                                        End If

                                    End If

                                    ' check D position
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb & "_4")
                                    If ctrl IsNot Nothing Then
                                        m_radio = CType(ctrl, myRadioButton)

                                        If Not m_radio.Checked And value_upl.ToString = "4" Then
                                            m_script &= "UpdateRecipe.radio('" & m_radio.ClientID & "');"
                                            Continue For
                                        End If

                                    End If

                                    ' check E position
                                    ctrl = UsersGUI.tools.FindControlRecursive(Me.Page, f.FieldDb & "_5")
                                    If ctrl IsNot Nothing Then
                                        m_radio = CType(ctrl, myRadioButton)

                                        If Not m_radio.Checked And value_upl.ToString = "5" Then
                                            m_script &= "UpdateRecipe.radio('" & m_radio.ClientID & "');"
                                            Continue For
                                        End If

                                    End If

                            End Select

                            Exit For
                        End If
                    Next
                End If
            Next
        End If

        m_script &= "myLoader.stop();"

        myScript.InvokeJS(Me.Page, m_script)
    End Sub

End Class