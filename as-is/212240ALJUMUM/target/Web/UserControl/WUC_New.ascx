﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_New.ascx.vb" Inherits="WUC_WUC_New" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>

<div class="tabMrg3 tabMrg3Top">
    <div id="print_area">
        <div class="customerHeader"></div>
        <table id="titleTable" class="tabMrg3">
            <asp:HiddenField ID="HiddenAskUserUnfilledDataCycle" runat="server" />
            <asp:HiddenField ID="HiddenAskUserConfirmDataCycle" runat="server" />
            <tr class="noPrint">
                <td class="bgImg2">&nbsp;
                </td>
                <td class="tabBground1">&nbsp;
                </td>
            </tr>
            <tr>
                <td id="pageTitle" colspan="2" class="txtLeft txtBold">
                    <%= If(mPageName IsNot Nothing, m_config.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue(), "")%>
                </td>
            </tr>
        </table>
        <table class="tabDimRid1">
            <tr>
                <td>
                    <div id="dView" runat="server" visible="true">
                        <table id="tblNew" runat="server">
                        </table>
                        <br />
                        <table id="tblButtons" runat="server" class="noPrint">
                            <tr>
                                <td class="txtLeft">
                                    <asp:Button ID="btnSubmit" runat="server" OnClientClick="myLoader.start()" CssClass="btnGeneral" CausesValidation="true" />&nbsp;
                                    <asp:Button ID="btnCancel" runat="server" CssClass="btnGeneral" />
                                </td>
                            </tr>
                        </table>
                        <br />
                        <table id="tblCompare" runat="server" class="noPrint">
                        </table>
                    </div>
                    <div id="dError" runat="server" visible="false">
                        <div class="verticalSpace"></div>
                        <table id="Table1" runat="server">
                            <tr>
                                <td>
                                    <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                                </td>
                            </tr>
                        </table>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>

<div id="div_blanket" runat="server" class="blanket" blanket></div>

<script type="text/javascript">
    blanket.show();
</script>