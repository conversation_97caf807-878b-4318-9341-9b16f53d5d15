﻿Imports UsersGUI
Imports WebDataBaseLayer
Imports System.Web.HttpContext

Partial Class UserControl_WUC_StatusFull
    Inherits UserControl

    Public m_config As UsersGUI.config = Nothing
    Private mMenuName As String
    Public mPageName As String
    Private mScreen As Screen
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Private b_enable_erp_integration_sync As Boolean = False

    Protected Sub Page_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded
        If Me.Visible Then
            Dim b_at_least_one_cpu_off As Boolean = False
            Dim languages_visible_count As Integer = 0

            m_config = CType(Application("Config"), UsersGUI.config)

            If Not WebTools.tools.GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
                lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If

            If Current.Request.QueryString("setLanguage") IsNot Nothing Then
                Dim new_language As EnumTranslation
                new_language = UsersGUI.tools.String2Enum(new_language.GetType, Current.Request.QueryString("setLanguage").ToString)
                SetLanguage(new_language)
            End If

            ' tblLanguageTitle
            Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
            tblLanguageTitle.Rows.Add(row)
            Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
            tc.InnerText = m_config.GetEntryByKeyName("MAIN_LANGUAGES_TITLE").GetValue
            tblLanguageTitle.Rows(tblLanguageTitle.Rows.Count - 1).Cells.Add(tc)

            ' tblLanguage
            Dim row2 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblLanguage.Rows.Add(row2)

            For Each l As Language In m_config.GetListLanguages
                If l.Visible Then
                    Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim link As New HyperLink

                    link.ImageUrl = "~/image/" & l.ImageFlagName
                    link.NavigateUrl = "~/default.aspx?control=status&pagename=status&menuname=MENU_SYSTEM" & "&setLanguage=" & l.Type.ToString
                    tc2.Controls.Add(link)
                    tblLanguage.Rows(tblLanguage.Rows.Count - 1).Cells.Add(tc2)

                    languages_visible_count += 1
                End If
            Next

            ' se ho più di una lingua mostro il div per il cambio lingua, altrimenti lo nascondo
            If languages_visible_count > 1 Then
                divLanguage.Visible = True
            Else
                divLanguage.Visible = False
            End If

            ' tblTimeTitle
            Dim row3 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblTimeTitle.Rows.Add(row3)
            Dim tc3 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc3.InnerText = m_config.GetEntryByKeyName("MAIN_TIME_TITLE").GetValue
            tblTimeTitle.Rows(tblTimeTitle.Rows.Count - 1).Cells.Add(tc3)

            ' tblTime
            ' 1. @mill time
            Dim row4 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblTime.Rows.Add(row4)

            Dim tc4 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc4.InnerText = m_config.GetEntryByKeyName("ATMILL_TIME").GetValue()
            tblTime.Rows(tblTime.Rows.Count - 1).Cells.Add(tc4)

            Dim tc5 As New System.Web.UI.HtmlControls.HtmlTableCell
            tblTime.Rows(tblTime.Rows.Count - 1).Cells.Add(tc5)

            Dim tc6 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc6.InnerText = Now.ToString(m_config.GetLanguage().FormatDateTime)
            tblTime.Rows(tblTime.Rows.Count - 1).Cells.Add(tc6)

            ' 2. PLC time
            Dim sSelect As String = String.Empty
            Dim dt As Data.DataTable

            sSelect = "SELECT DESCRIPTION, STATUS, STATUS_LED, CAN_CHANGE_STATUS, YEAR, MONTH, DAY, HOUR, MINUTE, SECOND FROM VIEW_SYSTEM_ZBUS"
            dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

            If Not dt Is Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows
                    Dim row5 As New System.Web.UI.HtmlControls.HtmlTableRow
                    tblTime.Rows.Add(row5)

                    Dim tc7 As New System.Web.UI.HtmlControls.HtmlTableCell
                    tc7.InnerText = m_config.GetEntryByKeyName(dr("DESCRIPTION")).GetValue
                    tblTime.Rows(tblTime.Rows.Count - 1).Cells.Add(tc7)

                    Dim tc8 As New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim img As New System.Web.UI.HtmlControls.HtmlImage
                    img.Src = dr("STATUS_LED").ToString
                    tc8.Controls.Add(img)
                    tblTime.Rows(tblTime.Rows.Count - 1).Cells.Add(tc8)

                    Dim tc9 As New System.Web.UI.HtmlControls.HtmlTableCell

                    If Not (IsDBNull(dr("YEAR")) OrElse IsDBNull(dr("MONTH")) OrElse IsDBNull(dr("DAY")) OrElse
                                    IsDBNull(dr("HOUR")) OrElse IsDBNull(dr("MINUTE")) OrElse IsDBNull(dr("SECOND"))) Then

                        Dim year_str As String = dr("YEAR").ToString

                        If (year_str.Length = 2) Then
                            year_str = "20" & year_str
                        End If

                        Dim plc_date As New DateTime(year_str, dr("MONTH").ToString, dr("DAY").ToString,
                                            dr("HOUR").ToString, dr("MINUTE").ToString, dr("SECOND").ToString)

                        tc9.InnerText = plc_date.ToString(m_config.GetLanguage().FormatDateTime)
                    Else
                        tc9.InnerText = String.Empty
                    End If

                    tblTime.Rows(tblTime.Rows.Count - 1).Cells.Add(tc9)

                    If dr("STATUS") = costanti.m_StringOff AndAlso dr("CAN_CHANGE_STATUS") = costanti.m_StringYes Then
                        b_at_least_one_cpu_off = True
                    End If
                Next
            End If

            ' abilitazione manuale CPU
            If b_at_least_one_cpu_off Then
                btnEnableCPU.Visible = True
            Else
                btnEnableCPU.Visible = False
            End If

            ' tblForceSyncBrdhnd
            Dim row7 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblForceSyncBrdhnd.Rows.Add(row7)
            Dim tc11 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc11.InnerText = m_config.GetEntryByKeyName("MAIN_BRDHND_TITLE").GetValue
            tblForceSyncBrdhnd.Rows(tblForceSyncBrdhnd.Rows.Count - 1).Cells.Add(tc11)

            ' div ForceSyncBrdhnd visibility
            dForceSyncBrdhnd.Visible = b_enable_erp_integration_sync

            ' tblSystemUtilitiesTitle
            Dim row9 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblSystemUtilitiesTitle.Rows.Add(row9)
            row9.Attributes("class") = "rowSection"
            Dim tc13 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc13.InnerText = m_config.GetEntryByKeyName("MAIN_SYSTEM_UTILITIES_TITLE").GetValue
            tblSystemUtilitiesTitle.Rows(tblSystemUtilitiesTitle.Rows.Count - 1).Cells.Add(tc13)

            ' tblReloadXmlTitle
            Dim row6 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblReloadXmlTitle.Rows.Add(row6)
            Dim tc10 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc10.InnerText = m_config.GetEntryByKeyName("MAIN_RELOAD_XML_TITLE").GetValue
            tblReloadXmlTitle.Rows(tblReloadXmlTitle.Rows.Count - 1).Cells.Add(tc10)

            ' tblForceClientUpdate
            Dim row8 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblForceClientUpdate.Rows.Add(row8)
            Dim tc12 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc12.InnerText = m_config.GetEntryByKeyName("MAIN_RELOAD_CLIENT_RESOURCES_TITLE").GetValue
            tblForceClientUpdate.Rows(tblForceClientUpdate.Rows.Count - 1).Cells.Add(tc12)

            ' Dll Version
            Dim row10 As New System.Web.UI.HtmlControls.HtmlTableRow
            tblVersionInfo.Rows.Add(row10)
            Dim tc15 As New System.Web.UI.HtmlControls.HtmlTableCell
            tc15.InnerText = m_config.GetEntryByKeyName("MAIN_VERSION_INFO_TITLE").GetValue
            tblVersionInfo.Rows(tblForceClientUpdate.Rows.Count - 1).Cells.Add(tc15)

            ' div SystemUtilities visibility
            Try
                If Long.Parse(Current.Session("UserID").ToString) = costanti.m_SystemIdUser Then
                    dSystemUtilities.Visible = True
                End If
            Catch ex As Exception
            End Try

            If Not Page.IsPostBack Then
                Me.btnEnableCPU.Text = m_config.GetEntryByKeyName("ENABLE_CPU").GetValue()
                Me.btnForceSyncBrdhnd.Text = m_config.GetEntryByKeyName("SYNC_BRDHND").GetValue()
                Me.btnReloadXml.Text = m_config.GetEntryByKeyName("RELOAD_LINK").GetValue()
                Me.btnReloadClientResources.Text = m_config.GetEntryByKeyName("RELOAD_LINK").GetValue()

                ' lblLastClientUpdate
                Me.lblLastClientUpdate.Text = m_config.GetEntryByKeyName("LAST_CLIENT_UPDATE").GetValue() & ": " & myScript.GetLastClientUpdateDate().ToString
            End If
        End If
    End Sub

    Protected Sub btnbtnForceSyncBrdhnd_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnForceSyncBrdhnd.Click
        Dim req As New CommandInterface(EnumRequest.ImportDataFromERP)
        req.PostUniqueRequest()

        Response.Redirect(myScript.AppendSessionIdToUrl(costanti.a_home))
    End Sub

    Protected Sub btnReloadClientResources_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnReloadClientResources.Click
        myScript.TriggerClientUpdate()

        ' Update lblLastClientUpdate
        Me.lblLastClientUpdate.Text = m_config.GetEntryByKeyName("LAST_CLIENT_UPDATE").GetValue() & ": " & myScript.GetLastClientUpdateDate().ToString
    End Sub

    Private Sub SetLanguage(new_language As EnumTranslation)

        Current.Application("Language") = Nothing

        '1.
        Select Case (new_language)
            Case EnumTranslation.Francese
                Me.m_config.SetCurrentLanguage = EnumTranslation.Francese
            Case EnumTranslation.Inglese
                Me.m_config.SetCurrentLanguage = EnumTranslation.Inglese
            Case EnumTranslation.Italiano
                Me.m_config.SetCurrentLanguage = EnumTranslation.Italiano
            Case EnumTranslation.Spagnolo
                Me.m_config.SetCurrentLanguage = EnumTranslation.Spagnolo
            Case EnumTranslation.Russo
                Me.m_config.SetCurrentLanguage = EnumTranslation.Russo
            Case EnumTranslation.Portoghese
                Me.m_config.SetCurrentLanguage = EnumTranslation.Portoghese
            Case EnumTranslation.Tedesco
                Me.m_config.SetCurrentLanguage = EnumTranslation.Tedesco
            Case EnumTranslation.Indiano
                Me.m_config.SetCurrentLanguage = EnumTranslation.Indiano
            Case EnumTranslation.Arabo
                Me.m_config.SetCurrentLanguage = EnumTranslation.Arabo
            Case EnumTranslation.Altro
                Me.m_config.SetCurrentLanguage = EnumTranslation.Altro
        End Select

        '2.
        Me.m_config.ReLoadLanguagesXml()

        '3.
        Current.Application("Language") = m_config.GetCurrentLanguage

        Current.Session("updateLanguagesJS") = m_StringYes

        Current.Response.Redirect(myScript.AppendSessionIdToUrl(costanti.a_home))
    End Sub

    Protected Sub btnReloadXml_Click(sender As Object, e As EventArgs) Handles btnReloadXml.Click
        Dim m_config_asa As UsersGUI.config
        m_config_asa = New UsersGUI.config(Server.MapPath("~") & "\" & UsersGUI.costanti.m_ConfigXml, Server.MapPath("~") & "\" & UsersGUI.costanti.m_ConfigXmlLanguages)
        Application("Config") = m_config_asa
        Application("Language") = m_config_asa.GetCurrentLanguage

        Current.Session("updateLanguagesJS") = m_StringYes

        Current.Response.Redirect(myScript.AppendSessionIdToUrl(costanti.a_home))
    End Sub

    Protected Sub btnEnableCPU_Click(sender As Object, e As EventArgs) Handles btnEnableCPU.Click
        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable

        sSelect = "SELECT ID FROM SYSTEM_ZBUS WHERE STATUS = '" & costanti.m_StringOff & "' AND CAN_CHANGE_STATUS = '" & costanti.m_StringYes & "'"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If Not dt Is Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As Data.DataRow In dt.Rows
                Dim req As New CommandInterface(EnumRequest.EnableCpu, dr("ID"), costanti.m_StringOn)
                req.PostUniqueRequest()
            Next
        End If

        Current.Response.Redirect(myScript.AppendSessionIdToUrl(costanti.a_home))
    End Sub

End Class