﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_Procexecution.ascx.vb"
    Inherits="WUC_WUC_Procexecution" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>

<div id="status" runat="server" visible="true">
    <table class="tabMrg3">
        <tr>
            <td class="bgImg2">&nbsp;
            </td>
            <td class="tabBground1">&nbsp;
            </td>
        </tr>
        <tr>
            <td colspan="2" class="txtLeft txtBold">
                <%= If(mPageName IsNot Nothing, m_config.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue(), "")%>
            </td>
        </tr>
    </table>
    <table id="tblProcExecution" runat="server">
        <tr class="rowOdd">
            <td>
                <asp:Label ID="lblEquipment" runat="server" CssClass="label-text"></asp:Label>
            </td>
            <td>
                <asp:Label ID="txtEquipment" runat="server"></asp:Label>
            </td>
        </tr>
        <tr class="rowEven">
            <td>
                <asp:Label ID="lblProcedure" runat="server" CssClass="label-text"></asp:Label>
            </td>
            <td>
                <asp:Label ID="txtProcedure" runat="server"></asp:Label>
            </td>
        </tr>
        <tr class="rowOdd">
            <td>
                <asp:Label ID="lblDescription" runat="server" CssClass="label-text"></asp:Label>
            </td>
            <td>
                <asp:Label ID="txtDescription" runat="server"></asp:Label>
            </td>
        </tr>
        <tr class="rowEven">
            <td>
                <asp:Label ID="lblInterventionReport" runat="server" CssClass="label-text"></asp:Label>
            </td>
            <td>
                <asp:TextBox ID="txtInterventionReport" runat="server" TextMode="MultiLine"
                    CssClass="textbox-text" onKeyUp="CheckMaxLength(this,4096)" onChange="CheckMaxLength(this,4096)"></asp:TextBox>
            </td>
        </tr>
        <tr>
            <td />
            <td>
                <asp:Button ID="btnConferma" runat="server" CssClass="btnGeneral" />
                <asp:Button ID="btnAnnulla" runat="server" CssClass="btnGeneral" />
            </td>
        </tr>
    </table>
</div>
<div id="dViewError" runat="server" visible="false">
    <table id="Table2" runat="server">
        <tr>
            <td>
                <asp:Label runat="server" ID="lblError" class="lblError"></asp:Label>
            </td>
        </tr>
    </table>
</div>