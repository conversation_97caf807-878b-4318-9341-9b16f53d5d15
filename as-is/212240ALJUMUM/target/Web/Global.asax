﻿<%@ Application Language="VB" %>
<%@ Import Namespace="System.IO" %>
<script RunAt="server">

    Sub Application_Start(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs on application startup
        Application("AtMillConfig") = New AtMillConfig.AtMillConfig(Server.MapPath("~") & "\PathConfig.xml", False, False)

        Dim m_config_asa As UsersGUI.config
        m_config_asa = New UsersGUI.config(Path.Combine(Server.MapPath("~"), UsersGUI.costanti.m_ConfigXml), Path.Combine(Server.MapPath("~"), UsersGUI.costanti.m_ConfigXmlLanguages))
        Application("Config") = m_config_asa
        Application("Language") = m_config_asa.GetCurrentLanguage

        ' Cartella per grafici (Graphics.aspx)
        If Not Directory.Exists("C:\TempImageFiles\") Then
            Directory.CreateDirectory("C:\TempImageFiles\")
        End If

        ' Cartella per file di log
        If Not Directory.Exists(Path.Combine(Server.MapPath("~"), "diag")) Then
            Directory.CreateDirectory(Path.Combine(Server.MapPath("~"), "diag"))
        End If

        ' Debug flag of <compilation> (used in LogTools)
        Try
            Dim compilationConfig As Web.Configuration.CompilationSection = CType(System.Configuration.ConfigurationManager.GetSection("system.web/compilation"), Web.Configuration.CompilationSection)

            Application("Debug") = compilationConfig.Debug
        Catch ex As Exception
            Application("Debug") = False
        End Try

        myScript.TriggerClientUpdate()
    End Sub

    Sub Application_End(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs on application shutdown
    End Sub

    Sub Application_Error(ByVal sender As Object, ByVal e As EventArgs)
        If Server.GetLastError() IsNot Nothing Then
            ExceptionTools.LogUnhandledException(Server.GetLastError())

            ExceptionTools.HandleExceptionRedirect(Server.GetLastError())
        End If
    End Sub

    Sub Session_Start(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs when a new session is started

        Current.Session("updateLanguagesJS") = UsersGUI.costanti.m_StringYes

        myScript.UpdateJSResources(CType(Application("Config"), UsersGUI.config))

        myScript.ResetBrowserSessionIdentifiers()
    End Sub

    Sub Session_End(ByVal sender As Object, ByVal e As EventArgs)
        ' Code that runs when a session ends.
        ' Note: The Session_End event is raised only when the sessionstate mode
        ' is set to InProc in the Web.config file. If session mode is set to StateServer
        ' or SQLServer, the event is not raised.
    End Sub
</script>