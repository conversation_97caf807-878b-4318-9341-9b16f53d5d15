﻿Imports ReportsTools
Imports UsersGUI
Imports WebTools.tools

Partial Class _Reports
    Inherits myWebPage

    Public mConfig As config
    Private mPageName As String
    Private mMenuName As String

    Private mTypeReport As EnumTypeReport
    Private mYieldsId As Integer = 1

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        mConfig = CType(Application("Config"), config)

        ' Controlla se è necessario fare un update delle risorse del JS
        myScript.UpdateJSResources(mConfig)

        myScript.InvokeJS(Me.Page, "ShowUCCont();")

        If mConfig Is Nothing Then
            Exit Sub
        End If

        If Current.Request("pagename") Is Nothing Then
            Exit Sub
        End If
        mPageName = Current.Request("pagename").ToString

        If Current.Request("yieldsid") IsNot Nothing Then
            mYieldsId = Integer.Parse(Current.Request("yieldsid").ToString)
        End If

        If Current.Request("typereport") IsNot Nothing Then
            mTypeReport = UsersGUI.tools.String2Enum(mTypeReport.GetType, Current.Request("typereport").ToString)
        Else
            lblError.Text = "TypeReport not specified"
            Me.dView.Visible = False
            Me.dError.Visible = True

            Exit Sub
        End If

        If Current.Request("menuname") Is Nothing Then
            Exit Sub
        End If
        mMenuName = Current.Request("menuname").ToString

        CheckPageDB(mConfig, mMenuName, mPageName)
        If Not CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
            lblError.Text = mConfig.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
            Me.dView.Visible = False
            Me.dError.Visible = True
            Me.divExtra.Visible = False
            myScript.InvokeJS(Me.Page, "myLoader.stop();")
            Exit Sub
        End If

        If Not Page.IsPostBack Then

            Me.lblYieldsStartDate.Text = mConfig.GetEntryByKeyName("YIELDS_START_DATE").GetValue & ":"
            Me.lblYieldsStopDate.Text = mConfig.GetEntryByKeyName("YIELDS_STOP_DATE").GetValue & ":"
            Me.lblYieldsPeriodSelect.Text = mConfig.GetEntryByKeyName("YIELDS_PERIOD_SELECT").GetValue
            Me.lblJob.Text = mConfig.GetEntryByKeyName("Job").GetValue & ":"

            Me.btnConferma.Text = mConfig.GetEntryByKeyName("SUBMIT_BUTTON").GetValue

            If Current.Request("typereport") IsNot Nothing AndAlso Current.Request("typereport").ToString() <> "" Then
                ddlPeriodicTime = PopolaPeriodiByReport(ddlPeriodicTime, mConfig, Current.Request("typereport").ToString())
            Else
                ddlPeriodicTime = WebTools.tools.PopolaPeriodi(ddlPeriodicTime, mConfig)
            End If

            ddlPeriodicTime.DataBind()

            ddlJob = PopolaJob(ddlJob)
            ddlJob.DataBind()

            Me.LoadReportChooser(Me.rbl_chooser)
            Me.rbl_chooser.DataBind()
            Me.rbl_chooser.SelectedIndex = EnumReportChooser.ByPeriod
            rbl_chooser_SelectedIndexChanged(sender, e)

            Select Case mTypeReport
                Case EnumTypeReport.InstantReport, EnumTypeReport.InstantPowerReport, EnumTypeReport.ShipmentReport, EnumTypeReport.ParcelReport, EnumTypeReport.MixingReport
                    Me.divPeriodic.Visible = False
                    Me.divJob.Visible = False
                    Me.divBtnConferma.Visible = False
                    Me.divExtra.Visible = False
                    btnConferma_Click(sender, e)

                Case EnumTypeReport.PeriodicReport, EnumTypeReport.PeriodicPowerReport, EnumTypeReport.PeriodicShipmentReport, EnumTypeReport.ProductsSummationReport, EnumTypeReport.GrainsSummationReport
                    Me.divPeriodic.Visible = True
                    Me.divJob.Visible = False
                    Me.divBtnConferma.Visible = True
                    Me.ddlPeriodicTime.SelectedIndex = 1
                    btnConferma_Click(sender, e)

                Case EnumTypeReport.JobReport, EnumTypeReport.JobPowerReport
                    Me.divPeriodic.Visible = False
                    Me.divJob.Visible = True
                    Me.divBtnConferma.Visible = True

                    ' Select the desired job if JOB_ID is in the URL, otherwise the current active one
                    If Current.Request("JOB_ID") IsNot Nothing AndAlso Current.Request("JOB_ID") <> String.Empty AndAlso ddlJob.Items.FindByValue(Current.Request("JOB_ID").ToString()) IsNot Nothing Then
                        ddlJob.SelectedValue = Long.Parse(Current.Request("JOB_ID").ToString())
                    Else
                        Me.ddlJob.SelectedIndex = 1
                    End If

                    btnConferma_Click(sender, e)

                Case Else
                    lblError.Text = "TypeReport non configurato"
                    Me.dView.Visible = False
                    Me.dError.Visible = True
                    Exit Sub
            End Select
        End If
    End Sub

    Public Shared Function PopolaPeriodiByReport(ByVal cmb As DropDownList, ByVal m_config As UsersGUI.config, ByVal m_typereport As String) As DropDownList
        Dim i As Integer = 0
        Dim dt As New Data.DataTable
        dt.Columns.Add("VALUE")
        dt.Columns.Add("NAME")

        Dim dr As Data.DataRow

        dr = dt.NewRow
        dr("VALUE") = "useCalendars"
        dr("NAME") = String.Empty
        dt.Rows.Add(dr)

        Select Case m_typereport.ToLower()
            Case "grainssummationreport"

                dr = dt.NewRow
                dr("VALUE") = "currweek"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_WEEK").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "currmonth"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_MONTH").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "lastweek"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_WEEK").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "lastmonth"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_MONTH").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "AllData"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_ALL").GetValue
                dt.Rows.Add(dr)

                cmb.DataSource = dt
                cmb.DataTextField = "NAME"
                cmb.DataValueField = "VALUE"
            Case "productssummationreport"

                dr = dt.NewRow
                dr("VALUE") = "currweek"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_WEEK").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "currmonth"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_MONTH").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "lastweek"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_WEEK").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "lastmonth"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_MONTH").GetValue
                dt.Rows.Add(dr)

                dr = dt.NewRow
                dr("VALUE") = "AllData"
                dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_ALL").GetValue
                dt.Rows.Add(dr)

                cmb.DataSource = dt
                cmb.DataTextField = "NAME"
                cmb.DataValueField = "VALUE"
            Case Else
                cmb = WebTools.tools.PopolaPeriodi(cmb, m_config)
        End Select

        Return cmb
    End Function

    Private Function LoadReportChooser(ByVal rbl As RadioButtonList) As RadioButtonList

        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("STEP")

        Dim dr As Data.DataRow
        dr = dt.NewRow
        dr("ID") = EnumReportChooser.ByDate
        dr("STEP") = mConfig.GetEntryByKeyName("BY_DATE").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = EnumReportChooser.ByPeriod
        dr("STEP") = mConfig.GetEntryByKeyName("BY_PERIOD").GetValue
        dt.Rows.Add(dr)

        rbl.DataSource = dt
        rbl.DataTextField = "STEP"
        rbl.DataValueField = "ID"

        Return rbl
    End Function

    Protected Sub btnConferma_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnConferma.Click
        Dim frame_url As String = String.Empty
        Dim full_start_date As String = String.Empty
        Dim full_stop_date As String = String.Empty

        Select Case mTypeReport
            Case EnumTypeReport.InstantReport, EnumTypeReport.InstantPowerReport
                frame_url = "ReportYields.aspx?typereport=" & mTypeReport & "&yieldsid=" & mYieldsId

            Case EnumTypeReport.PeriodicReport, EnumTypeReport.PeriodicPowerReport, EnumTypeReport.PeriodicShipmentReport
                frame_url = "ReportYields.aspx?typereport=" & mTypeReport & "&yieldsid=" & mYieldsId

                If Me.ddlPeriodicTime.SelectedValue = "useCalendars" Then
                    If Me.start_date.Text <> String.Empty AndAlso IsDate(Me.start_date.Text) AndAlso
                        Me.stop_date.Text <> String.Empty AndAlso IsDate(Me.stop_date.Text) Then
                        full_start_date = UsersGUI.tools.ConvertDateTimeFormat(start_date.Text, mConfig.GetLanguage().FormatDateTimeCalendar, costanti.m_ODBCSqlDateTimeFormat)
                        full_stop_date = UsersGUI.tools.ConvertDateTimeFormat(stop_date.Text, mConfig.GetLanguage().FormatDateTimeCalendar, costanti.m_ODBCSqlDateTimeFormat)

                        frame_url &= "&start_date=" & full_start_date & "&stop_date=" & full_stop_date
                    Else
                        Exit Sub
                    End If

                ElseIf Me.ddlPeriodicTime.SelectedValue = "currhour" Then
                    frame_url &= "&currhour=1"
                Else
                    Dim _start_date, _stop_date As DateTime
                    If GetStartAndStopDateForPeriod(Me.ddlPeriodicTime.SelectedValue.ToString, _start_date, _stop_date) Then
                        frame_url &= "&start_date=" & _start_date.ToString(costanti.m_ODBCSqlDateTimeFormat) &
                            "&stop_date=" & _stop_date.ToString(costanti.m_ODBCSqlDateTimeFormat)
                    Else
                        Exit Sub
                    End If

                End If

            Case EnumTypeReport.JobReport
                frame_url = "ReportYields.aspx?typereport=" & mTypeReport & "&yieldsid=" & mYieldsId

                If ddlJob.SelectedIndex = 1 Then
                    ' potrebbe essere il job in corso

                    Dim act_job As Integer
                    If Not Integer.TryParse(GetParameter(myReports.YieldsConfig.GetActiveJobCounterParameterByWebId(mYieldsId)), act_job) Then
                        act_job = 0
                    End If

                    If act_job <> 0 Then
                        frame_url &= "&currjob=1"
                    End If
                End If

                If ddlJob.SelectedIndex <> 0 Then
                    frame_url &= "&job=" & ddlJob.SelectedValue
                Else
                    Exit Sub
                End If

            Case EnumTypeReport.JobPowerReport

            Case EnumTypeReport.ProductsSummationReport
                frame_url = "reports/ReportProductsSummation.aspx?typereport=" & mTypeReport
                If Current.Request("CYC_ID") IsNot Nothing Then
                    frame_url &= "&CYC_ID=" & Current.Request("CYC_ID").ToString()
                End If

                If Me.ddlPeriodicTime.SelectedValue = "useCalendars" Then
                    If Me.start_date.Text <> String.Empty AndAlso IsDate(Me.start_date.Text) AndAlso
                        Me.stop_date.Text <> String.Empty AndAlso IsDate(Me.stop_date.Text) Then
                        full_start_date = UsersGUI.tools.ConvertDateTimeFormat(start_date.Text, mConfig.GetLanguage().FormatDateTimeCalendar, costanti.m_ODBCSqlDateTimeFormat)
                        full_stop_date = UsersGUI.tools.ConvertDateTimeFormat(stop_date.Text, mConfig.GetLanguage().FormatDateTimeCalendar, costanti.m_ODBCSqlDateTimeFormat)

                        frame_url &= "&start_date=" & full_start_date & "&stop_date=" & full_stop_date
                    Else
                        Exit Sub
                    End If

                ElseIf Me.ddlPeriodicTime.SelectedValue = "currhour" Then
                    frame_url &= "&currhour=1"
                Else
                    Dim _start_date, _stop_date As DateTime
                    If GetStartAndStopDateForPeriod(Me.ddlPeriodicTime.SelectedValue.ToString, _start_date, _stop_date) Then
                        frame_url &= "&start_date=" & _start_date.ToString(costanti.m_ODBCSqlDateTimeFormat) &
                            "&stop_date=" & _stop_date.ToString(costanti.m_ODBCSqlDateTimeFormat)
                    Else
                        Exit Sub
                    End If

                End If

            Case EnumTypeReport.GrainsSummationReport
                frame_url = "reports/ReportGrainsSummation.aspx?typereport=" & mTypeReport
                If Current.Request("CYC_ID") IsNot Nothing Then
                    frame_url &= "&CYC_ID=" & Current.Request("CYC_ID").ToString()
                End If
                If Current.Request("pagename") IsNot Nothing Then
                    frame_url &= "&pagename=" & Current.Request("pagename").ToString()
                End If
                If Me.ddlPeriodicTime.SelectedValue = "useCalendars" Then
                    If Me.start_date.Text <> String.Empty AndAlso IsDate(Me.start_date.Text) AndAlso
                        Me.stop_date.Text <> String.Empty AndAlso IsDate(Me.stop_date.Text) Then
                        full_start_date = UsersGUI.tools.ConvertDateTimeFormat(start_date.Text, mConfig.GetLanguage().FormatDateTimeCalendar, costanti.m_ODBCSqlDateTimeFormat)
                        full_stop_date = UsersGUI.tools.ConvertDateTimeFormat(stop_date.Text, mConfig.GetLanguage().FormatDateTimeCalendar, costanti.m_ODBCSqlDateTimeFormat)

                        frame_url &= "&start_date=" & full_start_date & "&stop_date=" & full_stop_date
                    Else
                        Exit Sub
                    End If

                ElseIf Me.ddlPeriodicTime.SelectedValue = "currhour" Then
                    frame_url &= "&currhour=1"
                Else
                    Dim _start_date, _stop_date As DateTime
                    If GetStartAndStopDateForPeriod(Me.ddlPeriodicTime.SelectedValue.ToString, _start_date, _stop_date) Then
                        frame_url &= "&start_date=" & _start_date.ToString(costanti.m_ODBCSqlDateTimeFormat) &
                            "&stop_date=" & _stop_date.ToString(costanti.m_ODBCSqlDateTimeFormat)
                    Else
                        Exit Sub
                    End If

                End If
            Case Else
                Throw New Exception("Report.aspx - TypeReport non configurato")
        End Select

        ' entry del frame
        IFrameReport.Attributes.Add("src", myScript.AppendSessionIdToUrl(frame_url))
    End Sub

    Protected Sub rbl_chooser_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles rbl_chooser.SelectedIndexChanged
        Select Case rbl_chooser.SelectedIndex

            Case EnumReportChooser.ByDate
                'clear and hide Period section
                Me.ddlPeriodicTime.SelectedIndex = 0
                Me.tblByPeriod.Visible = False

                Me.tblByDate.Visible = True

            Case EnumReportChooser.ByPeriod
                'clear and hide Date section
                Me.start_date.Text = String.Empty
                Me.stop_date.Text = String.Empty
                Me.tblByDate.Visible = False

                Me.tblByPeriod.Visible = True

        End Select
    End Sub

    Private Function PopolaJob(ByVal cmb As DropDownList) As DropDownList
        Dim sSelect As String = String.Empty
        Dim act_job As Integer
        Dim dt_jobs As Data.DataTable

        Dim dt As New Data.DataTable
        dt.Columns.Add("JOB_ID")
        dt.Columns.Add("JOB_NAME")

        Dim dr As Data.DataRow

        dr = dt.NewRow
        dr("JOB_ID") = 0
        dr("JOB_NAME") = String.Empty
        dt.Rows.Add(dr)

        ' controllo l'esistenza di certe tabelle (che differiscono tra @mill e sistema rese)
        If WebDataBaseLayer.DataBase.TableExist("PRODUCTION_PLAN") AndAlso
            WebDataBaseLayer.DataBase.TableExist("RECIPES") AndAlso
            WebDataBaseLayer.DataBase.TableExist("PRODUCTION_REPORTS") Then

            ' Act Job
            If Not Integer.TryParse(GetParameter(myReports.YieldsConfig.GetActiveJobCounterParameterByWebId(mYieldsId)), act_job) Then
                act_job = 0
            End If

            If act_job <> 0 Then
                sSelect = " SELECT COUNTER AS JOB_ID, START_DATE, RECIPES.DESCRIPTION AS RECIPE_DESCRIPTION FROM PRODUCTION_PLAN INNER JOIN RECIPES ON PRODUCTION_PLAN.REC_ID = RECIPES.ID " &
                                "WHERE COUNTER ='" & act_job & "' " &
                                "UNION "
            End If

            ' Archived jobs
            sSelect &= "SELECT DISTINCT YIELDS_DATA.JOB_ID, PRODUCTION_REPORTS.START_DATE, PRODUCTION_REPORTS.RECIPE_DESCRIPTION " &
                                        "FROM YIELDS_DATA INNER JOIN PRODUCTION_REPORTS ON JOB_ID = COUNTER WHERE JOB_ID IS NOT NULL "
            sSelect &= "AND CYC_ID = " & myReports.YieldsConfig.GetCycleIdByWebId(mYieldsId) & " "

            sSelect &= "ORDER BY START_DATE DESC"

            dt_jobs = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt_jobs.Rows.Count > 0 Then
                For Each dr_jobs As Data.DataRow In dt_jobs.Rows
                    dr = dt.NewRow
                    dr("JOB_ID") = dr_jobs.Item(0).ToString
                    dr("JOB_NAME") = Long.Parse(dr_jobs.Item(0).ToString) & " - " & dr_jobs.Item(2).ToString & " - " & Date.Parse(dr_jobs.Item(1).ToString).Date
                    dt.Rows.Add(dr)
                Next
            End If
        End If

        cmb.DataSource = dt
        cmb.DataTextField = "JOB_NAME"
        cmb.DataValueField = "JOB_ID"

        Return cmb
    End Function

End Class