﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="BinLayers.aspx.vb" Inherits="BinLayers" %>

<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_Edit.ascx" TagPrefix="WUCEdit" TagName="eEdit" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
</head>
<body onload="InitPage();">
    <form id="form_BinLayers" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true">
        </asp:ScriptManager>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
                <div id="print_area">
                    <div id="customerHeader"></div>
                    <table id="titleTable" class="tabMrg3">
                        <tr class="noPrint">
                            <td width="200px" class="bgImg2">&nbsp;
                            </td>
                            <td class="tabBground1">&nbsp;
                            </td>
                        </tr>
                        <tr>
                            <td id="pageTitle" colspan="2" class="txtLeft txtBold">
                                <%
                                    If m_b_jobs Then
                                        Response.Write(m_config.GetEntryByKeyName("BINLAYER_JOBS_TITLE").GetValue())
                                    ElseIf m_b_lots Then
                                        Response.Write(m_config.GetEntryByKeyName("BINLAYER_LOTS_TITLE").GetValue())
                                    ElseIf m_b_stocks Then
                                        Response.Write(m_config.GetEntryByKeyName("BINLAYER_STOCKS_TITLE").GetValue())
                                    End If
                                %>
                            </td>
                        </tr>
                    </table>
                    <div id="dView" runat="server" visible="true" class="tabMrg3 tabMrg3Top">
                        <table id="tblHeader" runat="server" visible="true"></table>
                        <br />
                        <table>
                            <tr>
                                <td class="tdBin">

                                    <!-- tabella flussi di ingresso  -->
                                    <table id="tblFlowIn" runat="server">
                                    </table>

                                    <!-- Tabella della cella -->
                                    <table id="tblBin" class="tblBin" runat="server">
                                    </table>

                                    <!-- tabella flussi di uscita  -->
                                    <table id="tblFlowOut" runat="server">
                                    </table>
                                </td>

                                <!-- Gestione della legenda -->
                                <td class="tdLegenda">
                                    <table id="tblLegend" runat="server">
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="rightSection">
                <!-- Operazioni.... inizio -->
                <WUCOperation:eOperation ID="WebOperation" runat="server" />
                <!-- Operazioni.... fine-->

                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->
            </div>
        </div>
    </form>
</body>
</html>