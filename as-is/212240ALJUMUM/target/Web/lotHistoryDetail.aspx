﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="lotHistoryDetail.aspx.vb" Inherits="lots_lotHistoryDetail" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title><%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%></title>
    <link rel="stylesheet" type="text/css" href="<%= "./css/default.css" & myScript.GetQueryClientUpdate() %>" />
    <link rel="stylesheet" type="text/css" href="<%= "./css/print.css" & myScript.GetQueryClientUpdate() %>" media="print" />

    <link rel="stylesheet" type="text/css" href="<%= "./css/lots/index.css" & myScript.GetQueryClientUpdate() %>" />
    <link rel="stylesheet" type="text/css" href="<%= "./css/lots/print.css" & myScript.GetQueryClientUpdate() %>" media="print" />

    <script type="text/javascript" src="./js/jquery-1.9.1.js"></script>

    <script type="text/javascript" src="<%= "./js/common/constants.js" & myScript.GetQueryClientUpdate() %>"></script>

    <script type="text/javascript" src="<%= "./js/header/constants.js" & myScript.GetQueryClientUpdate() %>"></script>
    <script type="text/javascript" src="<%= "./js/header/config.js" & myScript.GetQueryClientUpdate() %>"></script>
    <script type="text/javascript" src="<%= "./js/header/index.js" & myScript.GetQueryClientUpdate() %>"></script>

    <script type="text/javascript" src="<%= "./js/myScript.js" & myScript.GetQueryClientUpdate() %>"></script>
    <script type="text/javascript" src="<%= "./js/EventInterface.js" & myScript.GetQueryClientUpdate() %>"></script>
</head>
<body onload="InitPage();">
    <form id="form_lotHistoryDetail" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true"></asp:ScriptManager>
        <div id="print_area">
            <div id="printContainer">
                <table class="lot_title_table">
                    <tr>
                        <td>
                            <asp:Label ID="lblTitolo" runat="server"></asp:Label><br />
                        </td>
                    </tr>
                </table>

                <br />

                <div id="div_lotInfo" class="lotSection" runat="server">
                    <asp:Label ID="lblLotInfo" runat="server"></asp:Label>
                    <table id="tblLotInfo" runat="server">
                        <!-- QTY update field and submit button -->
                        <tr style="display: none">
                            <td id="qty_td" runat="server" class="qty_td">
                                <div>
                                    <div id="qty_text" runat="server" name="qty_text"></div>
                                    <input type="text" id="qty_textbox" visible="false" runat="server" name="qty_textbox" class="textbox-text" maxlength="15" />
                                    <div id="unit_text" runat="server" name="unit_text" style="margin-left: 5px; margin-top: 2px; float: left"></div>
                                </div>
                            </td>
                        </tr>
                        <!-- AUTO_NOTES update field and submit button -->
                        <tr style="display: none">
                            <td id="autoNote_td" runat="server" class="autoNote_td">
                                <div>
                                    <div id="autoNote_text" runat="server" name="autoNote_text"></div>
                                    <input type="text" id="autoNote_textbox" visible="false" runat="server" name="autoNote_textbox" class="textbox-text" maxlength="255" style="width: 300px !important" />
                                </div>
                            </td>
                        </tr>
                        <!-- USER_NOTES update field and submit button -->
                        <tr style="display: none">
                            <td id="userNote_td" runat="server" class="userNote_td">
                                <div>
                                    <div id="userNote_text" runat="server" name="userNote_text"></div>
                                    <textarea id="userNote_textarea" visible="false" runat="server" name="userNote_textarea" class="textbox-text" cols="1" rows="1" maxlength="255"></textarea>
                                    <asp:Button ID="userNote_editmode" runat="server" CssClass="btnGeneral" />
                                    <asp:Button ID="userNote_submit" runat="server" CssClass="btnGeneral" Visible="false" />
                                    <asp:Button ID="userNote_cancel" runat="server" CssClass="btnGeneral" Visible="false" />
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div id="div_parcels" class="lotSection" runat="server">
                    <asp:Label ID="lblParcels" runat="server"></asp:Label>
                    <table id="tblParcels" runat="server"></table>
                </div>

                <div id="div_shipments" class="lotSection" runat="server" visible="false">
                    <asp:Label ID="lblShipments" runat="server"></asp:Label>
                    <table id="tblShipments" runat="server"></table>
                </div>

                <div id="div_productionReports" class="lotSection" runat="server" visible="false">
                    <asp:Label ID="lblProductionReports" runat="server"></asp:Label>
                    <table id="tblProductionReports" runat="server"></table>
                </div>

                <div id="div_reportYields" class="lotSection" runat="server" visible="false">
                    <asp:Label ID="lblReportYields" runat="server"></asp:Label>
                </div>

                <div id="div_lotsToCell" class="lotSection" runat="server">
                    <asp:Label ID="lblLotsToCell" runat="server"></asp:Label>
                    <table id="tblLotsToCell" runat="server"></table>
                </div>

                <div id="div_composizione" class="lotSection" runat="server">
                    <asp:Label ID="lblComposizione" runat="server"></asp:Label>
                    <table id="tblComposizione" runat="server"></table>
                </div>

                <div id="div_utilizzo" class="lotSection" runat="server">
                    <asp:Label ID="lblUtilizzo" runat="server"></asp:Label>
                    <table id="tblUtilizzo" runat="server"></table>
                </div>

                <div id="div_userNotes" class="lotSection" runat="server" visible="false">
                    <asp:Label ID="lblUserNotes" runat="server"></asp:Label>
                    <table id="tblUserNotes" runat="server"></table>
                </div>
            </div>
        </div>
    </form>
</body>
</html>