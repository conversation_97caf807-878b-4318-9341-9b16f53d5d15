﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="SsrsReports.aspx.vb" Inherits="ssrs_SsrsReports" %>
<%@ Register assembly="Microsoft.ReportViewer.WebForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" namespace="Microsoft.Reporting.WebForms" tagprefix="rsweb" %>

<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_Edit.ascx" TagPrefix="WUCEdit" TagName="eEdit" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
    <style type="text/css">
        .MSRS-RVC .NormalButton table,  
        .MSRS-RVC .HoverButton table,  
        .MSRS-RVC .DisabledButton table,  
        .MSRS-RVC .aspNetDisabled table {  
            width: 56px;  
            height: 100%;  
        }  
    </style>
</head>
<body onload="InitPage();">
    <form id="frmMainReport" runat="server">
        <asp:ScriptManager runat="server">
        </asp:ScriptManager>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
               <div id="dError" runat="server" visible="false">
                    <table id="Table1" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
                <rsweb:ReportViewer ID="ReportViewer1" runat="server" width="750px" Height="1000px" ShowParameterPrompts="false" >
                </rsweb:ReportViewer>
            </div>
            <div class="rightSection">
                
                <!-- Extra.... inizio -->
                <div id="divExtra" runat="server">
                    <div class="menuTOP">
                        <div class="txtCenter txtWhite txtBold">
                            <%= mConfig.GetEntryByKeyName("Selections").GetValue%>
                        </div>
                    </div>
                    <div id="divSelect" class="menuMID" runat="server">
                        <div id="divPeriodic" runat="server" visible="false">
                            <asp:RadioButtonList ID="rbl_chooser" runat="server" AutoPostBack="true"></asp:RadioButtonList>
                            <table id="tblByDate" runat="server" class="bgWhite">
                                <tr>
                                    <td>
                                        <asp:Label ID="lblYieldsStartDate" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="txtLeft">
                                        <asp:TextBox ID="start_date" runat="server" CssClass="text DateTimePicker_jq"
                                            datetimepicker="step:60;" type="text"></asp:TextBox>
                                    </td>
                                </tr>
                                <tr style="height: 5px">
                                </tr>
                                <tr>
                                    <td>
                                        <asp:Label ID="lblYieldsStopDate" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="txtLeft">
                                        <asp:TextBox ID="stop_date" runat="server" CssClass="text DateTimePicker_jq"
                                            datetimepicker="step:60;" type="text"></asp:TextBox>
                                    </td>
                                </tr>
                            </table>
                            <table id="tblByPeriod" runat="server" class="bgWhite">
                                <tr>
                                    <td>
                                        <asp:Label ID="lblYieldsPeriodSelect" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="txtLeft">
                                        <asp:DropDownList ID="ddlPeriodicTime" runat="server" CssClass="label-text">
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div id="divJob" runat="server" visible="false">
                            <table id="tblJob" runat="server">
                                <tr class="bgWhite">
                                    <td>
                                        <asp:Label ID="lblJob" runat="server"></asp:Label>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <asp:DropDownList ID="ddlJob" runat="server" CssClass="label-text exclude-length-check">
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div id="divBtnConferma" runat="server">
                            <div class="txtCenter">
                                <br />
                                <asp:Button ID="btnConferma" runat="server" CssClass="btnGeneral" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Extra.... fine-->
                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->
            </div>
        </div>
    </form>
</body>
</html>
    
