Imports UsersGUI

Partial Class _ReportShipmentParcel
    Inherits myWebPage

    Private mConfig As UsersGUI.config
    Private mTypeReport As EnumTypeReport
    Private m_counter As Long

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim script As String = String.Empty

        mConfig = CType(Application("Config"), UsersGUI.config)

        script &= ReportsTools.myReports.GetReportsTranslations(mConfig)

        If Current.Request("typereport") Is Nothing Then
            Exit Sub
        End If
        mTypeReport = Current.Request("typereport").ToString

        If Current.Request.QueryString("counter") IsNot Nothing AndAlso Current.Request.QueryString("counter") <> String.Empty Then
            Long.TryParse(Current.Request.QueryString("counter"), m_counter)
        Else
            Exit Sub
        End If

        Select Case mTypeReport
            Case EnumTypeReport.ShipmentReport
                script &= ReportsTools.ShipmentReport.GetShipmentScript(mConfig, m_counter)

            Case EnumTypeReport.ParcelReport
                script &= ReportsTools.ParcelReport.GetParcelScript(mConfig, m_counter)

        End Select

        myScript.InvokeJS(Me.Page, script)
    End Sub

End Class