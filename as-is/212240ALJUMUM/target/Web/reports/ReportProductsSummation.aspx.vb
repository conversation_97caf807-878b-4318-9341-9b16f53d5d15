Imports UsersGUI

Partial Class _ReportProductsSummation
    Inherits myWebPage

    Private mConfig As UsersGUI.config
    Private mTypeReport As EnumTypeReport
    Private start_date As Date
    Private stop_date As Date
    Private IdCycle As Integer = 0

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim script As String = String.Empty

        mConfig = CType(Application("Config"), UsersGUI.config)

        script &= ReportsTools.myReports.GetReportsTranslations(mConfig)

        If Current.Request("typereport") Is Nothing Then
            Exit Sub
        End If
        mTypeReport = Current.Request("typereport").ToString

        If Current.Request("start_date") IsNot Nothing Then
            start_date = Date.ParseExact(Current.Request("start_date").ToString,
                                                 costanti.m_ODBCSqlDateTimeFormat,
                                                 System.Threading.Thread.CurrentThread.CurrentCulture)
        End If

        If Current.Request("stop_date") IsNot Nothing Then
            stop_date = Date.ParseExact(Current.Request("stop_date").ToString,
                                                costanti.m_ODBCSqlDateTimeFormat,
                                                System.Threading.Thread.CurrentThread.CurrentCulture)
        End If

        If Current.Request("currhour") IsNot Nothing Then
            Timer1.Enabled = True
        Else
            Timer1.Enabled = False
        End If

        If Current.Request("disabletimer") IsNot Nothing Then
            Me.Timer1.Enabled = False
        End If

        If Current.Request("CYC_ID") IsNot Nothing Then
            IdCycle = Integer.Parse(Current.Request("CYC_ID").ToString())
            script &= ReportsTools.ProductsSummationReport.GetProductsSummationScript(mConfig, start_date, stop_date, Current.Request("currhour") IsNot Nothing, False, IdCycle)
        Else
        script &= ReportsTools.ProductsSummationReport.GetProductsSummationScript(mConfig, start_date, stop_date, Current.Request("currhour") IsNot Nothing, False)
        End If

        myScript.InvokeJS(Me.Page, script)

        Timer1_Tick(sender, e)
    End Sub

    Protected Sub Timer1_Tick(ByVal sender As Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        Dim script As String = String.Empty

        If IdCycle > 0 Then
            script &= ReportsTools.ProductsSummationReport.GetProductsSummationScript(mConfig, start_date, stop_date, Current.Request("currhour") IsNot Nothing, True, IdCycle)
        Else
        script &= ReportsTools.ProductsSummationReport.GetProductsSummationScript(mConfig, start_date, stop_date, Current.Request("currhour") IsNot Nothing, True)
        End If

        myScript.InvokeJS(Me.Page, script)
    End Sub

End Class