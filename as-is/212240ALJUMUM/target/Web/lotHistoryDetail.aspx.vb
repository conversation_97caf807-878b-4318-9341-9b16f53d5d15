﻿Option Strict On

Imports WebDataBaseLayer
Imports ReportsTools
Imports UsersGUI
Imports UnitsTools

Partial Class lots_lotHistoryDetail
    Inherits myWebPage

    Private lot_id As Long
    Private lot_type As Integer
    Private lot_creation_date As Date
    Private b_only_initial_or_final_lots As Boolean
    Private lot_mode As LotMode

    Private str_sql As String = String.Empty
    Private dt As Data.DataTable
    Private lot_amount As Double = 0.0
    Private lot_amount_in_bins As Double = 0.0
    Private lot_amount_to_be_historicized As Double = 0.0
    Private counter As Integer = 0
    Private mConfig As config
    Private sessionId As String

    ' configurazione unità di misura a DB ed ad interfaccia utente
    Private Class CfgMeasurementUnit

        Public Const WaterLotType As SSLotTypes = SSLotTypes.H

        Public Class Database
            Public Const Lot As MeasurementUnit = MeasurementUnit.Kg
            Public Const LotHistory As MeasurementUnit = MeasurementUnit.Kg
            Public Const LotsToCell As MeasurementUnit = MeasurementUnit.Kg
            Public Const Water As MeasurementUnit = MeasurementUnit.L
        End Class

        Public Class UserInterface

            Public Class StandardAmounts
                Public Const Lot As MeasurementUnit = MeasurementUnit.Ton
                Public Const LotHistory As MeasurementUnit = MeasurementUnit.Ton
                Public Const LotsToCell As MeasurementUnit = MeasurementUnit.Ton
                Public Const Water As MeasurementUnit = MeasurementUnit.L
            End Class

            ' quantità (espressa in unità base del database [kg/lbs]) oltre la quale considero le unità di misura standard, altriemnti se inferiore quelle little amounts
            Public Class StandardVsLittle
                Public Const Lot As Double = 1
                Public Const LotHistory As Double = 1
                Public Const LotsToCell As Double = 1
                Public Const Water As Double = 0
            End Class

            Public Class LittleAmounts
                Public Const Lot As MeasurementUnit = MeasurementUnit.Kg
                Public Const LotHistory As MeasurementUnit = MeasurementUnit.Kg
                Public Const LotsToCell As MeasurementUnit = MeasurementUnit.Kg
                Public Const Water As MeasurementUnit = MeasurementUnit.L
            End Class

        End Class

    End Class

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Current.Request.QueryString("session") IsNot Nothing Then
            sessionId = Current.Request.QueryString("session")
            mConfig = CType(Application("Config"), config)

            If mConfig Is Nothing Then
                Exit Sub
            End If

            ' Controlla se è necessario fare un update delle risorse del JS
            myScript.UpdateJSResources(mConfig)

            If Request.QueryString("LO_ID") <> "" Then
                lot_id = Long.Parse(Request.QueryString("LO_ID"))
            ElseIf Request.QueryString("ID") <> "" Then
                lot_id = Long.Parse(Request.QueryString("ID"))
            End If

            Current.Session(sessionId & "_mLotId_tosource") = lot_id

            If Request("Comp") <> "" Then
                b_only_initial_or_final_lots = CBool(Request("Comp"))
            Else
                b_only_initial_or_final_lots = True
            End If

            If Current.Request.QueryString("lotmode") IsNot Nothing Then
                lot_mode = CType(Integer.Parse(Current.Request.QueryString("lotmode")), LotMode)
            Else
                ' Default
                lot_mode = LotMode.Tracciabilità
            End If

            If Not Page.IsPostBack Then

                'Intestazione del lotto (LOTS) da chiamare prima di altre funzioni
                Me.GetLotInfo()

                'Gestione LotsToCell (LOTS_TO_CELL) da chiamare prima di altre funzioni
                Me.GetLotsToCell()

                'Gestione della composizione
                Me.GetComposizioneEUtilizzo()

                'Rapporto di resa
                Me.GetRapportoResa()

                'Gestione rapporti di produzione
                Me.GetProductionReports()

                'Gestione ricevimenti relativi
                Me.GetParcels()

                'Gestione spedizioni relative
                Me.GetShipments()

                ' User notes on the lots of the tree
                Me.GetTreeUserNotes()
            End If

            ' Handle edit mode for USER_NOTES
            If WebTools.tools.CheckAccess(mConfig, "MENU_LOTS", "lothistory", UsersGUI.EnumAccessLevel.Editing) Then
                If Current.Request.QueryString("edit") IsNot Nothing AndAlso Current.Request.QueryString("edit").ToUpper = UsersGUI.costanti.m_StringYes Then
                    userNote_text.Visible = False
                    userNote_editmode.Visible = False

                    userNote_textarea.Visible = True
                    userNote_cancel.Visible = True
                    userNote_submit.Visible = True
                End If
            End If

            Me.lblTitolo.Text = mConfig.GetEntryByKeyName("Lot information").GetValue
            Me.lblLotInfo.Text = mConfig.GetEntryByKeyName("LOT_DETAILS").GetValue
            Me.lblShipments.Text = mConfig.GetEntryByKeyName("Related shipments").GetValue & ":"
            Me.lblParcels.Text = mConfig.GetEntryByKeyName("Related parcels").GetValue & ":"
            Me.lblProductionReports.Text = mConfig.GetEntryByKeyName("Production report").GetValue & ":"
            Me.lblLotsToCell.Text = mConfig.GetEntryByKeyName("LOTS_TO_CELL").GetValue & ":"
            Me.lblUserNotes.Text = mConfig.GetEntryByKeyName("USER_NOTES").GetValue & ":"

            myScript.InvokeJS(Me.Page, "handleTextAreaSize();")
            myScript.InvokeJS(Me.Page, "calculateLotDetailHeight();")
            myScript.InvokeJS(Me.Page, "window.parent.extractPrintDataFromIFrame();")
        End If
    End Sub

    Private Sub GetLotInfo()
        Dim amount As Double = 0.0
        Dim lot_type As Integer = 0

        str_sql = "SELECT * FROM VIEW_LOTS WHERE ID = " & lot_id
        dt = DataBase.ExecuteSQL_DataTable(str_sql, False)

        For Each r As Data.DataRow In dt.Rows
            ' descriptor
            Dim row As New HtmlTableRow
            Me.tblLotInfo.Rows.Add(row)

            Dim tc As New HtmlTableCell
            tc.InnerText = mConfig.GetEntryByKeyName("Lot name").GetValue & ": "
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc)

            Dim tc2 As New HtmlTableCell
            tc2.InnerText = r.Item("DESCRIPTOR").ToString
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc2)

            ' product
            Dim row2 As New HtmlTableRow
            Me.tblLotInfo.Rows.Add(row2)

            Dim tc3 As New HtmlTableCell
            tc3.InnerText = mConfig.GetEntryByKeyName("Product name").GetValue & ": "
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc3)

            Dim tc4 As New HtmlTableCell
            tc4.InnerText = r.Item("NAME").ToString
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc4)

            ' amount
            Dim row4 As New HtmlTableRow
            Me.tblLotInfo.Rows.Add(row4)

            Dim tc7 As New HtmlTableCell
            tc7.InnerText = mConfig.GetEntryByKeyName("Lot size").GetValue & ": "
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc7)

            qty_text.InnerText = Math.Round(Double.Parse(r("AMOUNT").ToString), 3).ToString & " " & r.Item("UNIT_ASP").ToString
            qty_textbox.Value = Math.Round(Double.Parse(r("AMOUNT").ToString), 3).ToString()
            unit_text.InnerText = r.Item("UNIT_ASP").ToString

            qty_text.Visible = True
            qty_textbox.Visible = False
            unit_text.Visible = False
            If WebTools.tools.CheckAccess(mConfig, "MENU_LOTS", "lothistory", UsersGUI.EnumAccessLevel.Editing) Then
                If Current.Request.QueryString("edit") IsNot Nothing AndAlso Current.Request.QueryString("edit").ToUpper = UsersGUI.costanti.m_StringYes Then
                    lot_type = CInt(r.Item("LTE_ID").ToString())
                    ' da configurare per ogni commessa
                    'If (lot_type = SSLotTypes.R) OrElse (lot_type = SSLotTypes.AS_) OrElse (lot_type = SSLotTypes.RV) Then
                    '    qty_textbox.Visible = True
                    '    qty_textbox.Disabled = False
                    '    qty_text.Visible = False
                    '    unit_text.Visible = True
                    'End If
                End If
            End If

            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(qty_td)

            lot_type = CInt(r.Item("LTE_ID"))

            ' creation date
            Dim row5 As New HtmlTableRow
            Me.tblLotInfo.Rows.Add(row5)

            Dim tc9 As New HtmlTableCell
            tc9.InnerText = mConfig.GetEntryByKeyName("Creation date").GetValue & ": "
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc9)

            Dim tc10 As New HtmlTableCell
            tc10.InnerText = CDate(r.Item("CREATION_DATE").ToString).ToLongDateString & " " & CDate(r.Item("CREATION_DATE").ToString).ToLongTimeString
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc10)

            lot_creation_date = CDate(r.Item("CREATION_DATE").ToString)

            ' auto notes (disegnate solo se contengono un valore)
            If r.Item("AUTO_NOTES").ToString <> String.Empty Then
                Dim row6 As New HtmlTableRow
                Me.tblLotInfo.Rows.Add(row6)

                Dim tc11 As New HtmlTableCell
                tc11.InnerText = mConfig.GetEntryByKeyName("AUTO_NOTES").GetValue & ": "
                tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc11)

                autoNote_text.InnerText = r.Item("AUTO_NOTES").ToString
                autoNote_textbox.Value = r.Item("AUTO_NOTES").ToString

                autoNote_text.Visible = True
                autoNote_textbox.Visible = False
                If WebTools.tools.CheckAccess(mConfig, "MENU_LOTS", "lothistory", UsersGUI.EnumAccessLevel.Editing) Then
                    If Current.Request.QueryString("edit") IsNot Nothing AndAlso Current.Request.QueryString("edit").ToUpper = UsersGUI.costanti.m_StringYes Then
                        lot_type = CInt(r.Item("LTE_ID").ToString())
                        ' da configurare per ogni commessa
                        'If (lot_type = SSLotTypes.R) OrElse (lot_type = SSLotTypes.AS_) OrElse (lot_type = SSLotTypes.RV) Then
                        '    autoNote_textbox.Visible = True
                        '    autoNote_textbox.Disabled = False
                        '    autoNote_text.Visible = False
                        'End If
                    End If
                End If

                tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(autoNote_td)
            End If

            ' user notes
            Dim row7 As New HtmlTableRow
            Me.tblLotInfo.Rows.Add(row7)

            Dim tc13 As New HtmlTableCell
            tc13.InnerText = mConfig.GetEntryByKeyName("USER_NOTES").GetValue & ": "
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(tc13)

            userNote_text.InnerText = r.Item("USER_NOTES").ToString
            userNote_textarea.InnerText = r.Item("USER_NOTES").ToString
            userNote_submit.Text = mConfig.GetEntryByKeyName("SUBMIT_BUTTON").GetValue()
            userNote_cancel.Text = mConfig.GetEntryByKeyName("CANCEL_BUTTON").GetValue()
            userNote_editmode.Text = mConfig.GetEntryByKeyName("EDIT_BUTTON").GetValue()
            tblLotInfo.Rows(tblLotInfo.Rows.Count - 1).Cells.Add(userNote_td)

            ' Checks for the editing right set on the lothistory page
            If Not WebTools.tools.CheckAccess(mConfig, "MENU_LOTS", "lothistory", UsersGUI.EnumAccessLevel.Editing) Then
                Me.userNote_submit.Visible = False
                Me.userNote_cancel.Visible = False
                Me.userNote_editmode.Visible = False
                Me.userNote_textarea.Disabled = True
            Else
                Me.userNote_editmode.Enabled = True
            End If

            ' lot amount
            lot_amount = Double.Parse(r.Item("AMOUNT").ToString)
        Next

    End Sub

    Private Sub GetLotsToCell()
        Dim amount As Double = 0.0

        Dim sql As String = "SELECT CEL_NAME, AMOUNT, CEL_ID, TRACING_MODEL FROM VIEW_LOTS_TO_CELL WHERE LO_ID = '" & lot_id & "'"
        Dim dt As Data.DataTable = DataBase.ExecuteSQL_DataTable(sql, False)

        If dt.Rows.Count > 0 Then
            Dim row As New HtmlTableRow
            row.Attributes("class") = "rowSection"
            Me.tblLotsToCell.Rows.Add(row)

            ' Bin
            tblLotsToCell.Rows(tblLotsToCell.Rows.Count - 1).Cells.Add(
                New HtmlTableCell() With {.InnerText = mConfig.GetEntryByKeyName("Bin").GetValue})

            ' Quantity
            tblLotsToCell.Rows(tblLotsToCell.Rows.Count - 1).Cells.Add(
                New HtmlTableCell() With {.InnerText = mConfig.GetEntryByKeyName("Lot size inside").GetValue})

            For Each dr As Data.DataRow In dt.Rows
                tblLotsToCell.Rows.Add(New HtmlTableRow)

                ' Bin
                Dim link As New HyperLink
                Dim tc As New HtmlTableCell()
                link.Text = dr.Item("CEL_NAME").ToString()
                link.NavigateUrl = myFunction.GetRoot & "/BinLayers.aspx?drawflow=1&typeview=LOTS&menuname=MENU_STOCKS&TRACING_MODEL=" & dr.Item("TRACING_MODEL").ToString & "&CEL_ID=" & dr.Item("CEL_ID").ToString
                link.Target = "_parent"
                tc.Controls.Add(link)
                tblLotsToCell.Rows(tblLotsToCell.Rows.Count - 1).Cells.Add(tc)

                ' Quantity
                Dim tc4 As New HtmlTableCell
                amount = Double.Parse(dr.Item("AMOUNT").ToString)

                If lot_type = CfgMeasurementUnit.WaterLotType Then
                    If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                        tc4.InnerText = Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
                    Else
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                        tc4.InnerText = Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
                    End If
                Else
                    If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                        tc4.InnerText = Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
                    Else
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                        tc4.InnerText = Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
                    End If
                End If
                tblLotsToCell.Rows(tblLotsToCell.Rows.Count - 1).Cells.Add(tc4)

                lot_amount_in_bins += Double.Parse(dr.Item("AMOUNT").ToString)
            Next

            Me.div_lotsToCell.Visible = True
        Else
            Me.div_lotsToCell.Visible = False
        End If
    End Sub

    Private Sub GetRapportoResa()
        Dim m_yr_id As Integer = 0
        Dim strMenuname As String = String.Empty
        Me.lblReportYields.Text = String.Empty
        Me.lblReportYields.Visible = False
        str_sql = "SELECT COUNTER FROM LOTS WHERE ID='" & lot_id & "'"
        dt = DataBase.ExecuteSQL_DataTable(str_sql, False)
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 AndAlso dt.Rows(0).Item("COUNTER") IsNot DBNull.Value Then
            counter = Integer.Parse(dt.Rows(0).Item("COUNTER").ToString)
            str_sql = "SELECT * FROM YIELDS_DATA WHERE JOB_ID='" & counter & "'"
            dt = DataBase.ExecuteSQL_DataTable(str_sql, False)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 AndAlso IsNumeric(dt.Rows(0).Item(0).ToString) AndAlso Long.Parse(dt.Rows(0).Item(0).ToString) > 0 Then
                m_yr_id = Integer.Parse(dt.Rows(0).Item("YR_ID").ToString)

                Me.lblReportYields.Visible = True

                Me.lblReportYields.Text = mConfig.GetEntryByKeyName("CeUn").GetValue & " <a href=""" & myFunction.GetRoot & myReports.GetJobScalesReportUrl(m_yr_id, counter) & """ target=""_parent"">" & mConfig.GetEntryByKeyName("YieldsReport").GetValue & "</A> " & mConfig.GetEntryByKeyName("PerLotto").GetValue & "."
                Me.div_reportYields.Visible = True
            End If
        End If
    End Sub

    Private Sub GetComposizioneEUtilizzo()
        If lot_mode = LotMode.Tracciabilità Then
            ' da lotto finale a lotti iniziali
            lot_amount_to_be_historicized = lot_amount

            If b_only_initial_or_final_lots Then
                ' Composition only "COMPOSITION"
                lblComposizione.Text = mConfig.GetEntryByKeyName("LOT_USED_MADE_OF_PRIMARY").GetValue & ":"
                Me.div_composizione.Visible = Me.GetPrimaryComposition(tblComposizione)
            Else
                ' Standard "COMPOSITION"
                Me.lblComposizione.Text = mConfig.GetEntryByKeyName("LOT_USED_MADE_OF").GetValue & ":"
                Me.div_composizione.Visible = Me.GetComposition(tblComposizione, lot_mode)
            End If

            lot_amount_to_be_historicized = lot_amount - lot_amount_in_bins

            ' Standard "USED_TO_MAKE"
            lblUtilizzo.Text = mConfig.GetEntryByKeyName("LOT_USED_TO_MAKE").GetValue & ":"
            Me.div_utilizzo.Visible = Me.GetComposition(tblUtilizzo, LotMode.Rintracciabilità)
        Else
            ' da lotto iniziale a lotti finali
            lot_amount_to_be_historicized = lot_amount

            ' Standard "COMPOSITION"
            Me.lblComposizione.Text = mConfig.GetEntryByKeyName("LOT_USED_MADE_OF").GetValue & ":"
            Me.div_composizione.Visible = Me.GetComposition(tblComposizione, LotMode.Tracciabilità)

            lot_amount_to_be_historicized = lot_amount - lot_amount_in_bins

            If b_only_initial_or_final_lots Then
                ' Composition only "USED_TO_MAKE"
                lblUtilizzo.Text = mConfig.GetEntryByKeyName("LOT_USED_TO_MAKE_FINAL").GetValue & ":"
                Me.div_utilizzo.Visible = Me.GetPrimaryComposition(tblUtilizzo)
            Else
                ' Standard "USED_TO_MAKE"
                lblUtilizzo.Text = mConfig.GetEntryByKeyName("LOT_USED_TO_MAKE").GetValue & ":"
                Me.div_utilizzo.Visible = Me.GetComposition(tblUtilizzo, lot_mode)
            End If
        End If

    End Sub

    Private Function GetComposition(ByVal table As HtmlTable, ByVal lotMode As LotMode) As Boolean
        Dim retVal As Boolean = False
        Dim b_at_least_one_non_water_lot_in_history As Boolean = False

        ' recupero tutta la storia con LOT_HISTORY.AMOUNT > 0
        str_sql = "SELECT VIEW_LOTS.ID AS LID, VIEW_LOTS.DESCRIPTOR, LOTS_HISTORIES.AMOUNT, VIEW_LOTS.AMOUNT AS FULL_AMOUNT, VIEW_LOTS.UNIT_ASP, VIEW_LOTS.NAME, VIEW_LOTS.PID, VIEW_LOTS.PT_ID, VIEW_LOTS.LTE_ID, LOTS_HISTORIES.CREATION_DATE " &
                    "FROM VIEW_LOTS INNER JOIN LOTS_HISTORIES ON VIEW_LOTS.ID = LOTS_HISTORIES." & If(lotMode = LotMode.Tracciabilità, "SOURCE_LOT", "FINAL_LOT") & " " &
          "WHERE LOTS_HISTORIES." & If(lotMode = LotMode.Tracciabilità, "FINAL_LOT", "SOURCE_LOT") & " = " & lot_id &
          "ORDER BY VIEW_LOTS.LTE_ID, VIEW_LOTS.ID"

        dt = DataBase.ExecuteSQL_DataTable(str_sql, False)

        If lotMode = LotMode.Tracciabilità Then
            ' controllo di avere almeno un lotto di prodotto != acqua nella history
            For Each dr As Data.DataRow In dt.Rows
                If Integer.Parse(dr("PID").ToString) <> CommonDefines.Defines.SSProducts.Water Then
                    b_at_least_one_non_water_lot_in_history = True
                End If
            Next
        End If

        If b_at_least_one_non_water_lot_in_history OrElse lotMode = LotMode.Rintracciabilità Then
            If dt.Rows.Count > 0 Then
                Me.GetComponenti(dt, table)
                retVal = True
            End If
        End If

        Return retVal
    End Function

    Private Function GetPrimaryComposition(ByVal table As HtmlTable) As Boolean
        Dim componentsRaw As List(Of PrimaryCompositionLot) = GetPrimaryComponentsRecursively(lot_id, lot_amount_to_be_historicized, True)
        Dim b_atLeastOneComponent As Boolean = componentsRaw.Count > 0

        Dim sql As String

        Dim lots_total_full_amount As Double = 0.0
        Dim lots_total_amount_scaled As Double = 0.0
        Dim lots_total_percentage_scaled As Double = 0.0

        ' Check the components list for duplicates and merge their quantities
        Dim components As New List(Of PrimaryCompositionLot)
        Dim duplicates As IEnumerable(Of PrimaryCompositionLot)

        Dim amount As Double

        For Each component As PrimaryCompositionLot In componentsRaw
            duplicates = components.Where(Function(l) l.Id = component.Id)

            If duplicates.Count = 0 Then
                ' Add the lot to the final list
                components.Add(component)
            Else
                ' Adds up the quantities of the same lot
                duplicates(0).Quantity += component.Quantity
            End If
        Next

        components = components.OrderBy(Function(l) l.Id).OrderBy(Function(l) l.LotType).ToList

        Dim row As HtmlTableRow
        Dim tc As HtmlTableCell

        row = New HtmlTableRow()
        row.Attributes("class") = "rowSection"

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Lot name").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Lot size").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("% / lotto").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Lot size inside").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("% / mix").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Product name").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("CREATION_DATE").GetValue
        }
        row.Cells.Add(tc)

        table.Rows.Add(row)

        For Each component As PrimaryCompositionLot In components
            row = New HtmlTableRow()

            tc = New HtmlTableCell

            Dim link As New HyperLink
            link.Text = component.Descriptor
            link.Target = "_top"
            link.NavigateUrl = myFunction.GetRoot & "/Lot_History.aspx?control=lothistory&pagename=lothistory&menuname=MENU_LOTS&ID=" & component.Id
            link.Attributes("class") = "lotLink"
            tc.Controls.Add(link)
            row.Cells.Add(tc)

            tc = New HtmlTableCell
            amount = component.FullAmount
            If component.LotType = CfgMeasurementUnit.WaterLotType Then
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
                End If
            Else
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
                End If
            End If

            row.Cells.Add(tc)

            tc = New HtmlTableCell With {
                .InnerText = If(component.FullAmount <= 0 OrElse Math.Round(component.Quantity / component.FullAmount * 100, Decimals.Ratio) = 0, "~", "") &
                    If(component.FullAmount > 0, Math.Round(component.Quantity / component.FullAmount * 100, Decimals.Ratio).ToString, "0") & " %"
            }
            row.Cells.Add(tc)

            tc = New HtmlTableCell
            amount = component.Quantity
            If component.LotType = CfgMeasurementUnit.WaterLotType Then
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
                End If
            Else
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
                End If
            End If
            row.Cells.Add(tc)

            tc = New HtmlTableCell With {
                .InnerText = If(Math.Round(component.Quantity / lot_amount_to_be_historicized * 100, Decimals.Ratio) = 0, "~", "") & Math.Round(component.Quantity / lot_amount_to_be_historicized * 100, Decimals.Ratio).ToString & " %"
            }
            row.Cells.Add(tc)

            ' increment the totalizers
            lots_total_full_amount += component.FullAmount
            lots_total_amount_scaled += component.Quantity
            lots_total_percentage_scaled += component.Quantity / lot_amount_to_be_historicized * 100

            tc = New HtmlTableCell With {
                .InnerText = component.ProductName
            }
            row.Cells.Add(tc)

            tc = New HtmlTableCell
            tc.InnerText = component.CreationDate.ToString()
            row.Cells.Add(tc)

            table.Rows.Add(row)
        Next

        ' Righe di totali parziali
        Dim curr_lot_type As Integer = m_InvalidInteger
        Dim curr_lot_type_desc As String

        Dim curr_lot_full_qty_totalizer As Double = 0.0
        Dim curr_lot_qty_totalizer As Double = 0.0
        Dim curr_lot_perc_totalizer As Double = 0.0

        ' Necessario perchè si inserisce in testa alla table
        components.Reverse()

        ' Per far disegnare l'ultima riga (che è quella più in alto)
        components.Add(
            New PrimaryCompositionLot() With {
                .Id = m_InvalidInteger,
                .LotType = m_InvalidInteger - 1
            }
        )

        For Each lot In components
            If curr_lot_type = m_InvalidInteger Then
                curr_lot_type = lot.LotType
            End If

            If lot.LotType <> curr_lot_type Then
                ' Crea riga per somma corrente
                row = New HtmlTableRow()
                row.Attributes("class") = "rowPartial"

                sql = "SELECT TYPE, UNIT_ASP FROM LOT_TYPES WHERE ID = " & curr_lot_type
                dt = DataBase.ExecuteSQL_DataTable(sql, False)
                curr_lot_type_desc = dt.Rows(0).Item("TYPE").ToString

                tc = New HtmlTableCell() With {
                    .InnerText = curr_lot_type_desc
                }
                row.Cells.Add(tc)

                tc = New HtmlTableCell()
                amount = curr_lot_full_qty_totalizer
                If curr_lot_type = CfgMeasurementUnit.WaterLotType Then
                    If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
                    Else
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
                    End If
                Else
                    If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
                    Else
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
                    End If
                End If
                row.Cells.Add(tc)

                tc = New HtmlTableCell() With {
                    .InnerText = Math.Round(curr_lot_qty_totalizer / curr_lot_full_qty_totalizer * 100, Decimals.Ratio).ToString & " %"
                }
                row.Cells.Add(tc)

                tc = New HtmlTableCell()
                amount = curr_lot_qty_totalizer
                If curr_lot_type = CfgMeasurementUnit.WaterLotType Then
                    If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
                    Else
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
                    End If
                Else
                    If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
                    Else
                        amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                        tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
                    End If
                End If
                row.Cells.Add(tc)

                tc = New HtmlTableCell() With {
                    .InnerText = Math.Round(curr_lot_perc_totalizer, Decimals.Ratio).ToString & " %"
                }
                row.Cells.Add(tc)

                tc = New HtmlTableCell() With {
                    .InnerText = "-"
                }
                row.Cells.Add(tc)

                tc = New HtmlTableCell() With {
                    .InnerText = "-"
                }
                row.Cells.Add(tc)

                ' ultimo comando per inserire la riga di totale alla tabella
                table.Rows.Insert(1, row)

                curr_lot_type = lot.LotType

                curr_lot_full_qty_totalizer = 0.0
                curr_lot_qty_totalizer = 0.0
                curr_lot_perc_totalizer = 0.0
            End If

            ' Somma al totalizzatore attuale
            curr_lot_full_qty_totalizer += lot.FullAmount
            curr_lot_qty_totalizer += lot.Quantity
            curr_lot_perc_totalizer += lot.Quantity / lot_amount_to_be_historicized * 100
        Next

        ' ultimo comando per inserire la riga di totale alla tabella
        table.Rows.Insert(1, row)

        ' riga di totale
        row = New HtmlTableRow()
        row.Attributes("class") = "rowTotal"

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("TOTAL").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell()
        amount = lots_total_full_amount
        If lot_type = CfgMeasurementUnit.WaterLotType Then
            If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
            Else
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
            End If
        Else
            If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
            Else
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
            End If
        End If
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = Math.Round(lots_total_amount_scaled / lots_total_full_amount * 100, Decimals.Ratio).ToString & " %"
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell()
        amount = lots_total_amount_scaled
        If lot_type = CfgMeasurementUnit.WaterLotType Then
            If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
            Else
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
            End If
        Else
            If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
            Else
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
            End If
        End If
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = Math.Round(lots_total_percentage_scaled, Decimals.Ratio).ToString & " %"
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = "-"
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = lot_creation_date.ToString
        }
        row.Cells.Add(tc)

        ' ultimo comando per inserire la riga di totale alla tabella
        table.Rows.Insert(1, row)

        Return b_atLeastOneComponent
    End Function

    Private Function GetPrimaryComponentsRecursively(ByVal lotId As Long, ByVal weight As Double, Optional ByVal firstCall As Boolean = False) As List(Of PrimaryCompositionLot)
        Dim retVal As New List(Of PrimaryCompositionLot)

        Dim sql As String = "SELECT " & If(lot_mode = LotMode.Tracciabilità, "SOURCE_LOT", "FINAL_LOT") & " AS CHILD_LOT, AMOUNT FROM LOTS_HISTORIES WHERE " & If(lot_mode = LotMode.Tracciabilità, "FINAL_LOT", "SOURCE_LOT") & " = " & lotId
        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sql, False)

        If dt.Rows.Count > 0 Then
            Dim weightSum As Double = 0.0
            Dim multiplier As Double

            For Each dr As Data.DataRow In dt.Rows
                weightSum += Double.Parse(dr.Item("AMOUNT").ToString)
            Next

            multiplier = If(weightSum = 0, 0, weight / weightSum)

            For Each dr As Data.DataRow In dt.Rows
                retVal.AddRange(GetPrimaryComponentsRecursively(Long.Parse(dr.Item("CHILD_LOT").ToString), Double.Parse(dr.Item("AMOUNT").ToString) * multiplier))
            Next
        ElseIf Not firstCall Then
            sql = "SELECT DESCRIPTOR, LTE_ID, AUTO_NOTES, AMOUNT, UNIT_ASP, NAME AS PRODUCT_NAME, CREATION_DATE FROM VIEW_LOTS WHERE ID = " & lotId
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sql, False)

            If dt.Rows.Count > 0 Then
                retVal.Add(New PrimaryCompositionLot() With {
                    .Id = lotId,
                    .LotType = CInt(dt.Rows(0).Item("LTE_ID")),
                    .Descriptor = dt.Rows(0).Item("DESCRIPTOR").ToString,
                    .AutoNotes = If(IsDBNull(dt.Rows(0).Item("AUTO_NOTES")), Nothing, dt.Rows(0).Item("AUTO_NOTES").ToString),
                    .FullAmount = CDbl(dt.Rows(0).Item("AMOUNT").ToString),
                    .Quantity = weight,
                    .Unit = dt.Rows(0).Item("UNIT_ASP").ToString,
                    .ProductName = dt.Rows(0).Item("PRODUCT_NAME").ToString,
                    .CreationDate = CType(dt.Rows(0).Item("CREATION_DATE"), Date)
                })
            End If
        End If

        Return retVal
    End Function

    Private Sub GetComponenti(ByVal dt As Data.DataTable, ByRef table As HtmlTable)

        Dim lot_history_total As Double = 0.0
        Dim lot_history_amount As Double = 0.0
        Dim lot_history_amount_scaled As Double = 0.0
        Dim lot_history_percentage_scaled As Double = 0.0
        Dim lot_percentage_scaled As Double = 0.0
        Dim lot_full_amount As Double = 0.0

        Dim lots_total_amount_scaled As Double = 0.0
        Dim lots_total_percentage_scaled As Double = 0.0

        Dim amount As Double

        Dim row As HtmlTableRow
        Dim tc As HtmlTableCell

        row = New HtmlTableRow()
        row.Attributes("class") = "rowSection"

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Lot name").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Lot size").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("% / lotto").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Lot size inside").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("% / mix").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("Product name").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("CREATION_DATE").GetValue
        }
        row.Cells.Add(tc)

        ' ultimo comando per inserire la riga alla tabella
        table.Rows.Add(row)

        ' prima calcolo l'amount totale
        For Each dr As Data.DataRow In dt.Rows
            lot_history_total += Double.Parse(dr("AMOUNT").ToString)
        Next

        ' poi popolo la riga dei dati
        For Each r As Data.DataRow In dt.Rows
            row = New HtmlTableRow()

            tc = New HtmlTableCell

            ' lot name and link
            Dim link As New HyperLink
            link.Text = r.Item("DESCRIPTOR").ToString
            link.Target = "_top"
            link.NavigateUrl = myFunction.GetRoot & "/Lot_History.aspx?control=lothistory&pagename=lothistory&menuname=MENU_LOTS&ID=" & r.Item("LID").ToString
            link.Attributes("class") = "lotLink"
            tc.Controls.Add(link)
            row.Cells.Add(tc)

            lot_full_amount = Double.Parse(r("FULL_AMOUNT").ToString)
            lot_history_amount = Double.Parse(r("AMOUNT").ToString)

            ' show lot full amount
            tc = New HtmlTableCell
            amount = lot_full_amount
            If CInt(r.Item("LTE_ID")) = CfgMeasurementUnit.WaterLotType Then
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
                End If
            Else
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
                End If
            End If
            row.Cells.Add(tc)

            ' show lot size percentage
            If lot_history_amount > 0.0 AndAlso lot_full_amount > 0.0 Then
                lot_percentage_scaled = (lot_history_amount * 100) / lot_full_amount
            Else
                ' scalo in parti percentuali uguali le lot history con amount == 0.0
                lot_percentage_scaled = 100.0 / dt.Rows.Count
            End If

            tc = New HtmlTableCell With {
                .InnerText = If(Math.Round(lot_percentage_scaled, Decimals.Ratio) = 0, "~", "") & Math.Round(lot_percentage_scaled, Decimals.Ratio).ToString & " %"
            }
            row.Cells.Add(tc)

            ' show lot history amount
            If lot_history_total > 0.0 Then
                '' scalo la dimensione storicizzata per avere sempre una storicizzazione al 100% rispetto ai lotti componente
                'lot_history_amount_scaled = (lot_history_amount * lot_amount_to_be_historicized) / lot_history_total

                ' al valore scalato, che è quello che mostrerò, assegno la dimensione per come è, anche a costo di non avere il 100% nella somma dei componenti
                lot_history_amount_scaled = lot_history_amount
            Else
                lot_history_amount_scaled = 0.0
            End If

            tc = New HtmlTableCell
            amount = lot_history_amount_scaled
            If CInt(r.Item("LTE_ID")) = CfgMeasurementUnit.WaterLotType Then
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
                End If
            Else
                If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
                Else
                    amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                    tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
                End If
            End If
            row.Cells.Add(tc)

            ' show lot history percentage
            If lot_history_total > 0.0 AndAlso lot_amount_to_be_historicized > 0.0 Then
                lot_history_percentage_scaled = (lot_history_amount_scaled * 100) / lot_amount_to_be_historicized
            Else
                ' scalo in parti percentuali uguali le lot history con amount == 0.0
                lot_history_percentage_scaled = 100.0 / dt.Rows.Count
            End If

            tc = New HtmlTableCell With {
                .InnerText = If(Math.Round(lot_history_percentage_scaled, Decimals.Ratio) = 0, "~", "") & Math.Round(lot_history_percentage_scaled, Decimals.Ratio).ToString & " %"
            }
            row.Cells.Add(tc)

            ' increment the totalizers
            lots_total_amount_scaled += lot_history_amount_scaled
            lots_total_percentage_scaled += lot_history_percentage_scaled

            ' product name
            tc = New HtmlTableCell With {
                .InnerText = r.Item("NAME").ToString
            }
            row.Cells.Add(tc)

            ' creation date
            tc = New HtmlTableCell
            tc.InnerText = Date.Parse(r.Item("CREATION_DATE").ToString).ToString
            row.Cells.Add(tc)

            table.Rows.Add(row)

        Next

        ' riga di totale
        row = New HtmlTableRow()
        row.Attributes("class") = "rowTotal"

        tc = New HtmlTableCell() With {
            .InnerText = mConfig.GetEntryByKeyName("TOTAL").GetValue
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = "-"
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = "-"
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell()
        amount = lots_total_amount_scaled
        If lot_type = CfgMeasurementUnit.WaterLotType Then
            If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Water Then
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Water)
            Else
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Water), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Water)
            End If
        Else
            If amount > CfgMeasurementUnit.UserInterface.StandardVsLittle.Lot Then
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.StandardAmounts.Lot)
            Else
                amount *= UsersGUI.tools.GetConversionFactorToASP(GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.Database.Lot), GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot))
                tc.InnerText = If(Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) = 0, "~", "") & Math.Round(amount, GetDecimalsFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)) & " " & GetUnitSymbolFromMeasurementUnit(CfgMeasurementUnit.UserInterface.LittleAmounts.Lot)
            End If
        End If
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = Math.Round(lots_total_percentage_scaled, Decimals.Ratio).ToString & " %"
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = "-"
        }
        row.Cells.Add(tc)

        tc = New HtmlTableCell() With {
            .InnerText = lot_creation_date.ToString
        }
        row.Cells.Add(tc)

        ' ultimo comando per inserire la riga di totale alla tabella
        table.Rows.Insert(1, row)
    End Sub

    Private Sub GetShipments()
        str_sql = "SELECT SH_ID, AMOUNT, CUS_NAME, DATE, CYCLE_TYPE, CAR_NAME FROM VIEW_SHIPMENTS_CONFIRMED " &
            "WHERE JOB_COUNTER =" & counter

        If lot_id > 0 Then
            str_sql &= " AND LO_ID = " & lot_id
        End If

        str_sql &= " ORDER BY DATE DESC"

        dt = DataBase.ExecuteSQL_DataTable(str_sql, False)

        If dt.Rows.Count > 0 Then
            Dim row As New HtmlTableRow
            row.Attributes("class") = "rowSection"
            Me.tblShipments.Rows.Add(row)

            Dim tc As New HtmlTableCell
            tc.InnerText = mConfig.GetEntryByKeyName("Shipment code").GetValue
            tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc)

            Dim tc2 As New HtmlTableCell
            tc2.InnerText = mConfig.GetEntryByKeyName("Quantity").GetValue
            tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc2)

            Dim tc3 As New HtmlTableCell
            tc3.InnerText = mConfig.GetEntryByKeyName("Customer").GetValue
            tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc3)

            Dim tc4 As New HtmlTableCell
            tc4.InnerText = mConfig.GetEntryByKeyName("Type").GetValue
            tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc4)

            Dim tc5 As New HtmlTableCell
            tc5.InnerText = mConfig.GetEntryByKeyName("Carrier").GetValue
            tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc5)

            Dim tc6 As New HtmlTableCell
            tc6.InnerText = mConfig.GetEntryByKeyName("Date").GetValue
            tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc6)

            For Each r As Data.DataRow In dt.Rows
                Dim row2 As New HtmlTableRow
                Me.tblShipments.Rows.Add(row2)

                Dim tc7 As New HtmlTableCell
                tc7.InnerText = r.Item("SH_ID").ToString
                tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc7)

                Dim tc8 As New HtmlTableCell
                tc8.InnerText = r.Item("AMOUNT").ToString & " " & mConfig.GetEntryByKeyName("UNIT_KG").GetValue
                tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc8)

                Dim tc9 As New HtmlTableCell
                tc9.InnerText = r.Item("CUS_NAME").ToString
                tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc9)

                Dim tc10 As New HtmlTableCell
                tc10.InnerText = r.Item("CYCLE_TYPE").ToString
                tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc10)

                Dim tc11 As New HtmlTableCell
                tc11.InnerText = r.Item("CAR_NAME").ToString
                tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc11)

                Dim tc12 As New HtmlTableCell
                tc12.InnerText = Date.Parse(r.Item("DATE").ToString).ToString
                tblShipments.Rows(tblShipments.Rows.Count - 1).Cells.Add(tc12)
            Next

            Me.div_shipments.Visible = True
        Else
            Me.div_shipments.Visible = False
        End If
    End Sub

    Private Sub GetParcels()
        str_sql = "SELECT PA_ID, AMOUNT, SUP_NAME, DATE, CYCLE_TYPE, CAR_NAME FROM VIEW_PARCELS_CONFIRMED " &
                 "WHERE JOB_COUNTER =" & counter

        If lot_id > 0 Then
            str_sql &= " AND LO_ID = " & lot_id
        End If

        str_sql &= " ORDER BY DATE DESC"

        dt = DataBase.ExecuteSQL_DataTable(str_sql, False)

        If dt.Rows.Count > 0 Then

            Dim row As New HtmlTableRow
            row.Attributes("class") = "rowSection"
            Me.tblParcels.Rows.Add(row)

            'Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
            'tc.InnerText = mConfig.GetEntryByKeyName("Description").GetValue
            'tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc)

            Dim tc As New HtmlTableCell
            tc.InnerText = mConfig.GetEntryByKeyName("Code").GetValue
            tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc)

            Dim tc2 As New HtmlTableCell
            tc2.InnerText = mConfig.GetEntryByKeyName("Quantity").GetValue
            tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc2)

            Dim tc3 As New HtmlTableCell
            tc3.InnerText = mConfig.GetEntryByKeyName("Supplier").GetValue
            tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc3)

            Dim tc4 As New HtmlTableCell
            tc4.InnerText = mConfig.GetEntryByKeyName("Type").GetValue
            tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc4)

            Dim tc5 As New HtmlTableCell
            tc5.InnerText = mConfig.GetEntryByKeyName("Carrier").GetValue
            tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc5)

            Dim tc6 As New HtmlTableCell
            tc6.InnerText = mConfig.GetEntryByKeyName("Date").GetValue
            tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc6)

            For Each r As Data.DataRow In dt.Rows
                Dim row2 As New HtmlTableRow
                Me.tblParcels.Rows.Add(row2)

                Dim tc7 As New HtmlTableCell
                tc7.InnerText = r.Item("PA_ID").ToString
                tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc7)

                Dim tc8 As New HtmlTableCell
                tc8.InnerText = r.Item("AMOUNT").ToString & " " & mConfig.GetEntryByKeyName("UNIT_KG").GetValue
                tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc8)

                Dim tc9 As New HtmlTableCell
                tc9.InnerText = r.Item("SUP_NAME").ToString
                tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc9)

                Dim tc10 As New HtmlTableCell
                tc10.InnerText = r.Item("CYCLE_TYPE").ToString
                tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc10)

                Dim tc11 As New HtmlTableCell
                tc11.InnerText = r.Item("CAR_NAME").ToString
                tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc11)

                Dim tc12 As New HtmlTableCell
                tc12.InnerText = Date.Parse(r.Item("DATE").ToString).ToString
                tblParcels.Rows(tblParcels.Rows.Count - 1).Cells.Add(tc12)
            Next

            Me.div_parcels.Visible = True
        Else
            Me.div_parcels.Visible = False
        End If
    End Sub

    Private Sub GetProductionReports()
        str_sql = "SELECT COUNTER, DESCRIPTION, START_DATE, STOP_DATE, NOTES, CYC_ID " &
                 "FROM VIEW_PRODUCTION_REPORTS " &
                 "WHERE COUNTER = " & counter

        dt = DataBase.ExecuteSQL_DataTable(str_sql, False)

        If dt.Rows.Count > 0 Then
            Dim row As System.Web.UI.HtmlControls.HtmlTableRow
            Dim tc As System.Web.UI.HtmlControls.HtmlTableCell
            Dim link As HyperLink

            For Each r As Data.DataRow In dt.Rows
                ' Flows (printing)
                row = New System.Web.UI.HtmlControls.HtmlTableRow
                row.Attributes("class") = "showRowOnlyOnPrint"

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = mConfig.GetEntryByKeyName("Job").GetValue & ": "
                row.Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                link = New HyperLink
                link.Text = r.Item("COUNTER").ToString
                link.NavigateUrl = myFunction.GetRoot & "/default.aspx?control=view&pagename=FLOW_LOGS_PRODUCTION_REPORTS&menuname=MENU_LOTS&COUNTER=" & counter
                link.Target = "_parent" ' apre nel frame padre, in alternativa _blank aprirebbe una nuova tab
                tc.Controls.Add(link)
                row.Cells.Add(tc)

                Me.tblProductionReports.Rows.Add(row)

                ' Flows
                row = New System.Web.UI.HtmlControls.HtmlTableRow
                row.Attributes("class") = "hideOnPrint"

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = mConfig.GetEntryByKeyName("FLOWS").GetValue & ": "
                row.Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                link = New HyperLink
                link.Text = r.Item("COUNTER").ToString
                link.NavigateUrl = myFunction.GetRoot & "/default.aspx?control=view&pagename=FLOW_LOGS_PRODUCTION_REPORTS&menuname=MENU_LOTS&COUNTER=" & counter
                link.Target = "_parent" ' apre nel frame padre, in alternativa _blank aprirebbe una nuova tab
                tc.Controls.Add(link)
                row.Cells.Add(tc)

                Me.tblProductionReports.Rows.Add(row)

                ' Report
                row = New System.Web.UI.HtmlControls.HtmlTableRow
                row.Attributes("class") = "hideOnPrint"

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = mConfig.GetEntryByKeyName("GENERIC_REPORT").GetValue & ": "
                row.Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                link = New HyperLink
                link.Text = r.Item("COUNTER").ToString
                link.NavigateUrl = myFunction.GetRoot & "/ProductionReports.aspx?excol=true&control=report&pagename=PRODUCTION_REPORTS_&menuname=MENU_LOTS&from=lothistory&session=1&sql_where=COUNTER=""" & counter & """ AND CYC_ID=""" & r.Item("CYC_ID").ToString & """&COUNTER=" & counter & "&CYC_ID=" & r.Item("CYC_ID").ToString
                link.Target = "_parent" ' apre nel frame padre, in alternativa _blank aprirebbe una nuova tab
                tc.Controls.Add(link)
                row.Cells.Add(tc)

                Me.tblProductionReports.Rows.Add(row)

                ' Description
                row = New System.Web.UI.HtmlControls.HtmlTableRow

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = mConfig.GetEntryByKeyName("Description").GetValue & ": "
                row.Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = r.Item("DESCRIPTION").ToString
                row.Cells.Add(tc)

                Me.tblProductionReports.Rows.Add(row)

                ' Start date
                row = New System.Web.UI.HtmlControls.HtmlTableRow

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = mConfig.GetEntryByKeyName("YIELDS_START_DATE").GetValue & ": "
                row.Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = CDate(r.Item("START_DATE")).ToString(mConfig.GetLanguage().FormatDateTime)
                row.Cells.Add(tc)

                Me.tblProductionReports.Rows.Add(row)

                ' Stop date
                row = New System.Web.UI.HtmlControls.HtmlTableRow

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = mConfig.GetEntryByKeyName("YIELDS_STOP_DATE").GetValue & ": "
                row.Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.InnerText = CDate(r.Item("STOP_DATE")).ToString(mConfig.GetLanguage().FormatDateTime)
                row.Cells.Add(tc)

                Me.tblProductionReports.Rows.Add(row)

                ' Notes
                If r.Item("NOTES").ToString().Trim().Length > 0 Then
                    row = New System.Web.UI.HtmlControls.HtmlTableRow

                    tc = New System.Web.UI.HtmlControls.HtmlTableCell
                    tc.InnerText = mConfig.GetEntryByKeyName("NOTES").GetValue & ": "
                    row.Cells.Add(tc)

                    tc = New System.Web.UI.HtmlControls.HtmlTableCell
                    tc.InnerText = r.Item("NOTES").ToString
                    row.Cells.Add(tc)

                    Me.tblProductionReports.Rows.Add(row)
                End If
            Next

            Me.div_productionReports.Visible = True
        Else
            Me.div_productionReports.Visible = False
        End If
    End Sub

    ''' <summary>
    ''' Fills the table with the user notes set on the lots in the tree of the current lot
    ''' </summary>
    Private Sub GetTreeUserNotes()
        Dim lotList As New List(Of Lot)

        If b_only_initial_or_final_lots Then
            If lot_mode = LotMode.Tracciabilità Then
                lotList = UsersGUI.FunctionLots.GetListaComponenti(CInt(lot_id))
            ElseIf lot_mode = LotMode.Rintracciabilità Then
                lotList = UsersGUI.FunctionLots.GetListaComponentiRin(CInt(lot_id))
            End If
        Else
            If lot_mode = LotMode.Tracciabilità Then
                lotList = UsersGUI.FunctionLots.GetLotTreeRecursively(lot_id, True, m_MaxLotDepth)   ' Tracciabilità
            ElseIf lot_mode = LotMode.Rintracciabilità Then
                lotList = UsersGUI.FunctionLots.GetLotTreeRecursively(lot_id, False, m_MaxLotDepth)  ' Rintracciabilità
            End If
        End If

        tblUserNotes.Rows.Clear()

        ' Filter out the current lot
        lotList.RemoveAll(Function(l) l.Id = lot_id)

        ' Filter out lots without user notes
        lotList = lotList.Where(Function(l) l.UserNotes IsNot Nothing AndAlso l.UserNotes.Length > 0).ToList

        ' Filter out dupes
        Dim uniqueLotIds As New HashSet(Of Long)
        lotList = lotList.Where(
            Function(l)
                If Not uniqueLotIds.Contains(l.Id) Then
                    uniqueLotIds.Add(l.Id)
                    Return True
                Else
                    Return False
                End If
            End Function
            ).ToList

        If lotList.Count > 0 Then
            div_userNotes.Visible = True

            Dim tr As HtmlTableRow
            Dim tc As HtmlTableCell
            Dim link As HyperLink

            ' 1 - Header
            tr = New HtmlTableRow()
            tr.Attributes("class") = "rowSection"

            '   1.1 - Lot
            tc = New HtmlTableCell() With {
                .InnerText = mConfig.GetEntryByKeyName("Lot").GetValue
            }
            tr.Controls.Add(tc)

            '   1.2 - User notes
            tc = New HtmlTableCell() With {
                .InnerText = mConfig.GetEntryByKeyName("NOTES").GetValue
            }
            tr.Controls.Add(tc)

            tblUserNotes.Rows.Add(tr)

            ' 2 - Body
            For Each lot As Lot In lotList
                tr = New HtmlTableRow()

                '   2.1 - Lot
                tc = New HtmlTableCell()
                link = New HyperLink() With {
                    .Text = lot.Descrizione,
                    .Target = "_top",
                    .NavigateUrl = myFunction.GetRoot & "/Lot_History.aspx?control=lothistory&pagename=lothistory&menuname=MENU_LOTS&ID=" & lot.Id
                }
                link.Attributes("class") = "lotLink"
                tc.Controls.Add(link)
                tr.Controls.Add(tc)

                '   2.2 - User notes
                tc = New HtmlTableCell() With {
                    .InnerText = lot.UserNotes
                }
                tr.Controls.Add(tc)

                tblUserNotes.Rows.Add(tr)
            Next
        End If
    End Sub

    Protected Sub userNote_submit_Click(ByVal sender As Object, ByVal e As EventArgs) Handles userNote_submit.Click
        Dim lotToUpdate As New Lot(lot_id)
        Dim value As String = If(userNote_textarea.InnerText.Trim.Length > 0, userNote_textarea.InnerText.Trim, Nothing)
        Dim qty As String = If(qty_textbox.Value.Trim.Length > 0, qty_textbox.Value.Trim, Nothing)
        Dim ddt As String = If(autoNote_textbox.Value.Trim.Length > 0, autoNote_textbox.Value.Trim, Nothing)

        If (qty IsNot Nothing Or ddt IsNot Nothing) Then
            lotToUpdate.UpdateUserNotes(value, qty, ddt)
        Else
            lotToUpdate.UpdateUserNotes(value)
        End If

        ' Trigger the update of the lots menu
        Current.Session(sessionId & "_reloadWucLotsMenu") = UsersGUI.costanti.m_StringUno

        Dim objValueCollection As NameValueCollection = System.Web.HttpUtility.ParseQueryString(Current.Request.QueryString.ToString)
        objValueCollection.Remove("edit")

        Current.Response.Redirect(Current.Request.Path & "?" & objValueCollection.ToString)
    End Sub

    Protected Sub userNote_cancel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles userNote_cancel.Click
        Dim objValueCollection As NameValueCollection = System.Web.HttpUtility.ParseQueryString(Current.Request.QueryString.ToString)
        objValueCollection.Remove("edit")

        Current.Response.Redirect(Current.Request.Path & "?" & objValueCollection.ToString)
    End Sub

    Protected Sub userNote_editmode_Click(ByVal sender As Object, ByVal e As EventArgs) Handles userNote_editmode.Click
        Current.Response.Redirect(Current.Request.RawUrl & "&edit=yes")
    End Sub

    Public Class PrimaryCompositionLot
        Public Id As Long
        Public LotType As Integer
        Public Descriptor As String
        Public AutoNotes As String
        Public FullAmount As Double
        Public Quantity As Double
        Public Unit As String
        Public ProductName As String
        Public CreationDate As Date
    End Class

End Class