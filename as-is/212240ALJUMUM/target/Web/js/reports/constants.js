//#region Enumerables
const ELEMENT_TYPES = {
    GRID: 0,
    TABLE: 1,
    OPERATION: 2,
    0: "GRID",
    1: "TABLE",
    2: "OPERATION",
};

const REPORT_TYPES = {
    SCALES_INSTANT: 0,
    SCALES_PERIODIC: 1,
    SCALES_JOB: 2,
    POWER_INSTANT: 3,
    POWER_PERIODIC: 4,
    SHIPMENT: 8,
    PRODUCTS_SUMMATION: 9,
    PARCEL: 10,
    // MIXING: defined in its own files
    TRANSFER: 12,
    CLEANING: 13,
    MILLING: 14,
    GRAINS_SUMMATION: 15,
    0: "SCALES_INSTANT",
    1: "SCALES_PERIODIC",
    2: "SCALES_JOB",
    3: "POWER_INSTANT",
    4: "POWER_PERIODIC",
    8: "SHIPMENT",
    9: "PRODUCTS_SUMMATION",
    10: "PARCEL",
    // MIXING: defined in its own files
    12: "TRANSFER",
    13: "CLEANING",
    14: "MILLING",
    15: "GRAINS_SUMMATION",
};

const OPERATIONS = {
    TEST: 0,
    LABEL_VALUE: 1,
    LOSS_ON_B1: 2,
    LOSS_ON_B0: 3,
    CAPACITY_PERC_OVER_PLANT: 4,
    0: "TEST",
    1: "LABEL_VALUE",
    2: "LOSS_ON_B1",
    3: "LOSS_ON_B0",
    4: "CAPACITY_PERC_OVER_PLANT",
};

const PRECALCULATED_VALUES = {
    PERIOD: 0,
    EMT_OVER_PERIOD: 1,
    CAPACITY_ON_PERIOD: 2,
    UTILIZATION_OVER_CAPACITY: 3,
    CONSUMPTION_PER_QUANTITY: 4,
    0: "PERIOD",
    1: "EMT_OVER_PERIOD",
    2: "CAPACITY_ON_PERIOD",
    3: "UTILIZATION_OVER_CAPACITY",
    4: "CONSUMPTION_PER_QUANTITY",
};

const PIPES = {
    SECONDS_TO_TIME: "secondsToTime",
    PERCENTAGE: "percentage",
    DYNAMIC_WEIGHT_UNIT: "dynamicWeightUnit",
    DYNAMIC_FLOWRATE_UNIT: "dynamicFlowrateUnit",
    LINK: "link",
};

const UNITS = {
    WEIGHT_E_06_PER_HOUR: 0,
    WEIGHT_E_03_PER_HOUR: 1,
    WEIGHT_PER_HOUR: 2,
    DYNAMIC_FLOWRATE_UNIT: 3,
    VOLUME_PER_HOUR: 4,
    POWER_PER_HOUR: 5,
    WEIGHT: 6,
    VOLUME: 7,
    POWER: 8,
    POWER_PER_WEIGHT: 9,
    POWER_HOUR_PER_WEIGHT: 10,
    DYNAMIC_WEIGHT_UNIT: 11,
    WEIGHT_E_03: 12,
    WEIGHT_E_06: 13,
    PERCENTAGE: 14,
    0: "WEIGHT_E_06_PER_HOUR",
    1: "WEIGHT_E_03_PER_HOUR",
    2: "WEIGHT_PER_HOUR",
    3: "DYNAMIC_FLOWRATE_UNIT",
    4: "VOLUME_PER_HOUR",
    5: "POWER_PER_HOUR",
    6: "WEIGHT",
    7: "VOLUME",
    8: "POWER",
    9: "POWER_PER_WEIGHT",
    10: "POWER_HOUR_PER_WEIGHT",
    11: "DYNAMIC_WEIGHT_UNIT",
    12: "WEIGHT_E_03",
    13: "WEIGHT_E_06",
    14: "PERCENTAGE",
};

const DECIMAL_MODES = {
    AUTO: 0,
    FIXED: 1,
    0: "AUTO",
    1: "FIXED",
};
//#endregion Enumerables

//#region Constants
const DEFAULT_DECIMAL_PLACES = 2;

// "contents" is an array of strings which represent the names of the sub-elements that make up the table.
const ELEMENT_PARAMETERS = {
    GRID: ["grid", "content", "styles"],
    TABLE: ["header", "columns", "styles", "contents"],
    OPERATION: ["operation", "label", "styles"],
};

const MANDATORY_ELEMENT_PARAMETERS = {
    GRID: ["grid"],
    TABLE: ["columns", "contents"],
    OPERATION: ["operation"],
};

const DEFAULT_STRINGS = {
    PERCENTAGE: "%",
    GRAM: "g",
    KILOGRAM: "kg",
    TON: "t",
};

const PLACEHOLDER_STRING = "--"
//#endregion Constants

//#region Configurations
const CONFIGURATION = {
    SCALES_INSTANT: {
        title: "[MAIN_INSTANT_REPORT_TITLE]",
        grid: { x: 12, y: 5 },
        content: [
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 1 },
                size: { x: 6, y: 1 },
                parameters: {
                    operation: OPERATIONS.LABEL_VALUE,
                    label: "[DATE_LAST_READ]:",
                    value: "{top.last_read.val}",
                    styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 6, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            valueIndex: 0,
                        },
                    },
                    contents: ["wheat.tb_a"],
                },
            },
            {
                id: "table2",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 8, y: 2 },
                size: { x: 5, y: 1 },
                print: {
                    position: { x: 7, y: 2 },
                    size: { x: 6, y: 1 },
                },
                parameters: {
                    header: true,
                    hideIfEmpty: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[YIELDS_WATER]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.VOLUME_PER_HOUR,
                            valueIndex: 0,
                        },
                    },
                    contents: ["water.tb_a"],
                },
            },
            {
                id: "table3",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 3 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            valueIndex: 0,
                        },
                        onB0: {
                            index: 2,
                            title: "[YIELDS_B0_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B0.val" },
                        },
                        onB1: {
                            index: 3,
                            title: "[YIELDS_B1_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B1.val" },
                        },
                        onTot: {
                            index: 4,
                            title: "[YIELDS_TOT_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "product.tot.val" },
                        },
                    },
                    contents: ["product.tb_a", "product.sub_a", "product.tb_b", "product.sub_b", "product.tot"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-MD"]],
                },
            },
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 4 },
                size: { x: 4, y: 1 },
                parameters: {
                    operation: OPERATIONS.LOSS_ON_B0,
                    label: "[YIELDS_PLANT_LOSS]:",
                    styles: [CUSTOM_STYLES.BORDER, CUSTOM_STYLES["VALUE-TEXT-RIGHT"]],
                },
            },
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 5 },
                size: { x: 4, y: 1 },
                parameters: {
                    operation: OPERATIONS.LOSS_ON_B1,
                    label: "[YIELDS_MILL_LOSS]:",
                    styles: [CUSTOM_STYLES.BORDER, CUSTOM_STYLES["VALUE-TEXT-RIGHT"]],
                },
            },
        ],
    },
    SCALES_PERIODIC: {
        title: "[MAIN_PERIODIC_YIELDS_TITLE]",
        grid: { x: 12, y: 5 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 3, y: 3 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_MILL_TIME]:",
                                value: "{top.EMT.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                value: "{values.EMT_OVER_PERIOD.val:percentage} [YIELDS_OF_PERIOD]",
                                styles: [CUSTOM_STYLES["VALUE-TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            id: "scales-CAPACITY_UTILIZATION",
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_CAPACITY_UTILIZATION]:",
                                value: "{top.quantity.val} [WEIGHT] ([YIELDS_EXPECTED_UTILIZATION]: {values.CAPACITY_ON_PERIOD.val} [WEIGHT])",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                        {
                            id: "scales-UTILIZATION_OVER_CAPACITY",
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                value: "{values.UTILIZATION_OVER_CAPACITY.val:percentage} [YIELDS_OF_CAPACITY]",
                                styles: [CUSTOM_STYLES["VALUE-TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 6, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            valueIndex: 0,
                        },
                        averageOnPeriod: {
                            index: 2,
                            title: "[YIELDS_AVG_ON_PERIOD]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "values.PERIOD.val", over: 3600 },
                        },
                        averageOnTot: {
                            index: 3,
                            title: "[YIELDS_AVG_ON_WORKING_TIME]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "top.EMT.val", over: 3600 },
                        },
                    },
                    contents: ["wheat.tb_a"],
                },
            },
            {
                id: "table2",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 8, y: 2 },
                size: { x: 5, y: 1 },
                print: {
                    position: { x: 7, y: 2 },
                    size: { x: 6, y: 1 },
                },
                parameters: {
                    header: true,
                    hideIfEmpty: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[YIELDS_WATER]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.VOLUME,
                            valueIndex: 0,
                        },
                        averageOnPeriod: {
                            index: 2,
                            title: "[YIELDS_AVG_ON_PERIOD]",
                            unit: UNITS.VOLUME_PER_HOUR,
                            average: { of: "$i.val", on: "values.PERIOD.val", over: 3600 },
                        },
                        averageOnTot: {
                            index: 3,
                            title: "[YIELDS_AVG_ON_WORKING_TIME]",
                            unit: UNITS.VOLUME_PER_HOUR,
                            average: { of: "$i.val", on: "top.EMT.val", over: 3600 },
                        },
                    },
                    contents: ["water.tb_a"],
                },
            },
            {
                id: "table3",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 3 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            valueIndex: 0,
                        },
                        averageOnPeriod: {
                            index: 2,
                            title: "[YIELDS_AVG_ON_PERIOD]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "values.PERIOD.val", over: 3600 },
                        },
                        averageOnWorkTime: {
                            index: 3,
                            title: "[YIELDS_AVG_ON_WORKING_TIME]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "top.EMT.val", over: 3600 },
                        },
                        onB0: {
                            index: 4,
                            title: "[YIELDS_B0_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B0.val" },
                        },
                        onB1: {
                            index: 5,
                            title: "[YIELDS_B1_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B1.val" },
                        },
                        onTot: {
                            index: 6,
                            title: "[YIELDS_TOT_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "product.tot.val" },
                        },
                    },
                    contents: ["product.tb_a", "product.sub_a", "product.tb_b", "product.sub_b", "product.tot"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-MD"]],
                },
            },
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 4 },
                size: { x: 4, y: 1 },
                parameters: {
                    operation: OPERATIONS.LOSS_ON_B0,
                    label: "[YIELDS_PLANT_LOSS]:",
                    styles: [CUSTOM_STYLES.BORDER, CUSTOM_STYLES["VALUE-TEXT-RIGHT"]],
                },
            },
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 5 },
                size: { x: 4, y: 1 },
                parameters: {
                    operation: OPERATIONS.LOSS_ON_B1,
                    label: "[YIELDS_MILL_LOSS]:",
                    styles: [CUSTOM_STYLES.BORDER, CUSTOM_STYLES["VALUE-TEXT-RIGHT"]],
                },
            },
        ],
    },
    SCALES_JOB: {
        title: "[MAIN_JOB_YIELDS_TITLE]",
        grid: { x: 12, y: 5 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 12, y: 1 },
                parameters: {
                    grid: { x: 4, y: 4 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[JOB]:",
                                value: "{top.job.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 3, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[RECIPE NAME]:",
                                value: "{top.recipe_name.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_MILL_TIME]:",
                                value: "{top.EMT.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                value: "{values.EMT_OVER_PERIOD.val:percentage} [YIELDS_OF_PERIOD]",
                                styles: [CUSTOM_STYLES["VALUE-TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            id: "scales-CAPACITY_UTILIZATION",
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 4 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_CAPACITY_UTILIZATION]:",
                                value: "{top.quantity.val} [WEIGHT] ([YIELDS_EXPECTED_UTILIZATION]: {values.CAPACITY_ON_PERIOD.val} [WEIGHT])",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                        {
                            id: "scales-UTILIZATION_OVER_CAPACITY",
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 4 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                value: "{values.UTILIZATION_OVER_CAPACITY.val:percentage} [YIELDS_OF_CAPACITY]",
                                styles: [CUSTOM_STYLES["VALUE-TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 6, y: 1 },
                print: {
                    size: { x: 9, y: 1 },
                },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            valueIndex: 0,
                        },
                        averageOnPeriod: {
                            index: 2,
                            title: "[YIELDS_AVG_ON_PERIOD]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "values.PERIOD.val", over: 3600 },
                        },
                        averageOnTot: {
                            index: 3,
                            title: "[YIELDS_AVG_ON_WORKING_TIME]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "top.EMT.val", over: 3600 },
                        },
                    },
                    contents: ["wheat.tb_a"],
                },
            },
            {
                id: "table3",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 3 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    hideIfEmpty: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            valueIndex: 0,
                        },

                        averageOnPeriod: {
                            index: 2,
                            title: "[YIELDS_AVG_ON_PERIOD]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "values.PERIOD.val", over: 3600 },
                        },
                        averageOnWorkTime: {
                            index: 3,
                            title: "[YIELDS_AVG_ON_WORKING_TIME]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "top.EMT.val", over: 3600 },
                        },
                        onB0: {
                            index: 4,
                            title: "[YIELDS_B0_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B0.val" },
                        },
                        onB1: {
                            index: 5,
                            title: "[YIELDS_B1_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B1.val" },
                        },
                        onTot: {
                            index: 6,
                            title: "[YIELDS_TOT_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "product.tot.val" },
                        },
                    },
                    contents: ["product.tb_a", "product.sub_a", "product.tb_b", "product.sub_b", "product.tot"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-MD"]],
                },
            },
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 4 },
                size: { x: 4, y: 1 },
                parameters: {
                    operation: OPERATIONS.LOSS_ON_B0,
                    label: "[YIELDS_PLANT_LOSS]:",
                    styles: [CUSTOM_STYLES.BORDER, CUSTOM_STYLES["VALUE-TEXT-RIGHT"]],
                },
            },
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 5 },
                size: { x: 4, y: 1 },
                parameters: {
                    operation: OPERATIONS.LOSS_ON_B1,
                    label: "[YIELDS_MILL_LOSS]:",
                    styles: [CUSTOM_STYLES.BORDER, CUSTOM_STYLES["VALUE-TEXT-RIGHT"]],
                },
            },
        ],
    },
    POWER_INSTANT: {
        title: "[MAIN_INSTANT_POWER_REPORT_TITLE]",
        grid: { x: 12, y: 3 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 1, y: 4 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 6, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[DATE_LAST_READ]:",
                                value: "{top.last_read.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 6, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.quantity.val]:",
                                value: "{top.quantity.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                                hideIfNullName: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 4 },
                            size: { x: 6, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[CONSUMPTION_PER_QUANTITY]:",
                                value: "{values.CONSUMPTION_PER_QUANTITY.val} [POWER_PER_WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                            },
                        },
                    ],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 6, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[YIELDS_POW_MILL]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.POWER,
                            valueIndex: 0,
                        },
                    },
                    contents: ["power.tb_a", "power.tot"],
                },
            },
        ],
    },
    POWER_PERIODIC: {
        title: "[MAIN_PERIODIC_POWER_REPORT_TITLE]",
        grid: { x: 12, y: 3 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 3, y: 5 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_MILL_TIME]:",
                                value: "{top.EMT.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                value: "{values.EMT_OVER_PERIOD.val:percentage} [YIELDS_OF_PERIOD]",
                                styles: [CUSTOM_STYLES["VALUE-TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.quantity.val]:",
                                value: "{top.quantity.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                                hideIfNullName: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 5 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[CONSUMPTION_PER_QUANTITY]:",
                                value: "{values.CONSUMPTION_PER_QUANTITY.val} [POWER_HOUR_PER_WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 6, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[YIELDS_POW_MILL]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.POWER_PER_HOUR,
                            valueIndex: 0,
                        },
                    },
                    contents: ["power.tb_a", "power.tot"],
                },
            },
        ],
    },
    PRODUCTS_SUMMATION: {
        title: "[MAIN_PRODUCTS_SUMMATION_TITLE]",
        grid: { x: 12, y: 2 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 3, y: 2 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]]
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.B1.val]:",
                                value: "{top.B1.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[PRODUCTS]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.WEIGHT,
                            valueIndex: 0,
                        },
                        onB1: {
                            index: 2,
                            title: "[PROD_SUMMATION_ON_B1]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B1.val" },
                        },
                        onTot: {
                            index: 3,
                            title: "[PROD_SUMMATION_ON_TOT]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "product.tot.val" },
                        },
                    },
                    contents: ["product.tb_a", "product.tot"],
                },
            },
        ],
    },
    SHIPMENT: {
        title: "[MAIN_SHIPMENT_REPORT_TITLE]",
        grid: { x: 12, y: 3 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 5, y: 3 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[JOB]:",
                                value: "{top.job.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 1 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.shipmentDate.val]:",
                                value: "{top.shipmentDate.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.recipeDescription.val]:",
                                value: "{top.recipeDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.productDescription.val]:",
                                value: "{top.productDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.jobTotal.val]:",
                                value: "{top.jobTotal.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 4, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.lot.val]:",
                                value: "{top.lot.val:link}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "parcelShipmentTopGrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 2 },
                size: { x: 12, y: 1 },
                parameters: {
                    grid: { x: 5, y: 2 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 2, y: 3 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.shipmentDestination.val]:",
                                value: "{top.shipmentDestination.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-VERTICAL"], CUSTOM_STYLES["TEXT-TOP"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 4 },
                            size: { x: 2, y: 3 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.carrier.val]:",
                                value: "{top.carrier.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-VERTICAL"], CUSTOM_STYLES["TEXT-TOP"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"], CUSTOM_STYLES["PADDING-TOP-LG"]],
                },
            },
        ],
    },
    PARCEL: {
        title: "[MAIN_PARCEL_REPORT_TITLE]",
        grid: { x: 12, y: 4 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 5, y: 3 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[JOB]:",
                                value: "{top.job.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 1 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.parcelDate.val]:",
                                value: "{top.parcelDate.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.recipeDescription.val]:",
                                value: "{top.recipeDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.productDescription.val]:",
                                value: "{top.productDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.jobTotal.val]:",
                                value: "{top.jobTotal.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 4, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.lot.val]:",
                                value: "{top.lot.val:link}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "parcelShipmentTopGrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 2 },
                size: { x: 12, y: 1 },
                parameters: {
                    grid: { x: 5, y: 2 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 2, y: 3 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.parcelOrigin.val]:",
                                value: "{top.parcelOrigin.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-VERTICAL"], CUSTOM_STYLES["TEXT-TOP"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 4, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.carrier.val]:",
                                value: "{top.carrier.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"], CUSTOM_STYLES["PADDING-TOP-LG"]],
                },
            },
            {
                id: "IntakeTopGrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 4 },
                size: { x: 12, y: 1 },
                parameters: {
                    grid: { x: 12, y: 3 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 9, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[intake.titleintake.val]",
                                value: "",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-VERTICAL"], CUSTOM_STYLES["TEXT-TOP"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 4 },
                            size: { x: 3, y: 3 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[intake.intakeOrigin.val]",
                                value: "{intake.intakeOrigin.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-VERTICAL"], CUSTOM_STYLES["TEXT-TOP"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 7 },
                            size: { x: 3, y: 3 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[intake.carrier.val]",
                                value: "{intake.carrier.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-VERTICAL"], CUSTOM_STYLES["TEXT-TOP"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"], CUSTOM_STYLES["PADDING-TOP-LG"]],
                },
            },
            //{
            //    id: "table1",
            //    type: ELEMENT_TYPES.TABLE,
            //    position: { x: 1, y: 3 },
            //    size: { x: 12, y: 1 },
            //    parameters: {
            //        header: true,
            //        columns: {
            //            product: {
            //                index: 0,
            //                title: "[PRODUCTS]",
            //                main: true,
            //            },
            //            lot: {
            //                index: 1,
            //                title: "[Lotto]",
            //                noTotal: true,
            //                valueIndex: 0,
            //            },
            //            qty: {
            //                index: 2,
            //                title: "[QUANTITY]",
            //                unit: UNITS.WEIGHT,
            //                valueIndex: 1,
            //            },
            //        },
            //        contents: ["product.tb_a", "product.tot_a"],
            //    },
            //},
        ],
    },
    TRANSFER: {
        title: "[MAIN_TRANSFER_REPORT_TITLE]",
        grid: { x: 12, y: 2 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 4, y: 5 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[JOB]:",
                                value: "{top.job.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 3, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.cycle.val]:",
                                value: "{top.cycle.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "",
                                value: "{spacing}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                                hideIfNullValue: true
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 4 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.productDescription.val]:",
                                value: "{top.productDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 4 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.recipeDescription.val]:",
                                value: "{top.recipeDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 5 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.jobTotal.val]:",
                                value: "{top.jobTotal.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 5 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.destinationBin.val]:",
                                value: "{top.destinationBin.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 4, y: 5 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.lot.val]:",
                                value: "{top.lot.val:link}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[Source]",
                            main: true,
                        },
                        requested: {
                            index: 1,
                            title: "[REQUESTED_PERCENTAGE]",
                            unit: UNITS.PERCENTAGE,
                            valueIndex: 0,
                            hideIfNullValue: true,
                        },
                        actualWeight: {
                            index: 2,
                            title: "[ACTUAL_WEIGHT]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            decimals: 3,
                            valueIndex: 1,
                        },
                        actual: {
                            index: 3,
                            title: "[ACTUAL_PERCENTAGE]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.actualWeight", over: "product.tot.actualWeight" },
                        },
                    },
                    contents: ["product.tb_a", "product.tot"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-MD"]],
                },
            },
        ],
    },
    CLEANING: {
        title: "[MAIN_CLEANING_REPORT_TITLE]",
        grid: { x: 12, y: 3 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 12, y: 1 },
                parameters: {
                    grid: { x: 4, y: 7 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[JOB]:",
                                value: "{top.job.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 3, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.cycle.val]:",
                                value: "{top.cycle.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "",
                                value: "{spacing}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                                hideIfNullValue: true
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 4 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.productDescription.val]:",
                                value: "{top.productDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 4 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.recipeDescription.val]:",
                                value: "{top.recipeDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 5 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.jobTotal.val]:",
                                value: "{top.jobTotal.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 5 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.destinationBin.val]:",
                                value: "{top.destinationBin.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 4, y: 5 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.lot.val]:",
                                value: "{top.lot.val:link}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true,
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[PRODUCTS]",
                            main: true,
                        },
                        originDescription: {
                            title: "[Source]",
                            valueIndex: 0,
                            noTotal: true,
                        },
                        requested: {
                            index: 1,
                            title: "[REQUESTED_PERCENTAGE]",
                            unit: UNITS.PERCENTAGE,
                            valueIndex: 1,
                            hideIfNullValue: true,
                        },
                        actualWeight: {
                            index: 2,
                            title: "[ACTUAL_WEIGHT]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            decimals: 3,
                            valueIndex: 2,
                        },
                        actual: {
                            index: 3,
                            title: "[ACTUAL_PERCENTAGE]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.actualWeight", over: "product.tot_a.actualWeight" },
                        },
                    },
                    contents: ["product.tb_a", "product.tot_a"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-MD"], CUSTOM_STYLES["PADDING-BOTTOM-LG"]],
                },
            }, {
                id: "table2",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 3 },
                size: { x: 6, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[PRODUCTS]",
                            main: true,
                        },
                        quantity: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            decimals: 3,
                            valueIndex: 0,
                        },
                    },
                    contents: ["product.tb_b", "product.tot_b"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-LG"]],
                },
            },
        ],
    },
    MILLING: {
        title: "[MAIN_MILLING_REPORT_TITLE]",
        grid: { x: 12, y: 4 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 12, y: 1 },
                parameters: {
                    grid: { x: 4, y: 8 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[JOB]:",
                                value: "{top.job.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 3, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.cycle.val]:",
                                value: "{top.cycle.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 2 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 2 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 3 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "",
                                value: "{spacing}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                                hideIfNullValue: true
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 4 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.productDescription.val]:",
                                value: "{top.productDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 4 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.recipeDescription.val]:",
                                value: "{top.recipeDescription.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 5 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.jobTotal.val]:",
                                value: "{top.jobTotal.val} [WEIGHT]",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 5 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[top.lot.val]:",
                                value: "{top.lot.val:link}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                                hideIfNullValue: true,
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 6 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "",
                                value: "{spacing}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                                hideIfNullValue: true
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 7 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_MILL_TIME]:",
                                value: "{top.EMT.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 7 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                value: "{values.EMT_OVER_PERIOD.val:percentage} [YIELDS_OF_PERIOD]",
                                styles: [CUSTOM_STYLES["VALUE-TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            id: "scales-CAPACITY_UTILIZATION",
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 8 },
                            size: { x: 2, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_CAPACITY_UTILIZATION]:",
                                value: "{top.B1.val} [WEIGHT] ([YIELDS_EXPECTED_UTILIZATION]: {values.CAPACITY_ON_PERIOD.val} [WEIGHT])",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                            },
                        },
                        {
                            id: "scales-UTILIZATION_OVER_CAPACITY",
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 8 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                value: "{values.UTILIZATION_OVER_CAPACITY.val:percentage} [YIELDS_OF_CAPACITY]",
                                styles: [CUSTOM_STYLES["VALUE-TEXT-LEFT"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "tb_a",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 9, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        products: {
                            index: 1,
                            title: "[PRODUCTS]",
                            valueIndex: 0,
                            noTotal: true,
                        },
                        val: {
                            index: 2,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            valueIndex: 2,
                        },
                        averageOnPeriod: {
                            index: 3,
                            title: "[YIELDS_AVG_ON_PERIOD]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "values.PERIOD.val", over: 3600 },
                        },
                        averageOnTot: {
                            index: 4,
                            title: "[YIELDS_AVG_ON_WORKING_TIME]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "top.EMT.val", over: 3600 },
                        },
                    },
                    contents: ["product.tb_a", "product.tot_a"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-MD"], CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "tb_b",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 3 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[SCALE]",
                            main: true,
                        },
                        products: {
                            index: 1,
                            title: "[PRODUCTS]",
                            valueIndex: 0,
                            noTotal: true,
                        },
                        dest: {
                            index: 2,
                            title: "[CELL_TYPE]",
                            valueIndex: 1,
                            noTotal: true,
                        },
                        val: {
                            index: 3,
                            title: "[QUANTITY]",
                            unit: UNITS.DYNAMIC_WEIGHT_UNIT,
                            valueIndex: 2,
                        },
                        averageOnPeriod: {
                            index: 4,
                            title: "[YIELDS_AVG_ON_PERIOD]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "values.PERIOD.val", over: 3600 },
                        },
                        averageOnWorkTime: {
                            index: 5,
                            title: "[YIELDS_AVG_ON_WORKING_TIME]",
                            unit: UNITS.DYNAMIC_FLOWRATE_UNIT,
                            average: { of: "$i.val", on: "top.EMT.val", over: 3600 },
                        },
                        onB1: {
                            index: 6,
                            title: "[YIELDS_B1_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "top.B1.val" },
                        },
                        onTot: {
                            index: 7,
                            title: "[YIELDS_TOT_YIELDS]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "product.tot.val" },
                        },
                    },
                    contents: ["product.tb_b", "product.tot_b", "product.tb_c", "product.tot_c", "product.tot"],
                    styles: [CUSTOM_STYLES["PADDING-TOP-MD"]],
                },
            },
            {
                type: ELEMENT_TYPES.OPERATION,
                position: { x: 1, y: 4 },
                size: { x: 4, y: 1 },
                parameters: {
                    operation: OPERATIONS.LOSS_ON_B1,
                    label: "[YIELDS_MILL_LOSS]:",
                    styles: [CUSTOM_STYLES.BORDER, CUSTOM_STYLES["VALUE-TEXT-RIGHT"]],
                },
            },
        ],
    },
    GRAINS_SUMMATION: {
        title: "[MAIN_GRAINS_SUMMATION_TITLE]",
        grid: { x: 12, y: 2 },
        content: [
            {
                id: "topgrid",
                type: ELEMENT_TYPES.GRID,
                position: { x: 1, y: 1 },
                size: { x: 9, y: 1 },
                print: {
                    size: { x: 12, y: 1 },
                },
                parameters: {
                    grid: { x: 3, y: 1 },
                    content: [
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 1, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[FROM]:",
                                value: "{top.dateFrom.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 2, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[TO]:",
                                value: "{top.dateTo.val}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]]
                            },
                        },
                        {
                            type: ELEMENT_TYPES.OPERATION,
                            position: { x: 3, y: 1 },
                            size: { x: 1, y: 1 },
                            parameters: {
                                operation: OPERATIONS.LABEL_VALUE,
                                label: "[YIELDS_PERIOD_LENGTH]:",
                                value: "{values.PERIOD.val:secondsToTime}",
                                styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                            },
                        },
                    ],
                    styles: [CUSTOM_STYLES["PADDING-BOTTOM-MD"]],
                },
            },
            {
                id: "table1",
                type: ELEMENT_TYPES.TABLE,
                position: { x: 1, y: 2 },
                size: { x: 12, y: 1 },
                parameters: {
                    header: true,
                    columns: {
                        name: {
                            index: 0,
                            title: "[PRODUCTS]",
                            main: true,
                        },
                        val: {
                            index: 1,
                            title: "[QUANTITY]",
                            unit: UNITS.WEIGHT,
                            valueIndex: 0,
                        },
                        onTot: {
                            index: 3,
                            title: "[PROD_SUMMATION_ON_TOT]",
                            unit: UNITS.PERCENTAGE,
                            percentage: { of: "$i.val", over: "product.tot.val" },
                        },
                    },
                    contents: ["product.tb_a", "product.tot"],
                },
            },
        ],
    },
};

var specificReportCode = {};
//#endregion Configurations