// (*): string in which words in brackets are replaced by the translation identified by the key in brackets

// (**): string in which words in brackets are replaced by the translation identified by the key in brackets, and words in curly braces
//        are replaced by the value identified by the text inside. Also accepts pipes (function that modify the raw value, for example by turning a number of seconds in
//        a string representing time) after the value key followe by ':', like so {valuekey:pipe}.

// (***): the yields reports only get one value as input for the tables, the quantity, the other columns are calculations between these values and totals/peculiar values.
//        With the shipment/parcel repors came the need to work with more inputs: in this case the objects that held the values previously, structured like this [... {"id": "XXX", "val": YYY} ...],
//        now can have the "val" key corresponding to an array-type value ( [... {"id": "XXX", "val": [YYY, ZZZ]} ...] ). In the columns configuration, therefore, the key "valueIndex" represents
//        the index of the desired value in said array. This improvement expands the capabilities of the reports, making them capable of drawing and managing many, if not all, kinds of data tables.

//#region Sub

type Coordinate = {
    x: number;
    y: number;
}

enum HEADER_ELEMENT_TYPE {
    IMG,
    TEXT
};

enum CUSTOM_STYLES {
    BORDER,
    "BORDER-TOP",
    "BORDER-RIGHT",
    "BORDER-BOTTOM",
    "BORDER-LEFT",
    "BORDER-Y",
    "BORDER-X",
    BOLD,
    "BOLD-VALUE",
    "BOLD-LABEL",
    "BG-GRAY",
    "MARGIN-AUTO",
    "MARGIN-TOP-AUTO",
    "MARGIN-RIGHT-AUTO",
    "MARGIN-BOTTOM-AUTO",
    "MARGIN-LEFT-AUTO",
    "MARGIN-Y-AUTO",
    "MARGIN-X-AUTO",
    "TEXT-CENTER",
    "TEXT-LEFT",
    "TEXT-RIGHT",
    "TEXT-TOP",
    "TEXT-MIDDLE",
    "TEXT-BOTTOM",
    "BG-POSITION-Y-TOP",
    "BG-POSITION-Y-CENTER",
    "BG-POSITION-Y-BOTTOM",
    "BG-POSITION-X-LEFT",
    "BG-POSITION-X-CENTER",
    "BG-POSITION-X-RIGHT",
    "LABEL-TEXT-CENTER",
    "LABEL-TEXT-LEFT",
    "LABEL-TEXT-RIGHT",
    "VALUE-TEXT-CENTER",
    "VALUE-TEXT-LEFT",
    "VALUE-TEXT-RIGHT",
    "LABEL-VALUE-WIDTH-ADAPT",
    "LABEL-VALUE-25-75",
    "LABEL-VALUE-75-25",
    "PADDING-TOP-SM",
    "PADDING-TOP-MD",
    "PADDING-TOP-LG",
    "PADDING-RIGHT-SM",
    "PADDING-RIGHT-MD",
    "PADDING-RIGHT-LG",
    "PADDING-BOTTOM-SM",
    "PADDING-BOTTOM-MD",
    "PADDING-BOTTOM-LG",
    "PADDING-LEFT-SM",
    "PADDING-LEFT-MD",
    "PADDING-LEFT-LG",
    "PADDING-X-SM",
    "PADDING-X-MD",
    "PADDING-X-LG",
    "PADDING-Y-SM",
    "PADDING-Y-MD",
    "PADDING-Y-LG",
    "LABEL-VALUE-VERTICAL",
}

type HeaderElementParameters = {
    src?: string;               // For images, the src attribute of the image
    text?: string;              // For text elements, a representation of the contained text (*)
    styles?: CUSTOM_STYLES[];
}

type HeaderElement = {
    type: HEADER_ELEMENT_TYPE;
    position: Coordinate;
    size: Coordinate;
    parameters: HeaderElementParameters;
}

enum REPORT_TYPE {
    SCALES_INSTANT,
    SCALES_PERIODIC,
    SCALES_JOB,
    POWER_INSTANT,
    POWER_PERIODIC,
    SHIPMENT = 8,
    PRODUCTS_SUMMATION = 9,
    PARCEL = 10,
    MIXING = 11,
}

enum REPORT_ELEMENT_TYPE {
    GRID,
    TABLE,
    OPERATION,
    MIXING_PANEL,
}

enum UNIT {
    WEIGHT_E_06_PER_HOUR,
    WEIGHT_E_03_PER_HOUR,
    WEIGHT_PER_HOUR,
    DYNAMIC_FLOWRATE_UNIT,
    VOLUME_PER_HOUR,
    POWER_PER_HOUR,
    WEIGHT,
    VOLUME,
    POWER,
    POWER_PER_WEIGHT,
    POWER_HOUR_PER_WEIGHT,
    DYNAMIC_WEIGHT_UNIT,
    WEIGHT_E_03,
    WEIGHT_E_06,
    PERCENTAGE
}

enum OPERATION {
    TEST,
    LABEL_VALUE,
    LOSS_ON_B1,
    LOSS_ON_B0,
    UTILIZATION,
    CAPACITY_PERC_OVER_PLANT,
}

interface ReportGridParameters {
    grid?: Coordinate;
    content?: ReportElement[];
}

type ReportTableColumn = {
    index: number;                                                                    // Column order
    title?: string;                                                                   // Translation key for the header
    main?: boolean;                                                                   // True only for the main column, or the one filled by nameJSON
    valueIndex?: boolean;                                                             // Index of the value in the value array from the server ({id: X, val: [Y,Z]}) ***
    unit?: UNIT | string;
    decimals?: number;                                                                // Number of decimal places, if not defined will default to DEFAULT_DECIMAL_PLACES
    average?: { of: string | number; on: string | number; over: string | number };    // Calculates the average by dividing X (of) by Y (on) and multiplying by Z (over)
    percentage?: { of: string | number; over: string | number };                      // Calculates the percentage by dividing X (of) by Y (over) and multiplying by 100
    width?: number;                                                                   // Width as a ratio (0 => no width, 1 => full width)
    noTotal?: boolean;                                                                // Ignore total for the column
    hideIfNullValue?: boolean;                                                        // The content of the table cell will become invisible if the value is null
}

type ReportTableColumns = {
    [columnName: string]: ReportTableColumn;
}

interface ReportTableParameters {
    header?: boolean;                   // Shows or hides the table header, defaults to false
    hideIfEmpty?: boolean;              // Hides the table if no rows were found for it
    columns?: ReportTableColumns;
    contents?: string[];                // List of strings identifying which data from the server goes in the table (for example ['wheat.tb_a', 'wheat.tot'])
    styles?: CUSTOM_STYLES[];
}

interface ReportOperationParameters {
    operation?: OPERATION;
    label?: string;                 // Text to put into the label, formatted like (*)
    value?: string;                 // Value to put into the value span, formatted like (**)
    styles?: CUSTOM_STYLES[];
    hideIfNullValue?: boolean;      //  The whole element will become invisible if the value is null
    hideIfNullName?: boolean;       //  The whole element will become invisible if the name is null
}

interface ReportMixingPanelParameters {
    header?: boolean;                   // Shows or hides the header, defaults to false
    contents?: string[];                // List of strings identifying which data from the server goes in the table (for example ['batch.tb_a', 'batch.tot'])
    styles?: CUSTOM_STYLES[];
}

interface ReportElementParameters extends ReportGridParameters, ReportTableParameters, ReportOperationParameters, ReportMixingPanelParameters { }

type ReportElement = {
    id?: string;                                                // Id of the element, it is best practise to specify it, because it's required for some behaviours
    type: REPORT_ELEMENT_TYPE;
    position: Coordinate;
    size: Coordinate;
    print?: { position?: Coordinate; size?: Coordinate };     // Position and size that overwrite standard ones when printing
    parameters: ReportElementParameters;
    visible?: boolean;                                          // Pretty straight forward, the element doesn't get drawn if set to false
}

type ReportConfiguration = {
    title: string;                // Top title formatted like (*)
    grid: Coordinate;
    content: ReportElement[];
}

enum REPORT_TYPE_IDX {
    // Specific reference to a report type (allows for customizing reports of the same type in different contexts)
}

//#endregion Sub

//#region Main

type Header = {
    grid: Coordinate;
    content: HeaderElement[];
};

type Configuration = {
    [key in keyof typeof REPORT_TYPE]: ReportConfiguration
};

type SpecificConfiguration = {
    [key in keyof typeof REPORT_TYPE_IDX]: { [key in keyof typeof REPORT_TYPE]: ReportConfiguration }
};

//#endregion Main