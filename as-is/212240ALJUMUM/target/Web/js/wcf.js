﻿/* Inizio sezione per la gestione degli export data */
function ExportOWS() {
    document.getElementById('hLink').style.display = "none";

    var data_inizio = document.getElementById("txtFromData").value;
    var data_fine = document.getElementById("txtToData").value;

    if (data_inizio == null || data_inizio == '') {
        alert(document.getElementById("HiddenExportResultFailedFromDate").value);
        return;
    }
    if (data_fine == null || data_fine == '') {
        alert(document.getElementById("HiddenExportResultFailedToDate").value);
        return;
    }

    Url = "";
    Type = "POST";
    if (document.getElementById("rbIntake").checked) {
        Url = window.location.origin + "/" + document.getElementById("HiddenWcfPath").value + "/WebExportOWS/wGetIntakeData";
    }
    else if (document.getElementById("rbBagging").checked) {
        Url = window.location.origin + "/" + document.getElementById("HiddenWcfPath").value + "/WebExportOWS/wGetBaggingData";
    }
    else if (document.getElementById("rbLoadout").checked) {
        Url = window.location.origin + "/" + document.getElementById("HiddenWcfPath").value + "/WebExportOWS/wGetLoadoutData";
    }
    else if (document.getElementById("rbShip").checked) {
        Url = window.location.origin + "/" + document.getElementById("HiddenWcfPath").value + "/WebExportOWS/wGetShipmentsData";
    }
    else if (document.getElementById("rbFlows").checked) {
        Url = window.location.origin + "/" + document.getElementById("HiddenWcfPath").value + "/WebExportOWS/wGetFlowLogsData";
    }
    else if (document.getElementById("rbProd").checked) {
        Url = window.location.origin + "/" + document.getElementById("HiddenWcfPath").value + "/WebExportOWS/wGetProductionData";
    }
    else {
        alert('[ExportOWS] Radio Button non configurato in wcf.js');
        return;
    }
    Data = "{from_date: '" + data_inizio + "',to_date: '" + data_fine + "'}";
    Data = JSON.stringify({ s_json_string: "'" + Data + "'" });
    ContentType = "application/json; charset=utf-8";
    varProcessData = true;
    CallService('ExportOWS');
}

function ExportSucceeded(result) {
    if (result.d.length >= 1) {
        if (result.d[0].includes('file:')) {
            let text = result.d[0];
            const myArray = text.split("file:");
            let file = myArray[1];

            document.getElementById('hLink').style.display = 'inline';
            document.getElementById("HiddenExportPath").value = file;

            alert(document.getElementById("HiddenExportResultOk").value);
        }
        else {
            alert(result.d[0]);
        }
    }
    else {
        alert("[" + from_called + "] Warning control log file");
    }
}

/*****************************************************/

/* chiamate generiche da valutare se possono essere utilizzate in tutti i contesti */
function CallService(from_called) {
    $.ajax({
        type: Type, //GET or POST or PUT or DELETE verb
        url: Url, // Location of the service
        data: Data, //Data sent to server
        contentType: ContentType, // content type sent to server
        dataType: 'json', //Expected data format from server
        processdata: varProcessData, //True or False
        success: function (msg) {
            //On Successfull service call
            ServiceSucceeded(msg, from_called);
        },
        error: function (msg) {
            // When Service call fails
            ServiceFailed(msg);
        }
    });
}

function ServiceSucceeded(result, from_called) {
    switch (from_called) {
        case 'ExportOWS':
            ExportSucceeded(result);
            break;
        default:
            alert("[ServiceSucceeded][" + from_called + "] Not switch case configured");
            break;
    }
}

function ServiceFailed(xhr) {
    alert('Error: ' + xhr.responseText);
    return;
}