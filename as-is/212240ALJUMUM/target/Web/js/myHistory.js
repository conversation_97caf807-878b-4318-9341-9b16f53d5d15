﻿var my_history = null;
var current_page = null;

//definizione oggetto contenente i parametri della sezione view-search
search_obj = function () {
    this.btn_search = null;   //id: UCView_btnViewSearch
    this.input_elems = { list: [] };
    this.select_elems = { list: [] };
    this.check_elems = { list: [] };
    this.exist_filter = false;
    this.all_filter = [];
    this.applied_filters = [];
};
//definizione oggetto history
history_obj = function (old_history) {
    this.page_list = ((old_history != undefined) && (old_history != null)) ? old_history.page_list : [];
    this.act_index = ((old_history != undefined) && (old_history != null)) ? old_history.act_index : null;
    this.last_valid_index = ((old_history != undefined) && (old_history != null)) ? old_history.last_valid_index : null;

    this.history_length = 50;
    this.back_step = (old_history != undefined) ? old_history.back_step : BACK_STEP.APPLIED;
    this.jump_back = ((old_history != undefined) && (old_history != null)) ? old_history.jump_back : 0;
    this.jump_next = ((old_history != undefined) && (old_history != null)) ? old_history.jump_next : 0;

    //definisco "page_list" come array di dimensione finita di "history_length" oggetti di tipo "page_data_obj"
    this.setNewPageList = function () {
        this.page_list = [];
        for (var c = 0; c < this.history_length; c++) {
            this.page_list.push(new page_data_obj())
        }
    };
    /** restituisco l'oggetto relativo alla pagina di indice "act_index"
     * @return {page_data_obj} oggetto che definisce la pagina corrente
     */
    this.getActPage = function () {
        var page_to_return = new page_data_obj();
        var pag_act_data = null;
        if (this.act_index != null) {
            pag_act_data = this.page_list[this.act_index];
            page_to_return.isValid = pag_act_data.isValid;
            page_to_return.isReloaded = pag_act_data.isReloaded;
            page_to_return.url = pag_act_data.url;
            page_to_return.aspx_name = pag_act_data.aspx_name;
            page_to_return.name = pag_act_data.name;
            page_to_return.url_params = pag_act_data.url_params;
            page_to_return.search_params = pag_act_data.search_params;
            page_to_return.pagination_index = pag_act_data.pagination_index;
        }
        return page_to_return;
    };
    /** setto la nuova pagina come attuale, incrementando l'indice della pagina puntata da "act_index"
     * @param {page_data_obj} page  -   oggetto pagina da inserire
     */
    this.setActPage = function (page) {
        page.isReloaded = false;

        if ((this.act_index == this.history_length - 1) || (this.act_index == null)) { //sono all'ultimo elemento o è il primo inserito
            this.act_index = 0;
        } else {
            this.act_index++;
        }

        this.page_list[this.act_index] = page;
        this.updateValid();
        this.last_valid_index = this.act_index;
    };
    /** aggiorno i dati relativi alla pagina di indice "act_index"
     * @param {page_data_obj} page  -   oggetto pagina da aggiornare
     */
    this.updateActPage = function (page) {
        page.isReloaded = true;
        this.page_list[this.act_index] = page;
    };
    /** per ogni "jump_back" precedentemetne effettuato, setto a "valid=false"
     * tutte le pagine tra la "act_index" e quella precedentemente visualizzata
     */
    this.updateValid = function () {
        if (this.jump_back > 0) {
            var temp_index = 0;

            for (var p = 0; p < this.jump_back - 1; p++) {
                if (p > this.last_valid_index) {
                    temp_index = this.history_length + this.last_valid_index - p;
                } else {
                    temp_index = this.last_valid_index - p;
                }
                this.page_list[temp_index].isValid = false;
            }

            this.jump_back = 0;
        }

        if (this.jump_next > 0) {
            this.jump_next = 0;
        }
    };

    /** ritorna l'indice precedente a quello in ingresso
     * tenendo conto della lunghezza massima dell'array "page_list" ("history_length")
     * @param {integer} index   -   indice da elaborare
     * @return {integer} "index-1" o "history_length-1" se "index==0"
     */
    this.GetPrevIndex = function (index) {
        var temp_index = index;
        if (temp_index == 0) {
            temp_index = this.history_length - 1;
        } else {
            temp_index -= 1;
        }
        return temp_index;
    };
    /** ritorna l'indice successivo a quello in ingresso
    * tenendo conto della lunghezza massima dell'array "page_list" ("history_length")
    * @param {integer} index   -   indice da elaborare
    * @return {integer} "index+1" o "0" se "index==history_length-1"
    */
    this.GetNextIndex = function (index) {
        var temp_index = index;
        if (temp_index == this.history_length - 1) {
            temp_index = 0;
        } else {
            temp_index += 1;
        }
        return temp_index;
    };
    /** ritorna l'indice precedente dell'array di "page_list" con "valid == true"
     * @return {integer} indice precedente tale che page.isValid == true
     */
    this.GetPrevValidIndex = function () {
        var temp_index = this.act_index;
        var old_jump_back = this.jump_back;
        if (this.jump_back < this.history_length) {
            do {
                old_jump_back += 1;
                temp_index = this.GetPrevIndex(temp_index);
            } while ((!this.page_list[temp_index].isValid) && (old_jump_back < this.history_length - 1));

            //nel caso non esistano altre pagine valide, accetto solo l'attuale
            if (!this.page_list[temp_index].isValid) {
                temp_index = this.act_index;
            }
        }
        return temp_index;
    };
    /** ritorna l'indice successivo dell'array di "page_list" solo se la pagina successiva è "valid == true"
     * @return {integer} indice successivo tale che page.isValid == true, non ritorna niente se la pagina successiva è page.isValid == false
     */
    this.GetNextValidIndex = function () {
        var temp_index = this.act_index;
        if (this.jump_back > 0) {
            if (this.page_list[temp_index + 1].isValid) {
                temp_index += 1;
            }
        }
        return temp_index;
    };
    /** ritorna un array contenente tutti gli indici delle pagine valide in "page_lsit"
     * @return {array} array di indici di pagine valide
     */
    this.GetAllValidIndex = function () {
        var to_return = [];

        for (var i = 0; i < this.history_length; i++) {
            if (this.page_list[i].isValid) {
                to_return.push(i);
            }
        }

        return to_return;
    };
};
//definizione oggetto pagina
page_data_obj = function (data) {
    this.isValid = ((data != undefined) && (data != null)) ? data.isValid : false;
    this.isReloaded = ((data != undefined) && (data != null)) ? data.isReloaded : false;
    this.url = ((data != undefined) && (data != null)) ? data.url : null;
    this.aspx_name = ((data != undefined) && (data != null)) ? data.aspx_name : null;
    this.url_params = ((data != undefined) && (data != null)) ? data.params : { list: [] };
    this.search_params = ((data != undefined) && (data != null)) ? data.search_params : null;
    this.pagination_index = ((data != undefined) && (data != null)) ? data.pagination_index : null;

    //pulisco i parametri acquisiti dal "url"
    this.clearData = function () {
        this.url_params = { list: [] };
    };
    /** restituisce una copia dell'pagina in questione
     * @return {page_data_obj} copia della pagina
     **/
    this.clone = function () {
        var obj_clone = new page_data_obj();
        obj_clone.isValid = this.isValid;
        obj_clone.isReloaded = this.isReloaded;
        obj_clone.url = this.url;
        obj_clone.aspx_name = this.aspx_name;
        obj_clone.url_params = this.url_params;
        obj_clone.search_params = this.search_params;
        obj_clone.pagination_index = this.pagination_index;
        return obj_clone;
    };

    /** verifica se tutti i parametri contenuti nella lista "list" dell'oggetto in ingresso, sono presenti e uguali a quelli
     * del secondo oggetto in ingresso
     * @param {obj} first_obj
     * @param {obj} second_obj
     */
    this.haveSameParam = function (first_obj, second_obj) {
        var to_return = true;
        for (var p = 0; p < first_obj.list.length; p++) {  //la sista di parametri url è la stessa, controllo ognuno di essi
            if (first_obj.list[p] == second_obj.list[p]) {    //i nomi dei parametri url sono gli stessi nelle stesse posizioni
                if (first_obj[first_obj.list[p]] != second_obj[first_obj.list[p]]) { //il valore dei parametri è lo stesso
                    to_return = false;
                }
            } else {
                to_return = false;
            }
        }
        return to_return;
    };

    /** verifica se i due url in ingresso sono gli stessi, applicando un confronto per carattere, verificando eventuali
     * equivalenze tra caratteri decimali ed esadecimali in caso di differenze prima di definire i due url diversi
     * @param {string} url_1    -   primo url
     * @param {string} url_2    -   secondo url
     * @return {bool}   ritorna true se url_1 === url_2
     */
    this.isSameUrl = function (url_1, url_2) {
        var to_return = false;
        if ((url_1 != null) && (url_2 != null)) {
            if (url_1 != url_2) {
                if ((url_1 != "") && (url_2 != "")) {
                    var ind_1 = 0, ind_2 = 0, ind_sub = 0;
                    var char_1, char_2, conv_1, conv_2;
                    var sub_bool = false;
                    to_return = true;
                    do {
                        char_1 = url_1[ind_1];
                        char_2 = url_2[ind_2];
                        if (char_1 == char_2) {
                            ind_1 += 1;
                            ind_2 += 1;
                        } else {
                            if (char_1 == "%") {
                                conv_2 = char_2.charCodeAt(0).toString(16);
                                sub_bool = true;
                                for (ind_sub = 0; ind_sub < conv_2.length; ind_sub++) {
                                    if (url_1[ind_1 + 1 + ind_sub] != conv_2[ind_sub]) {
                                        sub_bool = false;
                                    }
                                }
                                if (sub_bool) {
                                    ind_1 += conv_2.length + 1;
                                    ind_2 += 1;
                                } else {
                                    to_return = false;
                                }
                            } else if (char_2 == "%") {
                                conv_1 = char_1.charCodeAt(0).toString(16);
                                sub_bool = true;
                                for (ind_sub = 0; ind_sub < conv_1.length; ind_sub++) {
                                    if (url_2[ind_2 + 1 + ind_sub] != conv_1[ind_sub]) {
                                        sub_bool = false;
                                    }
                                }
                                if (sub_bool) {
                                    ind_1 += 1;
                                    ind_2 += conv_1.length + 1;
                                } else {
                                    to_return = false;
                                }
                            } else {
                                to_return = false;
                            }
                        }
                    } while ((ind_1 < url_1.length) && (ind_2 < url_2.length) && (to_return));

                    if ((ind_1 < url_1.length) || (ind_2 < url_2.length)) {
                        //uno dei due url ha ancora dei caratteri
                        to_return = false;
                    }
                }
            } else {
                to_return = true;
            }
        }
        return to_return;
    };

    /** verifica se la pagina in ingresso è la stessa di quella in questione, paragonando alcuni parametri delle stesse
     * @param {page_data_obj} old_page_data -   pagina da paragonare
     * @return {bool} true se le pagine hanno gli stessi parametri, false altrimenti e se non è presente il parametro in ingresso
     */
    this.isSamePage = function (old_page_data) {
        var to_return = false;
        if (old_page_data != null) {
            to_return = true;
            if ((this.isValid != old_page_data.isValid) ||
                (!this.isSameUrl(this.url, old_page_data.url)) ||
                (this.aspx_name != old_page_data.aspx_name)) {
                to_return = false;
            } else {
                /*  se anche la lista di parametri dell'url è uguale, passo a confrontare la lista di parametri di search:
                 * 1) entrambe le search sono diverse da "null" -> in entrambe ho dei valori di search -> isSamePage = (continuo la verifica)
                 * 2) entrembe sono "null" -> non esiste search in entrambe le pagine -> isSamePage = true
                 * 3) this != null && old = null -> prima non esisteva search e ora si nella pagina con stesso url -> prima ero sloggato e ora loggato -> isSamePage = true
                 * 4) this = null && old != null -> prima esisteva search e ora no nella pagina con stesso url -> prima ero loggato e ora no -> NON puo essere in quanto al logout viene reindirizzato alla home
                 */
                if ((this.search_params != null) && (old_page_data.search_params != null)) { //caso 1
                    if ((!this.search_params.input_elems.list.equals(old_page_data.search_params.input_elems.list)) ||
                        (!this.search_params.select_elems.list.equals(old_page_data.search_params.select_elems.list)) ||
                        (!this.search_params.check_elems.list.equals(old_page_data.search_params.check_elems.list))) {
                        to_return = false;
                    } else {
                        if (to_return) {
                            to_return = this.haveSameParam(this.search_params.input_elems, old_page_data.search_params.input_elems);
                            if (to_return) {
                                to_return = this.haveSameParam(this.search_params.select_elems, old_page_data.search_params.select_elems);
                                if (to_return) {
                                    to_return = this.haveSameParam(this.search_params.check_elems, old_page_data.search_params.check_elems);
                                }
                            }
                        }
                    }
                } else {    //caso 2, 3 e 4
                    //ritorno true
                }
            }
        } else {
            to_return = false;
        }
        return to_return;
    };
    //acquisizione dei parametri del view-search della pagina
    this.getParams = function () {
        function GetTagValue(elem) {    //dove "elem" è la seconda "td" della r-esima "tr"
            var is_invalid = false;
            var to_return = {
                elem_input: elem.querySelectorAll("input:not([type=checkbox]):not([type=radio])"),
                elem_select: elem.querySelectorAll("select"),
                elem_checkbox: elem.querySelectorAll("input[type=checkbox], input[type=radio]")
            };
            //condizione di invalidità della riga
            /* verifico se ho preso dei gruppi di tag di date, verificando se nell'id degli input è presente DATE_DATE_DA o DATE_DATE_A
             * che sono i campi principali tali da discriminare se la riga di dati in questione è da acquisire o meno
             */
            if (to_return.elem_input != null) {
                for (var i = 0; i < to_return.elem_input.length; i++) {
                    if ((/DATE_DATE_DA/i.test(to_return.elem_input[i].id)) ||
                        (/DATE_DATE_A/i.test(to_return.elem_input[i].id))) {
                        if (to_return.elem_input[i].value == "") {
                            is_invalid = true;
                        }
                    }
                }
            }

            //resetto to_return in caso di invalidità della riga
            if (is_invalid) {
                to_return.elem_input = [];
                to_return.elem_select = [];
                to_return.elem_checkbox = [];
            }

            return to_return;
        }

        var search_objs = new search_obj();
        /*  il campo di search è visibile al web (non nascosto da vb/non creato)
         * in questo caso procedo al recupero dei valori degli elementi al suo interno,
         * altrimenti setto a "null" il "search_objs"
         */
        if (document.getElementById("UCView_div_query") != null) {
            var reference_column = null;
            var elem_value_list = null;
            var elem_value = null;
            var is_applied_filter;

            var row_list = document.querySelectorAll("#UCView_tblQuery tbody tr")
            for (var r = 0; r < row_list.length; r++) {
                reference_column = row_list[r].children[1].getAttribute("ReferenceColumn");
                is_applied_filter = row_list[r].children[1].hasAttribute("applied");
                elem_value_list = GetTagValue(row_list[r].children[1]);

                if (is_applied_filter) {
                    search_objs.applied_filters.push(reference_column);
                }

                if (elem_value_list.elem_input.length > 0) {
                    for (var i = 0; i < elem_value_list.elem_input.length; i++) {
                        elem_value = elem_value_list.elem_input[i].value;
                        if ((elem_value != null) && (elem_value != undefined) && (elem_value != "")) {
                            search_objs.input_elems[elem_value_list.elem_input[i].id] = elem_value;
                            search_objs.input_elems.list.push(elem_value_list.elem_input[i].id);
                            if (reference_column != null) {
                                search_objs.all_filter.push(reference_column);
                            }
                            search_objs.exist_filter = true;
                        }
                    }
                }

                if (elem_value_list.elem_select.length > 0) {
                    for (var i = 0; i < elem_value_list.elem_select.length; i++) {
                        elem_value = elem_value_list.elem_select[i].value;
                        if ((elem_value != null) && (elem_value != undefined) && (elem_value != "")) {
                            if (elem_value_list.elem_select[i].getAttribute("disabled") != "disabled") {
                                search_objs.select_elems[elem_value_list.elem_select[i].id] = elem_value;
                                search_objs.select_elems.list.push(elem_value_list.elem_select[i].id);
                                if (reference_column != null) {
                                    search_objs.all_filter.push(reference_column);
                                }
                                search_objs.exist_filter = true;
                            }
                        }
                    }
                }

                if (elem_value_list.elem_checkbox.length > 0) {
                    for (var i = 0; i < elem_value_list.elem_checkbox.length; i++) {
                        elem_value = elem_value_list.elem_checkbox[i].checked;
                        if ((elem_value != null) && (elem_value != undefined) && (elem_value != "")) {
                            search_objs.check_elems[elem_value_list.elem_checkbox[i].id] = elem_value;
                            search_objs.check_elems.list.push(elem_value_list.elem_checkbox[i].id);
                            if (reference_column != null) {
                                search_objs.all_filter.push(reference_column);
                            }
                            search_objs.exist_filter = true;
                        }
                    }
                }
            }

            var btn_search = document.querySelector("#UCView_btnViewSearch");

            if (btn_search != null) {
                var btn_search_value = btn_search.getAttribute("myValue");
                search_objs.btn_search = btn_search_value;
            }
        } else {
            search_objs = null;
        }

        this.search_params = search_objs;
    };
    //inserimento dei parametri della view-search della pagina e simulazione pressione pulsanti per conferma
    this.restoreState = function () {
        function isAppliedFilter(elem_id) {
            return search_objs.applied_filters.indexOf($(`#${elem_id}`).parents(`[ReferenceColumn]`).attr("ReferenceColumn")) !== -1;
        }

        InsertValue = {
            inputElem: function (obj) {
                for (var el = 0; el < obj.list.length; el++) {
                    if (isAppliedFilter(obj.list[el])) {
                        document.getElementById(obj.list[el]).value = obj[obj.list[el]];
                        console.log("Applied filter: " + obj.list[el]);
                    }
                }
            },
            selectElem: function (obj) {
                for (var el = 0; el < obj.list.length; el++) {
                    if (isAppliedFilter(obj.list[el])) {
                        document.getElementById(obj.list[el]).value = obj[obj.list[el]];
                        console.log("Applied filter: " + obj.list[el]);
                    }
                }
            },
            checkElem: function (obj) {
                for (var el = 0; el < obj.list.length; el++) {
                    if (isAppliedFilter(obj.list[el])) {
                        document.getElementById(obj.list[el]).checked = obj[obj.list[el]];
                        console.log("Applied filter: " + obj.list[el]);
                    }
                }
            }
        };
        var search_objs = current_page.search_params;
        var search_value_up = false;

        if (search_objs) {
            // Insert values into the corresponding fields
            //  Field type: input
            if (search_objs.input_elems.list.length > 0 && search_objs.input_elems.list.filter((elem_id) => isAppliedFilter(elem_id))) {
                InsertValue.inputElem(search_objs.input_elems);
                search_value_up = true;
            } else {
                //nothing
            }

            //  Field type: select
            if (search_objs.select_elems.list.length > 0 && search_objs.select_elems.list.filter((elem_id) => isAppliedFilter(elem_id))) {
                InsertValue.selectElem(search_objs.select_elems);
                search_value_up = true;
            } else {
                //nothing
            }

            //  Field type: checkbox
            if (search_objs.check_elems.list.length > 0 && search_objs.check_elems.list.filter((elem_id) => isAppliedFilter(elem_id))) {
                InsertValue.checkElem(search_objs.check_elems);
                search_value_up = true;
            } else {
                //nothing
            }
        }

        // Set the back_step to APPLIED and save
        my_history.back_step = BACK_STEP.APPLIED;
        MyHistory.SetHistory();

        // Handle visibility of div query
        if (document.getElementById('UCView_divQueryVisibility_ControllerField')) {
            document.getElementById('UCView_divQueryVisibility_ControllerField').value = search_objs ? search_objs.btn_search : "0";
        }

        // Handle pagination
        if (document.getElementById('UCView_pager_ControllerField')) {
            document.getElementById('UCView_pager_ControllerField').value = this.pagination_index || 0;
        }

        if (search_value_up) {
            // Trigger a click on start query button if there are filters to apply
            document.getElementById('UCView_btnStartQuery').click();
        } else {
            // Otherwise trigger a simple postback in order to apply div query visibility and pagination
            __doPostBack();
        }

        MyHistory.SetVisibilityOnPage();
    };
};

//metodi di supporto alla history
MyHistory = {
    /*** gestione history ***/
    //recupera a history dallo storage, ne crea una nuova se non presente
    GetHistory: function () {
        var temp_history = sessionStorage.getItem("my_history");
        if (temp_history != null) { //history gia popolata
            temp_history = JSON.parse(temp_history);
            my_history = new history_obj(temp_history);
        } else {
            my_history = new history_obj(temp_history);
            my_history.setNewPageList();
        }
    },
    //salva la history nello storage
    SetHistory: function () {
        sessionStorage.setItem("my_history", JSON.stringify(my_history));
    },
    //elimina la history dallo storage e ne istanzia una nuova
    ClearHistory: function () {
        sessionStorage.removeItem("my_history");
        this.GetHistory();
    },

    /*** gestione extra delle pagine ***/
    //avvia l'inserimento dei parametri della pagina
    RestorePageState: function () {
        let hasFiltersToApply =
            current_page.search_params !== null ?
                current_page.search_params.applied_filters.length > 0
                : false;

        let hasPaginationToApply =
            current_page.pagination_index !== null ?
                current_page.pagination_index !== 0
                : false;

        if (my_history.back_step < BACK_STEP.APPLIED) {
            if (hasPaginationToApply || hasFiltersToApply) {
                // Something to restore
                current_page.restoreState();
            } else {
                // Nothing to restore
                my_history.back_step = BACK_STEP.APPLIED;
                MyHistory.SetHistory();
            }
        }
    },

    //gestisce la visibilità dei vari elementi nella pagina
    SetVisibilityOnPage: function () {
        const $btnShowQuery = $("#UCView_btnViewSearch");
        const $dQuery = $("#UCView_dQuery");
        let showDQuery = false;

        if ($btnShowQuery.length > 0) {
            if (Number($btnShowQuery.attr("myValue")) === 1) {
                showDQuery = true;
            }

            if (current_page.search_params !== null) {
                if (current_page.search_params.applied_filters.length > 0) {
                    SetFilterIndicators(current_page.search_params.applied_filters);
                }
            }

            if (showDQuery) {
                $dQuery.show();
            }
        }
    },

    /*** gestione elementi my_history ***/
    /* definisce e restituisce la current_page
     * @return {page_data_obj} pagina corrente
     */
    GetCurrentPage: function () {
        var url_page = "", page_aspx = "";
        var page_pathname = [], params_page_string = [];
        var params_page = null, single_param = null;
        var page_temp = new page_data_obj();

        url_page = location.href;
        page_pathname = location.pathname.split("/");
        page_aspx = page_pathname[page_pathname.length - 1].toLowerCase();
        params_page_string = location.search.split("?")[1];

        page_temp.url = url_page;
        page_temp.aspx_name = page_aspx;
        page_temp.isValid = true;
        //imposto i parametri (parte dopo "?" nell'url) della pagina
        if ((params_page_string != undefined) && (params_page_string.length > 0)) {
            params_page = params_page_string.split("&");
            if (params_page.length > 0) {
                for (var par = 0; par < params_page.length; par++) {
                    single_param = params_page[par].split("=");
                    page_temp.url_params["list"].push(single_param[0]);
                    page_temp.url_params[single_param[0]] = single_param[1];
                }
            }
        } else {
            page_temp.clearData();
        }

        return page_temp;
    },

    /** definisco l'oggetto "current_page" con i dati della pagina corrente, procedendo:
     * -nel caso la pagina sia la stessa di quella che stavo puntando con "act_index" -> aggiorno
     * -altrimenti aggiugo una nuova pagina
     */
    AddPage: function () {
        //impostazione variabili globali della pagina in visualizzazione
        current_page = this.GetCurrentPage();
        current_page.isValid = this.IsValidPage();

        var act_page = my_history.getActPage();
        current_page.getParams();
        var is_same_page = current_page.isSamePage(act_page);

        if (is_same_page) { //uguale all'ultima
            current_page.pagination_index = act_page.pagination_index;
            my_history.updateActPage(current_page);
        } else {    //nuova pagina
            my_history.setActPage(current_page);
        }
        this.SetHistory();
    },

    /** verifica se il path in ingresso è valido per essere aggiunto alla
     * history o se deve essere scartato; accettato se:
     *  -è una pagina di un iframe interno:
     *      -reportyields.aspx
     *      -ReportProductsSummation.aspx
     *      -ReportGrainsSummation.aspx
     *      -ReportShipmentParcel.aspx
     *      -ReportTranport.aspx
     *      -ReportCleaning.aspx
     *      -ReportMixing.aspx
     *      -lothistorydetail.aspx
     *  -non contiene i parametri:
     *      -deleted
     *      -ShowControls
     *      -AbortCycle
     *      -CloneCycle
     *      -CloneRecipe
     *      -ForceCompleteOrder
     *      -direction
     * @param {string} path -   path da verificare
     * @return {bool} true se valido, false altrimenti
     */
    IsValidPage: function () {
        var to_return = false;
        if ((current_page.aspx_name !== "reportyields.aspx") &&
            (current_page.aspx_name !== "ReportProductsSummation.aspx") &&
            (current_page.aspx_name !== "ReportGrainsSummation.aspx") &&
            (current_page.aspx_name !== "ReportShipmentParcel.aspx") &&
            (current_page.aspx_name !== "ReportTranport.aspx") &&
            (current_page.aspx_name !== "ReportCleaning.aspx") &&
            (current_page.aspx_name !== "ReportMixing.aspx") &&
            (current_page.aspx_name !== "lothistorydetail.aspx")) { //esclusione pagine interne
            if ((current_page.url_params["deleted"] == undefined) &&
                (current_page.url_params["ShowControls"] == undefined) &&
                (current_page.url_params["ShowRecipeExecutable"] == undefined) &&
                (current_page.url_params["AbortCycle"] == undefined) &&
                (current_page.url_params["CloneCycle"] == undefined) &&
                (current_page.url_params["CloneRecipe"] == undefined) &&
                (current_page.url_params["ForceCompleteOrder"] == undefined) &&
                (current_page.url_params["sessionExpired"] == undefined) &&
                (current_page.url_params["page"] == undefined) &&
                (current_page.url_params["direction"] == undefined)) {   //esclusione parametri
                let queryString = getCurrentQueryParams();
                if (queryString["session"] && queryString["session"] === Number(sessionStorage.getItem("session"))) {  //esclusione pagine senza session ID nella query string o con session ID diverso dal corrente
                    to_return = true;
                }
            }
        }
        return to_return;
    },

    /** carica la pagina salvata nella my_history nella posizione specificata da act_index
     */
    LoadPage: function (path) {
        if ((path != null) && (path != undefined) && (path != "")) {
            window.parent.location.href = path;
        } else {
            if (isNullOrUndefined(my_history.act_index)) {
                // In case the storage has been cleaned, set the current page
                this.AddPage();

                window.parent.location.href = removeResourceRequestFromQueryString(window.parent.location.href);
            } else {
                window.parent.location.href = removeResourceRequestFromQueryString(my_history.page_list[my_history.act_index].url);
            }
        }
        return false;
    },

    /** gestione "back"
     * applico le modifiche dei parametri
     *  -"act_index"
     *  -"back_step"
     * carico la nuova "act_index" dopo aver salvato la nuova history
     */
    Back: function () {
        if (my_history == null) {
            MyHistory.GetHistory();
        }
        var new_index = my_history.GetPrevValidIndex();
        if (new_index != my_history.act_index) {
            my_history.jump_back += (my_history.act_index - new_index);
            if (my_history.jump_next > 0) {
                my_history.jump_next -= (my_history.act_index - new_index);
                if (my_history.jump_next < 0) {
                    my_history.jump_next = 0;
                }
            }
            my_history.act_index = new_index;
            my_history.back_step = BACK_STEP.START;

            MyHistory.SetHistory();
            this.LoadPage();
        } else {
            SetNavigationButtonVisibility();
        }
    },
    /** gestione "realod"
     * ricarico la pagina che sto puntanto con l'attuale act_index
     */
    Reload: function () {
        if (my_history == null) {
            MyHistory.GetHistory();
        }
        this.LoadPage();
    },
    /** gestione dell'evento di "back" con annullamento
     * esegue il "back" facendo in modo che la pagina attualmente visitata non venga memorizzata nella my_history
     * rimuovendone il "valid" e applicando dei decrementi per bilanciare gli incrementi che saranno fatti nella back
     */
    Cancel: function () {
        if (my_history == null) {
            MyHistory.GetHistory();
        }

        my_history.page_list[my_history.act_index].isValid = false;
        my_history.last_valid_index -= 1;
        my_history.jump_back -= 1; //in quanto non è un back di cui dovrò tenere traccia per poterci tornare
        this.Back();
    },
    /** gestione dell'evento di "back" con submit
     * esegue il "back" facendo in modo che la pagina attualmente visitata non venga memorizzata nella my_history
     * rimuovendone il "valid" e applicando dei decrementi per bilanciare gli incrementi che saranno fatti nella back
     */
    Submit: function () {
        if (my_history == null) {
            MyHistory.GetHistory();
        }

        my_history.page_list[my_history.act_index].isValid = false;
        my_history.last_valid_index -= 1;
        my_history.jump_back -= 1; //in quanto non è un back di cui dovrò tenere traccia per poterci tornare
        this.Back();
    }
}