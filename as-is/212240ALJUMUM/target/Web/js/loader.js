﻿/****************************************************************
 *                          LOADER                              *
 ****************************************************************/
var my_loader = null;
var loader_timeout = null;
/**
 *  gestione del loader con messaggio custom
 */
myLoader = {
    create: function () {
        /** aspetto:
         *  "<section id='MyLoaderCont'>"
         *      "<div id='loaderAjaxBG' class='loaderAjaxBG' style='display: none;'> </div>"
         *      "<div id='loaderAjaxCont' class='loaderAjax' style='display: none;'>"
         *          "<img id='loaderImg' />"
         *          "<p id='msgLoader'> </p>"
         *          "<label id='subMsgLoader'> </label>"
         *      "</div>"
         *  "</section>"
         */
        //creazione elementi
        var section_cont = document.createElement("section");
        section_cont.id = "MyLoaderCont";

        var div_background = document.createElement("div");
        div_background.id = "loaderAjaxBG";
        div_background.className = "loaderAjaxBG";
        div_background.style.display = "none";

        var div_loader = document.createElement("div");
        div_loader.id = "loaderAjaxCont";
        div_loader.className = "loaderAjax";
        div_loader.style.display = "none";

        var img_loader = document.createElement("img");
        img_loader.id = "loaderImg";
        img_loader.src = "image/loader.gif?ts=" + Number(new Date());

        var p_loader = document.createElement("p");
        p_loader.id = "msgLoader";

        var label_loader = document.createElement("label");
        label_loader.id = "subMsgLoader";

        //annidiamento elementi
        div_loader.appendChild(img_loader);
        div_loader.appendChild(p_loader);
        div_loader.appendChild(label_loader);

        section_cont.appendChild(div_background);
        section_cont.appendChild(div_loader);

        var body_html = document.querySelector("body");
        if (body_html != null) {
            body_html.appendChild(section_cont);
        }
    },

    initLoader: function () {
        my_loader = document.querySelector("section#MyLoaderCont");
        if (my_loader == null) {
            myLoader.create();
        }
    },

    //@param {integer} [optional] time_to_timeout   -   eventuali millisecondi per abilitare lo stato di "timeout
    start: function (time_to_timeout) {
        if (document.querySelectorAll(":invalid").length == 0) {
            var txt = GetTraduct("WAIT_OPERATION");
            var body_height = $('body').height();
            if (body_height > 0) {
                $("#loaderAjaxBG").css("height", body_height + "px");
            }
            else {
                $("#loaderAjaxBG").css("height", "100%");
            }

            $("#loaderAjaxBG").show();
            $("#msgLoader").html(txt);
            $("#loaderAjaxCont").show();

            if ((time_to_timeout != null) && (time_to_timeout != undefined)) {
                loader_timeout = setTimeout(function () {
                    myLoader.timeout();
                }, time_to_timeout);
            }
        }
    },

    editSubMsg: function (txt) {
        $("#subMsgLoader").html(txt);
    },

    stop: function () {
        var blanket_div = $("div[blanket]");

        $("#loaderAjaxBG").hide();
        $("#msgLoader").html("");
        $("#subMsgLoader").html("");
        $("#loaderAjaxCont").hide();

        if (loader_timeout != null) {
            clearTimeout(loader_timeout);
            loader_timeout = null;
            document.getElementById("MyLoaderCont").removeEventListener("click", myLoader.stop);
        }
    },

    timeout: function () {
        myLoader.editSubMsg(GetTraduct("TIMEOUT_LOADER"));
        document.getElementById("MyLoaderCont").addEventListener("click", myLoader.stop);
    }
};
myLoader.initLoader();
myLoader.start();