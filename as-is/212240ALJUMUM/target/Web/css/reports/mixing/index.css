﻿.reports--mixing.reports--mixing--collapsed .reports--mixing--extended {
    display: none !important;
}

.reports--mixing.reports--mixing--extended .reports--mixing--collapsed {
    display: none !important;
}

.reports--mixing--row {
    display: grid !important;
    grid-template-columns: 1fr;
    gap: var(--sm);
    padding-bottom: var(--lg);
    position: relative;
}

    .reports--mixing--row:not(:first-child):not(:nth-child(2)) {
        border-top: solid 1px lightgray;
        padding-top: var(--lg);
    }

.reports--mixing--row--ingredients {
    display: grid;
    grid-template-columns: 1fr;
}

.reports--mixing--row--header {
    display: grid;
    grid-template-columns: minmax(300px, 30%) auto;
    grid-template-areas: "title datarow";
}

    .reports--mixing--row--header .reports--mixing--row--header--title {
        grid-area: title;
        font-size: 12px;
        display: flex;
        align-items: center;
    }

    .reports--mixing--row--header .reports--mixing--row--header--title--text span {
        font-weight: bold;
    }

    .reports--mixing--row--header .reports--mixing--row--header--title--icon {
        text-align: center;
        width: 50px;
    }

        .reports--mixing--row--header .reports--mixing--row--header--title--icon span {
            display: block;
            height: 0;
            width: 0;
            border: 0.4em solid transparent;
            border-top: 0.5em solid black;
            border-bottom: none;
            margin: auto;
            transition-property: transform, border;
            transition-duration: 500ms;
        }

.reports--mixing.reports--mixing--collapsed .reports--mixing--row--header .reports--mixing--row--header--title--icon span {
    transform: rotate(-90deg);
    border: 0.22em solid transparent;
    border-top: 0.35em solid black;
}

.reports--mixing--row--header .reports--mixing--row--datarow {
    grid-area: datarow;
}

.reports--mixing--row--datarow {
    display: flex;
    align-items: center;
}

    .reports--mixing--row--datarow > * {
        width: 33.3%;
        text-align: right;
    }

.reports--mixing--row--header .reports--mixing--row--datarow {
    font-weight: bold;
}

.reports--mixing--row--datarow .reports--mixing--row--datarow--nobold {
    font-weight: normal;
}

.reports--mixing--row--ingredientrow {
    display: grid;
    grid-template-columns: minmax(300px, 30%) auto;
    grid-template-areas: "name datarow";
    height: 30px;
}

    .reports--mixing--row--ingredientrow[ingredienttype] {
        position: relative;
        padding-top: 2em;
        background-color: #F2D1C4;
    }

        .reports--mixing--row--ingredientrow[ingredienttype]:before {
            content: attr(ingredienttype);
            position: absolute;
            top: 3px;
            left: 10px;
            font-style: italic;
        }

    .reports--mixing--row--ingredientrow.reports--mixing--row--ingredientrow--total {
        border: solid 1px black;
        font-weight: bold;
    }

        .reports--mixing--row--ingredientrow.reports--mixing--row--ingredientrow--total > * {
            background-color: transparent !important;
        }

    .reports--mixing--row--ingredientrow .reports--mixing--row--ingredientrow--name {
        grid-area: name;
        position: relative;
        display: flex;
        align-items: center;
        padding: var(--sm);
    }

    .reports--mixing--row--ingredientrow .reports--mixing--row--datarow {
        grid-area: datarow;
        padding: var(--sm);
    }

    .reports--mixing--row--ingredientrow .reports--mixing--row--datarow--diff:after {
        content: "";
        width: 1em;
        text-align: center;
        display: inline-block;
    }

    .reports--mixing--row--ingredientrow .reports--mixing--row--datarow--diff.alarm-low,
    .reports--mixing--row--ingredientrow .reports--mixing--row--datarow--diff.alarm-low:after,
    .reports--mixing--row--ingredientrow .reports--mixing--row--datarow--diff.alarm-high,
    .reports--mixing--row--ingredientrow .reports--mixing--row--datarow--diff.alarm-high:after {
        color: red;
        font-weight: bold;
    }

        .reports--mixing--row--ingredientrow .reports--mixing--row--datarow--diff.alarm-high:after {
            content: "\25b2";
        }

        .reports--mixing--row--ingredientrow .reports--mixing--row--datarow--diff.alarm-low:after {
            content: "\25bc";
        }

    .reports--mixing--row--ingredientrow:nth-child(2n+1) .reports--mixing--row--datarow {
        background-color: #E1F0F9;
    }

    .reports--mixing--row--ingredientrow:nth-child(2n) .reports--mixing--row--datarow {
        background-color: white;
    }

    .reports--mixing--row--ingredientrow:nth-child(2n+1) .reports--mixing--row--ingredientrow--name {
        background-color: #E1F0F9;
    }

    .reports--mixing--row--ingredientrow:nth-child(2n) .reports--mixing--row--ingredientrow--name {
        background-color: white;
    }

.reports--mixingtotal--row {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
}

    .reports--mixingtotal--row .reports--mixingtotal--row--ingredientrow--total {
        border: solid 1px black;
        background-color: transparent;
        font-weight: bold;
    }

        .reports--mixingtotal--row .reports--mixingtotal--row--ingredientrow--total > * {
            background-color: transparent !important;
        }

        .reports--mixingtotal--row .reports--mixingtotal--row--ingredientrow--total .reports--mixing--row--ingredientrow--name:before {
            content: none;
        }

    .reports--mixingtotal--row .reports--mixingtotal--row--header {
        display: grid;
        grid-template-columns: minmax(250px, 25%) auto;
        grid-template-areas: "title datarow";
        font-weight: bold;
        padding: 4px 0;
    }

    .reports--mixingtotal--row .reports--mixing--row--ingredientrow {
        display: grid;
        grid-template-columns: minmax(250px, 25%) auto;
        grid-template-areas: "name datarow";
    }

    .reports--mixingtotal--row .reports--mixing--row--datarow--diff:after {
        content: none;
    }

.reports--mixingtotal--missingflows {
    font-style: italic;
    text-align: right;
    width: calc(100% - (var(--lg) * 2));
    padding: var(--lg);
}

[missing]:before {
    content: "*";
    font-weight: bold;
}

.reports--operation--mixing_total_panel {
    flex-direction: column;
}