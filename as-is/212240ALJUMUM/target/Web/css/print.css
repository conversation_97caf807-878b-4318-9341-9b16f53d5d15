@media print {
    .noPrint {
        display: none !important;
    }
}

body, body > form, body > form {
    height: auto;
}

#pageTitle {
    font-size: 16pt;
    border-top: none !important;
    border-bottom: solid 1.5px #000;
    text-align: center;
    font-weight: normal;
}

#titleTable {
    margin-bottom: 20px;
}

.verticalSpace {
    height: 10px;
    width: 100%;
}

td > img[src*="led"] {
    width: 15px;
}

input, input:disabled {
    width: auto;
    border: none !important;
    text-align: right;
}

    input.input-wide {
        width: auto;
        min-width: 50%;
    }

    input[type='number'] {
        -moz-appearance: textfield;
        -webkit-appearance: textfield;
        margin: 0;
    }

    input[type=image] {
        width: auto;
    }

    input[type=checkbox] {
        width: auto;
    }

table, .tabDimRid1 {
    width: 98%;
    margin-left: 1% !important;
    text-align: left;
    border: none;
    border-collapse: collapse;
    border-spacing: 0;
}

    table.bord1px {
        border: none;
    }

    table.tableHeight tr {
        height: 25px;
    }

    table[id$="dgRisultati"] tbody tr {
        height: 27px;
    }

    table tbody tr {
        background-color: transparent !important;
    }

        table tbody tr:not(:first-child) td {
            border-top-style: solid !important;
            border-top: 1px solid rgba(128, 128, 128, 0.25);
        }

        table tbody tr.rowSection + tr > td {
            border-top: none !important;
        }

#UCView_tblSum {
    margin-top: 20px;
}

    #UCView_tblSum tr td {
        border: none !important;
    }

#UCView_dgRisultati th {
    vertical-align: bottom;
}

.tdBin table {
    border-collapse: initial;
}

div[id$="dView"] table tbody tr, div[id$="dQuery"] table tbody tr {
    height: 25px;
}

div[id$="dView"] table.tblBin tbody tr, div[id$="dQuery"] table.tblBin tbody tr {
    height: auto;
}

table td span.rb {
    padding-right: 20px;
    filter: grayscale(1);
}

select {
    -webkit-appearance: none;
    -moz-appearance: none;
    text-overflow: '';
}

a:link, a:visited {
    color: #000000;
    text-decoration: none;
}

body, .label-text, .ddl-text, .text-right, label {
    font-size: 10px;
    font-family: "Verdana", "Georgia", "Arial", "Helvetica", "Sans-serif";
}

.bord1px, .text-right {
    border: solid 1px;
    border-color: #000000;
}

.ddl-text, .ddl-text:disabled {
    border: none;
}

.text-right {
    text-align: right;
}

.hideOnPrint, .elementHide {
    display: none;
}

.showRowOnlyOnPrint {
    display: table-row;
}

.rowHeader {
    background-color: transparent !important;
    color: black !important;
}

    .rowHeader > th {
        border: none;
        border-bottom: 1px solid black !important;
        color: black !important;
    }

        .rowHeader > th > * {
            color: black !important;
        }

.rowSection {
    background-color: #999999;
}

.rowEven {
    background-color: #E5E5E5;
}

.rowOdd {
    background-color: #FFFFFF;
}

.txtPagerStyle td table tbody tr td span {
    font-weight: bold;
}

tr.txtPagerStyle td table {
    width: inherit;
}

table#UCView_tblSum {
    width: inherit;
}

    table#UCView_tblSum tr.rowHeader {
        display: none;
    }

    table#UCView_tblSum .tblSumSeparator {
        width: 50px;
    }

.label1 {
    font-family: "Verdana", "Georgia", "Arial", "Helvetica", "Sans-serif";
    font-size: 11px;
    font-weight: bold;
}

textarea.textbox-text {
    width: 90% !important;
    min-height: 1.2em;
    line-height: 1.2em;
    appearance: none;
    border: none;
}

textarea {
    resize: none;
    overflow: hidden;
}

.txtBold {
    font-weight: bold;
}

.label-text-section {
    font-weight: bold;
    font-size: 11px;
}

.tdBin {
    width: 200px;
}

.tblBin {
    border: solid 3px #000000;
    width: 200px;
    height: 350px;
}

.greenLed {
    background-image: url("./image/greenledbn.png");
}

.yellowLed {
    background-image: url("./image/yellowledbn.png");
}

.redLed {
    background-image: url("./image/redledbn.png");
}
/* report mixing */
.totalRow {
    background-color: #dddfe3;
    font-weight: bold;
}

    .totalRow td {
        height: 20px;
        line-height: 20px;
        border-bottom: solid 2px #000000;
        border-top: solid 2px #000000;
    }

        .totalRow td:first-child {
            border-left: solid 2px #000000;
        }

        .totalRow td:last-child {
            border-right: solid 2px #000000;
        }

.batchRow {
    background-color: #F0F0F0;
    height: 25px;
}

.mixingLed {
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 20px;
}

.filtroattivo_img {
    width: 17px;
    height: 17px;
}

.filtroattivo_img--top {
    margin-left: 0.5rem;
}

#UCView_dgRisultati th {
    vertical-align: middle;
}

    #UCView_dgRisultati th a {
        vertical-align: super;
    }

td div[name='div_rb'] {
    float: left;
}

.input-wide {
    width: 200px;
}

select:disabled, input[type="text"]:disabled, input[type="number"]:disabled, textarea:disabled {
    background-color: transparent;
    color: black;
    opacity: 1;
    padding: 2px;
}

input[type="radio"]:disabled, input[type="checkbox"]:disabled {
    color: black;
    opacity: 1;
}

input[type="radio"], input[type="checkbox"] {
    height: 12px;
}

#customerHeader {
    display: grid;
    width: 98%;
    height: 70pt;
    border: double 3pt #000000;
    margin: 5pt auto 0pt auto;
    font-size: 11px;
}

    #customerHeader + img {
        margin-top: 20px;
    }

    #customerHeader .customerheader--elem--img {
        margin: auto 0;
    }

#printContainer {
    width: 98%;
    margin: auto;
}

.lot_title_table {
    border-bottom: solid 1.5px black;
    font-size: 16pt;
}

.customstyle--border {
    border: solid 1px black;
}

.customstyle--bordertop {
    border-top: solid 1px black;
}

.customstyle--borderright {
    border-right: solid 1px black;
}

.customstyle--borderbottom {
    border-bottom: solid 1px black;
}

.customstyle--borderleft {
    border-left: solid 1px black;
}

.customstyle--borderx {
    border-right: solid 1px black;
    border-left: solid 1px black;
}

.customstyle--bordery {
    border-top: solid 1px black;
    border-bottom: solid 1px black;
}

.center-y {
    display: flex;
    align-items: center;
}

.center-x {
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Centers the graph in the page */
#Chart1 {
    width: 98% !important;
    height: auto !important;
    margin-left: 1%;
}

.rightSection, .navbarContainer {
    display: none !important;
}

.mainContainer, .lothistoryContainer, .lotsMenuCont {
    max-height: none;
    height: auto;
}

    .lothistoryContainer .leftSection {
        min-width: fit-content;
        max-width: none;
        box-shadow: none;
        padding-top: var(--lg);
    }

    .lothistoryContainer .mainSection #UpdatePanelIFrame {
        width: 100%;
    }

#WUClotsMenu_tblLots img {
    width: 15px;
}

#WUClotsMenu_tblLots {
    font-size: 8px;
}

    #WUClotsMenu_tblLots #WUClotsMenu_treeLots > :last-child {
        margin-bottom: 10px;
    }

    #WUClotsMenu_tblLots tr {
        height: auto !important;
    }