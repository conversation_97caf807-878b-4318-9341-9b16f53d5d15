﻿Imports System.Data
Imports UsersGUI
Imports WebTools.tools

Partial Class ViewRecipeHistory
    Inherits myWebPage

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing
    Public m_ReadOnly As Boolean = True

    Private mTopMenuName As String = String.Empty
    Private mMenuName As String = String.Empty
    Private dt As Data.DataTable
    Private sql_where As String = String.Empty
    Private mControl As String = String.Empty
    Private bResult As String
    Private adim As AddMenuItemName = Nothing
    Private mCounter As Long = m_InvalidId

    Private m_mtr_id As Integer = m_InvalidId
    Private m_rec_id As Integer = m_InvalidId
    Private m_recipe_log_id As Integer = m_InvalidId

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        m_config = CType(Application("Config"), config)

        ' Controlla se è necessario fare un update delle risorse del JS
        myScript.UpdateJSResources(m_config)

        If Current.Request("pagename") Is Nothing Then
            Exit Sub
        End If

        ' parte custom della pagina di report.
        ' voglio riciclare lo screen del ciclo, ma devo costruirmi al volo in nome
        If Current.Request.QueryString("MTR_ID") IsNot Nothing AndAlso Current.Request.QueryString("MTR_ID") <> String.Empty Then
            m_mtr_id = Integer.Parse(Current.Request.QueryString("MTR_ID"))
        Else
            Exit Sub
        End If

        If Current.Request("pagename") = "RECIPE_PARAMS_" Then
            Dim objNewValueCollection As NameValueCollection = HttpUtility.ParseQueryString(Request.QueryString.ToString())
            objNewValueCollection.Set("pagename", "RECIPE_PARAMS_" & m_mtr_id)

            Dim sRedirect As String = Request.Url.AbsolutePath
            Dim sNewQueryString As String = "?" + objNewValueCollection.ToString()

            sRedirect = sRedirect + sNewQueryString

            Current.Response.Redirect(sRedirect)
            Exit Sub
        End If

        If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
            Exit Sub
        End If

        ' parametri addizionali
        If Current.Request.QueryString("ID") IsNot Nothing AndAlso Current.Request.QueryString("ID") <> String.Empty Then
            m_recipe_log_id = Integer.Parse(Current.Request.QueryString("ID"))
        Else
            Exit Sub
        End If

        Dim nCount As Integer = 1
        If mScreen.AddMenuItemNames IsNot Nothing Then
            For Each Me.adim In mScreen.AddMenuItemNames
                If adim.MenuBound = EnumAddMenuBound.Edit Then
                    Dim hl As New HyperLink
                    If adim.NameLink <> String.Empty Then
                        hl.ID = adim.NameLink
                        hl.Text = m_config.GetEntryByKeyName(adim.NameLink).GetValue()
                    Else
                        hl.Text = m_config.GetEntryByKeyName(adim.Name).GetValue()
                        hl.ID = adim.Name
                    End If

                    hl.NavigateUrl = adim.GetNavigateUrl

                    For Each p As AddMenuItemNameParameter In adim.ListParameters
                        hl.NavigateUrl &= "&" & p.FieldDB & "=" & Current.Request.QueryString(p.FieldDB)
                    Next

                    nCount += 1
                End If
            Next
        End If

        'Access rights
        WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
        If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Editing) Then
            lblError.Text = m_config.GetEntryByKeyName("EDIT_DENIED").GetValue
            Me.dError.Visible = True
            Me.dView.Visible = False
            Exit Sub
        End If

        DrawHeader()
        GetData()

        For Each f As Field In mScreen.EditFields
            If f.CallEvent <> String.Empty Then
                ' ho un qualche tipo di call event specificato a livello di config.xml da gestire
                GenericsEvents.EventDelegate.AppendCallEvent(Me, f)
            End If
        Next

        ' Draw the print button in the operations div
        If WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Printing) Then
            DrawPrintButtonBase("ButtonEvents.Print()")
        End If

        ' Draw the back button in the operations div
        DrawBackButton()

        ' chiamata allo script custom per nascondere le righe con elementi non visibili
        myScript.InvokeJS(Me.Page, "HideRows('tblEdit');")
        myScript.InvokeJS(Me.Page, "HideRows('tblHeader');")
        ' chiamata allo script custom per settare il colspan
        myScript.InvokeJS(Me.Page, "SetTdColspan('tblEdit');")
        myScript.InvokeJS(Me.Page, "SetTdColspan('tblHeader');")
        myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('tblEdit');")
        myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('tblHeader');")

        ' gestione blanket (solo sul 1° load, gestito da js)
        div_blanket.Attributes.Add("IsFirstLoad", (Not Page.IsPostBack).ToString)
    End Sub

    Private Sub GetData()
        Dim sSelect As String = "SELECT * FROM RECIPE_PARAM_VALUES_ARCHIVED WHERE RECIPE_LOG_ID = '" & m_recipe_log_id & "'"
        Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

        ' riciclo lo screen (RECIPE_PARAMS_xx), ma devo cambiare l'origine dei dati
        For Each f As Field In mScreen.EditFields
            f.GetDataFrom = EnumGetDataFrom.RecipeParamValueArchive
        Next

        WebTools.drawings.DrawUpdateFields(tblEdit, mScreen, m_config, dt, m_ReadOnly, m_recipe_log_id)

        ' Reimposta il GetDataFrom a EnumGetDataFrom.RecipeParamValue, in modo da non inquinare lo screen
        For Each f As Field In mScreen.EditFields
            f.GetDataFrom = EnumGetDataFrom.RecipeParamValue
        Next
    End Sub

    Private Sub DrawHeader()
        Dim header As New Header()

        header.MasterDBName = "VIEW_RECIPE_LOGS"
        header.FilterColumns = New List(Of HeaderFilter)
        header.EditFieldList = New List(Of Field)

        header.FilterColumns.Add(New HeaderFilter() With {.ColumnName = "ID", .FilterValueParamName = "ID"})

        header.EditFieldList.Add(New Field(String.Empty) With {.FieldDb = "ID", .FieldName = "Recipe code", .FieldType = "Auto"})
        header.EditFieldList.Add(New Field(String.Empty) With {.FieldDb = "DESCRIPTION", .FieldName = "Recipe name", .FieldType = "WString"})

        WebTools.drawings.DrawHeaderFields(tblHeader, header, m_config, True)

        If tblHeader.Rows.Count <= 0 Then
            tblHeader.Visible = False
        End If
    End Sub

    Private Sub DrawPrintButtonBase(ByVal calling_function As String)
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New LinkButton
        link.ID = "lb_Stampa"
        link.Text = m_config.GetEntryByKeyName("PRINT_BUTTON").GetValue()
        link.Attributes("href") = "#"
        link.OnClientClick = calling_function & ";"

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub AddImageToOperation()
        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim img As New System.Web.UI.HtmlControls.HtmlImage

        img.Src = "~/image/RightArrow.png"
        tc.Attributes("class") = "buttonGreen"
        tc.Controls.Add(img)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub DrawBackButton()
        If Not Request.UrlReferrer Is Nothing Then
            Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
            row.ID = "BackCont"
            tblOperation.Rows.Add(row)

            AddImageToOperation()

            Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
            Dim link As New HyperLink
            With link
                .ID = "lbBack"
                .Text = m_config.GetEntryByKeyName("Back").GetValue()
                .CssClass = "hoverHand"
                .Attributes.Add("onclick", "ButtonEvents.Back();")
            End With

            tc.Attributes("class") = "txtButtonGreen"
            tc.Controls.Add(link)

            tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
        End If
    End Sub

End Class