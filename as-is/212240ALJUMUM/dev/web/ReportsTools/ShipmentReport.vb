﻿Option Strict On

Imports UsersGUI
Imports ReportsTools.myReports
Imports CommonDefines.Defines

Public Class ShipmentReport

#Region "Configuration"

    Public Class ShipmentConfig

    End Class

#End Region

#Region "Constants and classes"

    Private Class ShipmentData
        Property ProductId As Integer
        Property Product As String
        Property Weight As Double

    End Class

#End Region

#Region "Methods"

    Public Shared Function GetShipmentScript(ByVal mConfig As UsersGUI.config, ByVal jobId As Integer) As String
        Dim script As String = String.Empty

        Try
            Dim shipmentDate As Date
            Dim recipeDescription As String = String.Empty
            Dim jobTotal As Double

            Dim cycleId As SSCycles
            Dim recipeLogId As Integer = m_InvalidInteger

            Dim shipmentData As New List(Of ShipmentData)

            ' update according to changes to tables CARRIERS and CUSTOMERS
            Dim customer As String = String.Empty
            Dim carrier As String = String.Empty

            Dim query As String
            Dim result As DataTable

            ' Recover data
            ' 1 - Get info about the job
            query = "SELECT CYC_ID, RECIPE_LOG_ID, PRODUCED_AMOUNT, RECIPE_DESCRIPTION, PRO_ID, PRODUCTS.NAME AS PRODUCT_NAME, START_DATE, STOP_DATE FROM PRODUCTION_REPORTS INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID WHERE COUNTER = " & jobId
            result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            If result.Rows.Count > 0 Then
                cycleId = CType(result.Rows(0).Item("CYC_ID"), SSCycles)
                recipeLogId = If(IsDBNull(result.Rows(0).Item("RECIPE_LOG_ID")), m_InvalidInteger, CInt(result.Rows(0).Item("RECIPE_LOG_ID")))
                recipeDescription = result.Rows(0).Item("RECIPE_DESCRIPTION").ToString()
                jobTotal = Double.Parse(result.Rows(0).Item("PRODUCED_AMOUNT").ToString())
                shipmentDate = CDate(result.Rows(0).Item("STOP_DATE"))

                ' 2 - Analyze flows
                query = "SELECT FLOW_LOGS.PRO_ID, PRODUCTS.NAME AS PRODUCT_NAME, SUM(WEIGHT) AS WEIGHT" &
                    " FROM FLOW_LOGS" &
                        " INNER JOIN PRODUCTS ON FLOW_LOGS.PRO_ID = PRODUCTS.ID" &
                    " WHERE FLOW_LOGS.COUNTER = " & jobId &
                    " GROUP BY FLOW_LOGS.PRO_ID, PRODUCTS.NAME"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                Dim singleData As ShipmentData

                For Each row As DataRow In result.Rows
                    ' 2a - Save data from flows for the product
                    singleData = New ShipmentData() With {
                        .ProductId = CInt(row.Item("PRO_ID")),
                        .Product = row.Item("PRODUCT_NAME").ToString(),
                        .Weight = Double.Parse(row.Item("WEIGHT").ToString()) / 1000      ' kgs => tons
                    }

                    shipmentData.Add(singleData)
                Next

                ' 3 - Get shipment data
                Tools.GetShipmentData(jobId, customer, carrier)

                ' 4 - Translate to JSON
                script &= "nameJSON = {"
                script &= """top"": [" &
                            "{ ""id"": ""jobTotal"", ""val"": """ & GetTranslationForJSON("QUANTITY", mConfig) & """ }," &
                            "{ ""id"": ""shipmentDestination"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Customer", mConfig) & """ }," &
                            "{ ""id"": ""shipmentDate"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("SHIP_DATE", mConfig) & """ }," &
                            "{ ""id"": ""productDescription"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Product", mConfig) & """ }," &
                            "{ ""id"": ""lot"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Lotto", mConfig) & """ }," &
                            "{ ""id"": ""carrier"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Carrier", mConfig) & """ }" &
                        "]"
                script &= "};"
                script &= "sessionStorage.setItem(""nameJSON"", JSON.stringify(nameJSON));" &
                "initializeReport( " & GetIdReport(cycleId) & ", " & EnumTypeReport.ShipmentReport & ");"

                script &= "valueJSON = {"
                script &= """top"": [" &
                    "{ ""id"": ""job"", ""val"": " & jobId & " }," &
                    "{ ""id"": ""shipmentDate"", ""val"": """ & shipmentDate.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                    "{ ""id"": ""recipeDescription"", ""val"": " & If(recipeLogId.Equals(m_InvalidInteger) OrElse recipeDescription.Trim().Equals(String.Empty), "null", """" & recipeDescription & """") & " }," &
                    "{ ""id"": ""jobTotal"", ""val"": " & If(shipmentData.Count > 0, Tools.ConvertValueToJs(Math.Round(jobTotal / 1000, 3)), "null") & " }," &
                    "{ ""id"": ""productDescription"", ""val"": " & If(shipmentData.Count > 0, """" & myReports.ParseTranslationForJSON(shipmentData(0).Product) & """", "null") & " }," &
                    "{ ""id"": ""lot"", ""val"": " & GetLotValueLinkForJob(cycleId, jobId) & " }," &
                    "{ ""id"": ""shipmentDestination"", ""val"": " & If(customer <> String.Empty, """" & customer & """", "null") & " }," &
                    "{ ""id"": ""carrier"", ""val"": " & If(carrier <> String.Empty, """" & carrier & """", "null") & " }" &
                "]"
                script &= "};"
                script &= "sessionStorage.setItem(""valueJSON"", JSON.stringify(valueJSON));" &
                "updateReport();"
            End If
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Private Shared Function GetIdReport(CycleId As SSCycles) As String
        Dim IdReport As String = "8" 'Report standard per SHIPMENT

        'Creare le condizioni nel caso in cui sia necessario chiamamre una configurazione specifica per questo report

        Return IdReport
    End Function

#End Region

End Class