﻿Option Strict On

Imports UsersGUI
Imports ReportsTools.myReports
Imports CommonDefines.Defines

Public Class CleaningReport
    Inherits TransferReport

#Region "Configuration"

    Public Class CleaningConfig

        Public Shared IngredientsSetpointsConfig As New Dictionary(Of SSCycles, IngredientSetpointQuery)

        ' Needed only for cycles with mixing described in the recipe, but no setpoints in PROCESS_RECIPES_ARCHIVED
        Public Shared SourcesDict As New Dictionary(Of SSCycles, SSBins())

        Public Shared DestinationsDict As New Dictionary(Of SSCycles, SSBins())

        Public Const WaterProductId As Integer = 2

        Public Const WasteProductId As Integer = 3

    End Class

#End Region

#Region "Constants and classes"

    Public Enum CycleOrRecipe
        Cycle
        Recipe
    End Enum

    Public Class IngredientSetpointQuery
        Public ProductId As List(Of PrefixNumber)
        Public Setpoint As List(Of PrefixNumber)
        Public From As CycleOrRecipe
    End Class

    Private Class CompositionData
        Property ProductId As Integer
        Property Product As String
        Property Weight As Double

        Public Function ToNameJSON() As String
            Return "{ ""id"": ""P" & Me.ProductId & """, ""val"": """ & Me.Product & """ }"
        End Function

        Public Function ToValueJSON() As String
            Return "{ ""id"": ""P" & Me.ProductId & """, ""val"": " & Tools.ConvertValueToJs(Me.Weight) & " }"
        End Function

    End Class

    Private Class ExtractionData
        Property ReferenceKey As String
        Property ProductId As Integer = m_InvalidInteger
        Property Product As String = String.Empty
        Property Weight As Double = 0
        Property SourcesDescriptions As String = String.Empty
        Property Setpoint As Double = m_InvalidDblValue

        Public Function ToNameJSON() As String
            Return "{ ""id"": """ & Me.ReferenceKey & """, ""val"": """ & Me.Product & """ }"
        End Function

        Public Function ToValueJSON() As String
            Return "{ ""id"": """ & Me.ReferenceKey & """, ""val"": [""" & Me.SourcesDescriptions & """," & Tools.ConvertValueToJs(Me.Setpoint) & "," & Tools.ConvertValueToJs(Me.Weight) & "] }"
        End Function

    End Class

#End Region

#Region "Methods"

    Public Shared Function GetCleaningScript(ByVal mConfig As UsersGUI.config, ByVal jobId As Integer) As String
        Dim script As String = String.Empty

        Try
            Dim dateFrom As Date
            Dim dateTo As Date
            Dim productDescription As String
            Dim productId As Integer
            Dim recipeDescription As String = String.Empty
            Dim jobTotal As Double
            Dim destinationBins As New List(Of String)

            Dim destinationBinsIDs As New List(Of Integer)

            Dim cycleId As SSCycles
            Dim recipeLogId As Integer = m_InvalidInteger

            Dim query As String
            Dim result As DataTable

            Dim extractionsData As New List(Of ExtractionData)
            Dim compositionData As New List(Of CompositionData)
            Dim producedTotalFromFlows As Double = 0

            ' Recover data
            ' 1 - Get info about the job
            query = "SELECT CYC_ID, RECIPE_LOG_ID, PRODUCED_AMOUNT, RECIPE_DESCRIPTION, PRO_ID, PRODUCTS.NAME AS PRODUCT_NAME, START_DATE, STOP_DATE FROM PRODUCTION_REPORTS INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID WHERE COUNTER = " & jobId
            result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            If result.Rows.Count > 0 Then
                cycleId = CType(CInt(result.Rows(0).Item("CYC_ID")), SSCycles)
                recipeLogId = If(IsDBNull(result.Rows(0).Item("RECIPE_LOG_ID")), m_InvalidInteger, CInt(result.Rows(0).Item("RECIPE_LOG_ID")))
                recipeDescription = myReports.ParseTranslationForJSON(result.Rows(0).Item("RECIPE_DESCRIPTION").ToString())
                jobTotal = Double.Parse(result.Rows(0).Item("PRODUCED_AMOUNT").ToString())
                productDescription = myReports.ParseTranslationForJSON(result.Rows(0).Item("PRODUCT_NAME").ToString())
                productId = CInt(result.Rows(0).Item("PRO_ID"))
                dateFrom = CDate(result.Rows(0).Item("START_DATE"))
                dateTo = CDate(result.Rows(0).Item("STOP_DATE"))

                If CleaningConfig.IngredientsSetpointsConfig.ContainsKey(cycleId) Then

                    ' 2 - Setup config object according to the cycle
                    Dim sourcesConfig As IngredientSetpointQuery = CleaningConfig.IngredientsSetpointsConfig(cycleId)

                    ' 3 - Get the parameters of the job/recipe
                    Dim referenceKey As String = String.Empty
                    Dim currData As ExtractionData

                    If sourcesConfig.From = CycleOrRecipe.Cycle Then
                        query = "SELECT PARAMETER_VALUE AS VALUE, ASP_NAME, PRODUCTS.NAME AS PRODUCT_NAME" &
                                " FROM VIEW_ORDER_PARAMETERS_ARCHIVED AS VOPA" &
                                    " LEFT JOIN PRODUCTS ON (CASE WHEN ISNUMERIC(VOPA.PARAMETER_VALUE + 'e0') = 1 THEN VOPA.PARAMETER_VALUE ELSE -1 END) = PRODUCTS.ID" &
                                " WHERE COUNTER = " & jobId & " AND PARAMETER_VALUE IS NOT NULL"
                    ElseIf recipeLogId <> m_InvalidInteger Then
                        query = "SELECT FIELD_VALUE AS VALUE, ASP_NAME, PRODUCTS.NAME AS PRODUCT_NAME" &
                                " FROM VIEW_RECIPE_PARAM_VALUES_ARCHIVED AS VRPVA" &
                                    " LEFT JOIN PRODUCTS ON (CASE WHEN ISNUMERIC(VRPVA.FIELD_VALUE + 'e0') = 1 THEN VRPVA.FIELD_VALUE ELSE -1 END) = PRODUCTS.ID" &
                                " WHERE RECIPE_LOG_ID = " & recipeLogId & " AND VRPVA.FIELD_VALUE IS NOT NULL"
                    Else
                        Throw New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, "Uncorrectly configured cycle """ & cycleId & """ for CleaningReport: ingredients retrieval from recipe but no recipe_log_id.")
                    End If

                    result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                    For Each row As DataRow In result.Rows

                        ' Ingredients.ProductId
                        referenceKey = CheckForMatches(row.Item("ASP_NAME").ToString(), sourcesConfig.ProductId)
                        If Not referenceKey.Equals(String.Empty) Then
                            If extractionsData.Exists(Function(v) v.ReferenceKey.Equals(referenceKey)) Then
                                currData = extractionsData.Find(Function(v) v.ReferenceKey.Equals(referenceKey))
                                currData.ProductId = CInt(row.Item("VALUE"))
                                currData.Product = row.Item("PRODUCT_NAME").ToString()
                            Else
                                extractionsData.Add(New ExtractionData() With {
                                    .ReferenceKey = referenceKey,
                                    .ProductId = CInt(row.Item("VALUE")),
                                    .Product = row.Item("PRODUCT_NAME").ToString()
                                })
                            End If
                            Continue For
                        End If

                        ' Ingredients.Setpoint
                        referenceKey = CheckForMatches(row.Item("ASP_NAME").ToString(), sourcesConfig.Setpoint)
                        If Not referenceKey.Equals(String.Empty) Then
                            If extractionsData.Exists(Function(v) v.ReferenceKey.Equals(referenceKey)) Then
                                currData = extractionsData.Find(Function(v) v.ReferenceKey.Equals(referenceKey))
                                currData.Setpoint = Double.Parse(row.Item("VALUE").ToString())
                            Else
                                extractionsData.Add(New ExtractionData() With {
                                    .ReferenceKey = referenceKey,
                                    .Setpoint = Double.Parse(row.Item("VALUE").ToString())
                                })
                            End If
                            Continue For
                        End If
                    Next

                    ' 4 - Analyze flows
                    query = "SELECT FLOW_LOGS.PRO_ID, CELLS.DESCRIPTION AS CELL_DESCRIPTION, SUM(WEIGHT) AS EXTRACTED_WEIGHT FROM FLOW_LOGS INNER JOIN CELLS ON SOURCE_CELL = CELLS.ID" &
                            " WHERE COUNTER = " & jobId & " AND SOURCE_CELL IN (" & String.Join(",", CleaningConfig.SourcesDict(cycleId).Cast(Of Integer)) & ")" &
                            " GROUP BY FLOW_LOGS.PRO_ID, CELLS.DESCRIPTION"
                    result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                    For Each row As DataRow In result.Rows
                        If extractionsData.Exists(Function(v) v.ProductId.Equals(CInt(row.Item("PRO_ID")))) Then
                            currData = extractionsData.Find(Function(v) v.ProductId.Equals(CInt(row.Item("PRO_ID"))))

                            currData.Weight += Double.Parse(row.Item("EXTRACTED_WEIGHT").ToString()) / 1000 ' kgs -> tons
                            currData.SourcesDescriptions &= If(currData.SourcesDescriptions <> String.Empty, ",", "") & row.Item("CELL_DESCRIPTION").ToString
                        End If
                    Next

                    '   4.1 - Remove where the setpoint and the actual weight are invalid
                    extractionsData.RemoveAll(Function(v) v.Setpoint.Equals(m_InvalidDblValue) And v.Weight.Equals(m_InvalidDblValue))
                Else
                    Throw New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, "Unconfigured cycle """ & cycleId & """ for CleaningReport")
                End If

                ' 5 - Retrieve destinations
                query = "SELECT DISTINCT DEST_CELL, CELLS.DESCRIPTION AS CELL_DESCRIPTION FROM FLOW_LOGS INNER JOIN CELLS ON DEST_CELL = CELLS.ID" &
                                " WHERE COUNTER = " & jobId & " AND DEST_CELL IN (" & String.Join(",", CleaningConfig.DestinationsDict(cycleId).Cast(Of Integer)) & ") ORDER BY DEST_CELL ASC"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                For Each row As DataRow In result.Rows
                    destinationBins.Add(row.Item("CELL_DESCRIPTION").ToString())
                Next

                ' 6 - Retrieve composition data (water/waste/product)

                '   6.1 - Extracted weight
                For Each data As ExtractionData In extractionsData
                    compositionData.Add(New CompositionData() With {
                        .Product = data.Product,
                        .ProductId = data.ProductId,
                        .Weight = data.Weight
                    })
                Next

                '   6.2 - Water
                query = "SELECT PRODUCTS.NAME AS PRODUCT_NAME, PRO_ID, SUM(WEIGHT) AS WEIGHT FROM FLOW_LOGS INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID" &
                        " WHERE COUNTER = " & jobId & " AND PRO_ID = " & CleaningConfig.WaterProductId & " GROUP BY PRO_ID, PRODUCTS.NAME"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                If result.Rows.Count > 0 Then
                    compositionData.Add(New CompositionData() With {
                        .Product = result.Rows(0).Item("PRODUCT_NAME").ToString,
                        .ProductId = CInt(result.Rows(0).Item("PRO_ID")),
                        .Weight = Double.Parse(result.Rows(0).Item("WEIGHT").ToString()) / 1000 ' kgs -> tons
                    })
                End If

                '   6.3 - Waste
                query = "SELECT PRODUCTS.NAME AS PRODUCT_NAME, PRO_ID, SUM(WEIGHT) AS WEIGHT FROM FLOW_LOGS INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID" &
                        " WHERE COUNTER = " & jobId & " AND PRO_ID = " & CleaningConfig.WasteProductId & " GROUP BY PRO_ID, PRODUCTS.NAME"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                If result.Rows.Count > 0 Then
                    compositionData.Add(New CompositionData() With {
                        .Product = result.Rows(0).Item("PRODUCT_NAME").ToString,
                        .ProductId = CInt(result.Rows(0).Item("PRO_ID")),
                        .Weight = -Double.Parse(result.Rows(0).Item("WEIGHT").ToString()) / 1000 ' kgs -> tons
                    })
                End If

                ' 7 - Translate to JSON
                script &= "nameJSON = {"
                script &= """top"": [" &
                                "{ ""id"": ""productDescription"", ""val"": """ & GetTranslationForJSON("Product", mConfig) & """ }," &
                                "{ ""id"": ""recipeDescription"", ""val"": """ & GetTranslationForJSON("Recipe description", mConfig) & """ }," &
                                "{ ""id"": ""jobTotal"", ""val"": """ & GetTranslationForJSON("JOB_TOTAL", mConfig) & """ }," &
                                "{ ""id"": ""cycle"", ""val"": """ & GetTranslationForJSON("CYCLE_NAME", mConfig) & """ }," &
                                "{ ""id"": ""lot"", ""val"": """ & GetTranslationForJSON("Lotto", mConfig) & """ }," &
                                "{ ""id"": ""destinationBin"", ""val"": """ & GetTranslationForJSON("Destination bin", mConfig) & """ }" &
                            "],"
                script &= """product"": [" &
                    "{""name"": ""tb_a"", " &
                    " ""elem"": [" &
                            String.Join(",", Array.ConvertAll(extractionsData.ToArray(), Function(v) v.ToNameJSON())) &
                        "]}," &
                    "{""name"": ""tot_a""," &
                    " ""sum"": [""tb_a""], ""trad"": """ & GetTranslationForJSON("EXTRACTED_TOTAL", mConfig) & """, ""style"": ""total""}," &
                    "{""name"": ""tb_b"", " &
                    " ""elem"": [" &
                            String.Join(",", Array.ConvertAll(compositionData.ToArray(), Function(v) v.ToNameJSON())) &
                        "]}," &
                    "{""name"": ""tot_b""," &
                    " ""sum"": [""tb_b""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
                "]"
                script &= "};"
                script &= "sessionStorage.setItem(""nameJSON"", JSON.stringify(nameJSON));" &
                    "initializeReport( 0, " & EnumTypeReport.CleaningReport & ");"

                script &= "valueJSON = {"
                script &= """top"": [" &
                        "{ ""id"": ""job"", ""val"": " & jobId & " }," &
                        "{ ""id"": ""dateFrom"", ""val"": """ & dateFrom.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                        "{ ""id"": ""dateTo"", ""val"": """ & dateTo.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                        "{ ""id"": ""productDescription"", ""val"": """ & productDescription & """ }," &
                        "{ ""id"": ""recipeDescription"", ""val"": " & If(recipeLogId.Equals(m_InvalidInteger) OrElse recipeDescription.Trim().Equals(String.Empty), "null", """" & recipeDescription & """") & " }," &
                        "{ ""id"": ""jobTotal"", ""val"": " & Tools.ConvertValueToJs(Math.Round(jobTotal / 1000, 3)) & " }," &
                        "{ ""id"": ""cycle"", ""val"": """ & GetTranslationForJSON("MAIN_CYCLE_" & CInt(cycleId) & "_TITLE", mConfig) & """ }," &
                        "{ ""id"": ""destinationBin"", ""val"": """ & String.Join(",", destinationBins) & """ }," &
                        "{ ""id"": ""lot"", ""val"": " & GetLotValueLinkForJob(cycleId, jobId) & " }" &
                    "],"
                script &= """product"": [" &
                        "{""name"": ""tb_a"", " &
                        " ""elem"": [" &
                            String.Join(",", Array.ConvertAll(extractionsData.ToArray(), Function(v) v.ToValueJSON())) &
                        "]}," &
                        "{""name"": ""tb_b"", " &
                        " ""elem"": [" &
                            String.Join(",", Array.ConvertAll(compositionData.ToArray(), Function(v) v.ToValueJSON())) &
                        "]}" &
                    "]"
                script &= "};"
                script &= "sessionStorage.setItem(""valueJSON"", JSON.stringify(valueJSON));" &
                    "updateReport();"
            End If
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Reflection.Assembly.GetExecutingAssembly.GetName.Name, Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

#End Region

End Class