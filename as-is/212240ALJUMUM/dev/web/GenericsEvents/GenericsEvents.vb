﻿Imports System.Net.Mime.MediaTypeNames
Imports System.Web.UI.WebControls
Imports CommonDefines.Defines.SSDestinationSlots
Imports UsersGUI

Public Class GenericsEvents

    Private m_Config As config
    Private m_CheckBoxAtmill As myCheckBox
    Private m_RadioButtonAtmill As myRadioButton
    Private m_TextBoxAtmill As myTextBox
    Private m_DropDownListAtmill As myDropDownList
    Private m_Field As Field

    Public Sub New(ByVal mConfig As config, ByVal mCheckBoxAtmill As myCheckBox, ByVal mField As Field)
        m_Config = mConfig
        m_CheckBoxAtmill = mCheckBoxAtmill
        m_Field = mField
    End Sub

    Public Sub New(ByVal mConfig As config, ByVal mRadioButtonAtmill As myRadioButton, ByVal mField As Field)
        m_Config = mConfig
        m_RadioButtonAtmill = mRadioButtonAtmill
        m_Field = mField
    End Sub

    Public Sub New(ByVal mConfig As config, ByVal mTextBoxAtmill As myTextBox, ByVal mField As Field)
        m_Config = mConfig
        m_TextBoxAtmill = mTextBoxAtmill
        m_Field = mField
    End Sub

    Public Sub New(ByVal mConfig As config, ByVal mDropDownListAtmill As myDropDownList, ByVal mField As Field)
        m_Config = mConfig
        m_DropDownListAtmill = mDropDownListAtmill
        m_Field = mField
    End Sub

    ''' <summary>
    '''  Gestione degli eventi specifici per la commessa
    ''' </summary>
    ''' <param name="m_obj1">myCheckBox</param>
    ''' <param name="m_obj2">Field</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function CallEvent(ByVal m_obj1 As myCheckBox, ByVal m_obj2 As Field) As Object
        Dim t As myCheckBox = CType(m_obj1, myCheckBox)
        Dim obj As Field = CType(m_obj2, Field)

        t = EventFunction.GenericEvents(t, obj)

        Return t
    End Function

    ''' <summary>
    '''  Gestione degli eventi specifici per la commessa
    ''' </summary>
    ''' <param name="m_obj1">myRadioButton</param>
    ''' <param name="m_obj2">Field</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function CallEvent(ByVal m_obj1 As myRadioButton, ByVal m_obj2 As Field) As Object
        Dim t As myRadioButton = CType(m_obj1, myRadioButton)
        Dim obj As Field = CType(m_obj2, Field)

        t = EventFunction.GenericEvents(t, obj)

        Return t
    End Function

    ''' <summary>
    '''  Gestione degli eventi specifici per la commessa
    ''' </summary>
    ''' <param name="m_obj1">myTextBox</param>
    ''' <param name="m_obj2">Field</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function CallEvent(ByVal m_obj1 As myTextBox, ByVal m_obj2 As Field) As Object
        Dim t As myTextBox = CType(m_obj1, myTextBox)
        Dim obj As Field = CType(m_obj2, Field)

        t = EventFunction.GenericEvents(t, obj)

        Return t
    End Function

    ''' <summary>
    '''  Gestione degli eventi specifici per la commessa
    ''' </summary>
    ''' <param name="m_obj1">myDropDownList</param>
    ''' <param name="m_obj2">Field</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function CallEvent(ByVal m_obj1 As myDropDownList, ByVal m_obj2 As Field) As Object
        Dim t As myDropDownList = CType(m_obj1, myDropDownList)
        Dim obj As Field = CType(m_obj2, Field)

        t = EventFunction.GenericEvents(t, obj)

        Return t
    End Function

End Class

Public Class SourcePercToCycle
    Inherits EventArgs

    Public sources As New List(Of myCompositeObjects.SourcePerc)
End Class

Public Class EventFunction

    Public Shared Function GenericEvents(ByVal t As myCheckBox, ByVal obj As Field) As myCheckBox
        t.AutoPostBack = True
        Select Case obj.CallEvent
            Case "EventStartupBin_C04_ShowOptions"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventStartupBin_C04_ShowOptions
            Case "EventEndStartupBin_C04_ShowOptions"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventEndStartupBin_C04_ShowOptions
            Case Else
                t.AutoPostBack = False
        End Select

        Return t
    End Function

    Public Shared Function GenericEvents(ByVal t As myRadioButton, ByVal obj As Field) As myRadioButton
        t.AutoPostBack = True
        Select Case obj.CallEvent
            Case "EventLoadMode_Standard"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventLoadMode_Standard
            Case "EventSourceMode_Standard_1"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventSourceMode_Standard_1
            Case "EventSourceMode_Standard_2"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventSourceMode_Standard_2
            Case "EventSourceMode_Standard_3"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventSourceMode_Standard_3
            Case "EventSourceMode_Standard_4"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventSourceMode_Standard_4
            Case "EventSourceMode_Standard_5"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventSourceMode_Standard_5
            Case "EventLoadMode_C04SecondCleaningAndMilling_F1"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_F1
            Case "EventLoadMode_C04SecondCleaningAndMilling_F2"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_F2
            Case "EventLoadMode_C04SecondCleaningAndMilling_F3"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_F3
            Case "EventLoadMode_C04SecondCleaningAndMilling_Bran"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_Bran
            Case "EventLoadMode_Alternative_C10"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventLoadMode_Alternative_C10
            Case "EventStartupBin_C04_AutofillDest"
                AddHandler t.CheckedChanged, AddressOf EventDelegate.EventStartupBin_C04_AutofillDest
            Case Else
                t.AutoPostBack = False
        End Select

        Return t
    End Function

    Public Shared Function GenericEvents(ByVal t As myTextBox, ByVal obj As Field) As myTextBox
        t.AutoPostBack = True
        Select Case obj.CallEvent
            Case "EventEditStopQuantity_C04"
                AddHandler t.TextChanged, AddressOf EventDelegate.EventEditStopQuantity_C04
            Case Else
                t.AutoPostBack = False
        End Select

        Return t
    End Function

    Public Shared Function GenericEvents(ByVal t As myDropDownList, ByVal obj As Field) As myDropDownList
        t.AutoPostBack = True
        Select Case obj.CallEvent
            Case "EventSMCCell"
                AddHandler t.SelectedIndexChanged, AddressOf EventDelegate.EventSMCCell
            Case "EventTaskSchedulerHour"
                AddHandler t.SelectedIndexChanged, AddressOf EventDelegate.EventTaskSchedulerHour
            Case "EventThreadsLogLevel"
                AddHandler t.SelectedIndexChanged, AddressOf EventDelegate.EventThreadsLogLevel
            Case "EventRecIdShowIngredients_C02FirstCleaning"
                AddHandler t.SelectedIndexChanged, AddressOf EventDelegate.EventRecIdShowIngredients_C02FirstCleaning
            Case "EventRecIdShowIngredients_C03SecondTempering"
                AddHandler t.SelectedIndexChanged, AddressOf EventDelegate.EventRecIdShowIngredients_C03SecondTempering
            Case "EventRecIdShowIngredients_C04SecondCleaningAndMilling"
                AddHandler t.SelectedIndexChanged, AddressOf EventDelegate.EventRecIdShowIngredients_C04SecondCleaningAndMilling
            Case Else
                t.AutoPostBack = False
        End Select

        Return t
    End Function

End Class

Public Class EventDelegate

    Public Shared Sub AppendCallEvent(ByVal root As Web.UI.Control, ByVal f As UsersGUI.Field)
        If f.GetFieldType = EnumFieldType.FieldRadioButton Then
            Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
            If s_filter.Length > 1 Then
                If s_filter(0) = "CONFIG" Then
                    Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                    Dim i As Integer = 0

                    For i = 1 To n_ripetizioni
                        Dim radio_button As New myRadioButton

                        radio_button = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                        Select Case f.CallEvent
                            Case "EventLoadMode_Standard"
                                EventDelegate.EventLoadMode_Standard(radio_button, New System.EventArgs)
                            Case "EventSourceMode_Standard_1"
                                EventDelegate.EventSourceMode_Standard_1(radio_button, New System.EventArgs)
                            Case "EventSourceMode_Standard_2"
                                EventDelegate.EventSourceMode_Standard_2(radio_button, New System.EventArgs)
                            Case "EventSourceMode_Standard_3"
                                EventDelegate.EventSourceMode_Standard_3(radio_button, New System.EventArgs)
                            Case "EventSourceMode_Standard_4"
                                EventDelegate.EventSourceMode_Standard_4(radio_button, New System.EventArgs)
                            Case "EventSourceMode_Standard_5"
                                EventDelegate.EventSourceMode_Standard_5(radio_button, New System.EventArgs)
                            Case "EventLoadMode_C04SecondCleaningAndMilling_F1"
                                EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_F1(radio_button, New System.EventArgs)
                            Case "EventLoadMode_C04SecondCleaningAndMilling_F2"
                                EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_F2(radio_button, New System.EventArgs)
                            Case "EventLoadMode_C04SecondCleaningAndMilling_F3"
                                EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_F3(radio_button, New System.EventArgs)
                            Case "EventLoadMode_C04SecondCleaningAndMilling_Bran"
                                EventDelegate.EventLoadMode_C04SecondCleaningAndMilling_Bran(radio_button, New System.EventArgs)
                            Case "EventLoadMode_Alternative_C10"
                                EventDelegate.EventLoadMode_Alternative_C10(radio_button, New System.EventArgs)
                            Case "EventStartupBin_C04_AutofillDest"
                                EventDelegate.EventStartupBin_C04_AutofillDest(radio_button, New System.EventArgs)
                        End Select

                        Exit For
                    Next
                End If
            End If

        ElseIf f.GetFieldType = EnumFieldType.FieldCheckBoxOneZero Then
            Dim chk As New myCheckBox
            chk = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldDb), myCheckBox)

            Select Case f.CallEvent
                Case "EventStartupBin_C04_ShowOptions"
                    EventDelegate.EventStartupBin_C04_ShowOptions(chk, New System.EventArgs)
                Case "EventEndStartupBin_C04_ShowOptions"
                    EventDelegate.EventEndStartupBin_C04_ShowOptions(chk, New System.EventArgs)
            End Select

        ElseIf f.GetFieldType = EnumFieldType.FieldList Then
            Dim ddl As New myDropDownList
            ddl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldDb), myDropDownList)

            Select Case f.CallEvent
                Case "EventSMCCell"
                    EventDelegate.EventSMCCell(ddl, New System.EventArgs)
                Case "EventRecIdShowIngredients_C02FirstCleaning"
                    EventDelegate.EventRecIdShowIngredients_C02FirstCleaning(ddl, New System.EventArgs)
                Case "EventRecIdShowIngredients_C03SecondTempering"
                    EventDelegate.EventRecIdShowIngredients_C03SecondTempering(ddl, New System.EventArgs)
                Case "EventRecIdShowIngredients_C04SecondCleaningAndMilling"
                    EventDelegate.EventRecIdShowIngredients_C04SecondCleaningAndMilling(ddl, New System.EventArgs)
            End Select

        ElseIf f.GetFieldType = EnumFieldType.FieldString Then
            Dim txt As New myTextBox
            txt = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldDb), myTextBox)

            Select Case f.CallEvent
                Case "EventEditStopQuantity_C04"
                    EventDelegate.EventEditStopQuantity_C04(txt, New System.EventArgs)
            End Select
        End If
    End Sub

    Public Shared Sub EventTaskSchedulerHour(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventTaskSchedulerHour(sender, e)
    End Sub

    Public Shared Sub EventThreadsLogLevel(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventThreadsLogLevel(sender, e)
    End Sub

    Public Shared Sub EventSMCCell(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventSMCCell(sender, e)
    End Sub

    Public Shared Sub EventLoadMode_Standard(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventLoadMode_Standard(sender, e)
    End Sub

    Public Shared Sub EventSourceMode_Standard_1(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventSourceMode_Standard(sender, e, 1)
    End Sub

    Public Shared Sub EventSourceMode_Standard_2(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventSourceMode_Standard(sender, e, 2)
    End Sub

    Public Shared Sub EventSourceMode_Standard_3(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventSourceMode_Standard(sender, e, 3)
    End Sub

    Public Shared Sub EventSourceMode_Standard_4(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventSourceMode_Standard(sender, e, 4)
    End Sub

    Public Shared Sub EventSourceMode_Standard_5(ByVal sender As Object, ByVal e As System.EventArgs)
        StandardEvents.EventSourceMode_Standard(sender, e, 5)
    End Sub

    ' Chiamate agli eventi specifici di commessa
    Public Shared Sub EventRecIdShowIngredients_C02FirstCleaning(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventRecIdShowIngredients_C02FirstCleaning(sender, e)
    End Sub

    Public Shared Sub EventRecIdShowIngredients_C03SecondTempering(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventRecIdShowIngredients_C03SecondTempering(sender, e)
    End Sub

    Public Shared Sub EventRecIdShowIngredients_C04SecondCleaningAndMilling(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventRecIdShowIngredients_C04SecondCleaningAndMilling(sender, e)
    End Sub

    Public Shared Sub EventLoadMode_C04SecondCleaningAndMilling_F1(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventLoadMode_C04SecondCleaningAndMilling(sender, e, 1)
    End Sub

    Public Shared Sub EventLoadMode_C04SecondCleaningAndMilling_F2(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventLoadMode_C04SecondCleaningAndMilling(sender, e, 2)
    End Sub

    Public Shared Sub EventLoadMode_C04SecondCleaningAndMilling_F3(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventLoadMode_C04SecondCleaningAndMilling(sender, e, 3)
    End Sub

    Public Shared Sub EventLoadMode_C04SecondCleaningAndMilling_Bran(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventLoadMode_C04SecondCleaningAndMilling(sender, e, 4)
    End Sub

    Public Shared Sub EventEditStopQuantity_C04(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventEditStopQuantity_C04(sender, e)
    End Sub

    Public Shared Sub EventLoadMode_Alternative_C10(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventLoadMode_Alternative_C10(sender, e)
    End Sub

    Public Shared Sub EventStartupBin_C04_ShowOptions(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventStartupBin_C04_ShowOptions(sender, e)
    End Sub

    Public Shared Sub EventEndStartupBin_C04_ShowOptions(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventEndStartupBin_C04_ShowOptions(sender, e)
    End Sub

    Public Shared Sub EventStartupBin_C04_AutofillDest(ByVal sender As Object, ByVal e As System.EventArgs)
        SpecificEvents.EventStartupBin_C04_AutofillDest(sender, e)
    End Sub

End Class

Public Class SetEvents

    ''' <summary>
    ''' La funzione aggiorna gli oggetti di pagina con l'evento associato e configurato nel config.xml nella proprietà <callevent>
    ''' </summary>
    ''' <param name="root"></param>
    ''' <param name="scr"></param>
    ''' <param name="m_config"></param>
    Public Shared Sub UpdateCallEventFields(root As Web.UI.Control, ByVal scr As UsersGUI.Screen, ByVal m_config As UsersGUI.config)

        For Each f As Field In scr.EditFields
            Tools.AddedCallEvent(root, f, m_config)
        Next

        Return
    End Sub

End Class