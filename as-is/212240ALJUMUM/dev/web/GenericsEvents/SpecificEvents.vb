﻿Imports UsersGUI
Imports CommonDefines.Defines
Imports System.Reflection.Emit

Friend Class SpecificEvents

#Region "C02"

    Public Shared Sub EventRecIdShowIngredients_C02FirstCleaning(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim ddl_rec_id As myDropDownList = Nothing
        Dim ddl_pro_id_wheat As myDropDownList = Nothing
        Dim ddl_ingr_pro_id As myDropDownList = Nothing
        Dim ddl As myDropDownList = Nothing
        Dim txt_ingr_perc As myTextBox = Nothing
        Dim param_value As String
        Dim rb_linear As myRadioButton = Nothing
        Dim rb_circular As myRadioButton = Nothing
        Dim cb_enable_fc301 As myCheckBox = Nothing
        Dim rb_line_f1_fc301 As myRadioButton = Nothing
        Dim rb_line_f2_fc301 As myRadioButton = Nothing

        ddl_rec_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "REC_ID"), myDropDownList)

        If ddl_rec_id.SelectedIndex > 0 Then
            param_value = Tools.GetRecipeParameter("PRO_ID", ddl_rec_id.SelectedItem.Value)
            If IsNumeric(param_value) Then
                ddl_pro_id_wheat = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PRO_ID"), myDropDownList)
                ddl_pro_id_wheat.SelectedValue = param_value
                Tools.ShowDropDownList(sender.parent.page, "PRO_ID")
            End If

            'Gestisco le origini di estrazione
            For i As Integer = 1 To SSMaxNumOfIngredients.C02_FirstCleaning

                param_value = Tools.GetRecipeParameter("INGR_ID_" & i, ddl_rec_id.SelectedItem.Value)

                If IsNumeric(param_value) Then
                    Tools.ShowDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)

                    'Abilito i radio button della modalità di estrazione e setto come standard la modalità linear
                    Tools.ShowRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                    rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & i & "_1")
                    rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & i & "_2")

                    If Not rb_linear.Checked And Not rb_circular.Checked Then
                        rb_linear.Checked = True
                    End If

                    ddl_ingr_pro_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "RECIPE_INGR_ID_" & i), myDropDownList)
                    ddl_ingr_pro_id.SelectedValue = param_value

                    For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C02_FirstCleaning
                        Tools.ShowDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                    Next
                Else
                    Tools.HideDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)

                    'Disabilito i radio button della modalità di estrazione
                    Tools.HideRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                    ' Mostro sempre i campi delle origini del primo ingrediente
                    If i > 1 Then
                        For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C02_FirstCleaning
                            Tools.HideDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                        Next
                    End If
                End If

                param_value = Tools.GetRecipeParameter("INGR_PERC_" & i, ddl_rec_id.SelectedItem.Value)

                If IsNumeric(param_value) And Not param_value = "0" Then
                    Tools.ShowTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")

                    txt_ingr_perc = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "RECIPE_INGR_PERC_" & i), myTextBox)
                    txt_ingr_perc.Text = param_value
                Else
                    Tools.HideTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")
                End If
            Next
        Else
            Tools.HideDropDownList(sender.parent.page, "PRO_ID")

            'Gestisco le origini di estrazione
            For i As Integer = 1 To SSMaxNumOfIngredients.C02_FirstCleaning
                Tools.HideDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)
                Tools.HideTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")
            Next

            For i As Integer = 1 To SSMaxNumOfIngredients.C02_FirstCleaning

                'Disabilito i radio button della modalità di estrazione
                Tools.HideRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C02_FirstCleaning
                    Tools.HideDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                Next
            Next

        End If
    End Sub

#End Region

#Region "C03"

    Public Shared Sub EventRecIdShowIngredients_C03SecondTempering(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim ddl_rec_id As myDropDownList = Nothing
        Dim ddl_pro_id_wheat As myDropDownList = Nothing
        Dim ddl_ingr_pro_id As myDropDownList = Nothing
        Dim ddl As myDropDownList = Nothing
        Dim txt_ingr_perc As myTextBox = Nothing
        Dim param_value As String
        Dim rb_linear As myRadioButton = Nothing
        Dim rb_circular As myRadioButton = Nothing
        Dim cb_enable_fc301 As myCheckBox = Nothing
        Dim rb_line_f1_fc301 As myRadioButton = Nothing
        Dim rb_line_f2_fc301 As myRadioButton = Nothing

        ddl_rec_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "REC_ID"), myDropDownList)

        If ddl_rec_id.SelectedIndex > 0 Then
            param_value = Tools.GetRecipeParameter("PRO_ID", ddl_rec_id.SelectedItem.Value)
            If IsNumeric(param_value) Then
                ddl_pro_id_wheat = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PRO_ID"), myDropDownList)
                ddl_pro_id_wheat.SelectedValue = param_value
                Tools.ShowDropDownList(sender.parent.page, "PRO_ID")
            End If

            'Gestisco le origini di estrazione
            For i As Integer = 1 To SSMaxNumOfIngredients.C03_SecondTempering

                param_value = Tools.GetRecipeParameter("INGR_ID_" & i, ddl_rec_id.SelectedItem.Value)

                If IsNumeric(param_value) Then
                    Tools.ShowDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)

                    'Abilito i radio button della modalità di estrazione e setto come standard la modalità linear
                    Tools.ShowRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                    rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & i & "_1")
                    rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & i & "_2")

                    If Not rb_linear.Checked And Not rb_circular.Checked Then
                        rb_linear.Checked = True
                    End If

                    ddl_ingr_pro_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "RECIPE_INGR_ID_" & i), myDropDownList)
                    ddl_ingr_pro_id.SelectedValue = param_value

                    For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C03_SecondTempering
                        Tools.ShowDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                    Next
                Else
                    Tools.HideDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)

                    'Disabilito i radio button della modalità di estrazione
                    Tools.HideRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                    ' Mostro sempre i campi delle origini del primo ingrediente
                    If i > 1 Then
                        For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C03_SecondTempering
                            Tools.HideDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                        Next
                    End If
                End If

                param_value = Tools.GetRecipeParameter("INGR_PERC_" & i, ddl_rec_id.SelectedItem.Value)

                If IsNumeric(param_value) And Not param_value = "0" Then
                    Tools.ShowTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")

                    txt_ingr_perc = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "RECIPE_INGR_PERC_" & i), myTextBox)
                    txt_ingr_perc.Text = param_value
                Else
                    Tools.HideTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")
                End If
            Next
        Else
            Tools.HideDropDownList(sender.parent.page, "PRO_ID")

            'Gestisco le origini di estrazione
            For i As Integer = 1 To SSMaxNumOfIngredients.C03_SecondTempering
                Tools.HideDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)
                Tools.HideTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")
            Next

            For i As Integer = 1 To SSMaxNumOfIngredients.C03_SecondTempering

                'Disabilito i radio button della modalità di estrazione
                Tools.HideRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C03_SecondTempering
                    Tools.HideDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                Next
            Next

        End If
    End Sub

#End Region

#Region "C04"

    Public Shared Sub EventRecIdShowIngredients_C04SecondCleaningAndMilling(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim ddl_rec_id As myDropDownList = Nothing
        Dim ddl_pro_id As myDropDownList = Nothing
        Dim ddl_ingr_pro_id As myDropDownList = Nothing
        Dim ddl As myDropDownList = Nothing
        Dim txt_ingr_perc As myTextBox = Nothing
        Dim txt_set_point As myTextBox = Nothing
        Dim param_value As String
        Dim rb_linear As myRadioButton = Nothing
        Dim rb_circular As myRadioButton = Nothing
        Dim counter As Long = UsersGUI.costanti.m_InvalidId
        Dim ppl_id As Long = UsersGUI.costanti.m_InvalidId

        ddl_rec_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "REC_ID"), myDropDownList)

        If ddl_rec_id.SelectedIndex > 0 Then

            'Gestisco le origini di estrazione
            For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                'Tools.HideRadioButton(sender.parent.page, "LINE_MODE_INGR_" & i)

                param_value = Tools.GetRecipeParameter("INGR_ID_" & i, ddl_rec_id.SelectedItem.Value)

                If IsNumeric(param_value) Then

                    Tools.ShowDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)
                    ddl_ingr_pro_id = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "RECIPE_INGR_ID_" & i), myDropDownList)
                    ddl_ingr_pro_id.SelectedValue = param_value

                    'Abilito i radio button della modalità di estrazione e setto come standard la modalità linear
                    Tools.ShowRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                    rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & i & "_1")
                    rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & i & "_2")

                    If Not rb_linear.Checked And Not rb_circular.Checked Then
                        rb_linear.Checked = True
                    End If

                    If (CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "COUNTER"), myTextBox) IsNot Nothing) Then
                        If IsNumeric(CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "COUNTER"), myTextBox).Text) Then
                            counter = Long.Parse(CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "COUNTER"), myTextBox).Text)

                            ppl_id = UsersGUI.tools.GetPplIdFromCounter(counter)
                            param_value = UsersGUI.tools.GetOrderParameter("SOURCE_MODE_" & i, ppl_id)

                            If (param_value = "") Then
                                param_value = "0"
                            End If

                            Tools.ShowRadioButton(sender.parent.page, "SOURCE_MODE_" & i, param_value, Nothing)

                        End If
                    End If

                    For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C04_SecondCleaningAndMilling
                        Tools.ShowDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                    Next
                Else
                    Tools.HideDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)

                    'Disabilito i radio button della modalità di estrazione
                    Tools.HideRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                    ' Mostro sempre i campi delle origini del primo ingrediente
                    If i > 1 Then
                        For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C04_SecondCleaningAndMilling
                            Tools.HideDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                        Next
                    End If
                End If

                param_value = Tools.GetRecipeParameter("INGR_PERC_" & i, ddl_rec_id.SelectedItem.Value)

                If IsNumeric(param_value) And Not param_value = "0" Then
                    Tools.ShowTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")

                    txt_ingr_perc = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "RECIPE_INGR_PERC_" & i), myTextBox)
                    txt_ingr_perc.Text = param_value
                    Tools.ShowRadioButton(sender.parent.page, "LINE_MODE_INGR_" & i)
                Else
                    Tools.HideTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")
                End If

            Next

            'Gestisco i prodotti di destinazione
            ddl = Nothing
            param_value = Tools.GetRecipeParameter("PRO_ID", ddl_rec_id.SelectedItem.Value)
            If IsNumeric(param_value) Then
                ddl = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PRO_ID"), myDropDownList)
                ddl.SelectedValue = param_value
                Tools.ShowDropDownList(sender.parent.page, "PRO_ID")
            Else
                Tools.HideDropDownList(sender.parent.page, "PRO_ID")
            End If

            ddl = Nothing
            param_value = Tools.GetRecipeParameter("PROD_ID_F1", ddl_rec_id.SelectedItem.Value)
            If IsNumeric(param_value) Then
                ddl = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PROD_ID_F1"), myDropDownList)
                ddl.SelectedValue = param_value
                Tools.ShowDropDownList(sender.parent.page, "PROD_ID_F1")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F1
                    Tools.ShowDropDownList(sender.parent.page, "F1_DEST_BIN_" & i)
                Next

                Tools.ShowRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F1)
                rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_1" & "_1")
                rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_1" & "_2")

                If Not rb_linear.Checked And Not rb_circular.Checked Then
                    rb_linear.Checked = True
                End If
            Else
                Tools.HideDropDownList(sender.parent.page, "PROD_ID_F1")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F1
                    Tools.HideDropDownList(sender.parent.page, "F1_DEST_BIN_" & i)
                Next

                Tools.HideRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F1)
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F1 & "_CONTINUOUS_MIN", "min")
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F1 & "_CONTINUOUS_SEC", "sec")
            End If

            ddl = Nothing
            param_value = Tools.GetRecipeParameter("PROD_ID_F2", ddl_rec_id.SelectedItem.Value)
            If IsNumeric(param_value) Then
                ddl = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PROD_ID_F2"), myDropDownList)
                ddl.SelectedValue = param_value
                Tools.ShowDropDownList(sender.parent.page, "PROD_ID_F2")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F2
                    Tools.ShowDropDownList(sender.parent.page, "F2_DEST_BIN_" & i)
                Next

                Tools.ShowRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F2)
                rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_2" & "_1")
                rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_2" & "_2")

                If Not rb_linear.Checked And Not rb_circular.Checked Then
                    rb_linear.Checked = True
                End If
            Else
                Tools.HideDropDownList(sender.parent.page, "PROD_ID_F2")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F2
                    Tools.HideDropDownList(sender.parent.page, "F2_DEST_BIN_" & i)
                Next

                Tools.HideRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F2)
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F2 & "_CONTINUOUS_MIN", "min")
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F2 & "_CONTINUOUS_SEC", "sec")
            End If

            ddl = Nothing
            param_value = Tools.GetRecipeParameter("PROD_ID_F3", ddl_rec_id.SelectedItem.Value)
            If IsNumeric(param_value) Then
                ddl = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PROD_ID_F3"), myDropDownList)
                ddl.SelectedValue = param_value
                Tools.ShowDropDownList(sender.parent.page, "PROD_ID_F3")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F3
                    Tools.ShowDropDownList(sender.parent.page, "F3_DEST_BIN_" & i)
                Next

                Tools.ShowRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F3)
                rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_3" & "_1")
                rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_3" & "_2")

                If Not rb_linear.Checked And Not rb_circular.Checked Then
                    rb_linear.Checked = True
                End If
            Else
                Tools.HideDropDownList(sender.parent.page, "PROD_ID_F3")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F3
                    Tools.HideDropDownList(sender.parent.page, "F3_DEST_BIN_" & i)
                Next

                Tools.HideRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F3)
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F3 & "_CONTINUOUS_MIN", "min")
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_F3 & "_CONTINUOUS_SEC", "sec")
            End If

            ddl = Nothing
            param_value = Tools.GetRecipeParameter("PROD_ID_BRAN", ddl_rec_id.SelectedItem.Value)
            If IsNumeric(param_value) Then
                ddl = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PROD_ID_BRAN"), myDropDownList)
                ddl.SelectedValue = param_value
                Tools.ShowDropDownList(sender.parent.page, "PROD_ID_BRAN")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.BRAN
                    Tools.ShowDropDownList(sender.parent.page, "BRAN_DEST_BIN_" & i)
                Next

                Tools.ShowRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_BRAN)
                rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_4" & "_1")
                rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_4" & "_2")

                If Not rb_linear.Checked And Not rb_circular.Checked Then
                    rb_linear.Checked = True
                End If
            Else
                Tools.HideDropDownList(sender.parent.page, "PROD_ID_BRAN")
                For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.BRAN
                    Tools.HideDropDownList(sender.parent.page, "BRAN_DEST_BIN_" & i)
                Next

                Tools.HideRadioButton(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_BRAN)
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_BRAN & "_CONTINUOUS_MIN", "min")
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & SSDestinationLoadMode.C04_SecondCleaningAndMilling_LoadMode_BRAN & "_CONTINUOUS_SEC", "sec")
            End If

            'SET POINT
            For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling_SET_POINT

                param_value = Tools.GetRecipeParameter("FC_40" & i, ddl_rec_id.SelectedItem.Value)

                If IsNumeric(param_value) And Not param_value = "0" Then
                    Tools.ShowTextBox(sender.parent.page, "FC_40" & i, "g/T")
                    txt_set_point = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "FC_40" & i), myTextBox)
                    txt_set_point.Text = param_value
                Else
                    Tools.HideTextBox(sender.parent.page, "FC_40" & i, "g/T")
                End If
            Next
        Else
            'Gestisco le origini di estrazione
            For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                Tools.HideDropDownList(sender.parent.page, "RECIPE_INGR_ID_" & i)
                Tools.HideTextBox(sender.parent.page, "RECIPE_INGR_PERC_" & i, "%")
            Next

            For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                'Disabilito i radio button della modalità di estrazione
                Tools.HideRadioButton(sender.parent.page, "LINE_MODE_INGR_" & i)

                Tools.HideRadioButton(sender.parent.page, "SOURCE_MODE_" & i)

                For j As Integer = 1 To SSMaxNumOfSourcesPerIngredient.C04_SecondCleaningAndMilling
                    Tools.HideDropDownList(sender.parent.page, "SRC_INGR_" & i & "_BIN_" & j)
                Next
            Next

            'Gestisco le destinazioni
            Tools.HideDropDownList(sender.parent.page, "PROD_ID_F1")
            For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F1
                Tools.HideDropDownList(sender.parent.page, "F1_DEST_BIN_" & i)
            Next

            Tools.HideDropDownList(sender.parent.page, "PROD_ID_F2")
            For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F2
                Tools.HideDropDownList(sender.parent.page, "F2_DEST_BIN_" & i)
            Next

            Tools.HideDropDownList(sender.parent.page, "PROD_ID_F3")
            For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.F3
                Tools.HideDropDownList(sender.parent.page, "F3_DEST_BIN_" & i)
            Next

            Tools.HideDropDownList(sender.parent.page, "PROD_ID_BRAN")
            For i As Integer = 1 To SSDestinationSlots.C04_SecondCleaningAndMilling.BRAN
                Tools.HideDropDownList(sender.parent.page, "BRAN_DEST_BIN_" & i)
            Next

            'Gestisco i LoadMode
            For i As Integer = 1 To SSDestinationLoadMode.C04_SecondCleaningAndMilling

                'Disabilito i radio button della modalità di estrazione
                Tools.HideRadioButton(sender.parent.page, "LOAD_MODE_" & i)
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & i & "_CONTINUOUS_MIN", "min")
                Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & i & "_CONTINUOUS_SEC", "sec")
            Next

            'SET POINT
            For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling_SET_POINT
                Tools.HideTextBox(sender.parent.page, "FC_40" & i, "g/T")
            Next
        End If
    End Sub

    Public Shared Sub EventLoadMode_C04SecondCleaningAndMilling(ByVal sender As Object, ByVal e As System.EventArgs, i As Integer)
        Dim rb_linear As myRadioButton = Nothing
        Dim rb_circular As myRadioButton = Nothing
        Dim rb_continuous As myRadioButton = Nothing
        Dim txt_min As myTextBox = Nothing
        Dim txt_sec As myTextBox = Nothing

        If TypeOf sender Is myRadioButton Then
            rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_" & i & "_1")
            rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_" & i & "_2")
            rb_continuous = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_" & i & "_3")

            txt_min = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_" & i & "_CONTINUOUS_MIN")
            txt_sec = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_" & i & "_CONTINUOUS_SEC")
        Else
            Return
        End If

        If Not rb_linear.Checked And Not rb_circular.Checked And Not rb_continuous.Checked Then
            rb_linear.Checked = True
        End If

        If rb_continuous.Checked Then
            Tools.ShowTextBox(sender.parent.page, "LOAD_MODE_" & i & "_CONTINUOUS_MIN", "min")
            Tools.ShowTextBox(sender.parent.page, "LOAD_MODE_" & i & "_CONTINUOUS_SEC", "sec")
        Else
            Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & i & "_CONTINUOUS_MIN", "min")
            Tools.HideTextBox(sender.parent.page, "LOAD_MODE_" & i & "_CONTINUOUS_SEC", "sec")
        End If
    End Sub

    Public Shared Sub EventEditStopQuantity_C04(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim rb_1 As myRadioButton = Nothing
        Dim rb_2 As myRadioButton = Nothing
        Dim rb_3 As myRadioButton = Nothing
        Dim rb_4 As myRadioButton = Nothing

        Dim txt_quantity As myTextBox = Nothing

        If TypeOf sender Is myTextBox Then
            txt_quantity = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "OPZ_QTY")
        End If

        rb_1 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SS_C04_QTY_LINE_1")
        rb_2 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SS_C04_QTY_LINE_2")
        rb_3 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SS_C04_QTY_LINE_3")
        rb_4 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SS_C04_QTY_LINE_4")

        If (txt_quantity.Text <> String.Empty) Then
            Try
                If (Double.Parse(txt_quantity.Text) > 0) Then
                    rb_1.Checked = False
                    rb_1.Enabled = True
                    rb_2.Checked = False
                    rb_2.Enabled = True
                    rb_3.Checked = False
                    rb_3.Enabled = True
                    rb_4.Checked = False
                    rb_4.Enabled = True
                Else
                    rb_1.Checked = False
                    rb_1.Enabled = False
                    rb_2.Checked = False
                    rb_2.Enabled = False
                    rb_3.Checked = False
                    rb_3.Enabled = False
                    rb_4.Checked = False
                    rb_4.Enabled = False
                End If
            Catch ex As Exception
                rb_1.Checked = False
                rb_1.Enabled = False
                rb_2.Checked = False
                rb_2.Enabled = False
                rb_3.Checked = False
                rb_3.Enabled = False
                rb_4.Checked = False
                rb_4.Enabled = False
            End Try
        Else
            rb_1.Checked = False
            rb_1.Enabled = False
            rb_2.Checked = False
            rb_2.Enabled = False
            rb_3.Checked = False
            rb_3.Enabled = False
            rb_4.Checked = False
            rb_4.Enabled = False
        End If
    End Sub

    Public Shared Sub EventStartupBin_C04_ShowOptions(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim startup_line As myRadioButton = Nothing
        Dim txt_min As myTextBox = Nothing
        Dim txt_sec As myTextBox = Nothing
        Dim enable_startup As myCheckBox = Nothing

        If TypeOf sender Is myCheckBox Then
            startup_line = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "STARTUP_BIN_LINE")

            txt_min = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "T_MIN_SILO_STARTUP")
            txt_sec = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "T_SEC_SILO_STARTUP")

            enable_startup = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "ENABLE_STARTUP")
        Else
            Return
        End If

        If enable_startup.Checked Then 'andrebbe bene anche scrivere sender.checked, però così è più leggibile
            Tools.ShowRadioButton(sender.parent.page, "STARTUP_BIN_LINE")

            Tools.ShowTextBox(sender.parent.page, "T_MIN_SILO_STARTUP", "min")
            Tools.ShowTextBox(sender.parent.page, "T_SEC_SILO_STARTUP", "sec")
        Else
            Tools.HideRadioButton(sender.parent.page, "STARTUP_BIN_LINE")

            Tools.HideTextBox(sender.parent.page, "T_MIN_SILO_STARTUP", "min")
            Tools.HideTextBox(sender.parent.page, "T_SEC_SILO_STARTUP", "sec")

            EventStartupBin_C04_AutofillDest(sender, e)
        End If
    End Sub

    Public Shared Sub EventEndStartupBin_C04_ShowOptions(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim end_startup_line As myRadioButton = Nothing
        Dim enable_end_startup As myCheckBox = Nothing

        If TypeOf sender Is myCheckBox Then
            end_startup_line = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "END_STARTUP_BIN_LINE")

            enable_end_startup = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "ENABLE_END_STARTUP")
        Else
            Return
        End If

        If enable_end_startup.Checked Then 'andrebbe bene anche scrivere sender.checked, però così è più leggibile
            Tools.ShowRadioButton(sender.parent.page, "END_STARTUP_BIN_LINE")
        Else
            Tools.HideRadioButton(sender.parent.page, "END_STARTUP_BIN_LINE")
        End If
    End Sub

    Public Shared Sub EventStartupBin_C04_AutofillDest(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim startup_line_F1 As myRadioButton = Nothing
        Dim startup_line_F2 As myRadioButton = Nothing
        Dim startup_line_F3 As myRadioButton = Nothing
        Dim rb_linear As myRadioButton = Nothing
        Dim ddl_first_dest As myDropDownList = Nothing
        Dim ddl_dest As myDropDownList = Nothing
        Dim dest_bin_to_be_moved As Long = 0
        Dim i As Integer = 0

        If TypeOf sender Is myRadioButton OrElse TypeOf sender Is myCheckBox Then
            startup_line_F1 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "STARTUP_BIN_LINE_1")
            startup_line_F2 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "STARTUP_BIN_LINE_2")
            startup_line_F3 = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "STARTUP_BIN_LINE_3")
        Else
            Return
        End If

        ' LINEA F1
        If startup_line_F1.Checked Then
            ' Controllo se la prima destinazione è la SL401 (startup bin)
            ddl_first_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F1_DEST_BIN_1")

            If ddl_first_dest Is Nothing OrElse ddl_first_dest.SelectedValue = String.Empty OrElse ddl_first_dest.SelectedValue <> SSBins.SL401 Then
                ' Devo assegnare SL401 come prima destinazione, scalando le altre di un posto nella coda partendo dal fondo della coda
                i = SSDestinationSlots.C04_SecondCleaningAndMilling.F1 - 1
                While i > 0
                    ' Assegno la penultima destinazione all'ultimo posto della coda e così via
                    ' risalendo fino alla prima destinazione normale, che va assegnata al secondo posto
                    ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F1_DEST_BIN_" & i)
                    If ddl_dest IsNot Nothing AndAlso ddl_dest.SelectedValue <> String.Empty Then
                        ' Salvo il valore precedentemente impostato
                        dest_bin_to_be_moved = ddl_dest.SelectedValue.ToString
                        ' e svuoto la ddl_dest che ho usato per recuperare il valore
                        ddl_dest.SelectedValue = Nothing

                        ' Mi posiziono sulla destinazione successiva e assegno il valore appena recuperato
                        ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F1_DEST_BIN_" & i + 1)
                        ddl_dest.SelectedValue = dest_bin_to_be_moved
                    End If

                    ' Mi posiziono sulla destinazione PRECEDENTE
                    i = i - 1
                End While

                ' Infine assegno SL401 (startup bin) come prima destinazione
                ddl_first_dest.SelectedValue = SSBins.SL401
            Else
                ' Nothing to do, SL401 è già la prima destinazione
            End If
        Else
            ' Può essere che io debba eliminare SL401 (startup bin) dalla coda, se era già stata inserita precedentemente

            ' Controllo se la prima destinazione è la SL401 (startup bin)
            ddl_first_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F1_DEST_BIN_1")

            If ddl_first_dest IsNot Nothing AndAlso ddl_first_dest.SelectedValue <> String.Empty AndAlso ddl_first_dest.SelectedValue = SSBins.SL401 Then
                ' Tolgo la SL401 dalla prima posizione
                ' (lo faccio anche per coprire il caso in cui solo la SL401 sia stata selezionata e non ci siano altre destinazioni in coda per sovrascriverla)
                ddl_first_dest.SelectedValue = Nothing

                ' Sposto le destinazioni successive all'indietro
                i = 2
                While i <= SSDestinationSlots.C04_SecondCleaningAndMilling.F1
                    ' Assegno la seconda destinazione al posto della prima e così via
                    ' risalendo fino all'ultima destinazione, che va assegnata al penultimo posto
                    ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F1_DEST_BIN_" & i)

                    If ddl_dest IsNot Nothing AndAlso ddl_dest.SelectedValue <> String.Empty Then
                        ' Salvo il valore precedentemente impostato
                        dest_bin_to_be_moved = ddl_dest.SelectedValue.ToString
                        ' e svuoto la ddl_dest che ho usato per recuperare il valore
                        ddl_dest.SelectedValue = Nothing

                        ' Mi posiziono sulla destinazione precendente e assegno il valore appena recuperato
                        ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F1_DEST_BIN_" & i - 1)
                        ddl_dest.SelectedValue = dest_bin_to_be_moved
                    End If

                    ' Mi posiziono sulla destinazione SUCESSIVA
                    i = i + 1
                End While
            Else
                ' Nothing to do, SL401 non era prima destinazione
            End If

        End If

        ' LINEA F2
        If startup_line_F2.Checked Then
            ' Controllo se la prima destinazione è la SL401 (startup bin)
            ddl_first_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F2_DEST_BIN_1")

            If ddl_first_dest Is Nothing OrElse ddl_first_dest.SelectedValue = String.Empty OrElse ddl_first_dest.SelectedValue <> SSBins.SL401 Then
                ' Devo assegnare SL401 come prima destinazione, scalando le altre di un posto nella coda partendo dal fondo della coda
                i = SSDestinationSlots.C04_SecondCleaningAndMilling.F2 - 1
                While i > 0
                    ' Assegno la penultima destinazione all'ultimo posto della coda e così via
                    ' risalendo fino alla prima destinazione normale, che va assegnata al secondo posto
                    ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F2_DEST_BIN_" & i)
                    If ddl_dest IsNot Nothing AndAlso ddl_dest.SelectedValue <> String.Empty Then
                        ' Salvo il valore precedentemente impostato
                        dest_bin_to_be_moved = ddl_dest.SelectedValue.ToString
                        ' e svuoto la ddl_dest che ho usato per recuperare il valore
                        ddl_dest.SelectedValue = Nothing

                        ' Mi posiziono sulla destinazione successiva e assegno il valore appena recuperato
                        ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F2_DEST_BIN_" & i + 1)
                        ddl_dest.SelectedValue = dest_bin_to_be_moved
                    End If

                    ' Mi posiziono sulla destinazione PRECEDENTE
                    i = i - 1
                End While

                ' Infine assegno SL401 (startup bin) come prima destinazione
                ddl_first_dest.SelectedValue = SSBins.SL401
            Else
                ' Nothing to do, SL401 è già la prima destinazione
            End If
        Else
            ' Può essere che io debba eliminare SL401 (startup bin) dalla coda, se era già stata inserita precedentemente

            ' Controllo se la prima destinazione è la SL401 (startup bin)
            ddl_first_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F2_DEST_BIN_1")

            If ddl_first_dest IsNot Nothing AndAlso ddl_first_dest.SelectedValue <> String.Empty AndAlso ddl_first_dest.SelectedValue = SSBins.SL401 Then
                ' Tolgo la SL401 dalla prima posizione
                ' (lo faccio anche per coprire il caso in cui solo la SL401 sia stata selezionata e non ci siano altre destinazioni in coda per sovrascriverla)
                ddl_first_dest.SelectedValue = Nothing

                ' Sposto le destinazioni successive all'indietro
                i = 2
                While i <= SSDestinationSlots.C04_SecondCleaningAndMilling.F2
                    ' Assegno la seconda destinazione al posto della prima e così via
                    ' risalendo fino all'ultima destinazione, che va assegnata al penultimo posto
                    ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F2_DEST_BIN_" & i)

                    If ddl_dest IsNot Nothing AndAlso ddl_dest.SelectedValue <> String.Empty Then
                        ' Salvo il valore precedentemente impostato
                        dest_bin_to_be_moved = ddl_dest.SelectedValue.ToString
                        ' e svuoto la ddl_dest che ho usato per recuperare il valore
                        ddl_dest.SelectedValue = Nothing

                        ' Mi posiziono sulla destinazione precendente e assegno il valore appena recuperato
                        ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F2_DEST_BIN_" & i - 1)
                        ddl_dest.SelectedValue = dest_bin_to_be_moved
                    End If

                    ' Mi posiziono sulla destinazione SUCESSIVA
                    i = i + 1
                End While
            Else
                ' Nothing to do, SL401 non era prima destinazione
            End If

        End If

        ' LINEA F3
        If startup_line_F3.Checked Then
            ' Controllo se la prima destinazione è la SL401 (startup bin)
            ddl_first_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F3_DEST_BIN_1")

            If ddl_first_dest Is Nothing OrElse ddl_first_dest.SelectedValue = String.Empty OrElse ddl_first_dest.SelectedValue <> SSBins.SL401 Then
                ' Devo assegnare SL401 come prima destinazione, scalando le altre di un posto nella coda partendo dal fondo della coda
                i = SSDestinationSlots.C04_SecondCleaningAndMilling.F3 - 1
                While i > 0
                    ' Assegno la penultima destinazione all'ultimo posto della coda e così via
                    ' risalendo fino alla prima destinazione normale, che va assegnata al secondo posto
                    ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F3_DEST_BIN_" & i)
                    If ddl_dest IsNot Nothing AndAlso ddl_dest.SelectedValue <> String.Empty Then
                        ' Salvo il valore precedentemente impostato
                        dest_bin_to_be_moved = ddl_dest.SelectedValue.ToString
                        ' e svuoto la ddl_dest che ho usato per recuperare il valore
                        ddl_dest.SelectedValue = Nothing

                        ' Mi posiziono sulla destinazione successiva e assegno il valore appena recuperato
                        ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F3_DEST_BIN_" & i + 1)
                        ddl_dest.SelectedValue = dest_bin_to_be_moved
                    End If

                    ' Mi posiziono sulla destinazione PRECEDENTE
                    i = i - 1
                End While

                ' Infine assegno SL401 (startup bin) come prima destinazione
                ddl_first_dest.SelectedValue = SSBins.SL401
            Else
                ' Nothing to do, SL401 è già la prima destinazione
            End If
        Else
            ' Può essere che io debba eliminare SL401 (startup bin) dalla coda, se era già stata inserita precedentemente

            ' Controllo se la prima destinazione è la SL401 (startup bin)
            ddl_first_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F3_DEST_BIN_1")

            If ddl_first_dest IsNot Nothing AndAlso ddl_first_dest.SelectedValue <> String.Empty AndAlso ddl_first_dest.SelectedValue = SSBins.SL401 Then
                ' Tolgo la SL401 dalla prima posizione
                ' (lo faccio anche per coprire il caso in cui solo la SL401 sia stata selezionata e non ci siano altre destinazioni in coda per sovrascriverla)
                ddl_first_dest.SelectedValue = Nothing

                ' Sposto le destinazioni successive all'indietro
                i = 2
                While i <= SSDestinationSlots.C04_SecondCleaningAndMilling.F3
                    ' Assegno la seconda destinazione al posto della prima e così via
                    ' risalendo fino all'ultima destinazione, che va assegnata al penultimo posto
                    ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F3_DEST_BIN_" & i)

                    If ddl_dest IsNot Nothing AndAlso ddl_dest.SelectedValue <> String.Empty Then
                        ' Salvo il valore precedentemente impostato
                        dest_bin_to_be_moved = ddl_dest.SelectedValue.ToString
                        ' e svuoto la ddl_dest che ho usato per recuperare il valore
                        ddl_dest.SelectedValue = Nothing

                        ' Mi posiziono sulla destinazione precendente e assegno il valore appena recuperato
                        ddl_dest = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "F3_DEST_BIN_" & i - 1)
                        ddl_dest.SelectedValue = dest_bin_to_be_moved
                    End If

                    ' Mi posiziono sulla destinazione SUCESSIVA
                    i = i + 1
                End While
            Else
                ' Nothing to do, SL401 non era prima destinazione
            End If

        End If

    End Sub

#End Region

#Region "C10"

    Public Shared Sub EventLoadMode_Alternative_C10(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim rb_linear As myRadioButton = Nothing
        Dim rb_circular As myRadioButton = Nothing
        Dim rb_continuous As myRadioButton = Nothing
        Dim txt_min As myTextBox = Nothing
        Dim txt_sec As myTextBox = Nothing

        If TypeOf sender Is myRadioButton Then
            rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "ALT_LOAD_MODE_1")
            rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "ALT_LOAD_MODE_2")
            rb_continuous = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "ALT_LOAD_MODE_3")

            txt_min = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "ALT_LOAD_MODE_CONTINUOUS_MIN")
            txt_sec = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "ALT_LOAD_MODE_CONTINUOUS_SEC")
        Else
            Return
        End If

        If Not rb_linear.Checked And Not rb_circular.Checked And Not rb_continuous.Checked Then
            rb_linear.Checked = True
        End If

        If rb_continuous.Checked Then
            Tools.ShowTextBox(sender.parent.page, "ALT_LOAD_MODE_CONTINUOUS_MIN", "min")
            Tools.ShowTextBox(sender.parent.page, "ALT_LOAD_MODE_CONTINUOUS_SEC", "sec")
        Else
            Tools.HideTextBox(sender.parent.page, "ALT_LOAD_MODE_CONTINUOUS_MIN", "min")
            Tools.HideTextBox(sender.parent.page, "ALT_LOAD_MODE_CONTINUOUS_SEC", "sec")
        End If
    End Sub

#End Region

End Class