﻿Imports System.Web.UI.WebControls
Imports UsersGUI
Imports GenericsEvents.Tools
Imports CommonDefines.Defines

Friend Class StandardEvents

    Public Shared Sub EventSMCCell(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim ddl_cells As myDropDownList = Nothing
        Dim ddl_products As myDropDownList = Nothing
        Dim ddl_lot_types As myDropDownList = Nothing
        Dim txt As myTextBox
        Dim str_select_product_types As String = String.Empty
        Dim str_select_products As String = String.Empty
        Dim str_lot_types As String = String.Empty
        Dim str_select_cells As String = String.Empty
        Dim dt_product_types As DataTable
        Dim dt_products As DataTable
        Dim dt_cells As DataTable
        Dim dt_lot_types As DataTable
        Dim ddl_dr_product As Data.DataRow
        Dim ddl_dr_lot_type As Data.DataRow

        ddl_cells = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "CEL_ID"), myDropDownList)

        If ddl_cells.SelectedIndex >= 0 AndAlso IsNumeric(ddl_cells.SelectedItem.Value) Then

            str_select_product_types = "SELECT SMC_PT_ID FROM VIEW_CELLS_TO_SMC_PT_ID WHERE CEL_ID = " & ddl_cells.SelectedItem.Value & " ORDER BY SMC_PT_ID"
            dt_product_types = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_select_product_types, False)

            ' 1. products
            ddl_products = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "PRO_ID"), myDropDownList)
            ddl_products.Items.Clear()
            ddl_products.Enabled = True

            Dim ddl_dt_products As New Data.DataTable
            ddl_dt_products.Columns.Add("ID")
            ddl_dt_products.Columns.Add("VALUE")

            ddl_dr_product = ddl_dt_products.NewRow
            ddl_dr_product("ID") = String.Empty
            ddl_dr_product("VALUE") = String.Empty
            ddl_dt_products.Rows.Add(ddl_dr_product)

            ' 2. lot types
            ddl_lot_types = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "LT_ID"), myDropDownList)
            ddl_lot_types.Items.Clear()
            ddl_lot_types.Enabled = True

            Dim ddl_dt_lot_types As New Data.DataTable
            ddl_dt_lot_types.Columns.Add("ID")
            ddl_dt_lot_types.Columns.Add("VALUE")

            ddl_dr_lot_type = ddl_dt_lot_types.NewRow
            ddl_dr_lot_type("ID") = String.Empty
            ddl_dr_lot_type("VALUE") = String.Empty
            ddl_dt_lot_types.Rows.Add(ddl_dr_lot_type)

            For Each dr_product_type As DataRow In dt_product_types.Rows

                ' 1. products
                str_select_products = "SELECT ID, NAME, PT_TYPE FROM VIEW_PRODUCTS WHERE PT_ID = " & dr_product_type.Item("SMC_PT_ID").ToString & " AND IS_OBSOLETE LIKE 'NO' ORDER BY NAME"
                dt_products = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_select_products, False)

                For Each dr_product As DataRow In dt_products.Rows
                    ddl_dr_product = ddl_dt_products.NewRow
                    ddl_dr_product("ID") = dr_product.Item("ID").ToString
                    ddl_dr_product("VALUE") = dr_product.Item("NAME").ToString & " - " & dr_product.Item("PT_TYPE").ToString
                    ddl_dt_products.Rows.Add(ddl_dr_product)
                Next

                ' 2. lot types
                str_lot_types = "SELECT ID, TYPE, DESCRIPTION FROM LOT_TYPES WHERE SMC_PT_ID = " & dr_product_type.Item("SMC_PT_ID").ToString
                dt_lot_types = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_lot_types, False)

                For Each dr_lot_type As DataRow In dt_lot_types.Rows
                    ddl_dr_lot_type = ddl_dt_lot_types.NewRow
                    ddl_dr_lot_type("ID") = dr_lot_type.Item("ID").ToString
                    ddl_dr_lot_type("VALUE") = dr_lot_type.Item("TYPE").ToString & " - " & dr_lot_type.Item("DESCRIPTION").ToString
                    ddl_dt_lot_types.Rows.Add(ddl_dr_lot_type)
                Next
            Next

            ' 3 aggiungo esplicitamente il prodotto sconosciuto
            str_select_products = "SELECT ID, NAME, PT_TYPE FROM VIEW_PRODUCTS WHERE ID = " & SSProducts.Unknown
            dt_products = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_select_products, False)

            For Each dr_product As DataRow In dt_products.Rows
                ddl_dr_product = ddl_dt_products.NewRow
                ddl_dr_product("ID") = dr_product.Item("ID").ToString
                ddl_dr_product("VALUE") = dr_product.Item("NAME").ToString & " - " & dr_product.Item("PT_TYPE").ToString
                ddl_dt_products.Rows.Add(ddl_dr_product)
            Next

            ddl_products.DataSource = ddl_dt_products
            ddl_products.DataTextField = "VALUE"
            ddl_products.DataValueField = "ID"

            ddl_products.DataBind()

            ddl_lot_types.DataSource = ddl_dt_lot_types
            ddl_lot_types.DataTextField = "VALUE"
            ddl_lot_types.DataValueField = "ID"

            ddl_lot_types.DataBind()

            ' enable fields
            txt = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "AMOUNT"), myTextBox)

            If txt IsNot Nothing Then
                txt.Text = String.Empty
                txt.Enabled = True
            End If
        Else
            ' draw bins ddl
            str_select_cells = "SELECT DISTINCT(CEL_ID), DESCRIPTION, CURRENT_AMOUNT, PRO_NAME FROM VIEW_CELLS_TO_SMC_PT_ID"
            dt_cells = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_select_cells, False)

            ddl_cells = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "CEL_ID"), myDropDownList)
            ddl_cells.Items.Clear()
            ddl_cells.Enabled = True

            Dim ddl_dt As New Data.DataTable
            ddl_dt.Columns.Add("ID")
            ddl_dt.Columns.Add("VALUE")

            ddl_dr_product = ddl_dt.NewRow
            ddl_dr_product("ID") = String.Empty
            ddl_dr_product("VALUE") = String.Empty
            ddl_dt.Rows.Add(ddl_dr_product)

            For Each dr_cell As DataRow In dt_cells.Rows
                ddl_dr_product = ddl_dt.NewRow
                ddl_dr_product("ID") = dr_cell.Item("CEL_ID").ToString
                ddl_dr_product("VALUE") = dr_cell.Item("DESCRIPTION").ToString & " - " & dr_cell.Item("PRO_NAME").ToString & " - " &
                                            Math.Round(Double.Parse(dr_cell.Item("CURRENT_AMOUNT").ToString) * UsersGUI.tools.GetConversionFactorToASP("kg", "t"), 2) & " t"
                ddl_dt.Rows.Add(ddl_dr_product)

            Next

            ddl_cells.DataSource = ddl_dt
            ddl_cells.DataTextField = "VALUE"
            ddl_cells.DataValueField = "ID"

            ddl_cells.DataBind()

            ' disable unnecessary ddl
            ddl_products = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PRO_ID"), myDropDownList)
            If ddl_products IsNot Nothing Then
                ddl_products.Items.Clear()
                ddl_products.Enabled = False
            End If

            ddl_lot_types = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LT_ID"), myDropDownList)
            If ddl_lot_types IsNot Nothing Then
                ddl_lot_types.Items.Clear()
                ddl_lot_types.Enabled = False
            End If

            txt = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "AMOUNT"), myTextBox)
            If txt IsNot Nothing Then
                txt.Text = String.Empty
                txt.Enabled = False
            End If
        End If

    End Sub

    Public Shared Sub EventTaskSchedulerHour(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim ddl As myDropDownList = Nothing

        ddl = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "HOUR"), myDropDownList)

        If ddl IsNot Nothing AndAlso ddl.Items.Count = 0 Then

            Dim dt As New Data.DataTable
            dt.Columns.Add("ID")
            dt.Columns.Add("VALUE")

            For i As Integer = 0 To 23
                Dim dr As Data.DataRow

                dr = dt.NewRow
                dr("ID") = i
                dr("VALUE") = i & ":00"
                dt.Rows.Add(dr)
            Next

            ddl.DataSource = dt
            ddl.DataTextField = "VALUE"
            ddl.DataValueField = "ID"

            ddl.DataBind()

            ddl.AutoPostBack = False
        End If

    End Sub

    Public Shared Sub EventThreadsLogLevel(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim ddl As myDropDownList = Nothing

        ddl = CType(UsersGUI.tools.FindControlRecursive(sender.Parent.page, "LOG_LEVEL"), myDropDownList)

        If ddl IsNot Nothing AndAlso ddl.Items.Count = 0 Then

            Dim dt As New Data.DataTable
            dt.Columns.Add("ID")
            dt.Columns.Add("VALUE")

            Dim dr As Data.DataRow

            dr = dt.NewRow
            dr("ID") = 7
            dr("VALUE") = "7 - Trace"
            dt.Rows.Add(dr)

            dr = dt.NewRow
            dr("ID") = 6
            dr("VALUE") = "6 - Debug"
            dt.Rows.Add(dr)

            dr = dt.NewRow
            dr("ID") = 5
            dr("VALUE") = "5 - Information"
            dt.Rows.Add(dr)

            dr = dt.NewRow
            dr("ID") = 4
            dr("VALUE") = "4 - Warning"
            dt.Rows.Add(dr)

            dr = dt.NewRow
            dr("ID") = 3
            dr("VALUE") = "3 - Error"
            dt.Rows.Add(dr)

            dr = dt.NewRow
            dr("ID") = 2
            dr("VALUE") = "2 - Exception"
            dt.Rows.Add(dr)

            dr = dt.NewRow
            dr("ID") = 1
            dr("VALUE") = "1 - Fatal"
            dt.Rows.Add(dr)

            ddl.DataSource = dt
            ddl.DataTextField = "VALUE"
            ddl.DataValueField = "ID"

            ddl.DataBind()

            ddl.AutoPostBack = False
        End If

    End Sub

    Private Shared Sub AutomaticProductSelection(ByVal sender As Object, ByVal e As System.EventArgs, ByVal sources_number As Integer)
        Dim m_control As System.Web.UI.Control = Nothing
        Dim pro_id As Integer = -1
        Dim pro_id_in_bin As Integer = -1
        Dim sSelect As String
        Dim dt As DataTable
        Dim b_continue As Boolean = True
        Dim b_first_loop As Boolean = True
        Dim ddl As myDropDownList
        Dim perc As Double

        Dim sptc As New List(Of myCompositeObjects.SourcePerc)

        ddl = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PRO_ID")

        If ddl.SelectedIndex <> 0 Then
            ' ho già fatto la selezione del prodotto, non voglio interferire con l'operatore
            Return
        Else
            ' selezione automatica
            For j = 1 To sources_number

                m_control = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_PERC_" & j)

                If m_control IsNot Nothing AndAlso CType(m_control, myTextBox).Text <> String.Empty Then
                    perc = UsersGUI.tools.WebInputDoubleParse(CType(m_control, myTextBox).Text, 1)
                Else
                    perc = 0.0
                End If

                If perc > 0.0 Then
                    ddl = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_BIN_" & j)

                    Dim sp As New myCompositeObjects.SourcePerc(CType(ddl.SelectedValue, Integer), perc)
                    sptc.Add(sp)
                End If
            Next

            If sptc.Count = 0 Then
                ' non ho nessuna cella con perc > 0 -> resetto la selezione del prodotto
                b_continue = False
            End If

            For Each sp As myCompositeObjects.SourcePerc In sptc
                If b_continue Then
                    If sp.Perc > 0.0 Then
                        sSelect = "SELECT PRO_ID FROM CELLS WHERE ID = '" & sp.Bin & "'"
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                        For Each dr As DataRow In dt.Rows
                            If dr.Item(0) IsNot DBNull.Value Then
                                pro_id_in_bin = dr.Item(0)
                            Else
                                ' se non ho nessun prodotto in cella mi fermo
                                b_continue = False
                            End If
                        Next

                        If b_first_loop Then
                            pro_id = pro_id_in_bin
                            b_first_loop = False
                        ElseIf pro_id_in_bin <> pro_id Then
                            b_continue = False
                        End If
                    End If
                End If
            Next

            ddl = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "PRO_ID")

            If b_continue = True Then
                ' ho un unico prodotto (conosciuto) nelle celle di origine selezionate
                Dim i As Integer = 0

                For Each el As ListItem In ddl.Items
                    If el.Value <> String.Empty AndAlso el.Value = pro_id.ToString Then
                        ddl.SelectedIndex = i
                    End If

                    i = i + 1
                Next
            Else
                ddl.SelectedIndex = 0
            End If
        End If
    End Sub

    Public Shared Sub EventLoadMode_Standard(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim rb_linear As myRadioButton = Nothing
        Dim rb_circular As myRadioButton = Nothing
        Dim rb_continuous As myRadioButton = Nothing
        Dim txt_min As myTextBox = Nothing
        Dim txt_sec As myTextBox = Nothing

        If TypeOf sender Is myRadioButton Then
            rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_1")
            rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_2")
            rb_continuous = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_3")

            txt_min = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_CONTINUOUS_MIN")
            txt_sec = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "LOAD_MODE_CONTINUOUS_SEC")
        Else
            Return
        End If

        If Not rb_linear.Checked And Not rb_circular.Checked And Not rb_continuous.Checked Then
            rb_linear.Checked = True
        End If

        If rb_continuous.Checked Then
            ShowTextBox(sender.parent.page, "LOAD_MODE_CONTINUOUS_MIN", "min")
            ShowTextBox(sender.parent.page, "LOAD_MODE_CONTINUOUS_SEC", "sec")
        Else
            HideTextBox(sender.parent.page, "LOAD_MODE_CONTINUOUS_MIN", "min")
            HideTextBox(sender.parent.page, "LOAD_MODE_CONTINUOUS_SEC", "sec")
        End If
    End Sub

    Public Shared Sub EventSourceMode_Standard(ByVal sender As Object, ByVal e As System.EventArgs, index As Integer)
        'Il continuous nelle celle sorgenti non viene implementato
        Dim rb_linear As myRadioButton = Nothing
        Dim rb_circular As myRadioButton = Nothing

        If TypeOf sender Is myRadioButton Then
            rb_linear = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & index & "_1")
            rb_circular = UsersGUI.tools.FindControlRecursive(sender.Parent.Page, "SOURCE_MODE_" & index & "_2")
        Else
            Return
        End If

        If (rb_linear Is Nothing OrElse rb_circular Is Nothing) Then
            Return
        End If
        If Not rb_linear.Checked And Not rb_circular.Checked Then
            rb_linear.Checked = True
        End If
    End Sub

End Class