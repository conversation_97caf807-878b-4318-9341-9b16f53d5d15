﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class Supplier
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_Name As String = String.Empty
    Private m_Address As String = String.Empty
    Private m_PostCode As String = String.Empty
    Private m_TelephoneNumber As String = String.Empty
    Private m_FaxNumber As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("SUPPLIERS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As Supplier
        Me.IdSupplier = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "Name"
                            Me.Name = CType(m_Control, myTextBox).Text
                        Case "Address"
                            Me.Address = CType(m_Control, myTextBox).Text
                        Case "FaxNumber"
                            Me.FaxNumber = CType(m_Control, myTextBox).Text
                        Case "PostCode"
                            Me.PostCode = CType(m_Control, myTextBox).Text
                        Case "TelephoneNumber"
                            Me.TelephoneNumber = CType(m_Control, myTextBox).Text
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdSupplier() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property Name() As String
        Get
            Return Me.m_Name
        End Get
        Set(ByVal value As String)
            Me.m_Name = value.Trim
        End Set
    End Property

    Public Property Address() As String
        Get
            Return Me.m_Address
        End Get
        Set(ByVal value As String)
            Me.m_Address = value.Trim
        End Set
    End Property

    Public Property PostCode() As String
        Get
            Return Me.m_PostCode
        End Get
        Set(ByVal value As String)
            Me.m_PostCode = value.Trim
        End Set
    End Property

    Public Property TelephoneNumber() As String
        Get
            Return Me.m_TelephoneNumber
        End Get
        Set(ByVal value As String)
            Me.m_TelephoneNumber = value.Trim
        End Set
    End Property

    Public Property FaxNumber() As String
        Get
            Return Me.m_FaxNumber
        End Get
        Set(ByVal value As String)
            Me.m_FaxNumber = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdSupplier & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdSupplier))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio fornitore."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio fornitore sulla select di recupero dati ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdSupplier
            If Me.IdSupplier = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("NAME") = Me.TrimStringToMaxLen(Me.Name, "NAME")

            If Me.PostCode <> String.Empty Then
                drNodo("POST_CODE") = Me.TrimStringToMaxLen(Me.PostCode, "POST_CODE")
            Else
                drNodo("POST_CODE") = "-"
            End If

            If Me.Address <> String.Empty Then
                drNodo("ADDRESS") = Me.TrimStringToMaxLen(Me.Address, "ADDRESS")
            Else
                drNodo("ADDRESS") = "-"
            End If

            If Me.TelephoneNumber <> String.Empty Then
                drNodo("TELEPHONE_NUMBER") = Me.TrimStringToMaxLen(Me.TelephoneNumber, "TELEPHONE_NUMBER")
            Else
                drNodo("TELEPHONE_NUMBER") = "-"
            End If

            If Me.FaxNumber <> String.Empty Then
                drNodo("FAX_NUMBER") = Me.TrimStringToMaxLen(Me.FaxNumber, "FAX_NUMBER")
            Else
                drNodo("FAX_NUMBER") = "-"
            End If

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim m_error As String = "Errore durante aggiornamento fornitore. Controllare i dati immessi e riprovare."
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, m_error)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class