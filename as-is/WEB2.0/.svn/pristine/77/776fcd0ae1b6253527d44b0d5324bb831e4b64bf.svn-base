﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class RecipeLog
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdRecipe As Long = m_InvalidId
    Private m_LastUpdate As DateTime = m_InvalidDateTime
    Private m_Operator As String = String.Empty
    Private m_Version As Long = m_InvalidId

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New()
        MyBase.New("RECIPE_LOGS")
    End Sub

    Public Property Id() As Long
        Get
            Return m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdRecipe() As Long
        Get
            Return m_IdRecipe
        End Get
        Set(ByVal value As Long)
            Me.m_IdRecipe = value
        End Set
    End Property

    Public Property LastUpdate() As DateTime
        Get
            Return m_LastUpdate
        End Get
        Set(ByVal value As DateTime)
            Me.m_LastUpdate = value
        End Set
    End Property

    Public Property UserOperator() As String
        Get
            Return m_Operator
        End Get
        Set(ByVal value As String)
            Me.m_Operator = value.Trim
        End Set
    End Property

    Public Property Version() As Long
        Get
            Return m_Version
        End Get
        Set(ByVal value As Long)
            Me.m_Version = value
        End Set
    End Property

    Private Sub NextVersion()
        Dim sSelect As String
        Dim dt As DataTable

        sSelect = "SELECT MAX(VERSION) FROM " & Me.TableName & " WHERE REC_ID = '" & m_IdRecipe & "'"
        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

        For Each dr As DataRow In dt.Rows
            Try
                m_Version = Long.Parse(dr.Item(0).ToString) + 1
            Catch ex As Exception
                m_Version = 1
            End Try
        Next
    End Sub

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.Id & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.Id))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio recipe log"
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio recipe log sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.Id

            If Me.Id = m_InvalidId Then
                Me.Id = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.Id
            End If

            drNodo("REC_ID") = Me.IdRecipe
            drNodo("OPERATOR") = Me.TrimStringToMaxLen(Me.UserOperator, "OPERATOR")
            drNodo("LAST_UPDATE") = Now.ToString(m_ODBCSqlDateTimeFormat)

            'Chiamo la funzione per il calcolo della versione
            Me.NextVersion()
            drNodo("VERSION") = Me.Version

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento recipe log: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class