﻿Option Strict On

Public Module wsCostanti
    Public Const ws_MaxLifeLockLog As Integer = 10  'gg
    Public Const ws_DefaultMachineLockTime As Integer = 60  '1 minuto <- secondi
    Public Const ws_ConfigXml As String = "app\www\Config\config.xml"
    Public Const ws_ConfigXmlLanguages As String = "app\www\Config\languages.xml"
    Public Const ws_dec_plus As String = "pass"
    Public Const err_permiss_txt As String = "errPerm"

    Public Const ws_TimeToControl As Integer = 1000 * 60 * 60 * 24 '1gg <- millisecondi
    Public Const ws_Check4CloseTimer As Integer = 3000  '3secondi
    Public Const ws_MAX_GRAPH_POINT As Integer = 1000

    Public Const ws_StringOff As String = "OFF"
    Public Const ws_StringOn As String = "ON"
    Public Const ws_StringYes As String = "YES"
    Public Const ws_StringNo As String = "NO"
    Public Const ws_ParNull As String = "PARNULL"
    Public Const ws_StringStart As String = "Start"
    Public Const ws_StringStop As String = "Stop"
    Public Const ws_StringRelease As String = "Release"
    Public Const ws_txtLock As String = "lock"
    Public Const ws_txtUnlock As String = "unlock"
    Public Const ws_modScreen_view As String = "view"
    Public Const ws_modScreen_edit As String = "edit"
    Public Const ws_modScreen_update As String = "update"
    Public Const ws_modScreen_insert As String = "insert"
    Public Const ws_modScreen_remove As String = "remove"

    Public Const ws_StringFalse As String = "false"
    Public Const ws_StringTrue As String = "true"
    Public Const ws_FormatDateTimeToSqlServer As String = "yyyy/MM/dd HH:mm:ss:f"
    Public Const ws_FormatDateTimeFromCvs As String = "dd/MM/yyyy HH:mm:ss:f"
    Public Const ws_FormatDateTimeToJSClient As String = "dd/MM/yyyy HH:mm:ss"

    'Public Const ws_unlockType_user As String = "user"
    'Public Const ws_unlockType_admin As String = "admn"
    'Public Const ws_unlockType_auto As String = "auto"
    Public Const ws_txtPing As String = "PING"

    Public Const ws_txtPong As String = "PONG"

    Public Const ws_networkStream_error As String = "Unable to write data to the transport connection: An existing connection was forcibly closed by the remote host."

    Public Const AT_TIME_PLC_YY As Integer = 85
    Public Const AT_TIME_PLC_MM As Integer = 84
    Public Const AT_TIME_PLC_DD As Integer = 83
    Public Const AT_TIME_PLC_HH As Integer = 82
    Public Const AT_TIME_PLC_MI As Integer = 81
    Public Const AT_TIME_PLC_SS As Integer = 80
    Public Const num_section As Integer = 6
    Public Const num_floor_on_section As Integer = 10

    '***************************************************************************************************************************************************
    Public Const ws_InvalidId As Long = -1

    Public Const ws_TimeToPingPongResponse As Integer = 3000 '3 secondo <- millisecondi
    Public Const ws_TimeToUniqueResponse As Integer = 30 'millisecondi

    Public Const ws_term As String = "@term@"
    Public Const ws_timeformat As String = "HH:mm:ss"

    Public Const ws_default_id_info As Integer = 0
    Public Const ws_default_id_error As Integer = -1

    Public Const type_request_periodic As String = "periodicRqstSck"
    Public Const type_request_unique As String = "uniqueRqstSck"
    Public Const type_request_stop As String = "stopRqstSck"

    Public Const TAGID_HORN_TEST As Integer = 24001
    Public Const TAGID_HORN_RESETALARM As Integer = 24002

    Public Const SYSTEM_USERNAME As String = "system"
    Public Const SYSTEM_PASSWORD As String = "nl6oUpr"

    Public RollermillsAlarmsPrimary As String() = New String() {
        "AL_ROLLS_ENGAGEMENT",
        "AL_INVERTER",
        "AL_FEED_ROLL",
        "AL_SPARE",
        "AL_GRINDING_ROLLS",
        "AL_SPARE",
        "AL_SPARE",
        "AL_SPARE"
    }

    Public RollermillsAlarmsSecondary As String() = New String() {
        "WR_HOPPER_OVERLOAD",
        "WR_MOTOR_OVERLOAD",
        "WR_MOTOR_OVERLOAD_LOWER",
        "WR_SPARE",
        "WR_SPARE",
        "WR_SPARE",
        "WR_SPARE",
        "WR_INVERTER_COMM"
    }

End Module