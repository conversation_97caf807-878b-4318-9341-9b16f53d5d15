﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class MaintProcReport
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdEquipment As Long = m_InvalidId
    Private m_IdMaintProc As Long = m_InvalidId
    Private m_Username As String = String.Empty
    Private m_EventNumberCounterResetFla As String = String.Empty
    Private m_ExecMode As String = String.Empty
    Private m_ProgrammedFlag As String = String.Empty
    Private m_InterventionReport As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("MAINT_PROC_REPORT")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As MaintProcReport
        Me.IdMaintProcReport = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdEquipment"
                            Try
                                Me.IdEquipment = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdEquipment = m_InvalidId
                            End Try
                        Case "IdMaintProc"
                            Try
                                Me.IdMaintProc = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch ex As Exception
                                Me.IdMaintProc = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName

                        Case "InterventionReport"
                            Me.InterventionReport = CType(m_Control, myTextBox).Text
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdMaintProcReport() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdEquipment() As Long
        Get
            Return Me.m_IdEquipment
        End Get
        Set(ByVal value As Long)
            Me.m_IdEquipment = value
        End Set
    End Property

    Public Property IdMaintProc() As Long
        Get
            Return Me.m_IdMaintProc
        End Get
        Set(ByVal value As Long)
            Me.m_IdMaintProc = value
        End Set
    End Property

    Public Property InterventionReport() As String
        Get
            Return Me.m_InterventionReport
        End Get
        Set(ByVal value As String)
            Me.m_InterventionReport = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdMaintProcReport & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdMaintProcReport))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio maint proc report."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio maint proc report sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdMaintProcReport

            If Me.IdMaintProcReport = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("USERNAME") = Me.TrimStringToMaxLen(System.Web.HttpContext.Current.Session("username").ToString(), "USERNAME")
            drNodo("PROGRAMMED_FLAG") = m_StringNo
            drNodo("CLOSING_DATE") = DateTime.Now
            drNodo("EQU_ID") = Me.IdEquipment
            drNodo("MP_ID") = Me.IdMaintProc
            drNodo("EXEC_MODE") = Me.TrimStringToMaxLen(m_scr.Parent.GetEntryByKeyName("MANUAL").GetValue(), "EXEC_MODE")
            drNodo("INTERVENTION_REPORT") = Me.TrimStringToMaxLen(Me.InterventionReport, "INTERVENTION_REPORT")

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento maint proc report: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class