﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class Parcel
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_CycleType As String = String.Empty
    Private m_DestinationBin As Long = m_InvalidId
    Private m_ParcelDate As DateTime = m_InvalidDateTime
    Private m_Amount As Double = m_InvalidDblValue
    Private m_IdSupplier As Long = m_InvalidId
    Private m_IdCycle As Long = m_InvalidId
    Private m_IdCarrier As Long = m_InvalidId
    Private m_Description As String = String.Empty
    Private m_TransportDocument As String = String.Empty
    Private m_Counter As Long = m_InvalidId

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("PARCELS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As Parcel
        Me.IdParcel = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "DestinationBin"
                            Try
                                Me.DestinationBin = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.DestinationBin = m_InvalidId
                            End Try
                        Case "IdSupplier"
                            Try
                                Me.IdSupplier = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdSupplier = m_InvalidId
                            End Try
                        Case "IdCycle"
                            Try
                                Me.IdCycle = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdCycle = m_InvalidId
                            End Try
                        Case "IdCarrier"
                            Try
                                Me.IdCarrier = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdCarrier = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "CycleType"
                            Me.CycleType = CType(m_Control, myTextBox).Text
                        Case "Description"
                            Me.Description = CType(m_Control, myTextBox).Text
                        Case "TransportDocument"
                            Me.TransportDocument = CType(m_Control, myTextBox).Text
                        Case "Counter"
                            Try
                                Me.Counter = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch ex As Exception
                                Me.Counter = m_InvalidId
                            End Try
                        Case "Amount"
                            Me.Amount = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                    End Select
                ElseIf m_Control Is Nothing Then
                    Select Case s.PropertyName
                        Case "ParcelDate"
                            Try
                                Me.ParcelDate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.ParcelDate = m_InvalidDateTime
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdParcel() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property DestinationBin() As Long
        Get
            Return Me.m_DestinationBin
        End Get
        Set(ByVal value As Long)
            Me.m_DestinationBin = value
        End Set
    End Property

    Public Property ParcelDate() As DateTime
        Get
            Return Me.m_ParcelDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_ParcelDate = value
        End Set
    End Property

    Public Property CycleType() As String
        Get
            Return Me.m_CycleType
        End Get
        Set(ByVal value As String)
            Me.m_CycleType = value.Trim
        End Set
    End Property

    Public Property Description() As String
        Get
            Return Me.m_Description
        End Get
        Set(ByVal value As String)
            Me.m_Description = value.Trim
        End Set
    End Property

    Public Property TransportDocument() As String
        Get
            Return Me.m_TransportDocument
        End Get
        Set(ByVal value As String)
            Me.m_TransportDocument = value.Trim
        End Set
    End Property

    Public Property Amount() As Double
        Get
            Return Me.m_Amount
        End Get
        Set(ByVal value As Double)
            Me.m_Amount = value
        End Set
    End Property

    Public Property IdCycle() As Long
        Get
            Return Me.m_IdCycle
        End Get
        Set(ByVal value As Long)
            Me.m_IdCycle = value
        End Set
    End Property

    Public Property IdCarrier() As Long
        Get
            Return Me.m_IdCarrier
        End Get
        Set(ByVal value As Long)
            Me.m_IdCarrier = value
        End Set
    End Property

    Public Property IdSupplier() As Long
        Get
            Return Me.m_IdSupplier
        End Get
        Set(ByVal value As Long)
            Me.m_IdSupplier = value
        End Set
    End Property

    Public Property Counter() As Long
        Get
            Return Me.m_Counter
        End Get
        Set(ByVal value As Long)
            Me.m_Counter = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdParcel & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdParcel))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio parcel."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio parcel sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If Me.IdParcel = m_InvalidId Then
                Me.IdParcel = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.IdParcel
            End If

            'If Me.CycleType <> String.Empty Then
            '    drNodo("CYCLE_TYPE") = Me.TrimStringToMaxLen(Me.CycleType, "CYCLE_TYPE")
            'Else
            '    drNodo("CYCLE_TYPE") = String.Empty
            'End If

            'If Me.DestinationBin > m_InvalidId Then
            '    drNodo("DESTINATION_BIN") = Me.DestinationBin
            'Else
            '    drNodo("DESTINATION_BIN") = DBNull.Value
            'End If

            Try
                drNodo("AMOUNT") = Me.Amount
            Catch ex As Exception
                drNodo("AMOUNT") = m_InvalidDblValue
            End Try

            'If Me.ParcelDate <> m_InvalidDateTime Then
            '    drNodo("DATE") = Me.ParcelDate
            'Else
            '    drNodo("DATE") = Now
            'End If

            'If Me.IdCycle > m_InvalidId Then
            '    drNodo("CYCLE_ID") = Me.IdCycle
            'Else
            '    drNodo("CYCLE_ID") = 1
            'End If

            'If Me.IdSupplier > m_InvalidId Then
            '    drNodo("SUP_ID") = Me.IdSupplier
            'Else
            '    drNodo("SUP_ID") = DBNull.Value
            'End If

            'If Me.IdCarrier > m_InvalidId Then
            '    drNodo("CAR_ID") = Me.IdCarrier
            'Else
            '    drNodo("CAR_ID") = DBNull.Value
            'End If

            'If Me.TransportDocument <> String.Empty Then
            '    drNodo("TRANSPORT_DOCUMENT") = Me.TrimStringToMaxLen(Me.TransportDocument, "TRANSPORT_DOCUMENT")
            'Else
            '    drNodo("TRANSPORT_DOCUMENT") = DBNull.Value
            'End If

            'If Me.Counter > m_InvalidId Then
            '    drNodo("COUNTER") = Me.Counter
            'Else
            '    drNodo("COUNTER") = DBNull.Value
            'End If

            'If Me.Description <> String.Empty Then
            '    drNodo("DESCRIPTION") = Me.TrimStringToMaxLen(Me.Description, "DESCRIPTION")
            'Else
            '    drNodo("DESCRIPTION") = DBNull.Value
            'End If

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento parcel: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class