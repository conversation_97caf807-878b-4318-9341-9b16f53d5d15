﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class OrderParamValue
    Inherits myDbBaseClass

    Private m_IdOrderParamValue As Long = m_InvalidId
    Private m_IdProductionPlan As Long = m_InvalidId
    Private m_Value As String = String.Empty
    Private m_IdMetaCycleParam As Long = m_InvalidId

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control)
        MyBase.New("ORDER_PARAM_VALUES")
        Me.m_scr = scr
        Me.m_root = root
    End Sub

    Public Sub New()
        MyBase.New("ORDER_PARAM_VALUES")
    End Sub

    Public Property IdMetaCycleParam() As Long
        Get
            Return m_IdMetaCycleParam
        End Get
        Set(ByVal value As Long)
            Me.m_IdMetaCycleParam = value
        End Set
    End Property

    Public Property IdOrderParamValue() As Long
        Get
            Return m_IdOrderParamValue
        End Get
        Set(ByVal value As Long)
            Me.m_IdOrderParamValue = value
        End Set
    End Property

    Public Property IdProductionPlan() As Long
        Get
            Return m_IdProductionPlan
        End Get
        Set(ByVal value As Long)
            Me.m_IdProductionPlan = value
        End Set
    End Property

    Public Property Value() As String
        Get
            Return m_Value
        End Get
        Set(ByVal value As String)
            Me.m_Value = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE PPL_ID = '" & Me.IdProductionPlan & "' AND MCP_ID = '" & Me.IdMetaCycleParam & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("PPL_ID={0} AND MCP_ID = {1}", Me.IdProductionPlan, Me.IdMetaCycleParam))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio order param value."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio order param value sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If bRigaDaCreare Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("PPL_ID") = Me.IdProductionPlan
            drNodo("MCP_ID") = Me.IdMetaCycleParam

            If Me.Value <> String.Empty Then
                drNodo("PARAMETER_VALUE") = Me.TrimStringToMaxLen(Me.Value, "PARAMETER_VALUE")
            Else
                drNodo("PARAMETER_VALUE") = DBNull.Value
            End If

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento order param value:" & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class