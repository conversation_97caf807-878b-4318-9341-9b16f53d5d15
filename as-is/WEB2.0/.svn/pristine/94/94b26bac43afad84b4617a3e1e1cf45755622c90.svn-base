﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class ProductsToCells
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdCell As Long = m_InvalidId
    Private m_IdProduct As Long = m_InvalidId

    Private m_CoarseFlowrate As Double = m_DoubleZero
    Private m_MediumFlowrate As Double = m_DoubleZero
    Private m_FineFlowrate As Double = m_DoubleZero

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("PRODUCTS_TO_CELLS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As ProductsToCells
        Me.IdProductsToCells = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdProduct"
                            Try
                                Me.m_IdProduct = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.m_IdProduct = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "CoarseFlowrate"
                            Me.m_CoarseFlowrate = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                        Case "MediumFlowrate"
                            Me.m_MediumFlowrate = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                        Case "FineFlowrate"
                            Me.m_FineFlowrate = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdProductsToCells() As Long
        Get
            Return m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdCell() As Long
        Get
            Return m_IdCell
        End Get
        Set(ByVal value As Long)
            Me.m_IdCell = value
        End Set
    End Property

    Public Property IdProduct() As Long
        Get
            Return m_IdProduct
        End Get
        Set(ByVal value As Long)
            Me.m_IdProduct = value
        End Set
    End Property

    Public Property CoarseFlowrate() As Double
        Get
            Return m_CoarseFlowrate
        End Get
        Set(ByVal value As Double)
            Me.m_CoarseFlowrate = value
        End Set
    End Property

    Public Property MediumFlowrate() As Double
        Get
            Return m_MediumFlowrate
        End Get
        Set(ByVal value As Double)
            Me.m_MediumFlowrate = value
        End Set
    End Property

    Public Property FineFlowrate() As Double
        Get
            Return m_FineFlowrate
        End Get
        Set(ByVal value As Double)
            Me.m_FineFlowrate = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdProductsToCells & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdProductsToCells))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio prod_to_cell."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio prod_to_cell sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdProductsToCells
            If Me.IdProductsToCells = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            If Me.IdCell <> m_InvalidId Then
                drNodo("CEL_ID") = Me.IdCell
            End If

            If Me.IdProduct <> m_InvalidId Then
                drNodo("PRO_ID") = Me.IdProduct
            End If

            drNodo("COARSE_FLOW_RATE") = Me.CoarseFlowrate

            drNodo("MEDIUM_FLOW_RATE") = Me.MediumFlowrate

            drNodo("FINE_FLOW_RATE") = Me.FineFlowrate

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento prod_to_cell: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class