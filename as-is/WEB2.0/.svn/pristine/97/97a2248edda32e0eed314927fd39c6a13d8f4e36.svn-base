﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class Operation
    Inherits myDbBaseClass

    Private m_id As Long = m_InvalidId
    Private m_id_meta_operation As Long = m_InvalidId
    Private m_id_contract As Long = m_InvalidId
    Private m_description As String = String.Empty
    Private m_last_update As DateTime = m_InvalidDateTime
    Private m_creation_date As DateTime = m_InvalidDateTime

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New()
        MyBase.New("OPERATIONS")
    End Sub

    Public Sub New(ByVal m_IdRec As Long)
        MyBase.New("OPERATIONS")

        Dim sSelect As String = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & m_IdRec & "'"
        Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows

                Me.Description = dr.Item("DESCRIPTION").ToString
                Me.IdMetaOperation = Long.Parse(dr.Item("MTO_ID").ToString)
                Me.IdContract = Long.Parse(dr.Item("CONTRACT_ID").ToString)
                Me.Id = Long.Parse(dr.Item("ID").ToString)

                Try
                    Me.LastUpdate = Date.Parse(dr.Item("LAST_UPDATE").ToString)
                Catch ex As Exception
                    Me.LastUpdate = m_InvalidDateTime
                End Try

                Try
                    Me.CreationDate = Date.Parse(dr.Item("CREATION_DATE").ToString)
                Catch ex As Exception
                    Me.CreationDate = m_InvalidDateTime
                End Try

            Next

        End If

    End Sub

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("OPERATIONS")
        Me.m_scr = scr
        Me.m_root = root

        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As Operation
        Me.Id = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "CreationDate"
                            Try
                                Me.CreationDate = Date.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.CreationDate = m_InvalidDateTime
                            End Try
                        Case "IdMetaOperation"
                            Try
                                Me.IdMetaOperation = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.IdMetaOperation = m_InvalidId
                            End Try
                        Case "Description"
                            Me.Description = CType(m_Control, myTextBox).Text
                    End Select
                ElseIf TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdContract"
                            Try
                                Me.IdContract = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdContract = m_InvalidId
                            End Try
                    End Select
                ElseIf m_Control Is Nothing Then
                    Select Case s.PropertyName
                        Case "LastUpdate"
                            Try
                                Me.LastUpdate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.LastUpdate = m_InvalidDateTime
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property Id() As Long
        Get
            Return m_id
        End Get
        Set(ByVal value As Long)
            Me.m_id = value
        End Set
    End Property

    Public Property IdMetaOperation() As Long
        Get
            Return m_id_meta_operation
        End Get
        Set(ByVal value As Long)
            Me.m_id_meta_operation = value
        End Set
    End Property

    Public Property IdContract() As Long
        Get
            Return m_id_contract
        End Get
        Set(ByVal value As Long)
            Me.m_id_contract = value
        End Set
    End Property

    Public Property LastUpdate() As DateTime
        Get
            Return m_last_update
        End Get
        Set(ByVal value As DateTime)
            Me.m_last_update = value
        End Set
    End Property

    Public Property CreationDate() As DateTime
        Get
            Return m_creation_date
        End Get
        Set(ByVal value As DateTime)
            Me.m_creation_date = value
        End Set
    End Property

    Public Property Description() As String
        Get
            Return m_description
        End Get
        Set(ByVal value As String)
            Me.m_description = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.Id & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB

            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.Id))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio operation"
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio operation sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.Id

            If Me.Id = m_InvalidId Then
                Me.Id = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.Id
            End If

            If Me.IdMetaOperation <> m_InvalidId Then
                drNodo("MTO_ID") = Me.IdMetaOperation
            End If

            If Me.IdContract <> m_InvalidId Then
                drNodo("CONTRACT_ID") = Me.IdContract
            Else
                drNodo("CONTRACT_ID") = DBNull.Value
            End If

            drNodo("DESCRIPTION") = Me.TrimStringToMaxLen(Me.Description, "DESCRIPTION")

            If Me.CreationDate = m_InvalidDateTime Then
                Me.CreationDate = Now

                drNodo("CREATION_DATE") = Me.CreationDate.ToString(m_FormatDateTimeToSqlServer)
            End If

            Me.LastUpdate = Now
            drNodo("LAST_UPDATE") = Me.LastUpdate.ToString(m_FormatDateTimeToSqlServer)

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento operation: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

    Public Property Root() As Control
        Get
            Return m_root
        End Get
        Set(ByVal value As Control)
            Me.m_root = value
        End Set
    End Property

End Class