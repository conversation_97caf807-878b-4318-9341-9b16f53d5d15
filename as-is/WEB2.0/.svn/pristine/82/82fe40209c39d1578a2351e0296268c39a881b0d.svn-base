﻿Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports System.Globalization

Public Class tools

    ''' <summary>
    ''' Return the control found
    ''' </summary>
    ''' <param name="Root"></param>
    ''' <param name="Id"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function FindControlRecursive(ByVal Root As Control, ByVal Id As String) As Control
        If Root.ID = Id Then
            Return Root
        End If

        For Each Ctl As Control In Root.Controls
            Dim FoundCtl As Control = FindControlRecursive(Ctl, Id)
            If FoundCtl IsNot Nothing Then
                Return FoundCtl
            End If
        Next

        Return Nothing
    End Function

    ''' <summary>
    ''' La funzione esegue una conversione di tipo dai parametri passati in argomento UnitDb a UnitAsp.
    ''' Accetta stringhe di unità secondo norme SI (ma non si usa h-1 ma /h) e restituisce un valore
    ''' double tale che unitaASP * output = unitaDB
    ''' </summary>
    ''' <param name="UnitDb">Unità di misura del parametro salvato nel DataBase</param>
    ''' <param name="UnitAsp">Unità di misura del parametro da visualizzare nella pagina web</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetConversionFactorToDB(ByVal UnitDb As String, ByVal UnitAsp As String) As Double
        ' La conversione è di tipo (unitDB*factor2)=(unitASP*factor1)

        Dim factor1 As Double = 0.0
        Dim factor2 As Double = 0.0
        Dim origine1 As Double = 0.0
        Dim origine2 As Double = 0.0

        ' Unità di riferenza : più o meno le unità SI
        '               massa(kg), distanza(m), tempo(s), intensità (A), flusso volumico(L/s), volume (L)
        '               peso volumico (kg/dm3), flusso massico (kg/s), temperatura (°K)
        '				bushels (bu),bushels orari (bu/h), Galloni americani (gal),Pounds (lbs), Hundredweights (cwt), peso volumico (lbs/cu in)
        '				pound per secondo (lbs/s), Hundredweights per ora (cwt/h)

        Select Case LCase(Trim(UnitDb))
            ' Temperature (°K)
            Case "°k"
                factor1 = CDbl(1)
                origine1 = 0
            Case "°c"
                factor1 = CDbl(1)
                origine1 = -273
                ' Time (s)
            Case "s"
                factor1 = CDbl(1)
                origine1 = 0
            Case "mn"
                factor1 = CDbl(60)
                origine1 = 0
            Case "h"
                factor1 = CDbl(3600)
                origine1 = 0
                ' Corrente (A)
            Case "a"
                factor1 = CDbl(1)
                origine1 = 0
            Case "ma"
                factor1 = CDbl(0.001)
                origine1 = 0
                ' Capacità (l)
            Case "l"
                factor1 = CDbl(1)
                origine1 = 0
                ' Massa (kg)
            Case "kg"
                factor1 = CDbl(1)
                origine1 = 0
            Case "g"
                factor1 = CDbl(0.001)
                origine1 = 0
            Case "t"
                factor1 = CDbl(1000)
                origine1 = 0
                ' Lunghezza (m)
            Case "m"
                factor1 = CDbl(1)
                origine1 = 0
            Case "mm"
                factor1 = CDbl(0.001)
                origine1 = 0
            Case "um"
                factor1 = CDbl(0.000001)
                origine1 = 0
                ' Portata (kg/s)
            Case "kg/s"
                factor1 = CDbl(1 / 1)
                origine1 = 0
            Case "kg/mn"
                factor1 = CDbl(1 / 60)
                origine1 = 0
            Case "kg/h"
                factor1 = CDbl(1 / 3600)
                origine1 = 0
            Case "t/h"
                factor1 = CDbl(1000 / 3600)
                origine1 = 0
            Case "l/s"
                factor1 = CDbl(1 / 1)
                origine1 = 0
            Case "l/mn"
                factor1 = CDbl(1 / 60)
                origine1 = 0
            Case "l/h"
                factor1 = CDbl(1 / 3600)
                origine1 = 0
                ' Rapporto ponderale (kg/kg)
            Case "kg/kg"
                factor1 = CDbl(1 / 1)
                origine1 = 0
            Case "kg/t"
                factor1 = CDbl(1 / 1000)
                origine1 = 0
            Case "g/t"
                factor1 = CDbl(0.001 / 1000)
                origine1 = 0
                ' Percentuale (1)
            Case "%01"
                factor1 = CDbl(1)
            Case "%"
                factor1 = CDbl(0.01)
                ' Periodi di tempo (s)
            Case "days", "day"
                factor1 = CDbl(24 * 60 * 60)
            Case "ms"
                factor1 = CDbl(0.001)
                ' Unità USA (lbs...)
            Case "lbs"
                factor1 = CDbl(1)
            Case "lbs/h"
                factor1 = CDbl(1)
                '	Case "bu"
                '		factor1 = CDbl(1)
                '	Case "bu/h"
                '		factor1 = CDbl(1)
            Case "gal"
                factor1 = CDbl(1)
            Case "cwt"
                factor1 = CDbl(100)
            Case "cwt/h"
                factor1 = CDbl(100)
            Case "lbs/cu in"
                factor1 = CDbl(1)
            Case "lbs/s"
                factor1 = CDbl(3600)
            Case Else
                'PrintError("GetConversionFactorToDB: Unità non riconosciuta : " & UnitDb)
                'If LOG_DEBUGGING Then WriteDbg("ERRORE : Unità non riconosciuta : " & UnitDb)
                factor1 = 1
        End Select

        Select Case LCase(Trim(UnitAsp))
            ' Temperature (°K)
            Case "°k"
                factor2 = CDbl(1)
                origine2 = 0
            Case "°c"
                factor2 = CDbl(1)
                origine2 = -273
                ' Time (s)
            Case "s"
                factor2 = CDbl(1)
                origine2 = 0
            Case "mn"
                factor2 = CDbl(60)
                origine2 = 0
            Case "h"
                factor2 = CDbl(3600)
                origine2 = 0
                ' Corrente (A)
            Case "a"
                factor2 = CDbl(1)
                origine2 = 0
            Case "ma"
                factor2 = CDbl(0.001)
                origine2 = 0
                ' Capacità (l)
            Case "l"
                factor2 = CDbl(1)
                origine2 = 0
                ' Massa (kg)
            Case "kg"
                factor2 = CDbl(1)
                origine2 = 0
            Case "g"
                factor2 = CDbl(0.001)
                origine2 = 0
            Case "t"
                factor2 = CDbl(1000)
                origine2 = 0
                ' Lunghezza (m)
            Case "m"
                factor2 = CDbl(1)
                origine2 = 0
            Case "mm"
                factor2 = CDbl(0.001)
                origine2 = 0
            Case "um"
                factor2 = CDbl(0.000001)
                origine2 = 0
                ' Portata (kg/s)
            Case "kg/s"
                factor2 = CDbl(1 / 1)
                origine2 = 0
            Case "kg/mn"
                factor2 = CDbl(1 / 60)
                origine2 = 0
            Case "kg/h"
                factor2 = CDbl(1 / 3600)
                origine2 = 0
            Case "t/h"
                factor2 = CDbl(1000 / 3600)
                origine2 = 0
            Case "l/s"
                factor2 = CDbl(1 / 1)
                origine2 = 0
            Case "l/mn"
                factor2 = CDbl(1 / 60)
                origine2 = 0
            Case "l/h"
                factor2 = CDbl(1 / 3600)
                origine2 = 0
                ' Rapporto ponderale (kg/kg)
            Case "kg/kg"
                factor2 = CDbl(1 / 1)
                origine2 = 0
            Case "kg/t"
                factor2 = CDbl(1 / 1000)
                origine2 = 0
            Case "g/t"
                factor2 = CDbl(0.001 / 1000)
                origine2 = 0
                ' Percentuale (1)
            Case "%01"
                factor2 = CDbl(1)
            Case "%"
                factor2 = CDbl(0.01)
                ' Periodi di tempo (s)
            Case "days", "day"
                factor2 = CDbl(24 * 60 * 60)
            Case "ms"
                factor2 = CDbl(0.001)
                ' Unità USA (lbs...)
            Case "lbs"
                factor2 = CDbl(1)
            Case "lbs/h"
                factor2 = CDbl(1)
                '	Case "bu"
                '		factor2 = CDbl(1)
                '	Case "bu/h"
                '		factor2 = CDbl(1)
            Case "gal"
                factor2 = CDbl(1)
            Case "cwt"
                factor2 = CDbl(100)
            Case "cwt/h"
                factor2 = CDbl(100)
            Case "lbs/cu in"
                factor2 = CDbl(1)
            Case "lbs/s"
                factor2 = CDbl(3600)
            Case Else
                If Not m_Debugging Then
                    'PrintError("Unità non riconosciuta" & UnitAsp)
                Else
                    'WriteDbg("Unità non riconosciuta : " & UnitAsp)
                    'PrintError("Unità non riconosciuta")
                End If
                factor2 = 1
        End Select

        Return CDbl(factor2 / factor1) ' ASP factor / DB factor
    End Function

    ' Funzione duale alla GetConversionFactorToDB, da usare quando si vuole mostrare un valore archiviato nel DB
    Public Shared Function GetConversionFactorToASP(ByVal UnitDb As String, ByVal UnitAsp As String) As Double
        Return 1 / GetConversionFactorToDB(UnitDb, UnitAsp) ' 1 / (ASP factor / DB factor) === DB factor / ASP factor
    End Function

    ''' <summary>
    ''' Return the System.Web.UI.WebControls.HorizontalAlign object
    ''' Return Center as defualt value
    ''' </summary>
    ''' <param name="s_HAlign">String (left,right,center and justify)</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetHorizontalAlign(ByVal s_HAlign As String) As System.Web.UI.WebControls.HorizontalAlign
        Dim HAlign As System.Web.UI.WebControls.HorizontalAlign = HorizontalAlign.NotSet

        Select Case s_HAlign.ToUpper
            Case "RIGHT"
                HAlign = HorizontalAlign.Right
            Case "LEFT"
                HAlign = HorizontalAlign.Left
            Case "JUSTIFY"
                HAlign = HorizontalAlign.Justify
            Case "CENTER"
                HAlign = HorizontalAlign.Center
            Case Else
                HAlign = HorizontalAlign.Center
        End Select

        Return HAlign
    End Function

    Public Shared Function LoadGraphIndicatorTypes(ByVal m_config As config, ByVal plant_id As Integer) As Generic.List(Of GraphIndicatorType)
        Dim m_list As New Generic.List(Of GraphIndicatorType)
        Dim sSelect As String = "SELECT DISTINCT(GRAPH_INDICATORS.ID), GRAPH_INDICATORS.DESCRIPTION FROM GRAPH_INDICATORS " &
                                    "INNER JOIN INDICATORS_PER_SCALE ON GRAPH_INDICATORS.ID = INDICATORS_PER_SCALE.IND_ID " &
                                    "INNER JOIN SCALES ON INDICATORS_PER_SCALE.SCALE_ID = SCALES.ID " &
                                    "WHERE SCALES.PLANT_ID = " & plant_id & " AND GRAPH_INDICATORS.ID <> " & EnumGraphIndicators.Loss & " " &
                                    "ORDER BY GRAPH_INDICATORS.ID"
        Dim dt As Data.DataTable

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        Dim empty_git As New GraphIndicatorType(m_config)
        empty_git.IdType = 0
        empty_git.Description = String.Empty
        m_list.Add(empty_git)

        For Each dr As Data.DataRow In dt.Rows
            Dim git As New GraphIndicatorType(m_config)
            git.IdType = Long.Parse(dr("ID").ToString)
            git.Description = m_config.GetEntryByKeyName(dr("DESCRIPTION").ToString).GetValue()
            m_list.Add(git)
        Next

        Return m_list
    End Function

    ''' <summary>
    ''' La funzione converte una stranga in un enum
    ''' </summary>
    ''' <param name="TypeEnum"></param>
    ''' <param name="Name"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function String2Enum(ByVal TypeEnum As System.Type, ByVal Name As String) As [Enum]
        Return [Enum].GetValues(TypeEnum)(Array.IndexOf([Enum].GetNames(TypeEnum), Name))
    End Function

    ''' <summary>
    ''' Ritorna il valore di Id corretto per l'insert dei dati nella tabella specificata
    ''' </summary>
    ''' <param name="table_name"></param>
    ''' <param name="colum_name"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetNextId(ByVal table_name As String, ByVal colum_name As String) As Long
        Dim ret_val As Long = 1
        Dim sSelect As String
        Dim dt As DataTable
        Dim s_strSeq As String = String.Empty

        Select Case table_name
            Case "PRODUCTION_PLAN"
                s_strSeq = "SEQ_PRODUCTION_PLAN_" & colum_name
            Case "SHIPMENTS", "LOTS", "MASTER_RECIPES", "RECIPES", "LOTS", "RECIPE_PARAM_VALUES", "OUTLOAD_ORDERS", "CONTRACTS", "OPERATIONS", "ANALYSIS"
                s_strSeq = "SEQ_" & table_name
            Case "PARCELS"
                s_strSeq = "SEQ_PARCELS"
            Case Else
                s_strSeq = String.Empty
        End Select

        If s_strSeq <> String.Empty Then
            sSelect = "UPDATE SEQUENCES SET NEXTVAL=NEXTVAL+1 WHERE SEQ_NAME='" & s_strSeq & "'"
            WebDataBaseLayer.DataBase.ExecuteSQL(sSelect)

            sSelect = "SELECT NEXTVAL FROM SEQUENCES WHERE SEQ_NAME='" & s_strSeq & "'"
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

            For Each dr As DataRow In dt.Rows
                Try
                    'Non devo aggiungere +1 l'ho già fatto con l'update precedente
                    ret_val = Long.Parse(dr.Item(0).ToString)
                Catch ex As Exception
                    ret_val = 1
                End Try
            Next
        Else
            sSelect = "SELECT MAX(" & colum_name & ") FROM " & table_name
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

            For Each dr As DataRow In dt.Rows
                Try
                    ret_val = Long.Parse(dr.Item(0).ToString) + 1
                Catch ex As Exception
                    ret_val = 1
                End Try
            Next
        End If

        Return ret_val
    End Function

    Public Shared Function PopolaSegno(ByVal cmb As DropDownList) As DropDownList
        Dim i As Integer = 0
        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("SEGNO")

        Dim dr As Data.DataRow
        dr = dt.NewRow
        dr("ID") = "="
        dr("SEGNO") = "="
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = "<>"
        dr("SEGNO") = "<>"
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = "<"
        dr("SEGNO") = "<"
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = ">"
        dr("SEGNO") = ">"
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = "<="
        dr("SEGNO") = "<="
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = ">="
        dr("SEGNO") = ">="
        dt.Rows.Add(dr)

        cmb.DataSource = dt
        cmb.DataTextField = "SEGNO"
        cmb.DataValueField = "ID"

        Return cmb
    End Function

    ''' <summary>
    ''' Sostituisce l'apice con due apici. Da usare nelle query, non nelle classi UsersGUI che usano
    ''' datarow e dataset, in quel caso ci pensa il framework
    ''' </summary>
    ''' <param name="originalString"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function SqlStr(ByVal originalString As String) As String
        Dim ret_val As String = originalString
        If originalString <> String.Empty Then
            ret_val = Replace(originalString, "'", "''")
        End If

        Return ret_val
    End Function

    ''' <summary>
    ''' Parse per consentire il parsing corretto dei valori decimali in input dall'utente
    ''' con la sostituzione del "." in ","
    ''' </summary>
    ''' <param name="s_string"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function WebInputDoubleParse(ByVal s_string As String, ByVal n_digits As Integer) As Double
        Dim temp_str As String = String.Empty
        Dim ret_val As Double

        Try
            temp_str = s_string.Replace(".", ",")
            ret_val = Double.Parse(temp_str, CultureInfo.CreateSpecificCulture("it-IT"))
            ret_val = Math.Round(ret_val, n_digits)
        Catch ex As Exception
            ret_val = 0.0
        End Try

        Return ret_val
    End Function

    Public Shared Function GetValueFromControl(ByVal m_root As Control, ByVal m_control As Control) As String
        Dim ret_val As String = String.Empty

        Dim f_value As Double = 0.0
        Dim conv_factor As Double = 0.0

        Dim m_config As UsersGUI.config
        Dim m_language As UsersGUI.Language

        If TypeOf m_control Is myDropDownList Then
            ret_val = CType(m_control, myDropDownList).SelectedValue.ToString

        ElseIf TypeOf m_control Is myTextBox Then
            Dim m_TextBox As myTextBox = CType(m_control, myTextBox)

            ret_val = m_TextBox.Text

            If ret_val <> String.Empty AndAlso Not m_TextBox.FieldObject Is Nothing AndAlso m_TextBox.FieldObject.FieldType.ToUpper = "Number".ToUpper Then

                If m_TextBox.FieldObject.UnitAsp <> String.Empty AndAlso m_TextBox.FieldObject.UnitDb <> String.Empty Then
                    conv_factor = UsersGUI.tools.GetConversionFactorToDB(m_TextBox.FieldObject.UnitDb, m_TextBox.FieldObject.UnitAsp)
                Else
                    conv_factor = 1.0
                End If

                f_value = WebInputDoubleParse(ret_val, m_TextBox.FieldObject.nDecimal) * conv_factor

                ret_val = f_value.ToString
            End If

        ElseIf TypeOf m_control Is myCalendar Then

            Dim m_TextBox As myCalendar = CType(m_control, myCalendar)

            If m_TextBox.Text.Trim() <> String.Empty Then
                Try
                    m_config = Web.HttpContext.Current.Application("config")
                    m_language = m_config.GetLanguage()

                    ret_val = ConvertDateTimeToDB(m_TextBox.Text.Trim(), m_language.FormatDateTimeCalendar)
                Catch ex As Exception
                    ret_val = m_TextBox.Text
                End Try
            Else
                ret_val = m_TextBox.Text
            End If

        ElseIf TypeOf m_control Is myCheckBox Then
            Dim fl As Field = CType(CType(m_control, myCheckBox).FieldObject, Field)
            If fl.GetFieldType = EnumFieldType.FieldCheckBoxYesNo Then
                ret_val = m_StringNo
                If CType(m_control, myCheckBox).Checked Then
                    ret_val = m_StringYes
                End If
            ElseIf fl.GetFieldType = EnumFieldType.FieldCheckBoxOnOff Then
                ret_val = m_StringOff
                If CType(m_control, myCheckBox).Checked Then
                    ret_val = m_StringOn
                End If
            ElseIf fl.GetFieldType = EnumFieldType.FieldCheckBoxOneZero Then
                ret_val = m_StringZero
                If CType(m_control, myCheckBox).Checked Then
                    ret_val = m_StringUno
                End If
            End If

        ElseIf TypeOf m_control Is myRadioButton Then
            ret_val = m_StringZero
            If CType(m_control, myRadioButton).Checked Then
                ret_val = m_StringUno
            End If
        End If

        Return ret_val
    End Function

    Public Shared Function IsControlled(ByVal cycle_id As Integer) As Boolean
        Dim sSelect As String = "SELECT IS_CONTROLLED FROM CYCLES WHERE ID = '" & cycle_id & "'"
        Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing Then
            For Each dr As DataRow In dt.Rows
                If dr.Item("IS_CONTROLLED").ToString.Trim.ToUpper = costanti.m_StringYes.ToUpper Then
                    Return True
                    Exit Function
                End If
            Next
        End If

        Return False
    End Function

    ''' <summary>
    ''' La funzione crea un oggetto di tipo field in base al type ed ai parametri del meta recipe param
    ''' </summary>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetNewObject(ByVal FieldDb_s As String, ByVal m_FieldName As String,
                                        ByVal m_FieldType As String, ByVal m_FixedRuntimeValue As String,
                                        ByVal m_UnitAsp As String, ByVal m_UnitDB As String,
                                        ByVal m_GetTypeObject As EnumFieldType, ByVal m_ReadOnly As Boolean,
                                        ByVal m_CallEvent As String, ByVal m_IsHidden As Boolean, ByVal m_InsertField As Boolean,
                                        ByVal m_IsSqlExcluded As Boolean, ByVal m_IsVisibleButNotEditable As Boolean,
                                        ByVal m_UpdateField As Boolean, ByVal m_AspFilter As String) As Field
        Dim f As New Field(String.Empty)

        f.FieldDb = FieldDb_s
        f.FieldName = m_FieldName 'Database colonna AspName
        f.FieldType = m_FieldType 'Database colonna AspType
        f.CallEvent = m_CallEvent
        f.EnableKeyEntry = False
        f.FixedRuntimeValue = m_FixedRuntimeValue 'Database colonna DefaultValue
        f.InsertField = m_InsertField
        f.IsHidden = m_IsHidden
        f.IsReadOnly = m_ReadOnly
        f.IsSqlExcluded = m_IsSqlExcluded
        f.IsVisibleButNotEditable = m_IsVisibleButNotEditable
        f.QueryField = False
        f.UnitAsp = m_UnitAsp 'Database colonna AspUnit
        f.UnitDb = m_UnitDB 'Database colonna DbUnit
        f.UpdateField = m_UpdateField
        f.VisibleField = False
        f.PropertyName = "FieldValue"
        f.nDecimal = 2

        Select Case m_GetTypeObject
            Case EnumFieldType.FieldList

            Case EnumFieldType.FieldRadioButton
                f.RadioButtonAspFilter = m_AspFilter

            Case Else
                'not handled
        End Select

        Return f
    End Function

    Public Shared Function GetUserNameFromId(ByVal IdUser As Long) As String
        Dim sSelect As String = "SELECT * FROM SYSTEM_USERS WHERE ID = '" & IdUser & "'"
        Dim dt As DataTable

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Return dr.Item("USER_NAME").ToString
            Next
        End If

        Return String.Empty
    End Function

    ' ritorna la data in formato sql per essere utilizzata nelle query
    Public Shared Function PrepareDateForSQL(ByVal m_date As Date) As String
        Dim ret_val As String = String.Empty

        ret_val = " CONVERT(DATETIME,'" & m_date.Year.ToString & "-" & m_date.Month.ToString & "-" & m_date.Day.ToString & " " &
                        m_date.Hour.ToString & ":" & m_date.Minute.ToString & ":" & m_date.Second.ToString & ":" & m_date.Millisecond.ToString & "', 120) "

        Return ret_val

    End Function

    ''' <summary>
    ''' Handles the conversion of a string date from a format to another (silent failure)
    ''' </summary>
    ''' <param name="dateTime"></param>
    ''' <param name="inFormat"></param>
    ''' <param name="outFormat"></param>
    ''' <returns></returns>
    Public Shared Function ConvertDateTimeFormat(ByVal dateTime As String, inFormat As String, outFormat As String) As String
        Dim ret_val As String

        Try
            ret_val = Date.ParseExact(dateTime, inFormat, System.Threading.Thread.CurrentThread.CurrentCulture).ToString(outFormat)
        Catch ex As Exception
            ret_val = dateTime
        End Try

        Return ret_val
    End Function

    ''' <summary>
    ''' Converts a datetime string to the ODBC format
    ''' </summary>
    ''' <param name="dateTime"></param>
    ''' <param name="inFormat"></param>
    ''' <returns></returns>
    Public Shared Function ConvertDateTimeToDB(ByVal dateTime As String, inFormat As String) As String
        Return ConvertDateTimeFormat(dateTime, inFormat, m_ODBCSqlDateTimeFormat)
    End Function

    ''' <summary>
    ''' Converts an ODBC format datetime string to the desired format
    ''' </summary>
    ''' <param name="dateTime"></param>
    ''' <param name="inFormat"></param>
    ''' <returns></returns>
    Public Shared Function ConvertDateTimeFromDB(ByVal dateTime As String, outFormat As String) As String
        Return ConvertDateTimeFormat(dateTime, m_ODBCSqlDateTimeFormat, outFormat)
    End Function

    ' controlla se nella tabella esiste un'altra riga con lo stesso valore di column_name
    ' nella new passo come id m_invalidId, nella edit l'id della riga che edito
    Public Shared Function IsValueAlreadyPresentInTable(ByVal table_name As String, ByVal column_name As String, ByVal value As String, ByVal my_id As Long, Optional ByVal optional_str_where As String = "") As Boolean
        Dim ret_val As Boolean = True

        Dim sSelect As String = "SELECT * FROM " & table_name & " WHERE " & column_name & " = '" & UsersGUI.tools.SqlStr(value) & "'"

        If optional_str_where <> String.Empty Then
            sSelect &= " AND " & optional_str_where
        End If

        If my_id <> m_InvalidId Then
            sSelect &= " AND ID <> " & my_id
        End If

        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt.Rows.Count = 0 Then
            ret_val = False
        End If

        Return ret_val
    End Function

    Public Shared Function JSONparse(ByRef structObj As Object, ByVal to_parse As String) As Boolean
        Dim to_return As Boolean = False
        Dim type_obj As System.Type = structObj.GetType
        Dim properties_list As System.Reflection.PropertyInfo() = type_obj.GetProperties
        Dim sub_str As String = String.Empty

        Dim parsing_value As String = String.Empty
        Dim prop_name As String = String.Empty
        Dim property_type As String = String.Empty
        Dim base_type As String = String.Empty

        Dim index_start_name As Integer = Nothing
        Dim index_end_name As Integer = Nothing
        Dim index_start_value As Integer = Nothing
        Dim index_end_value As Integer = Nothing
        Dim val_to_set = Nothing
        Try
            For Each prop As System.Reflection.PropertyInfo In properties_list
                prop_name = prop.Name
                property_type = prop.PropertyType.Name
                If Not IsNothing(prop.PropertyType.BaseType) Then
                    base_type = prop.PropertyType.BaseType.Name
                Else
                    base_type = Nothing
                End If

                index_start_name = to_parse.IndexOf("""" & prop_name)   ' cerco l'indice di partenza del nome, del tipo: "ACK
                If index_start_name <> m_InvalidInteger Then
                    index_end_name = to_parse.IndexOf(":", index_start_name) ' cerco l'indice di fine del nome dopo il relativo indice di partenza: :

                    Select Case property_type
                        Case "Object", "SocketQueryObj", "ExtraScreenObj"
                            index_start_value = to_parse.IndexOf("{", index_end_name)  ' indice di partenza del valore, la prima successiva {
                            If index_start_value <> m_InvalidInteger Then
                                sub_str = to_parse.Substring(index_start_value)
                                index_end_value = index_start_value + GetEndIndexOfObject(sub_str) + 1
                                'index_end_value = to_parse.IndexOf("}", index_start_value) + 1 ' indice di fine del valore, la prima successiva } +1 per prendere anche la graffa stessa
                            End If

                        Case "List`1"
                            index_start_value = to_parse.IndexOf("[", index_end_name)
                            If index_start_value <> m_InvalidInteger Then
                                index_end_value = to_parse.IndexOf("]", index_start_value) + 1 ' indice di fine del valore, la prima successiva ] +1 per prendere anche la graffa stessa
                            End If

                        Case Else
                            index_start_value = index_end_name + 1  ' indice di partenza del valore = +1 rispetto alla fine del nome
                            index_end_value = to_parse.IndexOf(",", index_start_value)  ' cerco indice di fine del valore: , o }
                            If index_end_value = m_InvalidInteger Then
                                index_end_value = to_parse.IndexOf("}", index_start_value)
                            End If
                    End Select

                    'If property_type <> "Object" AndAlso property_type <> "List`1" AndAlso base_type <> "Object" Then
                    '    index_start_value = index_end_name + 1  ' indice di partenza del valore = +1 rispetto alla fine del nome
                    '    index_end_value = to_parse.IndexOf(",", index_start_value)  ' cerco indice di fine del valore: , o }
                    '    If index_end_value = m_InvalidInteger Then
                    '    index_end_value = to_parse.IndexOf("}", index_start_value)
                    '    End If
                    'ElseIf property_type = "List`1" Then
                    '    'index_start_value = index_end_name + 1  ' indice di partenza del valore = +1 rispetto alla fine del nome
                    '    'index_end_value = to_parse.IndexOf(",", index_start_value)  ' cerco indice di fine del valore: , o }
                    '    'If index_end_value = m_InvalidInteger Then
                    '    '    index_end_value = to_parse.IndexOf("}", index_start_value)
                    '    'End If
                    '    index_start_value = to_parse.IndexOf("[", index_end_name)
                    '    If index_start_value <> m_InvalidInteger Then
                    '        index_end_value = to_parse.IndexOf("]", index_start_value) + 1 ' indice di fine del valore, la prima successiva ] +1 per prendere anche la graffa stessa
                    '    End If
                    'Else
                    '    index_start_value = to_parse.IndexOf("{", index_end_name)  ' indice di partenza del valore, la prima successiva {
                    '    If index_start_value <> m_InvalidInteger Then
                    '        sub_str = to_parse.Substring(index_start_value)
                    '        index_end_value = index_start_value + GetEndIndexOfObject(sub_str) + 1
                    '        'index_end_value = to_parse.IndexOf("}", index_start_value) + 1 ' indice di fine del valore, la prima successiva } +1 per prendere anche la graffa stessa
                    '    End If
                    'End If

                    If index_start_value <> m_InvalidInteger AndAlso index_end_value <> m_InvalidInteger Then
                        parsing_value = to_parse.Substring(index_start_value, (index_end_value - index_start_value))

                        val_to_set = ConvertStringToObject(parsing_value, property_type, prop.ReflectedType.Name)

                        prop.SetValue(structObj, val_to_set, Nothing)
                    End If
                End If
            Next
            to_return = True
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, "JSONparse", System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return to_return
    End Function

    Private Shared Function GetEndIndexOfObject(ByVal str As String) As Integer
        Dim to_return As Integer = m_InvalidInteger
        Dim counter As Integer = 0
        'percorro tutta la stringa se non vuota
        If str <> String.Empty Then
            For s = 0 To str.Length
                If String.Compare(str(s), "{") = 0 Then
                    'se trovo una { aumento il contatore
                    counter += 1
                ElseIf String.Compare(str(s), "}") = 0 Then
                    'se trovo una } diminuisco il contatore
                    counter -= 1
                End If

                'se il contatore è = 0 allora l'oggetto è chiuso e ne ritorno l'indice
                If counter = 0 Then
                    to_return = s
                    Exit For
                End If
            Next
        End If

        Return to_return
    End Function

    Public Shared Function JSONstringify(ByVal to_stringify As Object, ByRef stringifiedObj As String) As Boolean
        Dim to_return As Boolean = False
        Dim type_obj As System.Type = to_stringify.GetType
        Dim properties_list As System.Reflection.PropertyInfo() = type_obj.GetProperties

        Dim converted_val As String = String.Empty
        Dim prop_name As String = String.Empty
        Dim property_type As System.Type = Nothing

        stringifiedObj = "{"

        Try
            For Each prop As System.Reflection.PropertyInfo In properties_list
                prop_name = prop.Name
                property_type = prop.PropertyType

                converted_val = ConvertObjectToString(prop.GetValue(to_stringify, Nothing), property_type)
                stringifiedObj &= """" & prop_name & """:" & converted_val
                If prop IsNot properties_list.Last Then
                    stringifiedObj &= ","
                End If
            Next
            to_return = True
        Catch ex As myException.myException
            stringifiedObj = "{"
            Throw ex
        Catch ex As Exception
            stringifiedObj = "{"
            Dim myExc As New myException.myException(ex, "JSONparse", System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        stringifiedObj &= "}"

        Return to_return
    End Function

    Private Shared Function ConvertStringToObject(ByVal to_convert As String, ByVal type_object As String, ByVal reflecting_type As String) As Object
        Dim to_return As Object = Nothing
        Dim index_start As Integer = 0
        Dim index_end As Integer = 0
        Dim length_str As Integer = 0
        Dim sub_sub_str As String = String.Empty
        Dim sub_convert As Boolean = False
        Dim sub_to_convert As String = String.Empty

        Select Case type_object
            Case "Int64"
                Long.TryParse(to_convert, to_return)
            Case "Int32"
                Integer.TryParse(to_convert, to_return)
            Case "String"
                If to_convert <> m_JSnull Then
                    to_return = to_convert.Substring(1, to_convert.Length - 2)   'tolgo il primo e l'ultimo carattere che sono i doppi apici (") per identificare la stringa
                End If
            Case "Boolean"
                If to_convert = "true" Then
                    to_return = True
                Else
                    to_return = False
                End If

            Case "SocketQueryObj"
                to_return = New SocketQueryObj()
                tools.JSONparse(to_return, to_convert)
            Case "ExtraScreenObj"
                to_return = New ExtraScreenObj()
                tools.JSONparse(to_return, to_convert)

            Case "List`1"

                'TODO sto imponendo che esista una sola lista all'interno dell'oggetto identificato da "reflecting_type"
                Select Case reflecting_type
                    Case "SocketUsersManagementActionObj"
                        If to_convert = m_JSnull Then
                            to_return = New List(Of SocketUsersManagementValueObj)
                        Else
                            to_return = New List(Of SocketUsersManagementValueObj)()
                            Dim sub_str As String = to_convert.Substring(1, to_convert.Length - 2)
                            Dim sub_obj As SocketUsersManagementValueObj = New SocketUsersManagementValueObj()

                            Do While index_end < sub_str.Length
                                index_start = sub_str.IndexOf("{", index_start)
                                sub_sub_str = sub_str.Substring(index_start)
                                length_str = GetEndIndexOfObject(sub_sub_str) + 1
                                index_end = index_start + length_str
                                sub_obj = New SocketUsersManagementValueObj()
                                sub_convert = JSONparse(sub_obj, sub_str.Substring(index_start, length_str))
                                If sub_convert Then
                                    to_return.Add(sub_obj)
                                End If
                                index_start = index_end
                            Loop
                        End If
                    Case Else
                        If to_convert = m_JSnull Then
                            to_return = New List(Of Integer)
                        Else
                            to_return = New List(Of Integer)()
                        End If
                End Select

            Case Else
                to_return = to_convert
        End Select

        Return to_return
    End Function

    Private Shared Function ConvertObjectToString(ByVal to_convert As Object, ByVal type_object As System.Type) As String
        Dim to_return As String = String.Empty

        Select Case type_object.Name
            Case "Int64"
                If Not IsNothing(to_convert) Then
                    to_return = to_convert.ToString
                Else
                    to_return = m_JSnull
                End If
            Case "Int32"
                If Not IsNothing(to_convert) Then
                    to_return = to_convert.ToString
                Else
                    to_return = m_JSnull
                End If
            Case "String"
                If Not IsNothing(to_convert) Then
                    to_return = """" & to_convert & """"   'tolgo il primo e l'ultimo carattere che sono i doppi apici (") per identificare la stringa
                Else
                    to_return = m_JSnull
                End If
            Case "Boolean"
                If Not IsNothing(to_convert) Then
                    If to_convert = True Then
                        to_return = m_JStrue
                    Else
                        to_return = m_JSfalse
                    End If
                Else
                    to_return = m_JSnull
                End If
            Case "NumberAtmill"
                If Not IsNothing(to_convert) Then
                    Try
                        to_return = to_convert.Value.ToString
                    Catch ex As Exception
                        to_return = m_JSnull
                    End Try
                Else
                    to_return = m_JSnull
                End If
            Case "List`1"
                to_return = "["
                If Not IsNothing(to_convert) Then
                    For Each i As Object In to_convert
                        Select Case i.GetType.Name
                            Case "SocketFieldObj"
                                'Dim to_return_temp As SocketFieldObj = CType(i, SocketFieldObj)
                                'JSONstringify(i, to_return_temp)
                                to_return &= i.GetJSON()
                            Case Else
                                to_return &= i
                        End Select
                        If Not i.Equals(to_convert(to_convert.Count - 1)) Then
                            'If i <> to_convert(to_convert.Count - 1) Then
                            to_return &= ","
                        End If
                    Next
                End If
                to_return &= "]"
            Case "Screen"
                Dim to_return_temp As String = String.Empty
                JSONstringify(to_convert, to_return_temp)
                to_return &= to_return_temp

            Case "SocketQueryObj"
                JSONstringify(to_convert, to_return)
            Case "ExtraScreenObj"
                JSONstringify(to_convert, to_return)

            Case Else
                If Not IsNothing(to_convert) Then
                    If Not IsNothing(type_object.BaseType) Then
                        Select Case type_object.BaseType.Name
                            Case "Enum"
                                to_return = to_convert

                            Case Else
                                to_return = to_convert.ToString

                        End Select
                    Else
                        to_return = to_convert.ToString
                    End If
                Else
                    to_return = m_JSnull
                End If
        End Select

        Return to_return
    End Function

End Class