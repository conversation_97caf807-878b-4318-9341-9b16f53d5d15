﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class MaintProcAssignem
    Inherits myDbBaseClass

    Private mId As Long
    Private mIdMaintenanceProcedures As Long
    Private mTimeNeeded As Long
    Private mAlarmNumber As Long
    Private mAlarmCounter As Long
    Private mEventNumber As Long
    Private mOperationCounter As Long
    Private mEventCounter As Long
    Private mWorkTimeCounter As Long
    Private mWorkedTimeIntervalHour As Long
    Private mElapsedTimeIntervalDay As Long
    Private mSpecialNotes As String
    Private mIdEquipment As Long
    Private mOperationNumber As Long
    Private mLastMaintDate As DateTime = costanti.m_InvalidDateTime
    Private mDayDelay As Long
    Private mAlarmDelay As Long
    Private mEventDelay As Long
    Private mWorkTimeDelay As Long
    Private mOperationDelay As Long

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("MAINT_PROC_ASSIGNEM")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As MaintProcAssignem
        Me.IdMaintProcAssignem = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdEquipment"
                            Try
                                Me.IdEquipment = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdEquipment = m_InvalidId
                            End Try
                        Case "IdMaintenanceProcedures"
                            Try
                                Me.IdMaintenanceProcedures = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch ex As Exception
                                Me.IdMaintenanceProcedures = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "AlarmNumber"
                            Try
                                Me.AlarmNumber = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.AlarmNumber = costanti.m_LongZero
                            End Try
                        Case "OperationNumber"
                            Try
                                Me.OperationNumber = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.OperationNumber = costanti.m_LongZero
                            End Try
                        Case "EventNumber"
                            Try
                                Me.EventNumber = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.EventNumber = costanti.m_LongZero
                            End Try
                        Case "DayDelay"
                            Try
                                Me.DayDelay = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.DayDelay = costanti.m_LongZero
                            End Try
                        Case "WorkTimeDelay"
                            Try
                                Me.WorkTimeDelay = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.WorkTimeDelay = costanti.m_LongZero
                            End Try
                        Case "AlarmDelay"
                            Try
                                Me.AlarmDelay = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.AlarmDelay = costanti.m_LongZero
                            End Try
                        Case "OperationDelay"
                            Try
                                Me.OperationDelay = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.OperationDelay = costanti.m_LongZero
                            End Try
                        Case "EventDelay"
                            Try
                                Me.EventDelay = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.EventDelay = costanti.m_LongZero
                            End Try
                        Case "SpecialNotes"
                            Me.SpecialNotes = CType(m_Control, myTextBox).Text
                        Case "TimeNeeded"
                            Try
                                Me.TimeNeeded = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.TimeNeeded = costanti.m_LongZero
                            End Try
                        Case "LastMaintDate"
                            Try
                                Me.LastMaintDate = Date.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.LastMaintDate = costanti.m_InvalidDateTime
                            End Try
                        Case "ElapsedTimeIntervalDay"
                            Try
                                Me.ElapsedTimeIntervalDay = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.ElapsedTimeIntervalDay = costanti.m_LongZero
                            End Try
                        Case "WorkedTimeIntervalHour"
                            Try
                                Me.WorkedTimeIntervalHour = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.WorkedTimeIntervalHour = costanti.m_LongZero
                            End Try

                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdMaintProcAssignem() As Long
        Get
            Return Me.mId
        End Get
        Set(ByVal value As Long)
            Me.mId = value
        End Set
    End Property

    Public Property IdMaintenanceProcedures() As Long
        Get
            Return Me.mIdMaintenanceProcedures
        End Get
        Set(ByVal value As Long)
            Me.mIdMaintenanceProcedures = value
        End Set
    End Property

    Public Property TimeNeeded() As Long
        Get
            Return Me.mTimeNeeded
        End Get
        Set(ByVal value As Long)
            Me.mTimeNeeded = value
        End Set
    End Property

    Public Property AlarmNumber() As Long
        Get
            Return Me.mAlarmNumber
        End Get
        Set(ByVal value As Long)
            Me.mAlarmNumber = value
        End Set
    End Property

    Public Property EventNumber() As Long
        Get
            Return Me.mEventNumber
        End Get
        Set(ByVal value As Long)
            Me.mEventNumber = value
        End Set
    End Property

    Public Property AlarmCounter() As Long
        Get
            Return Me.mAlarmCounter
        End Get
        Set(ByVal value As Long)
            Me.mAlarmCounter = value
        End Set
    End Property

    Public Property OperationCounter() As Long
        Get
            Return Me.mOperationCounter
        End Get
        Set(ByVal value As Long)
            Me.mOperationCounter = value
        End Set
    End Property

    Public Property EventCounter() As Long
        Get
            Return Me.mEventCounter
        End Get
        Set(ByVal value As Long)
            Me.mEventCounter = value
        End Set
    End Property

    Public Property WorkTimeCounter() As Long
        Get
            Return Me.mWorkTimeCounter
        End Get
        Set(ByVal value As Long)
            Me.mWorkTimeCounter = value
        End Set
    End Property

    Public Property WorkedTimeIntervalHour() As Long
        Get
            Return Me.mWorkedTimeIntervalHour
        End Get
        Set(ByVal value As Long)
            Me.mWorkedTimeIntervalHour = value
        End Set
    End Property

    Public Property ElapsedTimeIntervalDay() As Long
        Get
            Return Me.mElapsedTimeIntervalDay
        End Get
        Set(ByVal value As Long)
            Me.mElapsedTimeIntervalDay = value
        End Set
    End Property

    Public Property SpecialNotes() As String
        Get
            Return Me.mSpecialNotes
        End Get
        Set(ByVal value As String)
            Me.mSpecialNotes = value.Trim
        End Set
    End Property

    Public Property IdEquipment() As Long
        Get
            Return Me.mIdEquipment
        End Get
        Set(ByVal value As Long)
            Me.mIdEquipment = value
        End Set
    End Property

    Public Property OperationNumber() As Long
        Get
            Return Me.mOperationNumber
        End Get
        Set(ByVal value As Long)
            Me.mOperationNumber = value
        End Set
    End Property

    Public Property LastMaintDate() As DateTime
        Get
            Return Me.mLastMaintDate
        End Get
        Set(ByVal value As DateTime)
            Me.mLastMaintDate = value
        End Set
    End Property

    Public Property DayDelay() As Long
        Get
            Return Me.mDayDelay
        End Get
        Set(ByVal value As Long)
            Me.mDayDelay = value
        End Set
    End Property

    Public Property AlarmDelay() As Long
        Get
            Return Me.mAlarmDelay
        End Get
        Set(ByVal value As Long)
            Me.mAlarmDelay = value
        End Set
    End Property

    Public Property EventDelay() As Long
        Get
            Return Me.mEventDelay
        End Get
        Set(ByVal value As Long)
            Me.mEventDelay = value
        End Set
    End Property

    Public Property WorkTimeDelay() As Long
        Get
            Return Me.mWorkTimeDelay
        End Get
        Set(ByVal value As Long)
            Me.mWorkTimeDelay = value
        End Set
    End Property

    Public Property OperationDelay() As Long
        Get
            Return Me.mOperationDelay
        End Get
        Set(ByVal value As Long)
            Me.mOperationDelay = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdMaintProcAssignem & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdMaintProcAssignem))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio assegnamenti procedure."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio assegnamenti procedure sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If drNodo("ID") Is DBNull.Value Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("TIME_NEEDED") = Me.TimeNeeded
            drNodo("ALARM_NUMBER") = Me.AlarmNumber
            drNodo("EVENT_NUMBER") = Me.EventNumber
            drNodo("WORKED_TIME_INTERVAL_HOUR") = Me.WorkedTimeIntervalHour
            drNodo("ELAPSED_TIME_INTERVAL_DAY") = Me.ElapsedTimeIntervalDay
            drNodo("SPECIAL_NOTES") = Me.TrimStringToMaxLen(Me.SpecialNotes, "SPECIAL_NOTES")
            drNodo("EQU_ID") = Me.IdEquipment
            drNodo("MP_ID") = Me.IdMaintenanceProcedures
            drNodo("EVENT_DELAY") = Me.EventDelay
            drNodo("OPERATION_NUMBER") = Me.OperationNumber
            If Me.LastMaintDate = costanti.m_InvalidDateTime OrElse Not IsDate(Me.LastMaintDate) Then
                drNodo("LAST_MAINT_DATE") = Now.ToString(m_ODBCSqlDateTimeFormat)
            Else
                drNodo("LAST_MAINT_DATE") = Me.LastMaintDate
            End If

            drNodo("DAY_DELAY") = Me.DayDelay
            drNodo("ALARM_DELAY") = Me.AlarmDelay
            drNodo("WORK_TIME_DELAY") = Me.WorkTimeDelay
            drNodo("OPERATION_DELAY") = Me.OperationDelay

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento assegnamenti procedure: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class