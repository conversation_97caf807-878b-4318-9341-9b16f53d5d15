﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class Recipe
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdUser As Long = m_InvalidId
    Private m_IdMetaRecipe As Long = m_InvalidId

    Private m_LastUpdate As DateTime = m_InvalidDateTime
    Private m_CreationDate As DateTime = m_InvalidDateTime

    Private m_Description As String = String.Empty
    Private m_IsObsolete As String = String.Empty

    Private m_stored_recipe_params As New Generic.List(Of RecipeParam)
    Private m_actual_recipe_params As New Generic.List(Of RecipeParam)

    Private m_stored_description As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New()
        MyBase.New("RECIPES")
    End Sub

    Public Sub New(ByVal m_IdRec As Long)
        MyBase.New("RECIPES")

        Dim sSelect As String = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & m_IdRec & "'"
        Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows

                Me.Description = dr.Item("DESCRIPTION").ToString
                Me.IdMetaRecipe = Long.Parse(dr.Item("MTR_ID").ToString)
                Me.IdRecipe = Long.Parse(dr.Item("ID").ToString)

                Me.IsObsolete = dr.Item("IS_OBSOLETE").ToString

                Try
                    Me.LastUpdate = Date.Parse(dr.Item("LAST_UPDATE").ToString)
                Catch ex As Exception
                    Me.LastUpdate = m_InvalidDateTime
                End Try

                Try
                    Me.CreationDate = Date.Parse(dr.Item("CREATION_DATE").ToString)
                Catch ex As Exception
                    Me.CreationDate = m_InvalidDateTime
                End Try

                Try
                    Me.IdUser = Long.Parse(dr.Item("ID_USER").ToString)
                Catch ex As Exception
                    Me.IdUser = m_InvalidId
                End Try

            Next

            GetStoredRecipeDescription()
            GetStoredRecipeParams()

        End If

    End Sub

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long, ByVal user_id As Long)
        MyBase.New("RECIPES")
        Me.m_scr = scr
        Me.m_root = root
        Me.m_IdUser = user_id

        Me.CreateObjectFromControl(Id)

        GetStoredRecipeDescription()
        GetStoredRecipeParams()
    End Sub

    Private Sub GetStoredRecipeParams()
        Dim sSelect As String = "SELECT * FROM RECIPE_PARAM_VALUES WHERE REC_ID = '" & m_Id & "'"
        Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Dim rp As New RecipeParam(m_Id, Long.Parse(dr.Item("MRP_ID").ToString))
                m_stored_recipe_params.Add(rp)
            Next
        End If
    End Sub

    Private Sub GetStoredRecipeDescription()
        Dim sSelect As String = "SELECT DESCRIPTION FROM " & Me.TableName & " WHERE ID = '" & m_Id & "'"
        Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                m_stored_description = dr(0).ToString
            Next
        Else
            m_stored_description = String.Empty
        End If
    End Sub

    Public Sub SetActualRecipeParams(ByVal rpv As RecipeParam)
        m_actual_recipe_params.Add(rpv)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As Recipe
        Me.IdRecipe = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "CreationDate"
                            Try
                                Me.CreationDate = Date.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.CreationDate = m_InvalidDateTime
                            End Try
                        Case "IdMetaRecipe"
                            Try
                                Me.IdMetaRecipe = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.IdMetaRecipe = m_InvalidId
                            End Try
                        Case "Description"
                            Me.Description = CType(m_Control, myTextBox).Text
                    End Select
                ElseIf TypeOf m_Control Is myCheckBox Then
                    Select Case s.PropertyName
                        Case "IsObsolete"
                            Try
                                Me.IsObsolete = m_StringNo
                                If CType(m_Control, myCheckBox).Checked Then
                                    Me.IsObsolete = m_StringYes
                                End If
                            Catch
                                Me.IsObsolete = m_StringNo
                            End Try
                    End Select
                ElseIf m_Control Is Nothing Then
                    Select Case s.PropertyName
                        Case "LastUpdate"
                            Try
                                Me.LastUpdate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.LastUpdate = m_InvalidDateTime
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdRecipe() As Long
        Get
            Return m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdMetaRecipe() As Long
        Get
            Return m_IdMetaRecipe
        End Get
        Set(ByVal value As Long)
            Me.m_IdMetaRecipe = value
        End Set
    End Property

    Public Property IdUser() As Long
        Get
            Return m_IdUser
        End Get
        Set(ByVal value As Long)
            Me.m_IdUser = value
        End Set
    End Property

    Public Property LastUpdate() As DateTime
        Get
            Return m_LastUpdate
        End Get
        Set(ByVal value As DateTime)
            Me.m_LastUpdate = value
        End Set
    End Property

    Public Property CreationDate() As DateTime
        Get
            Return m_CreationDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_CreationDate = value
        End Set
    End Property

    Public Property Description() As String
        Get
            Return m_Description
        End Get
        Set(ByVal value As String)
            Me.m_Description = value.Trim
        End Set
    End Property

    Public Property IsObsolete() As String
        Get
            Return m_IsObsolete
        End Get
        Set(ByVal value As String)
            Me.m_IsObsolete = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection
        Dim old_recipe_log_id As Long = m_InvalidId
        Dim recipe_log_id As Long = m_InvalidId
        Dim dt As DataTable

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdRecipe & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB

            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdRecipe))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio recipe"
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio recipe sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdRecipe

            If Me.IdRecipe = m_InvalidId Then
                Me.IdRecipe = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.IdRecipe
            End If

            If Me.IdMetaRecipe <> m_InvalidId Then
                drNodo("MTR_ID") = Me.IdMetaRecipe
            End If

            If Me.IdUser <> m_InvalidId Then
                drNodo("ID_USER") = Me.IdUser
            Else
                drNodo("ID_USER") = DBNull.Value
            End If

            drNodo("DESCRIPTION") = Me.TrimStringToMaxLen(Me.Description, "DESCRIPTION")

            If Me.CreationDate = m_InvalidDateTime Then
                Me.CreationDate = Now ' lo riutilizzerò nella SaveArchive

                drNodo("CREATION_DATE") = Me.CreationDate.ToString(m_FormatDateTimeToSqlServer)
            End If

            Me.LastUpdate = Now ' lo riutilizzerò nella SaveArchive
            drNodo("LAST_UPDATE") = Me.LastUpdate.ToString(m_FormatDateTimeToSqlServer)

            If Me.IsObsolete.ToUpper = m_StringYes Then
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringYes, "IS_OBSOLETE")
            Else
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringNo, "IS_OBSOLETE")
            End If

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()

                If Not bRigaDaCreare Then
                    sSelect = "SELECT MAX(ID) FROM RECIPE_LOGS WHERE REC_ID = '" & Me.IdRecipe & "'"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                    For Each dr As DataRow In dt.Rows
                        old_recipe_log_id = Long.Parse(dr.Item(0).ToString)
                    Next
                End If

                ' se il nome della ricetta o i parametri sono cambiati archivio la ricetta vecchia
                If IsRecipeNameChanged() Or AreRecipeParametersChanged() Then

                    ' recipe log
                    recipe_log_id = Me.CreateRecipeLog()

                    ' recipe archived
                    Me.SaveArchive(recipe_log_id)

                    ' recupero i parametri sulla modifica del nome della ricetta
                    ' per versionare i parametri  con il nuovo recipe_log_id
                    If m_actual_recipe_params.Count() = 0 Then

                        ' caso 1
                        ' ho già parametri salvati, quindi li uso
                        sSelect = "SELECT * FROM RECIPE_PARAM_VALUES_ARCHIVED WHERE RECIPE_LOG_ID = '" & old_recipe_log_id & "'"
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                        For Each dr As DataRow In dt.Rows

                            Dim rp As New RecipeParam()

                            rp.IdRecipe = Me.IdRecipe
                            rp.IdMetaRecipeParam = Long.Parse(dr.Item("MRP_ID").ToString)
                            rp.FieldValue = dr.Item("FIELD_VALUE").ToString

                            m_actual_recipe_params.Add(rp)
                        Next

                        ' caso 2
                        ' non ho mai salvato parametri, li aggiungo alla lista dai mrp
                        If m_actual_recipe_params.Count() = 0 Then

                            sSelect = "SELECT * FROM META_RECIPE_PARAMS WHERE MTR_ID = '" & Me.IdMetaRecipe & "'"
                            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                            For Each dr As DataRow In dt.Rows

                                Dim rp As New RecipeParam()

                                rp.IdRecipe = Me.IdRecipe
                                rp.IdMetaRecipeParam = Long.Parse(dr.Item("ID").ToString)
                                rp.FieldValue = String.Empty

                                m_actual_recipe_params.Add(rp)
                            Next
                        End If

                    End If

                    ' recipe param values archived
                    For Each rp As RecipeParam In m_actual_recipe_params
                        rp.SaveArchive(recipe_log_id)
                    Next

                    'recipe param values
                    For Each rp As RecipeParam In m_actual_recipe_params
                        rp.Save()
                    Next
                End If
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento recipe: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

    Private Sub SaveArchive(ByVal m_IdRecipeLog As Long)
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM RECIPES_ARCHIVED WHERE RECIPE_LOG_ID = '" & m_IdRecipeLog & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, "RECIPES_ARCHIVED")
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB

            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables("RECIPES_ARCHIVED").NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables("RECIPES_ARCHIVED").Select(String.Format("RECIPE_LOG_ID={0}", m_IdRecipeLog))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio recipe archived"
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio recipe archived sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = tools.GetNextId("RECIPES_ARCHIVED", "ID")
            drNodo("RECIPE_LOG_ID") = m_IdRecipeLog

            If Me.IdMetaRecipe <> m_InvalidId Then
                drNodo("MTR_ID") = Me.IdMetaRecipe
            End If

            drNodo("ID_USER") = DBNull.Value
            If Me.IdUser <> m_InvalidId Then
                drNodo("ID_USER") = Me.IdUser
            End If

            drNodo("DESCRIPTION") = Me.TrimStringToMaxLen(Me.Description, "RECIPES_ARCHIVED", "DESCRIPTION")

            ' riutilizzo la data di creazione salvata nell'oggetto Recipe nella Save
            If Me.CreationDate <> m_InvalidDateTime Then
                drNodo("CREATION_DATE") = Me.CreationDate.ToString(m_FormatDateTimeToSqlServer)
            Else
                ' should never happen
                'drNodo("CREATION_DATE") = Now.ToString(m_FormatDateTimeToSqlServer)
            End If

            If Me.LastUpdate <> m_InvalidDateTime Then
                drNodo("LAST_UPDATE") = Me.LastUpdate.ToString(m_FormatDateTimeToSqlServer)
            Else
                ' should never happen
                'drNodo("LAST_UPDATE") = Now.ToString(m_FormatDateTimeToSqlServer)
            End If

            If Me.IsObsolete.ToUpper = m_StringYes Then
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringYes, "RECIPES_ARCHIVED", "DESCRIPTION")
            Else
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringNo, "RECIPES_ARCHIVED", "DESCRIPTION")
            End If

            If bRigaDaCreare Then
                dsNodo.Tables("RECIPES_ARCHIVED").Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, "RECIPES_ARCHIVED")
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento recipe archived: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

    Private Function CreateRecipeLog() As Long

        Dim rl As New RecipeLog
        rl.Id = m_InvalidId
        rl.IdRecipe = Me.IdRecipe
        rl.LastUpdate = Now
        rl.UserOperator = tools.GetUserNameFromId(Me.IdUser)
        rl.Save()

        Return rl.Id
    End Function

    Public Property Root() As Control
        Get
            Return m_root
        End Get
        Set(ByVal value As Control)
            Me.m_root = value
        End Set
    End Property

    Private Function IsRecipeNameChanged() As Boolean
        Dim ret_val As Boolean = False

        ' nome ricetta
        If m_Description <> m_stored_description Then
            ret_val = True
        End If

        Return ret_val
    End Function

    Private Function AreRecipeParametersChanged() As Boolean
        Dim ret_val As Boolean = False
        Dim b_found As Boolean = True

        ' parametri
        If Not ret_val Then
            For Each rpv_act As RecipeParam In m_actual_recipe_params
                b_found = False
                For Each rvp_db As RecipeParam In m_stored_recipe_params

                    If rpv_act.IdMetaRecipeParam = rvp_db.IdMetaRecipeParam Then

                        b_found = True

                        If rpv_act.FieldValue <> rvp_db.FieldValue Then
                            ret_val = True
                            Exit For
                        End If

                    End If
                Next

                ' gestisco il caso di un "nuovo" mrp salvato su una ricetta già esistente
                If Not b_found Then
                    ret_val = True
                End If

                ' se il for interno ha trovato una differenza esco
                If ret_val = True Then
                    Exit For
                End If
            Next
        End If

        Return ret_val
    End Function

End Class