﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>
    </SchemaVersion>
    <ProjectGuid>{A68913B9-4797-4F7F-B04E-49D8E51317C7}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>UsersGUI</RootNamespace>
    <AssemblyName>UsersGUI</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup>
    <TrackFileAccess>false</TrackFileAccess>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Anomalies\Anomaly.vb" />
    <Compile Include="Archives\AllowedBatchSizePerScale.vb" />
    <Compile Include="Archives\Carrier.vb" />
    <Compile Include="Archives\BatchSizePerScale.vb" />
    <Compile Include="Archives\ProductPerScale.vb" />
    <Compile Include="Archives\Customer.vb" />
    <Compile Include="Archives\GrainOrders.vb" />
    <Compile Include="Archives\IntakeOperations.vb" />
    <Compile Include="Archives\Parcel.vb" />
    <Compile Include="Archives\Product.vb" />
    <Compile Include="Archives\ProductType.vb" />
    <Compile Include="Archives\Supplier.vb" />
    <Compile Include="Charts.vb" />
    <Compile Include="CommandInterface\CommandInterface.vb" />
    <Compile Include="CompositeObjects.vb" />
    <Compile Include="config.vb" />
    <Compile Include="costanti.vb" />
    <Compile Include="Cycles\ConfirmFlowLog.vb" />
    <Compile Include="Cycles\OrderParamValue.vb" />
    <Compile Include="Cycles\ProductionPlan.vb" />
    <Compile Include="Entry.vb" />
    <Compile Include="Enum.vb" />
    <Compile Include="Graphics\GraphIndicatorType.vb" />
    <Compile Include="Language.vb" />
    <Compile Include="Lots\FunctionLots.vb" />
    <Compile Include="Lots\Lot.vb" />
    <Compile Include="Maintenance\ExecutedMaintProc.vb" />
    <Compile Include="Maintenance\MaintProcExecReq.vb" />
    <Compile Include="Maintenance\EquipmentDocs.vb" />
    <Compile Include="Maintenance\EquipmentModels.vb" />
    <Compile Include="Maintenance\Equipments.vb" />
    <Compile Include="Maintenance\MaintenancePlanning.vb" />
    <Compile Include="Maintenance\MaintenanceProcedures.vb" />
    <Compile Include="Maintenance\MaintProcAssignem.vb" />
    <Compile Include="Maintenance\NeededSpareParts.vb" />
    <Compile Include="Maintenance\ProcedureDoc.vb" />
    <Compile Include="Maintenance\SpareParts.vb" />
    <Compile Include="Menu.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="myDbBaseClass.vb" />
    <Compile Include="myBoundField.vb" />
    <Compile Include="myCheckBox.vb" />
    <Compile Include="myCheckBoxList.vb" />
    <Compile Include="myHyperLinkField.vb" />
    <Compile Include="myImageButton.vb" />
    <Compile Include="myObject.vb" />
    <Compile Include="myRadioButton.vb" />
    <Compile Include="myRegistry.vb" />
    <Compile Include="myTextBox.vb" />
    <Compile Include="myXmlDocument.vb" />
    <Compile Include="objectSocket.vb" />
    <Compile Include="Recipes\Recipe.vb" />
    <Compile Include="Recipes\RecipeLog.vb" />
    <Compile Include="Recipes\RecipeParam.vb" />
    <Compile Include="Screens.vb" />
    <Compile Include="Stocks\Cells.vb" />
    <Compile Include="Stocks\JobToCell.vb" />
    <Compile Include="Stocks\ProductsToCells.vb" />
    <Compile Include="Stocks\StockManualCorrReq.vb" />
    <Compile Include="System\SystemGroup.vb" />
    <Compile Include="System\SystemGroupsRights.vb" />
    <Compile Include="System\SystemUser.vb" />
    <Compile Include="System\SystemUserGroup.vb" />
    <Compile Include="tools.vb" />
    <Compile Include="Yields\AlarmConf.vb" />
    <Compile Include="Yields\YieldsPrintouts.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\myException\myException.vbproj">
      <Project>{b70cdf77-f848-4489-ae96-3894cea4e9c0}</Project>
      <Name>myException</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\WebDataBaseLayer\WebDataBaseLayer\WebDataBaseLayer.vbproj">
      <Project>{7133bf8e-add6-4a8c-9fb0-70cd843035aa}</Project>
      <Name>WebDataBaseLayer</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>