﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class YieldsPrintouts
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_Mailable As String = String.Empty
    Private m_Printable As String = String.Empty
    Private m_SendToList As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("YIELDS_PRINTOUTS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As YieldsPrintouts
        Me.IdYieldsPrintouts = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "SendToList"
                            Me.SendToList = CType(m_Control, myTextBox).Text
                    End Select
                ElseIf TypeOf m_Control Is myCheckBox Then
                    Dim chb As myCheckBox = CType(m_Control, myCheckBox)

                    Select Case s.PropertyName
                        Case "Mailable"
                            If chb.Checked Then
                                Me.Mailable = m_StringYes
                            Else
                                Me.Mailable = m_StringNo
                            End If
                        Case "Printable"
                            If chb.Checked Then
                                Me.Printable = m_StringYes
                            Else
                                Me.Printable = m_StringNo
                            End If
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdYieldsPrintouts() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property Printable() As String
        Get
            Return m_Printable
        End Get
        Set(value As String)
            m_Printable = value.Trim
        End Set
    End Property

    Public Property Mailable() As String
        Get
            Return m_Mailable
        End Get
        Set(value As String)
            m_Mailable = value.Trim
        End Set
    End Property

    Public Property SendToList() As String
        Get
            Return Me.m_SendToList
        End Get
        Set(ByVal value As String)
            Me.m_SendToList = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdYieldsPrintouts & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdYieldsPrintouts))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio yields printouts."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio yields printouts sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If drNodo("ID") Is DBNull.Value OrElse Me.IdYieldsPrintouts = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("PRINTABLE") = Me.TrimStringToMaxLen(Me.Printable, "PRINTABLE")
            drNodo("MAILABLE") = Me.TrimStringToMaxLen(Me.Mailable, "MAILABLE")
            drNodo("SEND_TO_LIST") = Me.TrimStringToMaxLen(Me.SendToList, "SEND_TO_LIST")

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento yields printouts: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class