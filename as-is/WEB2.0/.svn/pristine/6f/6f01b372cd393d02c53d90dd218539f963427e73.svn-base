﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class NeededSpareParts
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdMaintProcAssign As Long = m_InvalidId
    Private m_IdSparePart As Long = m_InvalidId
    Private m_RerquestQty As Double = costanti.m_DoubleZero
    Private m_IdEquipment As Long = m_InvalidId

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New()
        MyBase.New("NEEDED_SPARE_PARTS")
    End Sub

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("NEEDED_SPARE_PARTS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As NeededSpareParts
        Me.Id = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdMaintProcAssign"
                            Try
                                Me.IdMaintProcAssign = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdMaintProcAssign = m_InvalidId
                            End Try
                        Case "IdSparePart"
                            Try
                                Me.IdSparePart = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch ex As Exception
                                Me.IdSparePart = m_InvalidId
                            End Try
                        Case "IdEquipment"
                            Try
                                Me.IdEquipment = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch ex As Exception
                                Me.IdEquipment = m_InvalidId
                            End Try
                    End Select
                End If
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "RerquestQty"
                            Me.RerquestQty = tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property Id() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdEquipment() As Long
        Get
            Return Me.m_IdEquipment
        End Get
        Set(ByVal value As Long)
            Me.m_IdEquipment = value
        End Set
    End Property

    Public Property IdMaintProcAssign() As Long
        Get
            Return Me.m_IdMaintProcAssign
        End Get
        Set(ByVal value As Long)
            Me.m_IdMaintProcAssign = value
        End Set
    End Property

    Public Property IdSparePart() As Long
        Get
            Return Me.m_IdSparePart
        End Get
        Set(ByVal value As Long)
            Me.m_IdSparePart = value
        End Set
    End Property

    Public Property RerquestQty() As Double
        Get
            Return Me.m_RerquestQty
        End Get
        Set(ByVal value As Double)
            Me.m_RerquestQty = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE MPA_ID = '" & Me.IdMaintProcAssign & "' AND SP_ID = '" & Me.IdSparePart & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True

            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("MPA_ID={0} AND SP_ID={1}", Me.IdMaintProcAssign, Me.IdSparePart))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio  assegnamenti procedure parti di ricambio."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If

                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio assegnamenti procedure parti di ricambio sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If drNodo("ID") Is DBNull.Value Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("MPA_ID") = Me.IdMaintProcAssign
            drNodo("SP_ID") = Me.IdSparePart
            drNodo("REQUEST_QTY") = Me.RerquestQty

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento assegnamenti procedure parti di ricambio: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try

        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub
End Class