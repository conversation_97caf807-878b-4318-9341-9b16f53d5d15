﻿Option Strict On

Imports System.Data.OleDb

Public Class SystemGroupsRights
    Inherits myDbBaseClass

    Private m_Id As Long
    Private m_IdGroup As Long
    Private m_IdSal As Integer
    Private m_IdPage As Integer

    Public Sub New()
        MyBase.New("SYSTEM_GROUPS_RIGHTS")
    End Sub

    Public Property Id() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdGroup() As Long
        Get
            Return Me.m_IdGroup
        End Get
        Set(ByVal value As Long)
            Me.m_IdGroup = value
        End Set
    End Property

    Public Property IdSal() As Integer
        Get
            Return Me.m_IdSal
        End Get
        Set(ByVal value As Integer)
            Me.m_IdSal = value
        End Set
    End Property

    Public Property IdPage() As Integer
        Get
            Return Me.m_IdPage
        End Get
        Set(ByVal value As Integer)
            Me.m_IdPage = value
        End Set
    End Property

    Public Sub Delete()
        Dim sDelete As String = "DELETE FROM " & Me.TableName & " WHERE ID_GROUP = '" & Me.IdGroup & "' AND SAL_ID = '" & Me.IdSal & "' AND ID_PAGE = '" & Me.IdPage & "'"
        Try
            WebDataBaseLayer.DataBase.ExecuteSQL(sDelete)
        Catch ex As myException.myException
            Throw ex
        End Try
    End Sub

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID_GROUP = '" & Me.IdGroup & "' AND SAL_ID = '" & Me.IdSal & "' AND ID_PAGE = '" & Me.IdPage & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                'Esco non devo aggiornare niente
                Exit Sub
            End If

            drNodo("ID") = Me.Id
            drNodo("ID_GROUP") = Me.IdGroup
            drNodo("SAL_ID") = Me.IdSal
            drNodo("ID_PAGE") = Me.IdPage

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento system groups rights: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class