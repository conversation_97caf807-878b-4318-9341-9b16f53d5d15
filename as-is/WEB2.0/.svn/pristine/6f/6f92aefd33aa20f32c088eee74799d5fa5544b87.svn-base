﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class GrainOrder
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_Reference As String = String.Empty
    Private m_IdProduct As Long = m_InvalidId
    Private m_IdSupplier As Long = m_InvalidId
    Private m_FreeText As String = String.Empty
    Private m_ExpectedQty As Double = m_InvalidDblValue
    Private m_ExpectedDelivery As DateTime = m_InvalidDateTime
    Private m_CreationDate As DateTime = m_InvalidDateTime
    Private m_RealQty As Double = m_InvalidDblValue
    Private m_ToDo As Integer = m_IntegerZero
    Private m_Period As String = String.Empty
    Private m_IdWarehouse As Long = m_InvalidId
    Private m_IdCel As Long = m_InvalidId
    Private m_DownloadNote As String = String.Empty
    Private m_FlagRealQty As Integer = m_IntegerZero
    Private m_IdAgent As Long = m_InvalidId
    Private m_ExtRefNum As String = String.Empty
    Private m_TypeWheat As String = String.Empty
    Private m_TransportStatus As Integer = m_IntegerZero
    Private m_ExpirationDate As DateTime = m_InvalidDateTime

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("GRAIN_ORDERS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As GrainOrder
        Me.IdGrainOrder = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdProduct"
                            Try
                                Me.IdProduct = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdProduct = m_InvalidId
                            End Try
                        Case "IdSupplier"
                            Try
                                Me.IdSupplier = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdSupplier = m_InvalidId
                            End Try
                        Case "IdWarehouse"
                            Try
                                Me.IdWarehouse = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdWarehouse = m_InvalidId
                            End Try
                        Case "IdAgent"
                            Try
                                Me.IdAgent = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdAgent = m_InvalidId
                            End Try
                        Case "TransportStatus"
                            Try
                                Me.TransportStatus = Integer.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.TransportStatus = m_IntegerZero
                            End Try
                        Case "IdCel"
                            Try
                                Me.IdCel = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdCel = m_InvalidId
                            End Try
                        Case "ToDo"
                            Try
                                Me.ToDo = Integer.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.ToDo = m_IntegerZero
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myCheckBox Then
                    Select Case s.PropertyName
                        Case "FlagRealQty"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.FlagRealQty = 1
                            Else
                                Me.FlagRealQty = 0
                            End If
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "Reference"
                            Me.Reference = CType(m_Control, myTextBox).Text
                        Case "TypeWheat"
                            Me.TypeWheat = CType(m_Control, myTextBox).Text
                        Case "ExtRefNum"
                            Me.ExtRefNum = CType(m_Control, myTextBox).Text
                        Case "FreeText"
                            Me.FreeText = CType(m_Control, myTextBox).Text
                        Case "Period"
                            Me.Period = CType(m_Control, myTextBox).Text
                        Case "DownloadNote"
                            Me.DownloadNote = CType(m_Control, myTextBox).Text
                        Case "ExpectedQty"
                            Me.ExpectedQty = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                        Case "RealQty"
                            Me.RealQty = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                    End Select
                ElseIf m_Control Is Nothing Then
                    Select Case s.PropertyName
                        Case "ExpectedDelivery"
                            Try
                                Me.ExpectedDelivery = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.ExpectedDelivery = m_InvalidDateTime
                            End Try
                        Case "CreationDate"
                            Try
                                Me.CreationDate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.CreationDate = m_InvalidDateTime
                            End Try
                        Case "ExpirationDate"
                            Try
                                Me.ExpirationDate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.ExpirationDate = m_InvalidDateTime
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdGrainOrder() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdProduct() As Long
        Get
            Return Me.m_IdProduct
        End Get
        Set(ByVal value As Long)
            Me.m_IdProduct = value
        End Set
    End Property

    Public Property Reference() As String
        Get
            Return Me.m_Reference
        End Get
        Set(ByVal value As String)
            Me.m_Reference = value.Trim
        End Set
    End Property

    Public Property IdSupplier() As Long
        Get
            Return Me.m_IdSupplier
        End Get
        Set(ByVal value As Long)
            Me.m_IdSupplier = value
        End Set
    End Property

    Public Property FreeText() As String
        Get
            Return Me.m_FreeText
        End Get
        Set(ByVal value As String)
            Me.m_FreeText = value.Trim
        End Set
    End Property

    Public Property ExpectedQty() As Double
        Get
            Return Me.m_ExpectedQty
        End Get
        Set(ByVal value As Double)
            Me.m_ExpectedQty = value
        End Set
    End Property

    Public Property RealQty() As Double
        Get
            Return Me.m_RealQty
        End Get
        Set(ByVal value As Double)
            Me.m_RealQty = value
        End Set
    End Property

    Public Property ExpectedDelivery() As DateTime
        Get
            Return Me.m_ExpectedDelivery
        End Get
        Set(ByVal value As DateTime)
            Me.m_ExpectedDelivery = value
        End Set
    End Property

    Public Property CreationDate() As DateTime
        Get
            Return Me.m_CreationDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_CreationDate = value
        End Set
    End Property

    Public Property ToDo() As Integer
        Get
            Return Me.m_ToDo
        End Get
        Set(ByVal value As Integer)
            Me.m_ToDo = value
        End Set
    End Property

    Public Property Period() As String
        Get
            Return Me.m_Period
        End Get
        Set(ByVal value As String)
            Me.m_Period = value.Trim
        End Set
    End Property

    Public Property IdWarehouse() As Long
        Get
            Return Me.m_IdWarehouse
        End Get
        Set(ByVal value As Long)
            Me.m_IdWarehouse = value
        End Set
    End Property

    Public Property IdCel() As Long
        Get
            Return Me.m_IdCel
        End Get
        Set(ByVal value As Long)
            Me.m_IdCel = value
        End Set
    End Property

    Public Property DownloadNote() As String
        Get
            Return Me.m_DownloadNote
        End Get
        Set(ByVal value As String)
            Me.m_DownloadNote = value.Trim
        End Set
    End Property

    Public Property FlagRealQty() As Integer
        Get
            Return Me.m_FlagRealQty
        End Get
        Set(ByVal value As Integer)
            Me.m_FlagRealQty = value
        End Set
    End Property

    Public Property IdAgent() As Long
        Get
            Return Me.m_IdAgent
        End Get
        Set(ByVal value As Long)
            Me.m_IdAgent = value
        End Set
    End Property

    Public Property TypeWheat() As String
        Get
            Return Me.m_TypeWheat
        End Get
        Set(ByVal value As String)
            Me.m_TypeWheat = value.Trim
        End Set
    End Property

    Public Property ExtRefNum() As String
        Get
            Return Me.m_ExtRefNum
        End Get
        Set(ByVal value As String)
            Me.m_ExtRefNum = value.Trim
        End Set
    End Property

    Public Property TransportStatus() As Integer
        Get
            Return Me.m_TransportStatus
        End Get
        Set(ByVal value As Integer)
            Me.m_TransportStatus = value
        End Set
    End Property

    Public Property ExpirationDate() As DateTime
        Get
            Return Me.m_ExpirationDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_ExpirationDate = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdGrainOrder & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True

            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdGrainOrder))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio grain order."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If

                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio grain order sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If Me.IdGrainOrder = m_InvalidId Then
                Me.IdGrainOrder = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.IdGrainOrder
            End If

            If Me.IdProduct <> m_InvalidId Then
                drNodo("PRO_ID") = Me.IdProduct
            Else
                drNodo("PRO_ID") = costanti.m_IdProduct_Default
            End If

            If Me.Reference <> String.Empty Then
                drNodo("REFERENCE") = Me.TrimStringToMaxLen(Me.Reference, "REFERENCE")
            Else
                drNodo("REFERENCE") = String.Empty
            End If

            If IdSupplier > m_InvalidId Then
                drNodo("SUP_ID") = Me.IdSupplier
            End If

            If Me.FreeText <> String.Empty Then
                drNodo("FREE_TEXT") = Me.TrimStringToMaxLen(Me.FreeText, "FREE_TEXT")
            Else
                drNodo("FREE_TEXT") = DBNull.Value
            End If

            Try
                drNodo("EXPECTED_QTY") = Me.ExpectedQty
            Catch ex As Exception
                drNodo("EXPECTED_QTY") = m_InvalidDblValue
            End Try

            If Me.ExpectedDelivery <> m_InvalidDateTime Then
                drNodo("EXPECTED_DELIVERY") = Me.ExpectedDelivery
            Else
                drNodo("EXPECTED_DELIVERY") = DBNull.Value
            End If

            If Me.CreationDate <> m_InvalidDateTime Then
                drNodo("CREATION_DATE") = Me.CreationDate
            Else
                Me.CreationDate = Now
            End If

            Try
                drNodo("REAL_QTY") = Me.RealQty
            Catch ex As Exception
                drNodo("REAL_QTY") = m_InvalidDblValue
            End Try

            Try
                drNodo("TODO") = Me.ToDo
            Catch ex As Exception
                drNodo("TODO") = m_IntegerZero
            End Try

            If Me.Period <> String.Empty Then
                drNodo("PERIOD") = Me.TrimStringToMaxLen(Me.Period, "PERIOD")
            Else
                drNodo("PERIOD") = DBNull.Value
            End If

            If IdWarehouse > m_InvalidId Then
                drNodo("WH_ID") = Me.IdWarehouse
            Else
                drNodo("WH_ID") = m_IdWareHouse_Default
            End If

            If Me.IdCel > m_IntegerZero Then
                drNodo("CEL_ID") = Me.IdCel
            Else
                drNodo("CEL_ID") = DBNull.Value
            End If

            If Me.DownloadNote <> String.Empty Then
                drNodo("DOWNLOAD_NOTE") = Me.TrimStringToMaxLen(Me.DownloadNote, "DOWNLOAD_NOTE")
            Else
                drNodo("DOWNLOAD_NOTE") = DBNull.Value
            End If

            Try
                drNodo("FLAG_REAL_QTY") = Me.FlagRealQty
            Catch ex As Exception
                drNodo("FLAG_REAL_QTY") = m_IntegerZero
            End Try

            If Me.IdAgent > m_InvalidId Then
                drNodo("AG_ID") = Me.IdAgent
            Else
                drNodo("AG_ID") = DBNull.Value
            End If

            If Me.ExtRefNum <> String.Empty Then
                drNodo("EXT_REF_NUM") = Me.TrimStringToMaxLen(Me.ExtRefNum, "EXT_REF_NUM")
            Else
                drNodo("EXT_REF_NUM") = DBNull.Value
            End If

            If Me.TypeWheat <> String.Empty Then
                drNodo("TYPE_WHEAT") = Me.TrimStringToMaxLen(Me.TypeWheat, "TYPE_WHEAT")
            Else
                drNodo("TYPE_WHEAT") = DBNull.Value
            End If

            Try
                drNodo("TRANSPORT_STATUS") = Me.TransportStatus
            Catch ex As Exception
                drNodo("TRANSPORT_STATUS") = m_IntegerZero
            End Try

            If Me.ExpirationDate <> m_InvalidDateTime Then
                drNodo("EXPIRATION_DATE") = Me.ExpirationDate
            Else
                drNodo("EXPIRATION_DATE") = DBNull.Value
            End If

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento gran order: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try

        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub
End Class