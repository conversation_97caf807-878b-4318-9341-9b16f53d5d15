﻿Imports System.Web.HttpContext
Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports UsersGUI
Imports UsersGUI.costanti
Imports WebDataBaseLayer

Public Class tools

    Public Shared Function GetTime(ByVal m_timeinseconds As Long) As TimeSpan
        Dim h_modulo As Integer = 0
        Dim h_ora As Integer = 0
        Dim h_minuti As Integer = 0
        Dim h_secondi As Integer = 0
        Dim T As TimeSpan

        'Calcoliamo se esiste l'ora
        h_modulo = m_timeinseconds Mod 3600
        h_ora = Fix(m_timeinseconds / 3600)
        If h_modulo <> 0 Then
            h_minuti = Fix(h_modulo / 60)
            h_modulo = h_modulo Mod 60
        End If
        If h_modulo <> 0 Then
            h_secondi = h_modulo
        End If

        T = New TimeSpan(h_ora, h_minuti, h_secondi)
        Return T
    End Function

    ''' <summary>
    ''' Gets the start and stop date for the selected period.
    ''' </summary>
    ''' <param name="period">The period.</param>
    ''' <param name="start_date">The start_date.</param>
    ''' <param name="stop_date">The stop_date.</param>
    ''' <returns>true se period è valido, false altrimenti</returns>
    Public Shared Function GetStartAndStopDateForPeriod(ByVal period As String, ByRef start_date As Date, ByRef stop_date As Date) As Boolean

        Dim YIELDS_1ST_SHIFT_HOUR As Integer = CInt(GetParameter("1ST_SHIFT_HOUR"))
        Dim YIELDS_2ND_SHIFT_HOUR As Integer = CInt(GetParameter("2ND_SHIFT_HOUR"))
        Dim YIELDS_3RD_SHIFT_HOUR As Integer = CInt(GetParameter("3RD_SHIFT_HOUR"))
        Dim YIELDS_HOUR_START_DAY As Integer = CInt(GetParameter("HOUR_START_DAY"))
        Dim WEEK_FIRST_DAY As Integer = CInt(GetParameter("WEEK_FIRST_DAY"))

        Dim hour_diff As Integer

        Dim ret_val = True

        Select Case period
            'Case "currhour"
            '    stop_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, Now.Minute, Now.Second)
            '    start_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)

            Case "lasthour"
                stop_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                start_date = New DateTime(Now.AddHours(-1).Year, Now.AddHours(-1).Month, Now.AddHours(-1).Day, Now.AddHours(-1).Hour, 0, 0)

            Case "currshift"
                stop_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                start_date = GetStartShift(YIELDS_1ST_SHIFT_HOUR, YIELDS_2ND_SHIFT_HOUR, YIELDS_3RD_SHIFT_HOUR, stop_date)

            Case "lastshift"
                ' la fine del turno precedente coincide con l'inizio dell'attuale
                stop_date = GetStartShift(YIELDS_1ST_SHIFT_HOUR, YIELDS_2ND_SHIFT_HOUR, YIELDS_3RD_SHIFT_HOUR, Now())
                start_date = GetStartShift(YIELDS_1ST_SHIFT_HOUR, YIELDS_2ND_SHIFT_HOUR, YIELDS_3RD_SHIFT_HOUR, stop_date)

            Case "currday"
                stop_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                start_date = New DateTime(Now.Year, Now.Month, Now.Day, YIELDS_HOUR_START_DAY, 0, 0)

                ' se il giorno di lavoro non è ancora cominciato, ma sono oltre mezzanotte, devo togliere un giorno
                If (Now.Hour) < YIELDS_HOUR_START_DAY Then
                    start_date = DateAdd("d", -1, start_date)
                End If

            Case "lastday"
                stop_date = New DateTime(Now.Year, Now.Month, Now.Day, YIELDS_HOUR_START_DAY, 0, 0)

                ' se il giorno di lavoro non è ancora cominciato, ma sono oltre mezzanotte, devo togliere un giorno
                If (Now.Hour) < YIELDS_HOUR_START_DAY Then
                    stop_date = DateAdd("d", -1, stop_date)
                End If
                start_date = DateAdd("d", -1, stop_date)

            Case "currweek"
                stop_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                ' start date is the first hour of the first day of the current week
                hour_diff = -(24 * (Weekday(Now(), WEEK_FIRST_DAY) - 1))
                start_date = New DateTime(Now.AddHours(hour_diff).Year, Now.AddHours(hour_diff).Month, Now.AddHours(hour_diff).Day, YIELDS_HOUR_START_DAY, 0, 0)
                ' se la settimana di lavoro non è ancora cominciata, ma sono oltre mezzanotte, devo togliere una settimana
                If Now.DayOfWeek = (WEEK_FIRST_DAY - 1) AndAlso (Now.Hour) < YIELDS_HOUR_START_DAY Then
                    start_date = DateAdd("d", -7, start_date)
                End If

            Case "lastweek"
                ' stop date is the first hour of the first day of the current week
                hour_diff = -(24 * (Weekday(Now(), WEEK_FIRST_DAY) - 1))
                stop_date = New DateTime(Now.AddHours(hour_diff).Year, Now.AddHours(hour_diff).Month, Now.AddHours(hour_diff).Day, YIELDS_HOUR_START_DAY, 0, 0)
                ' se la settimana di lavoro non è ancora cominciata, ma sono oltre mezzanotte, devo togliere una settimana
                If Now.DayOfWeek = (WEEK_FIRST_DAY - 1) AndAlso (Now.Hour) < YIELDS_HOUR_START_DAY Then
                    stop_date = DateAdd("d", -7, stop_date)
                End If
                ' stop date is start date minus seven days
                start_date = DateAdd("d", -7, stop_date)

            Case "currmonth"
                stop_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                start_date = New DateTime(Now.Year, Now.Month, 1, YIELDS_HOUR_START_DAY, 0, 0)

                ' se il mese di lavoro non è ancora cominciato, ma sono oltre mezzanotte, devo togliere un mese
                If Now.Day = 1 AndAlso (Now.Hour) < YIELDS_HOUR_START_DAY Then
                    start_date = DateAdd("m", -1, start_date)
                End If

            Case "lastmonth"
                stop_date = New DateTime(Now.Year, Now.Month, 1, YIELDS_HOUR_START_DAY, 0, 0)

                ' se il mese di lavoro non è ancora cominciato, ma sono oltre mezzanotte, devo togliere un mese
                If Now.Day = 1 AndAlso (Now.Hour) < YIELDS_HOUR_START_DAY Then
                    stop_date = DateAdd("m", -1, stop_date)
                End If
                start_date = DateAdd("m", -1, stop_date)

            Case "AllData"
                stop_date = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                start_date = New DateTime(Now.AddYears(-3).Year, Now.AddYears(-3).Month, Now.AddYears(-3).Day, Now.AddYears(-3).Hour, 0, 0)

            Case Else
                ' periodo non riconosciuto
                ret_val = False
        End Select
        Return ret_val
    End Function

    ''' <summary>
    ''' Determina l'ora di inizio del turno in corso rispetto alla data passata come parametro
    ''' </summary>
    ''' <param name="YIELDS_1ST_SHIFT_HOUR">ora inizio primo turno 0..23 (Int)</param>
    ''' <param name="YIELDS_2ND_SHIFT_HOUR">ora inizio secondo turno 0..23 (Int)</param>
    ''' <param name="YIELDS_3RD_SHIFT_HOUR">ora inizio terzo turno 0..23 (Int)</param>
    ''' <param name="dataCorrente">Data con ora (e.g. Now())</param>
    ''' <returns>inizio turno CDate (data + ora)</returns>
    ''' <remarks></remarks>
    Private Shared Function GetStartShift(ByVal YIELDS_1ST_SHIFT_HOUR As Integer, ByVal YIELDS_2ND_SHIFT_HOUR As Integer, ByVal YIELDS_3RD_SHIFT_HOUR As Integer, ByVal dataCorrente As DateTime) As DateTime

        Dim inizio_primo, inizio_secondo, inizio_terzo, inizio_turno As DateTime
        Dim b_first As Boolean = True
        Dim span_time_1, span_time_2, span_time_3, min_span As Long

        inizio_primo = New DateTime(dataCorrente.Year, dataCorrente.Month, dataCorrente.Day, YIELDS_1ST_SHIFT_HOUR, 0, 0)
        If (dataCorrente.Hour < YIELDS_1ST_SHIFT_HOUR) Then
            inizio_primo = DateAdd("d", -1, inizio_primo)
        End If

        inizio_secondo = New DateTime(dataCorrente.Year, dataCorrente.Month, dataCorrente.Day, YIELDS_2ND_SHIFT_HOUR, 0, 0)
        If (dataCorrente.Hour < YIELDS_2ND_SHIFT_HOUR) Then
            inizio_secondo = DateAdd("d", -1, inizio_secondo)
        End If

        inizio_terzo = New DateTime(dataCorrente.Year, dataCorrente.Month, dataCorrente.Day, YIELDS_3RD_SHIFT_HOUR, 0, 0)
        If (dataCorrente.Hour < YIELDS_3RD_SHIFT_HOUR) Then
            inizio_terzo = DateAdd("d", -1, inizio_terzo)
        End If
        ' gestisco il caso di due turni
        If (inizio_primo = inizio_terzo) Then
            inizio_terzo = DateAdd("d", -1, inizio_terzo)
        End If

        ' adesso posso determinare l'ora di inizio
        span_time_1 = DateDiff("s", inizio_primo, dataCorrente)
        span_time_2 = DateDiff("s", inizio_secondo, dataCorrente)
        span_time_3 = DateDiff("s", inizio_terzo, dataCorrente)

        If (span_time_1 > 0) Then
            If (b_first) Then
                min_span = span_time_1
                inizio_turno = inizio_primo

                b_first = False
            End If
        End If

        If (span_time_2 > 0) Then
            If (Not b_first) Then
                If (span_time_2 < min_span) Then

                    min_span = span_time_2
                    inizio_turno = inizio_secondo
                End If
            Else
                min_span = span_time_2
                inizio_turno = inizio_secondo

                b_first = False
            End If
        End If

        If (span_time_3 > 0) Then
            If (Not b_first) Then
                If (span_time_3 < min_span) Then

                    min_span = span_time_3
                    inizio_turno = inizio_terzo
                End If
            Else
                min_span = span_time_3
                inizio_turno = inizio_terzo

                b_first = False
            End If
        End If

        Return inizio_turno
    End Function

    ''' <summary>
    ''' Return the control found
    ''' </summary>
    ''' <param name="Root"></param>
    ''' <param name="Id"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function FindControlRecursive(ByVal Root As Control, ByVal Id As String) As Control
        If Root.ID = Id Then
            Return Root
        End If

        For Each Ctl As Control In Root.Controls
            Dim FoundCtl As Control = FindControlRecursive(Ctl, Id)
            If FoundCtl IsNot Nothing Then
                Return FoundCtl
            End If
        Next

        Return Nothing
    End Function

    ''' <summary>
    ''' La funzione aggiorna un system parameter al valore passato come argomento
    ''' </summary>
    ''' <param name="parameterName">Nome del system parameter</param>
    ''' <param name="parameterValue">Valore da associare</param>
    ''' <remarks></remarks>
    Public Shared Sub SetParameter(ByVal parameterName As String, ByVal parameterValue As String)
        Dim sSelect As String = "UPDATE SYSTEM_PARAMETERS SET PARAMETER_VALUE = '" & parameterValue & "' WHERE PARAMETER_NAME=N'" & parameterName & "'"
        WebDataBaseLayer.DataBase.ExecuteSQL(sSelect)

    End Sub

    ''' <summary>
    ''' Recupera il valore del systrem_parameter passato come argomento
    ''' </summary>
    ''' <param name="parameterName"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetParameter(ByVal parameterName As String) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT PARAMETER_VALUE FROM SYSTEM_PARAMETERS WHERE PARAMETER_NAME=N'" & parameterName & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' Recupera il valore del order param passato come argomento
    ''' </summary>
    ''' <param name="OrderParamName"></param>
    ''' <param name="ppl_id"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetOrderParameter(ByVal OrderParamName As String, ByVal ppl_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT PARAMETER_VALUE FROM VIEW_ORDER_PARAMETERS WHERE ASP_NAME=N'" & OrderParamName & "' AND PPL_ID = '" & ppl_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' Recupera il valore del order param archived passato come argomento
    ''' </summary>
    ''' <param name="OrderParamName"></param>
    ''' <param name="prt_id"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetOrderParameterArchived(ByVal OrderParamName As String, ByVal prt_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT PARAMETER_VALUE FROM VIEW_ORDER_PARAMETERS_ARCHIVED WHERE ASP_NAME=N'" & OrderParamName & "' AND PRT_ID = '" & prt_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    Public Shared Function GetRecipeParameter(ByVal rcp_param_name As String, ByVal rec_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT FIELD_VALUE FROM VIEW_RECIPE_PARAMETERS WHERE ASP_NAME=N'" & rcp_param_name & "' AND REC_ID = '" & rec_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    Public Shared Function GetRecipeParameterArchived(ByVal rcp_param_name As String, ByVal recipe_log_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT FIELD_VALUE FROM VIEW_RECIPE_PARAM_VALUES_ARCHIVED WHERE ASP_NAME=N'" & rcp_param_name & "' AND RECIPE_LOG_ID = '" & recipe_log_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    Public Shared Function GetAnalysisParameter(ByVal analysis_param_name As String, ByVal analysis_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT PARAMETER_VALUE FROM VIEW_ANALYSIS_PARAM_VALUES WHERE ASP_NAME=N'" & analysis_param_name & "' AND ANALYSIS_ID = '" & analysis_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    Public Shared Function GetOperationParameter(ByVal operation_param_name As String, ByVal operation_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT PARAMETER_VALUE FROM VIEW_OPERATION_PARAM_VALUES WHERE ASP_NAME=N'" & operation_param_name & "' AND OPERATION_ID = '" & operation_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    Public Shared Function GetContractParameter(ByVal contract_param_name As String, ByVal contract_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String = "SELECT PARAMETER_VALUE FROM VIEW_CONTRACT_PARAM_VALUES WHERE ASP_NAME=N'" & contract_param_name & "' AND CONTRACT_ID = '" & contract_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)
        For Each dr As DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' Ritorna lo stato del job passato come argomento
    ''' </summary>
    ''' <param name="ppl_id"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetOrderStatus(ByVal ppl_id As Long) As Integer
        Dim ret_val As Integer
        Dim sSelect As String = "SELECT ORDER_STATUS FROM VIEW_PRODUCTION_PLAN WHERE ID = '" & ppl_id & "'"
        Dim dt As DataTable

        dt = DataBase.ExecuteSQL_DataTable(sSelect, True)
        For Each dr As DataRow In dt.Rows
            ret_val = CInt(dr.Item(0))
        Next

        Return ret_val

    End Function

    ''' <summary>
    ''' Recupera il valore dal datarow
    ''' </summary>
    ''' <param name="fl"></param>
    ''' <param name="dr"></param>
    ''' <param name="ref_id">reference id. Sarà PPL_ID nel caso di production plan/order param values, PRT_ID nel caso di produciton report/order param values archive</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetValueFromField(ByVal fl As Field, ByVal dr As DataRow, ByVal ref_id As Long)
        Dim m_value As String = String.Empty

        Dim conv_factor As Double = 0.0
        Dim f_value As Double = 0.0

        If fl.FixedRuntimeValue = String.Empty Then
            Select Case fl.GetDataFrom
                Case EnumGetDataFrom.OrderParamValue
                    m_value = tools.GetOrderParameter(fl.FieldDb, ref_id)

                    If m_value <> String.Empty AndAlso fl.UnitAsp <> String.Empty AndAlso fl.UnitDb <> String.Empty Then
                        conv_factor = UsersGUI.tools.GetConversionFactorToDB(fl.UnitDb, fl.UnitAsp)

                        f_value = CType(m_value, Double) / conv_factor

                        m_value = f_value.ToString
                    End If

                Case EnumGetDataFrom.OrderParamValueArchive
                    m_value = tools.GetOrderParameterArchived(fl.FieldDb, ref_id)

                    If m_value <> String.Empty AndAlso fl.UnitAsp <> String.Empty AndAlso fl.UnitDb <> String.Empty Then
                        conv_factor = UsersGUI.tools.GetConversionFactorToDB(fl.UnitDb, fl.UnitAsp)

                        f_value = CType(m_value, Double) / conv_factor

                        m_value = f_value.ToString
                    End If

                Case EnumGetDataFrom.RecipeParamValue
                    m_value = tools.GetRecipeParameter(fl.FieldDb, ref_id)

                    If m_value <> String.Empty AndAlso fl.UnitAsp <> String.Empty AndAlso fl.UnitDb <> String.Empty Then
                        conv_factor = UsersGUI.tools.GetConversionFactorToDB(fl.UnitDb, fl.UnitAsp)

                        f_value = CType(m_value, Double) / conv_factor

                        m_value = f_value.ToString
                    End If

                Case EnumGetDataFrom.RecipeParamValueArchive
                    m_value = tools.GetRecipeParameterArchived(fl.FieldDb, ref_id)

                    If m_value <> String.Empty AndAlso fl.UnitAsp <> String.Empty AndAlso fl.UnitDb <> String.Empty Then
                        conv_factor = UsersGUI.tools.GetConversionFactorToDB(fl.UnitDb, fl.UnitAsp)

                        f_value = CType(m_value, Double) / conv_factor

                        m_value = f_value.ToString
                    End If

                Case EnumGetDataFrom.AnalysisParamValue
                    m_value = tools.GetAnalysisParameter(fl.FieldDb, ref_id)

                    If m_value <> String.Empty AndAlso fl.UnitAsp <> String.Empty AndAlso fl.UnitDb <> String.Empty Then
                        conv_factor = UsersGUI.tools.GetConversionFactorToDB(fl.UnitDb, fl.UnitAsp)

                        f_value = CType(m_value, Double) / conv_factor

                        m_value = f_value.ToString
                    End If

                Case EnumGetDataFrom.OperationParamValue
                    m_value = tools.GetOperationParameter(fl.FieldDb, ref_id)

                    If m_value <> String.Empty AndAlso fl.UnitAsp <> String.Empty AndAlso fl.UnitDb <> String.Empty Then
                        conv_factor = UsersGUI.tools.GetConversionFactorToDB(fl.UnitDb, fl.UnitAsp)

                        f_value = CType(m_value, Double) / conv_factor

                        m_value = f_value.ToString
                    End If

                Case EnumGetDataFrom.ContractParamValue
                    m_value = tools.GetContractParameter(fl.FieldDb, ref_id)

                    If m_value <> String.Empty AndAlso fl.UnitAsp <> String.Empty AndAlso fl.UnitDb <> String.Empty Then
                        conv_factor = UsersGUI.tools.GetConversionFactorToDB(fl.UnitDb, fl.UnitAsp)

                        f_value = CType(m_value, Double) / conv_factor

                        m_value = f_value.ToString
                    End If

                Case EnumGetDataFrom.Standard
                    Try
                        m_value = dr(fl.FieldDb).ToString
                    Catch ex As Exception
                        m_value = String.Empty
                    End Try

                Case Else
                    m_value = dr(fl.FieldDb).ToString
            End Select
        Else
            m_value = fl.FixedRuntimeValue.ToString
        End If

        Return m_value
    End Function

    ''' <summary>
    ''' Check If the Page is into SYSTEM_PAGES table
    ''' </summary>
    ''' <param name="m_config"></param>
    ''' <param name="sPageName"></param>
    ''' <remarks></remarks>
    Public Shared Sub CheckPageDB(ByVal m_config As config, ByVal sMenuName As String, ByVal sPageName As String)
        Dim strSQL As String
        Dim rs As DataTable

        If sPageName.Trim <> String.Empty Then
            'Controllo che la pagina sia salvata all'interno della tabella, se non è così la aggiungo.
            strSQL = "SELECT * FROM SYSTEM_PAGES WHERE MENU_NAME = '" & sMenuName & "' AND PAGE_NAME='" & sPageName & "'"
            rs = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(strSQL, False)

            If rs Is Nothing Then
                Exit Sub
            End If

            If rs.Rows.Count = 0 Then

                strSQL = "INSERT INTO SYSTEM_PAGES(ID, MENU_NAME, PAGE_NAME, IS_MOBILE_PAGE) VALUES(" &
                       UsersGUI.tools.GetNextId("SYSTEM_PAGES", "ID") & ",'" & sMenuName.ToUpper & "','" & sPageName.ToUpper & "', 'NO')"
                WebDataBaseLayer.DataBase.ExecuteSQL(strSQL)
            End If
        End If
    End Sub

    ''' <summary>
    ''' Check permissions
    ''' </summary>
    ''' <param name="m_config"></param>
    ''' <param name="sPageName"></param>
    ''' <param name="m_AccessLevel"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function CheckAccess(ByVal m_config As config, ByVal sMenuName As String, ByVal sPageName As String, ByVal m_AccessLevel As EnumAccessLevel) As Boolean
        Try
            If m_config Is Nothing Then
                Return False
            End If
            If Current.Session("UserID") Is Nothing Then
                'Verifico che la pagina abbia il permesso del gruppo guest per il livello di accesso richiesto
                Dim sSelect As String = "SELECT * FROM VIEW_SYSTEM_GROUPS_RIGHTS WHERE SAL_ID = '" & m_AccessLevel &
                    "' AND MENU_NAME = '" & sMenuName & "' AND PAGE_NAME = '" & sPageName & "' AND ID_GROUP = '" & costanti.m_GuestIdGroup.ToString & "'"
                Dim dt As DataTable
                dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

                If dt Is Nothing OrElse dt.Rows.Count <= 0 Then
                    Return False
                End If
            Else
                If Long.Parse(Current.Session("UserID").ToString) = costanti.m_SystemIdUser Then
                    'L'utente system, in questo modo, ha tutti i permessi
                    Return True
                End If
                'Verifico che l'utente appartenga ad un gruppo con il livello di accesso richiesto
                Dim sSelect As String = "SELECT * FROM VIEW_SYSTEM_GROUPS_RIGHTS WHERE SAL_ID = '" & m_AccessLevel &
                    "' AND MENU_NAME = '" & sMenuName & "' AND PAGE_NAME = '" & sPageName & "' AND ID_USER = '" & Current.Session("UserID").ToString & "'"
                Dim dt As DataTable
                dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

                If dt Is Nothing OrElse dt.Rows.Count <= 0 Then
                    Return False
                End If
            End If

            Return True
        Catch ex As Exception
            Dim msg As String = "CheckAccess " & ex.Message
            Dim myExc As New myException.myException(ex, "WebTools", System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
            Throw myExc
        End Try
    End Function

    ''' <summary>
    ''' Esegue lo swap di un record: cerca il record con OrderField subito superiore (o inferiore)
    ''' e fa un update su tale record con OrderField = CurrentOrder. Il vecchio record viene posto a OrderField = al valore
    ''' di OrderField del record recuperato
    ''' WS è una condizione where aggiuntiva necessaria se si vuole filtrare in modo preventivo il recordset su cui effettuare
    ''' l'operazione di swap
    ''' </summary>
    ''' <param name="scr">Object Screen</param>
    ''' <param name="ID">ID del record corrente </param>
    ''' <param name="WS">WhereStatement aggiuntivo</param>
    ''' <param name="CurrentOrder">valore corrente di order</param>
    ''' <param name="Direction">Direzione dove spostare il record UP DOWN (default=DOWN)</param>
    ''' <remarks></remarks>
    Public Shared Sub MoveUpDownRecord(ByVal scr As Screen, ByRef ID As String, ByRef WS As String, ByRef CurrentOrder As String, ByRef Direction As String)

        Dim dt As DataTable
        Dim strSql As String
        Dim NomeTabella As String = scr.DBName
        Dim OrderBySortType As String
        Dim OrderByTrimmed As String

        If scr.DBName = "VIEW_PRODUCTION_PLAN" Then
            NomeTabella = "PRODUCTION_PLAN"
        End If

        If scr.OrderBy = String.Empty Then
            'PrintError(NomeTabella & " deve avere una chiave orderBy per uso in MoveUpDownRecord")
        End If

        ' Divido l'OrderBy tra parte propriamente di Order e particella di ordinamento DESC o ASC (default)
        If scr.OrderBy.EndsWith("DESC") Then
            OrderBySortType = "DESC"
            OrderByTrimmed = scr.OrderBy.Replace("DESC", "").Trim
        Else
            OrderBySortType = "ASC"
            OrderByTrimmed = scr.OrderBy.Replace("ASC", "").Trim
        End If

        If WS = "" Then
            WS = " 1=1 AND "
        Else
            WS &= " AND "
        End If

        If OrderBySortType = "ASC" Then
            ' ordinamento crescente (codice storico)
            If Direction.ToUpper = "UP" Then
                WS &= OrderByTrimmed & "<" & CurrentOrder & " ORDER BY " & OrderByTrimmed & " DESC"
            Else
                ' aggiungo la condizione < 1000000 per non muovere la riga dedicata ai cicli che partono da interfaccia scada
                WS &= OrderByTrimmed & " BETWEEN " & CurrentOrder + 1 & " AND 1000000 ORDER BY " & OrderByTrimmed
            End If
        Else
            ' ordinamento decrescente, azioni invertite
            If Direction.ToUpper = "UP" Then
                ' aggiungo la condizione < 1000000 per non muovere la riga dedicata ai cicli che partono da interfaccia scada
                WS &= OrderByTrimmed & " BETWEEN " & CurrentOrder + 1 & " AND 1000000 ORDER BY " & OrderByTrimmed
            Else
                WS &= OrderByTrimmed & "<" & CurrentOrder & " ORDER BY " & OrderByTrimmed & " DESC"
            End If
        End If

        'rs.cursorlocation=adopenstatic
        strSql = "SELECT * FROM " & NomeTabella & " WHERE " & WS
        dt = DataBase.ExecuteSQL_DataTable(strSql, False)

        If dt Is Nothing Then
            Exit Sub
        End If

        For Each dr As DataRow In dt.Rows
            Dim Id_2 As String = dr("ID").ToString
            Dim Order_2 As String = dr(OrderByTrimmed).ToString

            strSql = "UPDATE " & NomeTabella & " SET " & OrderByTrimmed & "=" & CurrentOrder & " WHERE ID='" & Id_2 & "'"
            DataBase.ExecuteSQL(strSql)

            strSql = "UPDATE " & NomeTabella & " SET " & OrderByTrimmed & "=" & Order_2 & " WHERE ID='" & ID & "'"
            DataBase.ExecuteSQL(strSql)

            Exit For
        Next

    End Sub

    Public Shared Function IsPlanned(ByVal cycle_id As Integer) As Boolean
        Dim ret_val = False

        If DataBase.TableExist("CYCLES") Then
            Dim sSelect As String = "SELECT IS_PLANNED FROM CYCLES WHERE ID = '" & cycle_id & "'"
            Dim dt As DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt IsNot Nothing Then
                For Each dr As DataRow In dt.Rows
                    If dr.Item("IS_PLANNED").ToString.Trim.ToUpper = costanti.m_StringYes.ToUpper Then
                        ret_val = True
                        Exit For
                    End If
                Next
            End If
        End If

        Return ret_val
    End Function

    Public Shared Function IsControlled(ByVal cycle_id As Integer) As Boolean
        Dim ret_val = False

        If DataBase.TableExist("CYCLES") Then
            Dim sSelect As String = "SELECT IS_CONTROLLED FROM CYCLES WHERE ID = '" & cycle_id & "'"
            Dim dt As DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt IsNot Nothing Then
                For Each dr As DataRow In dt.Rows
                    If dr.Item("IS_CONTROLLED").ToString.Trim.ToUpper = costanti.m_StringYes.ToUpper Then
                        ret_val = True
                        Exit For
                    End If
                Next
            End If
        End If

        Return ret_val
    End Function

    Public Shared Function GetPplIdFromCycleId(ByVal cycle_id As Integer) As Long
        Dim ret_val As Long = costanti.m_InvalidId
        Dim sSelect As String = "SELECT ID FROM PRODUCTION_PLAN WHERE CYC_ID = '" & cycle_id & "'"
        Dim dt As DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing Then
            For Each dr As DataRow In dt.Rows
                ret_val = Long.Parse(dr.Item("ID").ToString)
                Exit For
            Next
        End If
        Return ret_val
    End Function

    Public Shared Function GetCycleIdFromPplId(ByVal ppl_id As Integer) As Long
        Dim ret_val As Long = costanti.m_InvalidId
        Dim sSelect As String = "SELECT CYC_ID FROM PRODUCTION_PLAN WHERE ID = '" & ppl_id & "'"
        Dim dt As DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing Then
            For Each dr As DataRow In dt.Rows
                ret_val = Long.Parse(dr.Item("CYC_ID").ToString)
                Exit For
            Next
        End If
        Return ret_val
    End Function

    Public Shared Function GetPplIdFromCounter(ByVal m_counter As Long) As Long
        Dim ret_val As Long = costanti.m_InvalidId
        Dim sSelect As String = "SELECT ID FROM PRODUCTION_PLAN WHERE COUNTER = '" & m_counter & "'"
        Dim dt As DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing Then
            For Each dr As DataRow In dt.Rows
                ret_val = Long.Parse(dr.Item("ID").ToString)
                Exit For
            Next
        End If
        Return ret_val
    End Function

    ''' <summary>
    ''' Valido per i soli cicli controllati
    ''' </summary>
    ''' <param name="cycle_id"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetCounterFromCycleId(ByVal cycle_id As Integer) As Long
        Dim ret_val As Long = costanti.m_InvalidId
        Dim sSelect As String = "SELECT COUNTER FROM PRODUCTION_PLAN WHERE CYC_ID = '" & cycle_id & "'"
        Dim dt As DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing Then
            For Each dr As DataRow In dt.Rows
                ret_val = Long.Parse(dr.Item("COUNTER").ToString)
                Exit For
            Next
        End If
        Return ret_val
    End Function

    ''' <summary>
    ''' Ritorna il nome della tabella nel caso non sia esplicitamente passato in argomento
    ''' </summary>
    ''' <param name="s_pagename"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetTableName(ByVal s_pagename As String) As String
        Dim ret_val As String = s_pagename

        Select Case s_pagename
            Case "ALL_LOTS_TO_CELL", "VIEW_ALL_LOTS_TO_CELL"
                ret_val = "LOTS_TO_CELL"

            Case "ALL_JOB_TO_CEL"
                ret_val = "JOB_TO_CEL"

            Case "VIEW_RECIPES"
                ret_val = "RECIPES"

            Case "VIEW_PARCELS"
                ret_val = "PARCELS"

            Case "PROCESS_ANOMALIES", "SYSTEM_INFORMATIONS"
                ret_val = "SYSTEM_ANOMALIES"

            Case Else
                If s_pagename.StartsWith("CYCLE_") Then
                    ret_val = "PRODUCTION_PLAN"

                ElseIf s_pagename.StartsWith("RECIPE_") Then
                    If s_pagename <> "RECIPE_LOGS" AndAlso s_pagename <> "RECIPE_TO_PRODUCTS" Then
                        ret_val = "RECIPES"
                    End If
                ElseIf s_pagename.StartsWith("ANALYSIS_") Then
                    ret_val = "ANALYSIS"

                ElseIf s_pagename.StartsWith("OPERATION_") Then
                    ret_val = "OPERATIONS"

                ElseIf s_pagename.StartsWith("CONTRACT_") Then
                    ret_val = "CONTRACTS"

                End If
        End Select

        Return ret_val
    End Function

    ''' <summary>
    ''' Aggiunge al path i dati relativi al Filter Column Name
    ''' </summary>
    ''' <param name="m_root"></param>
    ''' <param name="scr"></param>
    ''' <param name="sPath"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function CombinePathFilterColumnsName(ByVal m_root As Control, ByVal scr As Screen, ByVal sPath As String) As String

        If scr.FilterColumnsName IsNot Nothing Then
            For Each p As FilterColumnName In scr.FilterColumnsName
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, p.FieldDB)
                If m_Control IsNot Nothing And TypeOf m_Control Is myTextBox Then
                    If Not sPath.Contains(p.FieldDB & "=") AndAlso CType(m_Control, myTextBox).Text <> String.Empty Then
                        sPath &= "&" & p.FieldDB & "=" & CType(m_Control, myTextBox).Text
                    End If
                End If
            Next
        End If

        Return sPath
    End Function

    Public Shared Function GetMetaRecipeFromRecId(ByVal m_IdRecipe As Long) As Long
        Dim ret_val As Long = m_InvalidId
        Dim sSelect As String = String.Empty
        Dim dt As DataTable = Nothing

        sSelect = "SELECT * FROM VIEW_RECIPES WHERE ID = '" & m_IdRecipe & "'"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            ret_val = Long.Parse(dt.Rows(0).Item("MTR_ID").ToString())
        End If

        Return ret_val
    End Function

    Public Shared Function CheckSourceBinProduct(ByVal scr As Screen, ByVal m_IdCell As Long, ByVal m_IdProduct As Long, ByRef m_StrError As String) As Boolean
        Return CheckBinProduct(scr, m_IdCell, m_IdProduct, m_StrError, True)
    End Function

    Public Shared Function CheckDestBinProduct(ByVal scr As Screen, ByVal m_IdCell As Long, ByVal m_IdProduct As Long, ByRef m_StrError As String) As Boolean
        Return CheckBinProduct(scr, m_IdCell, m_IdProduct, m_StrError, False)
    End Function

    ''' <summary>
    ''' Controlla se il prodotto in cella è lo stesso di quello passato come parametro.
    ''' </summary>
    ''' <param name="m_IdCell">ID della cella.</param>
    ''' <param name="m_IdProduct">ID del prodotto.</param>
    ''' <param name="m_StrError">Stringa contenente i messaggi di errore.</param>
    ''' <returns>
    '''  <c>true</c>se la cella non ha prodotto, oppure i prodotti coincidono, altrimenti <c>false</c>.
    ''' </returns>
    Private Shared Function CheckBinProduct(ByVal scr As Screen, ByVal m_IdCell As Long, ByVal m_IdProduct As Long, ByRef m_StrError As String, ByVal b_is_source_bin As Boolean) As Boolean
        Dim sSelect As String
        Dim dt As DataTable
        Dim bin_prod_list As New List(Of myCompositeObjects.BinProd)

        sSelect = "SELECT PRO_ID FROM CELLS WHERE ID = '" & m_IdCell & "'"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As DataRow In dt.Rows
            If dr.Item(0) IsNot DBNull.Value AndAlso Long.Parse(dr.Item(0).ToString) <> m_IdProduct Then
                'Non ho trovato nessuna associazione valida quindi il prodotto e la cella
                'non sono da considerarsi compatibili

                Dim bin_prod As New myCompositeObjects.BinProd(m_IdCell, CInt(dr.Item(0).ToString))

                bin_prod_list.Add(bin_prod)

                myErrorMessage.AppendCheckBinProductMsg(m_StrError, scr.Parent, m_IdProduct, bin_prod_list, b_is_source_bin)

                Return False
            End If
        Next

        Return True
    End Function

    ''' <summary>
    ''' Controlla se la cella contiene altri lotti.
    ''' </summary>
    ''' <param name="m_IdCell">ID della cella.</param>
    ''' <param name="m_StrError">Stringa contenente i messaggi di errore.</param>
    ''' <returns>
    '''  <c>true</c>se la cella non ha altri lotti al suo interno, altrimenti <c>false</c>.
    ''' </returns>
    Public Shared Function CheckBinLots(ByVal scr As Screen, ByVal m_IdCell As Long, ByRef m_StrError As String) As Boolean
        Dim sSelect As String
        Dim dt As DataTable
        Dim lot_list As New List(Of Integer)

        sSelect = "SELECT * FROM LOTS_TO_CELL WHERE CEL_ID = '" & m_IdCell & "'"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt.Rows.Count > 0 Then
            ' significa che ho almeno un lotto già presente in cella
            For Each dr As Data.DataRow In dt.Rows
                lot_list.Add(dr.Item("LO_ID"))
            Next

            myErrorMessage.AppendCheckBinLotsMsg(m_StrError, scr.Parent, m_IdCell, lot_list)
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' Controlla se il tempo di riposo per la cella è scaduto.
    ''' </summary>
    ''' <param name="m_IdCell">ID della cella.</param>
    ''' <param name="m_StrError">Stringa contenente i messaggi di errore.</param>
    ''' <returns>
    '''  <c>true</c>se la cella non ha altri lotti al suo interno, altrimenti <c>false</c>.
    ''' </returns>
    Public Shared Function CheckBinRestTime(ByVal scr As Screen, ByVal m_IdCell As Long, ByRef m_StrError As String) As Boolean
        Dim sSelect As String
        Dim dt As DataTable
        Dim lot_list As New List(Of Integer)

        sSelect = "SELECT * FROM JOB_TO_CEL WHERE CEL_ID = " & m_IdCell & " AND READY_DATE > " & UsersGUI.tools.PrepareDateForSQL(Now)
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt.Rows.Count > 0 Then
            ' significa che ho almeno un rest time non scaduto
            For Each dr As Data.DataRow In dt.Rows
                myErrorMessage.AppendCheckBinRestTimeMsg(m_StrError, scr.Parent, m_IdCell)
            Next

            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' Controlla se la cella è abilitata al carico
    ''' </summary>
    ''' <param name="m_IdCell">ID della cella.</param>
    ''' <param name="m_StrError">Stringa contenente i messaggi di errore.</param>
    ''' <returns>
    '''  <c>true</c>se la cella è abilitata al carico, altrimenti <c>false</c>.
    ''' </returns>
    Public Shared Function CheckBinLoadStatus(ByVal scr As Screen, ByVal m_IdCell As Long, ByRef m_StrError As String) As Boolean
        Dim sSelect As String
        Dim dt As DataTable
        Dim lot_list As New List(Of Integer)

        sSelect = "SELECT * FROM CELLS WHERE ID = " & m_IdCell
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As Data.DataRow In dt.Rows
            If (dr.Item("LOAD_ENABLE_STATUS").ToString.Trim = m_StringNo) Then
                myErrorMessage.AppendLoadNotEnabledMsg(m_StrError, scr.Parent, m_IdCell)

                Return False
            End If
        Next

        Return True
    End Function

    ''' <summary>
    ''' Controlla se la cella è abilitata allo scarico
    ''' </summary>
    ''' <param name="m_IdCell">ID della cella.</param>
    ''' <param name="m_StrError">Stringa contenente i messaggi di errore.</param>
    ''' <returns>
    '''  <c>true</c>se la cella è abilitata allo scarico, altrimenti <c>false</c>.
    ''' </returns>
    Public Shared Function CheckBinDownloadStatus(ByVal scr As Screen, ByVal m_IdCell As Long, ByRef m_StrError As String) As Boolean
        Dim sSelect As String
        Dim dt As DataTable
        Dim lot_list As New List(Of Integer)

        sSelect = "SELECT * FROM CELLS WHERE ID = " & m_IdCell
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As Data.DataRow In dt.Rows
            If (dr.Item("DOWNLOAD_ENABLE_STATUS").ToString.Trim = m_StringNo) Then
                myErrorMessage.AppendDownloadNotEnabledMsg(m_StrError, scr.Parent, m_IdCell)

                Return False
            End If
        Next

        Return True
    End Function

    Public Shared Function GetUserNameFromId(ByVal IdUser As Long) As String
        Dim sSelect As String = "SELECT * FROM SYSTEM_USERS WHERE ID = '" & IdUser & "'"
        Dim dt As DataTable

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Return dr.Item("NAME").ToString
            Next
        End If

        Return String.Empty
    End Function

    Public Shared Function GetProductNameFromId(ByVal IdProduct As Long) As String
        Dim sSelect As String = "SELECT * FROM PRODUCTS WHERE ID = '" & IdProduct & "'"
        Dim dt As DataTable

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Return dr.Item("NAME").ToString
            Next
        End If

        Return String.Empty
    End Function

    Public Shared Function GetLotNameFromId(ByVal IdLot As Long) As String
        Dim sSelect As String = "SELECT * FROM LOTS WHERE ID = '" & IdLot & "'"
        Dim dt As DataTable

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Return dr.Item("DESCRIPTOR").ToString
            Next
        End If

        Return String.Empty
    End Function

    Public Shared Function GetBinNameFromId(ByVal IdBin As Long) As String
        Dim sSelect As String = "SELECT * FROM CELLS WHERE ID = '" & IdBin & "'"
        Dim dt As DataTable

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Return dr.Item("DESCRIPTION").ToString
            Next
        End If

        Return String.Empty
    End Function

    Public Shared Function GetCustomerNameFromLotId(ByVal lo_id As Long) As String
        Dim ret_val As String = String.Empty
        Dim sSelect As String
        Dim dt As Data.DataTable

        sSelect = "SELECT CUS_NAME FROM VIEW_SHIPMENTS_CONFIRMED WHERE LO_ID = '" & lo_id & "'"
        dt = DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As Data.DataRow In dt.Rows
            ret_val = dr.Item(0).ToString
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' Ritorna tutti i parametri della web form inizializzati
    ''' </summary>
    ''' <param name="m_config"></param>
    ''' <param name="m_PageName"></param>
    ''' <param name="m_MenuName"></param>
    ''' <param name="m_CallerRequest"></param>
    ''' <param name="m_Control"></param>
    ''' <param name="m_Screen"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetWebParams(ByRef m_config As config,
                                        ByRef m_PageName As String,
                                        ByRef m_MenuName As String,
                                        ByRef m_CallerRequest As String,
                                        ByRef m_Control As String,
                                        ByRef m_Screen As Screen,
                                        ByRef m_TopMenuName As String) As Boolean

        'Config.....
        m_config = CType(Current.Application("Config"), config)
        If m_config Is Nothing Then
            Return False
        End If

        'PageName.....
        If Current.Request("pagename") Is Nothing Then
            Return False
        End If
        m_PageName = Current.Request("pagename").ToString

        'MenuName.....
        If Current.Request("menuname") Is Nothing Then
            Return False
        End If
        m_MenuName = Current.Request("menuname").ToString

        'Caller.....
        If Current.Request("Caller") IsNot Nothing Then
            m_CallerRequest = Current.Request("Caller").ToString
        End If

        'Control.....
        If Current.Request("control") Is Nothing Then
            Return False
        End If
        m_Control = Current.Request("control").ToString

        'Screen.....
        m_Screen = m_config.GetScreenByScreenName(m_PageName)
        If m_Screen.DBName = UsersGUI.costanti.m_InvalidScreen Then
            Return False
        End If

        'TopMenuName.....
        If Current.Request("topmenuname") Is Nothing Then
            ' Return False
            Return True ' mettere i topmenuname nel config
        End If
        m_TopMenuName = Current.Request("topmenuname").ToString

        Return True
    End Function

    ''' <summary>
    ''' Ritorna tutti i parametri della web form inizializzati senza lo screen nel caso
    ''' la pagina non ne preveda l'uso
    ''' </summary>
    ''' <param name="m_config"></param>
    ''' <param name="m_PageName"></param>
    ''' <param name="m_MenuName"></param>
    ''' <param name="m_CallerRequest"></param>
    ''' <param name="m_Control"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetWebParamsWithoutScreen(ByVal m_config As config,
                                        ByRef m_PageName As String,
                                        ByRef m_MenuName As String,
                                        ByRef m_CallerRequest As String,
                                        ByRef m_Control As String) As config

        'Config.....
        m_config = CType(Current.Application("Config"), config)
        If m_config Is Nothing Then
            Return Nothing
        End If

        'PageName.....
        If Current.Request("pagename") Is Nothing Then
            Return Nothing
        End If
        m_PageName = Current.Request("pagename").ToString

        'MenuName.....
        If Current.Request("menuname") Is Nothing Then
            Return Nothing
        End If
        m_MenuName = Current.Request("menuname").ToString

        'Caller.....
        If Current.Request("Caller") IsNot Nothing Then
            m_CallerRequest = Current.Request("Caller").ToString
        End If

        'Control.....
        If Current.Request("control") Is Nothing Then
            Return Nothing
        End If
        m_Control = Current.Request("control").ToString

        Return m_config
    End Function

    ''' <summary>
    ''' Restituisce L'IdGrainOrder dall'IntakeOperationId
    ''' </summary>
    ''' <param name="mIdIntake"></param>W
    ''' <returns></returns>
    ''' <remarks></remarks>

    Public Shared Function GetLabelFromJobToCel(ByVal m_Counter As Long, ByVal m_CelId As Long) As String
        Dim ret_val As Integer = m_IntegerZero
        Dim sSelect As String = "SELECT LABEL_TEXT FROM JOB_TO_CEL WHERE JOB_NUMBER = '" & m_Counter & "' AND CEL_ID = '" & m_CelId & "'"
        Dim dt As Data.DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt Is Nothing Then
            Return ret_val
        End If

        For Each dr As Data.DataRow In dt.Rows
            ret_val = Integer.Parse(dr.Item(0).ToString)
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' Restituisce il dato di capacità della cella passata come argomento
    ''' </summary>
    ''' <param name="m_CelId">Id Cella</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetBinCapacity(ByVal m_CelId As Long) As Double
        Dim ret_val As Double = m_InvalidDblValue
        Dim sSelect As String = "SELECT VOLUMIC_CAPACITY FROM CELLS WHERE ID = '" & m_CelId & "'"
        Dim dt As Data.DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt Is Nothing Then
            Return ret_val
        End If

        For Each dr As Data.DataRow In dt.Rows
            ret_val = Double.Parse(dr.Item(0).ToString)
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' Restituisce il tipo di visualizzazione del lotto
    ''' </summary>
    ''' <param name="m_StringModel">Model Tracing</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetModel(ByVal m_StringModel As String) As Integer
        Dim ret_val As Integer = m_IntegerZero
        Dim sSelect As String = "SELECT MODEL FROM TRACING_MODELS WHERE DESCRIPTION = '" & m_StringModel & "'"
        Dim dt As Data.DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        If dt Is Nothing Then
            Return ret_val
        End If

        For Each dr As Data.DataRow In dt.Rows
            ret_val = Integer.Parse(dr.Item(0).ToString)
        Next

        Return ret_val
    End Function

    ''' <summary>
    ''' In base al valore presente nella EnumPageNameCode dello screen passato come argomento
    ''' la funzione crea un oggetto relativo e lo compila per la procedura di salvataggio
    ''' </summary>
    ''' <param name="scr"></param>
    ''' <param name="root"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Sub SetObject(ByVal scr As Screen, ByVal root As Control, ByVal id As Long, Optional ByVal array_list As Generic.List(Of Object) = Nothing)
        Dim ctrl As Control
        Dim b_jump_out As Boolean = False
        Dim str_sql As String = String.Empty
        Dim dt As DataTable

        Try
            Select Case scr.EnumPageNameCode
                Case EnumPageName.ProductionPlan
                    Dim ppl As New ProductionPlan(scr, root, id)

                    '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                    ' salvo coerentemente lo stato dei controlli
                    ' se ho il check su ignore controls oppure nessun messaggio dalla AskUserConfirmDataCycle
                    ' (salvato nel field hidden HiddenAskUserConfirmDataCycle) salvo ControlsOK = "YES" altrimenti "NO"
                    ctrl = tools.FindControlRecursive(root, "IGNORE_CONTROLS")
                    If TypeOf ctrl Is myCheckBox Then

                        Dim chk_ignore As myCheckBox = CType(ctrl, myCheckBox)

                        If Not chk_ignore.Checked Then
                            ctrl = tools.FindControlRecursive(root, "HiddenAskUserConfirmDataCycle")

                            Dim confirm_msg As String = CType(ctrl, HiddenField).Value

                            If confirm_msg.Length > 0 Then
                                ppl.ControlsOK = m_StringNo
                            Else
                                ppl.ControlsOK = m_StringYes
                            End If
                        Else
                            ppl.ControlsOK = m_StringYes
                        End If
                    End If

                    '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                    ppl.Save()

                    ' posto una richiesta per forzare il ricalcolo dei controlli di ciclo da parte del c++
                    Dim req1 As New CommandInterface(EnumRequest.ForceCheckAllCyclesControls)
                    req1.PostUniqueRequest()

                    ' posto una richiesta per forzare la costruzione delle process recipe da parte del c++
                    Dim req2 As New CommandInterface(EnumRequest.ForceBuildAllProcessRecipes)
                    req2.PostUniqueRequest()

                    ctrl = tools.FindControlRecursive(root, "COUNTER")
                    If ctrl IsNot Nothing AndAlso TypeOf ctrl Is myTextBox Then
                        If ppl.Counter <> -1 Then
                            CType(ctrl, myTextBox).Text = ppl.Counter
                        Else
                            ' recupero il valore dall'opv
                            ppl.Counter = CType(ctrl, myTextBox).Text
                        End If
                    End If

                    str_sql = "SELECT * FROM META_CYCLE_PARAMS WHERE CYC_ID = '" & ppl.IdCycle & "' ORDER BY ID"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    For Each dr As DataRow In dt.Rows
                        Try
                            Dim opv As New OrderParamValue(scr, root)
                            ctrl = tools.FindControlRecursive(root, dr.Item("ASP_NAME").ToString)

                            If ctrl Is Nothing Then
                                For Each f As Field In scr.EditFields
                                    If Not b_jump_out Then
                                        If f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldRadioButton Then
                                            Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                                            If s_filter.Length > 1 Then
                                                If s_filter(0) = "CONFIG" Then
                                                    Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                                    Dim n As Integer = 0
                                                    Dim i As Integer = 0

                                                    For i = 1 To n_ripetizioni
                                                        ctrl = tools.FindControlRecursive(root, f.FieldName & "_" & i)

                                                        If UsersGUI.tools.GetValueFromControl(root, ctrl) <> 0 Then
                                                            opv.IdMetaCycleParam = Long.Parse(dr.Item("ID").ToString)
                                                            opv.IdProductionPlan = ppl.IdProductionPlan
                                                            opv.Value = i

                                                            b_jump_out = True
                                                            Exit For
                                                        End If
                                                    Next
                                                ElseIf s_filter(0) = "SQL" Then

                                                End If
                                            End If

                                        ElseIf f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldCalendar Then

                                            ctrl = tools.FindControlRecursive(root, f.FieldDb & "_DATE_DA")

                                            opv.IdMetaCycleParam = Long.Parse(dr.Item("ID").ToString)
                                            opv.IdProductionPlan = ppl.IdProductionPlan
                                            opv.Value = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                        Else
                                            opv.IdMetaCycleParam = Long.Parse(dr.Item("ID").ToString)
                                            opv.IdProductionPlan = ppl.IdProductionPlan
                                            opv.Value = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                        End If
                                    Else
                                        b_jump_out = False

                                        Exit For
                                    End If
                                Next
                            Else
                                opv.IdMetaCycleParam = Long.Parse(dr.Item("ID").ToString)
                                opv.IdProductionPlan = ppl.IdProductionPlan
                                opv.Value = UsersGUI.tools.GetValueFromControl(root, ctrl)
                            End If

                            opv.Save()
                        Catch ex As myException.myException
                            Throw ex
                        End Try
                    Next

                Case EnumPageName.RecipeParameters
                    Dim rec As New Recipe(id)

                    str_sql = "SELECT * FROM META_RECIPE_PARAMS WHERE MTR_ID = '" & rec.IdMetaRecipe & "' ORDER BY ID"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    For Each dr As DataRow In dt.Rows

                        Dim rp As New RecipeParam()

                        ctrl = tools.FindControlRecursive(root, dr.Item("ASP_NAME").ToString)

                        If ctrl Is Nothing Then
                            For Each f As Field In scr.EditFields
                                If Not b_jump_out Then
                                    If f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldRadioButton Then
                                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                                        If s_filter.Length > 1 Then
                                            If s_filter(0) = "CONFIG" Then
                                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                                Dim n As Integer = 0
                                                Dim i As Integer = 0

                                                For i = 1 To n_ripetizioni
                                                    ctrl = tools.FindControlRecursive(root, f.FieldName & "_" & i)

                                                    If UsersGUI.tools.GetValueFromControl(root, ctrl) <> 0 Then
                                                        rp.IdRecipe = id
                                                        rp.IdMetaRecipeParam = Long.Parse(dr.Item("ID").ToString)
                                                        rp.FieldValue = i

                                                        b_jump_out = True
                                                        Exit For
                                                    End If
                                                Next
                                            ElseIf s_filter(0) = "SQL" Then

                                            End If

                                        End If
                                    Else
                                        rp.IdRecipe = id
                                        rp.IdMetaRecipeParam = Long.Parse(dr.Item("ID").ToString)
                                        rp.FieldValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                    End If
                                Else
                                    b_jump_out = False

                                    Exit For
                                End If
                            Next
                        Else
                            rp.IdRecipe = id
                            rp.IdMetaRecipeParam = Long.Parse(dr.Item("ID").ToString)
                            rp.FieldValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                        End If

                        rec.SetActualRecipeParams(rp)
                    Next

                    rec.Save()

                    ' posto una richiesta per forzare la costruzione delle process recipe da parte del c++
                    Dim req As New CommandInterface(EnumRequest.ForceBuildAllProcessRecipes)
                    req.PostUniqueRequest()

                Case EnumPageName.ProductTypes
                    Dim obj As New ProductType(scr, root, id)
                    obj.Save()

                Case EnumPageName.Products
                    Dim obj As New Product(scr, root, id)
                    obj.Save()

                    Dim myRequest As New CommandInterface(EnumRequest.SendHectolitricWeight)
                    myRequest.PostUniqueRequest()

                Case EnumPageName.Recipes
                    Dim user_id As Long

                    If Current.Session("UserID") IsNot Nothing AndAlso IsNumeric(Current.Session("UserID").ToString) Then
                        user_id = Long.Parse(Current.Session("UserID").ToString)
                    Else
                        user_id = costanti.m_SystemIdUser
                    End If

                    Dim obj As New Recipe(scr, root, id, user_id)
                    obj.Save()

                Case EnumPageName.SystemGroups
                    Dim obj As New SystemGroup(scr, root, id)
                    obj.Save()

                Case EnumPageName.SystemUsers
                    Dim obj As New SystemUser(scr, root, id)
                    obj.Save()

                Case EnumPageName.SystemUsersGroups
                    'Lo faccio qui perchè essendo una checklist box devo creare n. oggetti quanti sono quelli selezionati
                    Dim user_id As Long = id

                    For Each s As Field In scr.EditFields
                        If s.GetFieldType <> EnumFieldType.FieldAuto Then
                            Dim m_Control As Control
                            m_Control = tools.FindControlRecursive(root, s.FieldDb)
                            If TypeOf m_Control Is myDropDownList Then
                                If s.PropertyName = "IdUser" Then
                                    user_id = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                                End If

                            End If
                        End If
                    Next
                    For Each s As Field In scr.EditFields
                        If s.GetFieldType <> EnumFieldType.FieldAuto Then
                            Dim m_Control As Control
                            m_Control = tools.FindControlRecursive(root, s.FieldDb)
                            If TypeOf m_Control Is myCheckBoxList Then
                                Dim chkList As myCheckBoxList = CType(m_Control, myCheckBoxList)
                                For Each lst As ListItem In chkList.Items
                                    Try
                                        Dim sau As New SystemUserGroup
                                        sau.IdUser = user_id
                                        sau.IdGroup = Integer.Parse(lst.Value)
                                        If lst.Selected Then
                                            sau.Save()
                                        Else
                                            sau.Delete()
                                        End If
                                    Catch ex As Exception
                                        Throw ex
                                    End Try
                                Next
                            End If
                        End If
                    Next

                Case EnumPageName.SystemGroupsRights
                    Dim group_id As Long = id
                    Dim page_id As Long
                    Dim sal_id As Integer = Integer.Parse(id.ToString)

                    For Each s As Field In scr.EditFields
                        If s.GetFieldType <> EnumFieldType.FieldAuto Then
                            ctrl = tools.FindControlRecursive(root, s.FieldDb)
                            If TypeOf ctrl Is myDropDownList Then
                                If s.PropertyName = "IdGroup" Then
                                    group_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                ElseIf s.PropertyName = "IdPage" Then
                                    page_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                End If
                            End If
                            If TypeOf ctrl Is myCheckBoxList Then
                                If s.PropertyName = "IdSal" Then
                                    Dim chkList As myCheckBoxList = CType(ctrl, myCheckBoxList)
                                    For Each lst As ListItem In chkList.Items
                                        Try

                                            Dim r As New SystemGroupsRights
                                            r.IdGroup = group_id
                                            r.IdSal = lst.Value
                                            r.IdPage = page_id

                                            If lst.Selected Then
                                                r.Id = UsersGUI.tools.GetNextId("SYSTEM_GROUPS_RIGHTS", "ID")
                                                r.Save()
                                            Else
                                                r.Delete()
                                            End If
                                        Catch ex As Exception
                                            Throw ex
                                        End Try
                                    Next
                                End If
                            End If
                        End If
                    Next

                Case EnumPageName.EquipmentsModels
                    Dim obj As New EquipmentModels(scr, root, id)
                    obj.Save()

                Case EnumPageName.Equipments
                    Dim obj As New Equipments(scr, root, id)
                    obj.Save()

                Case EnumPageName.MaintenanceProcedures
                    Dim obj As New MaintenanceProcedures(scr, root, id)
                    obj.Save()

                Case EnumPageName.MaintProcAssignem
                    Dim obj As New MaintProcAssignem(scr, root, id)
                    obj.Save()

                Case EnumPageName.EquipmentsMainData
                    Dim obj As New Equipments(scr, root, id)
                    obj.SaveOnlyRatio()

                Case EnumPageName.MaintenancePlanning
                    Dim obj As New MaintenancePlanning(scr, root, id)
                    obj.Save()

                Case EnumPageName.ProcedureDoc
                    Dim obj As New ProcedureDoc(scr, root, id)
                    obj.Save()

                Case EnumPageName.Cells
                    Dim obj As New Cells(scr, root, id)
                    obj.Save()

                Case EnumPageName.ProductsToCells
                    Dim cell_start As Integer = Integer.Parse(CType(UsersGUI.tools.FindControlRecursive(root, "CEL_ID_FROM"), myDropDownList).SelectedValue.ToString)
                    Dim cell_stop As Integer = Integer.Parse(CType(UsersGUI.tools.FindControlRecursive(root, "CEL_ID_TO"), myDropDownList).SelectedValue.ToString)

                    For cell_start = cell_start To cell_stop
                        Try
                            Dim ptc As New ProductsToCells(scr, root, id)
                            ptc.IdCell = cell_start
                            ptc.Save()
                        Catch ex As myException.myException
                            Throw ex
                        End Try
                    Next

                Case EnumPageName.Customers
                    Dim obj As New Customer(scr, root, id)
                    obj.Save()

                Case EnumPageName.Suppliers
                    Dim obj As New Supplier(scr, root, id)
                    obj.Save()

                Case EnumPageName.Carriers
                    Dim obj As New Carrier(scr, root, id)
                    obj.Save()

                Case EnumPageName.Parcels
                    Dim obj As New Parcel(scr, root, id)
                    obj.Save()

                Case EnumPageName.StockManualCorrReq
                    Dim user_id As Long

                    If Current.Session("UserID") IsNot Nothing AndAlso IsNumeric(Current.Session("UserID").ToString) Then
                        user_id = Long.Parse(Current.Session("UserID").ToString)
                    Else
                        user_id = costanti.m_SystemIdUser
                    End If

                    Dim obj As New StockManualCorrReq(scr, root, id, user_id)
                    obj.Save()

                Case EnumPageName.JobToCell
                    Dim obj As New JobToCell(scr, root, id)

                    If array_list IsNot Nothing AndAlso array_list.Count > 0 Then
                        obj.NewReadyDate = CType(array_list.Item(0), DateTime)
                    End If

                    obj.Save()

                Case EnumPageName.ConfirmFlowLogs, EnumPageName.FlowLogsToParcel666, EnumPageName.FlowLogsToParcel999
                    Dim obj As New ConfirmFlowLog(scr, root, id)
                    obj.Save()

                Case EnumPageName.EquipmentDoc
                    Dim obj As New EquipmentDocs(scr, root, id)
                    obj.Save()

                Case EnumPageName.ViewYieldsPrintouts
                    Dim obj As New YieldsPrintouts(scr, root, id)
                    obj.Save()

                Case EnumPageName.ProductPerScale
                    Dim obj As New ProductPerScale(scr, root, id)
                    obj.Save()

                Case EnumPageName.BatchSizePerScale
                    Dim obj As New BatchSizePerScale(scr, root, id)
                    obj.Save()

                Case EnumPageName.AllowedBatchSizePerScale
                    Dim obj As New AllowedBatchSizePerScale(scr, root, id)
                    obj.Save()

                Case EnumPageName.Analysis
                    Dim obj As New Analysis(scr, root, id)
                    obj.Save()

                Case EnumPageName.AnalysisParams
                    Dim analysis As New Analysis(id)

                    str_sql = "SELECT * FROM META_ANALYSIS_PARAMS WHERE MTA_ID = '" & analysis.IdMetaAnalysis & "' ORDER BY ID"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    For Each dr As DataRow In dt.Rows

                        Dim ap As New AnalysisParamValue()
                        ctrl = tools.FindControlRecursive(root, dr.Item("ASP_NAME").ToString)

                        If ctrl Is Nothing Then
                            For Each f As Field In scr.EditFields
                                If Not b_jump_out Then
                                    If f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldRadioButton Then
                                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                                        If s_filter.Length > 1 Then
                                            If s_filter(0) = "CONFIG" Then
                                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                                Dim n As Integer = 0
                                                Dim i As Integer = 0

                                                For i = 1 To n_ripetizioni
                                                    ctrl = tools.FindControlRecursive(root, f.FieldName & "_" & i)

                                                    If UsersGUI.tools.GetValueFromControl(root, ctrl) <> 0 Then
                                                        ap.IdAnalysis = analysis.Id
                                                        ap.IdMetaAnalysisParam = Long.Parse(dr.Item("ID").ToString)
                                                        ap.ParameterValue = i

                                                        b_jump_out = True
                                                        Exit For
                                                    End If
                                                Next
                                            ElseIf s_filter(0) = "SQL" Then

                                            End If
                                        End If

                                    ElseIf f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldCalendar Then

                                        ctrl = tools.FindControlRecursive(root, f.FieldDb & "_DATE_DA")

                                        ap.IdAnalysis = id
                                        ap.IdMetaAnalysisParam = Long.Parse(dr.Item("ID").ToString)
                                        ap.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                    Else
                                        ap.IdAnalysis = analysis.Id
                                        ap.IdMetaAnalysisParam = Long.Parse(dr.Item("ID").ToString)
                                        ap.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                    End If
                                Else
                                    b_jump_out = False

                                    Exit For
                                End If
                            Next
                        Else
                            ap.IdAnalysis = analysis.Id
                            ap.IdMetaAnalysisParam = Long.Parse(dr.Item("ID").ToString)
                            ap.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                        End If

                        ap.Save()
                    Next

                    analysis.Save()

                Case EnumPageName.Operations
                    Dim obj As New Operation(scr, root, id)
                    obj.Save()

                Case EnumPageName.OperationParams
                    Dim operation As New Operation(id)

                    str_sql = "SELECT * FROM META_OPERATION_PARAMS WHERE MTO_ID = '" & operation.IdMetaOperation & "' ORDER BY ID"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    For Each dr As DataRow In dt.Rows

                        Dim op As New OperationParamValue()
                        ctrl = tools.FindControlRecursive(root, dr.Item("ASP_NAME").ToString)

                        If ctrl Is Nothing Then
                            For Each f As Field In scr.EditFields
                                If Not b_jump_out Then
                                    If f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldRadioButton Then
                                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                                        If s_filter.Length > 1 Then
                                            If s_filter(0) = "CONFIG" Then
                                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                                Dim n As Integer = 0
                                                Dim i As Integer = 0

                                                For i = 1 To n_ripetizioni
                                                    ctrl = tools.FindControlRecursive(root, f.FieldName & "_" & i)

                                                    If UsersGUI.tools.GetValueFromControl(root, ctrl) <> 0 Then
                                                        op.IdOperation = id
                                                        op.IdMetaOperationParam = Long.Parse(dr.Item("ID").ToString)
                                                        op.ParameterValue = i

                                                        b_jump_out = True
                                                        Exit For
                                                    End If
                                                Next
                                            ElseIf s_filter(0) = "SQL" Then

                                            End If
                                        End If

                                    ElseIf f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldCalendar Then

                                        ctrl = tools.FindControlRecursive(root, f.FieldDb & "_DATE_DA")

                                        op.IdOperation = id
                                        op.IdMetaOperationParam = Long.Parse(dr.Item("ID").ToString)
                                        op.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                    Else
                                        op.IdOperation = id
                                        op.IdMetaOperationParam = Long.Parse(dr.Item("ID").ToString)
                                        op.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                    End If
                                Else
                                    b_jump_out = False

                                    Exit For
                                End If
                            Next
                        Else
                            op.IdOperation = id
                            op.IdMetaOperationParam = Long.Parse(dr.Item("ID").ToString)
                            op.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                        End If

                        op.Save()
                    Next

                    operation.Save()

                Case EnumPageName.Contracts
                    Dim obj As New Contract(scr, root, id)
                    obj.Save()

                Case EnumPageName.ContractParams
                    Dim contract As New Contract(id)

                    str_sql = "SELECT * FROM META_CONTRACT_PARAMS WHERE MTC_ID = '" & contract.IdMetaContract & "' ORDER BY ID"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    For Each dr As DataRow In dt.Rows

                        Dim cp As New ContractParamValue()
                        ctrl = tools.FindControlRecursive(root, dr.Item("ASP_NAME").ToString)

                        If ctrl Is Nothing Then
                            For Each f As Field In scr.EditFields
                                If Not b_jump_out Then
                                    If f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldRadioButton Then
                                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                                        If s_filter.Length > 1 Then
                                            If s_filter(0) = "CONFIG" Then
                                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                                Dim n As Integer = 0
                                                Dim i As Integer = 0

                                                For i = 1 To n_ripetizioni
                                                    ctrl = tools.FindControlRecursive(root, f.FieldName & "_" & i)

                                                    If UsersGUI.tools.GetValueFromControl(root, ctrl) <> 0 Then
                                                        cp.IdContract = id
                                                        cp.IdMetaContractParam = Long.Parse(dr.Item("ID").ToString)
                                                        cp.ParameterValue = i

                                                        b_jump_out = True
                                                        Exit For
                                                    End If
                                                Next
                                            ElseIf s_filter(0) = "SQL" Then

                                            End If
                                        End If

                                    ElseIf f.FieldDb = dr.Item("ASP_NAME").ToString AndAlso f.GetFieldType = EnumFieldType.FieldCalendar Then

                                        ctrl = tools.FindControlRecursive(root, f.FieldDb & "_DATE_DA")

                                        cp.IdContract = id
                                        cp.IdMetaContractParam = Long.Parse(dr.Item("ID").ToString)
                                        cp.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                    Else
                                        cp.IdContract = id
                                        cp.IdMetaContractParam = Long.Parse(dr.Item("ID").ToString)
                                        cp.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                                    End If
                                Else
                                    b_jump_out = False

                                    Exit For
                                End If
                            Next
                        Else
                            cp.IdContract = id
                            cp.IdMetaContractParam = Long.Parse(dr.Item("ID").ToString)
                            cp.ParameterValue = UsersGUI.tools.GetValueFromControl(root, ctrl)
                        End If

                        cp.Save()
                    Next

                    contract.Save()

                Case EnumPageName.RecipeToProducts
                    Dim obj As New RecipeToProducts(scr, root, id)
                    obj.Save()

                Case EnumPageName.TaskScheduler
                    Dim obj As New TaskScheduler(scr, root, id)
                    obj.Save()

                Case EnumPageName.TaskAutomations
                    Dim obj As New TaskAutomations(scr, root, id)
                    obj.Save()

                Case Else
                    Dim msg As String = "Case non gestito"
                    Dim myExc As New myException.myException("SetObject", System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
            End Select
        Catch ex As myException.myException
            Throw ex
        End Try
    End Sub

    Public Shared Function PopolaPeriodi(ByVal cmb As DropDownList, ByVal m_config As UsersGUI.config) As DropDownList
        Dim i As Integer = 0
        Dim dt As New Data.DataTable
        dt.Columns.Add("VALUE")
        dt.Columns.Add("NAME")

        Dim dr As Data.DataRow

        dr = dt.NewRow
        dr("VALUE") = "useCalendars"
        dr("NAME") = String.Empty
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "currhour"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_HOUR").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "currshift"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_SHIFT").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "currday"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_DAY").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "currweek"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_WEEK").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "currmonth"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_CURR_MONTH").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "lasthour"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_HOUR").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "lastshift"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_SHIFT").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "lastday"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_DAY").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "lastweek"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_WEEK").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "lastmonth"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_MONTH").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("VALUE") = "AllData"
        dr("NAME") = m_config.GetEntryByKeyName("YIELDS_PERIOD_ALL").GetValue
        dt.Rows.Add(dr)

        cmb.DataSource = dt
        cmb.DataTextField = "NAME"
        cmb.DataValueField = "VALUE"

        Return cmb
    End Function

    Public Shared Function PopolaJob(ByVal cmb As DropDownList) As DropDownList
        Dim sSelect As String = String.Empty
        Dim act_job As Integer
        Dim dt_jobs As DataTable

        Dim dt As New Data.DataTable
        dt.Columns.Add("JOB_ID")
        dt.Columns.Add("JOB_NAME")

        Dim dr As Data.DataRow

        dr = dt.NewRow
        dr("JOB_ID") = 0
        dr("JOB_NAME") = String.Empty
        dt.Rows.Add(dr)

        ' controllo l'esistenza di certe tabelle (che differiscono tra @mill e sistema rese)
        If WebDataBaseLayer.DataBase.TableExist("PRODUCTION_PLAN") AndAlso
            WebDataBaseLayer.DataBase.TableExist("RECIPES") AndAlso
            WebDataBaseLayer.DataBase.TableExist("PRODUCTION_REPORTS") Then

            ' Act Job
            act_job = CInt(GetParameter("ACTIVE_MILL_JOB_COUNTER"))

            If act_job <> 0 Then
                sSelect = " SELECT COUNTER AS JOB_ID, START_DATE, RECIPES.DESCRIPTION AS RECIPE_DESCRIPTION FROM PRODUCTION_PLAN INNER JOIN RECIPES ON PRODUCTION_PLAN.REC_ID = RECIPES.ID " &
                            "WHERE COUNTER ='" & act_job & "' " &
                            "UNION "
            End If

            ' Archived jobs
            sSelect &= "SELECT DISTINCT YIELDS_DATA.JOB_ID, PRODUCTION_REPORTS.START_DATE, PRODUCTION_REPORTS.RECIPE_DESCRIPTION " &
                                    "FROM YIELDS_DATA INNER JOIN PRODUCTION_REPORTS ON JOB_ID = COUNTER WHERE JOB_ID IS NOT NULL ORDER BY START_DATE DESC"
            dt_jobs = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt_jobs.Rows.Count > 0 Then
                For Each dr_jobs As DataRow In dt_jobs.Rows
                    dr = dt.NewRow
                    dr("JOB_ID") = dr_jobs.Item(0).ToString
                    dr("JOB_NAME") = Long.Parse(dr_jobs.Item(0).ToString) & " - " & Date.Parse(dr_jobs.Item(1).ToString).Date & " - " & dr_jobs.Item(2).ToString
                    dt.Rows.Add(dr)
                Next
            End If
        End If

        cmb.DataSource = dt
        cmb.DataTextField = "JOB_NAME"
        cmb.DataValueField = "JOB_ID"

        Return cmb
    End Function

    ' ritorna il valore del parametro nella stringa sql_where usato prevalentemente dalle extra columns del config
    Public Shared Function GetQueryParamFromWhereStatement(ByVal sql_where As String, ByVal param As String) As String
        Dim temp_str As String = String.Empty

        temp_str = sql_where.Substring(sql_where.IndexOf(param))
        Try
            ' rimuovo eventuali altri parametri in coda
            temp_str = temp_str.Substring(0, temp_str.IndexOf(" "))
        Catch ex As Exception

        End Try

        temp_str = temp_str.Trim(" ")

        temp_str = temp_str.Substring(param.Length + 1)

        temp_str = temp_str.Trim("'")

        Return temp_str
    End Function

#Region "CloneCycle"

    Public Shared Function CloneCycle(ByVal ppl_id As Long)
        Dim str_sql As String = String.Empty
        Dim new_ppl_id As Long = costanti.m_InvalidId

        str_sql = "SELECT * FROM PRODUCTION_PLAN WHERE ID='" & ppl_id & "'"
        new_ppl_id = CloneProductionPlanRs(str_sql, "PRODUCTION_PLAN")

        str_sql = "SELECT * FROM ORDER_PARAM_VALUES WHERE PPL_ID='" & ppl_id & "'"
        CloneOPVsRs(str_sql, new_ppl_id, "ORDER_PARAM_VALUES")

        Return new_ppl_id
    End Function

    Private Shared Function CloneProductionPlanRs(ByRef strSQL As String, ByRef tableName As String) As Long
        Dim dt As Data.DataTable
        Dim ret_val As Long = costanti.m_InvalidId
        Dim pp As New ProductionPlan
        pp.IdProductionPlan = m_InvalidId

        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        For Each dc As Data.DataColumn In dt.Columns
            Select Case UCase(dc.ColumnName)
                Case "ID"
                    pp.IdProductionPlan = UsersGUI.tools.GetNextId(tableName, "ID")
                    ret_val = pp.IdProductionPlan
                Case "COUNTER"
                    pp.Counter = UsersGUI.tools.GetNextId(tableName, "COUNTER")
                Case "LINE_POSITION"
                    pp.LinePosition = UsersGUI.tools.GetNextId(tableName, "LINE_POSITION")
                Case "START_DATE"
                    pp.StartDate = costanti.m_InvalidDateTime
                Case "STOP_DATE"
                    pp.StopDate = costanti.m_InvalidDateTime
                Case "ORDER_STATUS"
                    pp.OrderStatus = costanti.m_StatusLocked
                Case "PRODUCED_AMOUNT"
                    pp.ProducedAmount = costanti.m_DoubleZero
                Case "REC_ID"
                    Try
                        pp.IdRecipe = Long.Parse(dt.Rows(0).Item("REC_ID").ToString)
                    Catch ex As Exception
                        pp.IdRecipe = m_InvalidId
                    End Try
                Case "RECIPE_LOG_ID"
                    Try
                        pp.IdRecipeLog = Long.Parse(dt.Rows(0).Item("RECIPE_LOG_ID").ToString)
                    Catch ex As Exception
                        pp.IdRecipeLog = m_InvalidId
                    End Try
                Case "CYC_ID"
                    Try
                        pp.IdCycle = Integer.Parse(dt.Rows(0).Item("CYC_ID").ToString)
                    Catch ex As Exception
                        pp.IdCycle = m_IntegerZero
                    End Try
                Case "CONTROLS_OK"
                    pp.ControlsOK = dt.Rows(0).Item("CONTROLS_OK").ToString
                Case Else
                    Current.Response.Write(dc.ColumnName & "<br>")
            End Select
        Next
        If pp.IdProductionPlan <> m_InvalidId Then
            pp.Save()
        End If
        Return ret_val
    End Function

    Private Shared Sub CloneOPVsRs(ByRef strSQL As String, ByRef newPplID As Long, ByRef tableName As String)
        Dim dt As Data.DataTable
        Dim opv As OrderParamValue

        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        For Each dr As Data.DataRow In dt.Rows
            opv = New OrderParamValue
            opv.IdOrderParamValue = m_InvalidId

            For Each dc As Data.DataColumn In dt.Columns
                Select Case UCase(dc.ColumnName)
                    Case "ID"
                        opv.IdOrderParamValue = UsersGUI.tools.GetNextId(tableName, "ID")
                    Case "PPL_ID"
                        opv.IdProductionPlan = newPplID
                    Case "PARAMETER_VALUE"
                        opv.Value = dr.Item("PARAMETER_VALUE").ToString
                    Case "MCP_ID"
                        Try
                            opv.IdMetaCycleParam = Long.Parse(dr.Item("MCP_ID").ToString)
                        Catch ex As Exception
                            opv.IdMetaCycleParam = m_InvalidId
                        End Try
                End Select
            Next

            If opv.IdOrderParamValue <> m_InvalidId Then
                opv.Save()
            End If
        Next

    End Sub

#End Region

#Region "CloneRecipe"

    Public Shared Sub CloneRecipe(ByVal rec_id As Long, ByVal new_rec_name As String)
        Dim str_sql As String = String.Empty
        Dim new_rec_id As Long = costanti.m_InvalidId
        Dim new_recipe_log_id As Long = costanti.m_InvalidId
        Dim dt As Data.DataTable

        str_sql = "SELECT * FROM RECIPES WHERE ID = '" & rec_id & "'"
        new_rec_id = CloneRecordRecipe(str_sql, "RECIPES", new_rec_name)

        str_sql = "SELECT MAX(ID) FROM RECIPE_LOGS WHERE REC_ID = '" & new_rec_id & "'"
        dt = DataBase.ExecuteSQL_DataTable(str_sql, False)

        For Each dr As Data.DataRow In dt.Rows
            new_recipe_log_id = Integer.Parse(dr(0).ToString)
        Next

        str_sql = "SELECT * FROM RECIPE_PARAM_VALUES WHERE REC_ID = '" & rec_id & "'"
        CloneRecordRecipeOPVs(str_sql, new_rec_id, new_recipe_log_id, "RECIPE_PARAM_VALUES")

    End Sub

    Private Shared Function CloneRecordRecipe(ByRef strSQL As String, ByRef tableName As String, ByVal new_rec_name As String) As Long
        Dim dt As Data.DataTable
        Dim ret_val As Long = costanti.m_InvalidId
        Dim rc As New Recipe
        rc.IdRecipe = costanti.m_InvalidId

        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        For Each dc As Data.DataColumn In dt.Columns
            Select Case UCase(dc.ColumnName)
                Case "ID"
                    rc.IdRecipe = UsersGUI.tools.GetNextId(tableName, "ID")
                    ret_val = rc.IdRecipe
                Case "DESCRIPTION"
                    rc.Description = new_rec_name.Trim
                Case "CREATION_DATE"
                    rc.CreationDate = m_InvalidDateTime
                Case "ID_USER"
                    Try
                        rc.IdUser = Long.Parse(Current.Session("UserID").ToString)
                    Catch ex As Exception
                        rc.IdUser = Long.Parse(dt.Rows(0).Item("ID_USER").ToString)
                    End Try
                Case "MTR_ID"
                    Try
                        rc.IdMetaRecipe = Long.Parse(dt.Rows(0).Item("MTR_ID").ToString)
                    Catch ex As Exception
                        rc.IdMetaRecipe = m_InvalidId
                    End Try
                Case "IS_OBSOLETE"
                    rc.IsObsolete = dt.Rows(0).Item("IS_OBSOLETE").ToString
            End Select
        Next
        If rc.IdRecipe <> m_InvalidId Then
            rc.Save()
        End If
        Return ret_val
    End Function

    Private Shared Sub CloneRecordRecipeOPVs(ByRef strSQL As String, ByRef newRecId As Long, ByRef newRecipeLogId As Long, ByRef tableName As String)
        Dim dt As Data.DataTable = Nothing
        Dim opv As RecipeParam = Nothing

        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        For Each dr As Data.DataRow In dt.Rows
            opv = New RecipeParam
            opv.Id = m_InvalidId

            For Each dc As Data.DataColumn In dt.Columns
                Select Case UCase(dc.ColumnName)
                    Case "ID"
                        opv.Id = UsersGUI.tools.GetNextId(tableName, "ID")
                    Case "MRP_ID"
                        opv.IdMetaRecipeParam = Long.Parse(dr.Item("MRP_ID").ToString)
                    Case "FIELD_VALUE"
                        opv.FieldValue = dr.Item("FIELD_VALUE").ToString
                    Case "REC_ID"
                        opv.IdRecipe = newRecId
                End Select
            Next

            If opv.Id <> m_InvalidId Then
                opv.Save()
                opv.SaveArchive(newRecipeLogId)
            End If
        Next

    End Sub

#End Region

End Class