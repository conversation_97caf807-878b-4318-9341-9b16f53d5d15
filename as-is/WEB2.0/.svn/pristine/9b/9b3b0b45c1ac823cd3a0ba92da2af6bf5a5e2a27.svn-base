﻿Option Strict On

Imports System.Web.UI.WebControls

Public Class myBoundField
    Inherits BoundField

    Private m_Text As String

    Public Property Text() As String
        Get
            Return m_Text
        End Get
        Set(ByVal value As String)
            m_Text = value
        End Set
    End Property

    ''' <summary>
    ''' Returns a new instance of the <see cref="T:System.Web.UI.WebControls.BoundField"></see> class.
    ''' </summary>
    ''' <returns>
    ''' A new instance of <see cref="T:System.Web.UI.WebControls.BoundField"></see>.
    ''' </returns>
    Protected Overloads Overrides Function CreateField() As DataControlField
        Return New myBoundField()
    End Function

    Public Overloads Overrides Sub InitializeCell(ByVal cell As DataControlFieldCell, ByVal cellType As DataControlCellType, ByVal rowState As DataControlRowState, ByVal rowIndex As Integer)
        MyBase.InitializeCell(cell, cellType, rowState, rowIndex)
        If cellType <> DataControlCellType.DataCell Then
            Exit Sub
        End If

        If (rowState = DataControlRowState.Normal OrElse rowState = DataControlRowState.Alternate) AndAlso Me.Visible Then
            If cell Is Nothing Then
                cell = New DataControlFieldCell(Me)
            End If

            Dim lbl As New Label
            cell.Controls.Add(lbl)

            Dim LabelInCell As Label = TryCast(cell.Controls(0), Label)

            If LabelInCell Is Nothing Then
                Exit Sub
            End If

            LabelInCell.Text = Me.m_Text

        End If
    End Sub

End Class