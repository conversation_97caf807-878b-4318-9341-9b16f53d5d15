﻿Imports System.Web.UI.WebControls
Imports WebDataBaseLayer

Public Class myDropDownList
    Inherits DropDownList

    Private m_Tag As Object
    Private m_FieldObject As Field

    Public Property FieldObject() As Field
        Get
            Return m_FieldObject
        End Get
        Set(ByVal value As Field)
            m_FieldObject = value
        End Set
    End Property

    Public Property Tag() As Object
        Get
            Return m_Tag
        End Get
        Set(ByVal value As Object)
            Me.m_Tag = value
        End Set
    End Property

End Class

Public Class myList
    Private m_IsReadOnly As Boolean
    Private m_OrderBy As String
    Private m_Where As String
    Private m_SelectColumns As String
    Private m_ValueColumn As String
    Private m_From As String
    Private m_AddNull As Boolean
    Private m_Parent As Object

    Public Sub New()

    End Sub

    Public Property Parent() As Object
        Get
            Return Me.m_Parent
        End Get
        Set(ByVal value As Object)
            Me.m_Parent = value
        End Set
    End Property

    Public Property IsReadOnly() As Boolean
        Get
            Return Me.m_IsReadOnly
        End Get
        Set(ByVal value As Boolean)
            Me.m_IsReadOnly = value
        End Set
    End Property

    Public Property AddNull() As Boolean
        Get
            Return Me.m_AddNull
        End Get
        Set(ByVal value As Boolean)
            Me.m_AddNull = value
        End Set
    End Property

    Public Property From() As String
        Get
            Return Me.m_From
        End Get
        Set(ByVal value As String)
            Me.m_From = value
        End Set
    End Property

    Public Property ValueColumn() As String
        Get
            Return Me.m_ValueColumn
        End Get
        Set(ByVal value As String)
            Me.m_ValueColumn = value
        End Set
    End Property

    Public Property SelectColumns() As String
        Get
            Return Me.m_SelectColumns
        End Get
        Set(ByVal value As String)
            Me.m_SelectColumns = value
        End Set
    End Property

    Public Property OrderBy() As String
        Get
            Return Me.m_OrderBy
        End Get
        Set(ByVal value As String)
            Me.m_OrderBy = value
        End Set
    End Property

    Public Property Where() As String
        Get
            Return Me.m_Where
        End Get
        Set(ByVal value As String)
            Me.m_Where = value
        End Set
    End Property

    ''' <summary>
    ''' Disegna il myDropDownList In base ai dati passati in argomento
    ''' </summary>
    ''' <param name="FieldName"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function DrawObject(ByVal obj As Field, ByVal FieldName As String, ByVal value As String, ByVal m_ReadOnly As Boolean, ByVal dr As DataRow) As myDropDownList
        If Me IsNot Nothing Then
            Dim sSelect As String
            Dim dt As Data.DataTable
            Dim ddl As New myDropDownList

            ddl.ID = FieldName
            ddl.FieldObject = obj
            ddl.CssClass = "ddl-text"
            ddl.AutoPostBack = False
            If Me.Parent IsNot Nothing Then
                ddl.Visible = Not CType(Me.Parent, Field).IsHidden
            Else
                ddl.Visible = True
            End If

            If m_ReadOnly OrElse Me.IsReadOnly Then
                ddl.Enabled = False
            End If

            Dim sComp() As String
            Dim sSelectComp As String = String.Empty
            sComp = Me.SelectColumns.Split(",")

            If sComp.Count > 1 Then
                For Each s As String In sComp
                    If sSelectComp <> String.Empty Then
                        sSelectComp &= " + ' - ' + "
                    End If
                    sSelectComp &= " case when " & s & " is null then '' else convert(nvarchar(max)," & s & ") end "
                Next
            End If

            If sSelectComp <> String.Empty Then
                sSelectComp &= " as Componente "
            End If

            sSelect = "SELECT " & Me.SelectColumns
            If Not Me.SelectColumns.Contains(Me.ValueColumn) Then
                sSelect &= "," & Me.ValueColumn
            End If
            If sSelectComp <> String.Empty Then
                sSelect &= "," & sSelectComp
            End If
            sSelect &= " FROM " & Me.From
            If Me.Where <> String.Empty Then
                sSelect &= " WHERE " & Me.Where
            End If

            If Me.OrderBy <> String.Empty Then
                sSelect &= " ORDER BY " & Me.OrderBy
            End If

            dt = DataBase.ExecuteSQL_DataTable(sSelect, True)

            If dt Is Nothing Then
                Return ddl
            End If

            If Me.AddNull Then
                Dim r As Data.DataRow
                r = dt.NewRow
                r.Item(0) = String.Empty
                dt.Rows.InsertAt(r, 0)
            End If

            ddl.DataSource = dt
            If sSelectComp <> String.Empty Then
                ddl.DataTextField = "Componente"
            Else
                Dim sSetColum As String = Me.SelectColumns
                If sSetColum.Contains("DISTINCT") Then
                    sSetColum = sSetColum.Substring(sSetColum.IndexOf("DISTINCT") + "DISTINCT".Length)
                End If
                ddl.DataTextField = sSetColum.Trim
            End If

            ddl.DataValueField = Me.ValueColumn
            ddl.EnableViewState = True
            ddl.DataBind()

            If value <> String.Empty Then
                ddl.SelectedValue = value
            End If

            Return ddl

        End If
        Return Nothing
    End Function

End Class