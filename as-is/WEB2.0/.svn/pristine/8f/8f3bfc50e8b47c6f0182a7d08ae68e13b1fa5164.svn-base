﻿Option Strict On

Imports System.Web.HttpContext
Imports System.Web.UI.WebControls

Public Class Screen
    Private m_Name As String                                    '<ScreenName>
    Private m_DBName As String                                  '<DBName>
    Private m_Where As String                                   '<Where>
    Private m_OrderBy As String                                 '<OrderBy>
    Private m_SelectRowChkBoxColumnName As String              '<SelectRowChkBoxColumnName>
    Private m_LinesPerPage As Integer                           '<LinesPerPage>
    Private m_HasEditButton As Boolean                          '<HasEditButton>
    Private m_HasAddButton As Boolean                           '<HasAddButton>
    Private m_HasDelButton As Boolean                           '<HasDelButton>
    Private m_HasSimplePrintButton As Boolean                   '<HasSimplePrintButton>
    Private m_HasReportButton As Boolean                        '<HasReportButton>
    Private m_HasAllDeleteButton As Boolean                     '<HasAllDeleteButton>
    Private m_HasImportFileButton As Boolean                    '<HasImportFileButton>
    Private m_HasSearchButton As Boolean                        '<HasSearchButton>
    Private m_HasCancelButton As Boolean                        '<HasCancelButton>
    Private m_ReportNameSimplePrintButton As String             '<ReportNameSimplePrintButton>
    Private m_TypeReportSimplePrintButton As String             '<TypeReportSimplePrintButton>
    Private m_Parent As config
    Private m_EnumPageNameCode As EnumPageName

    Private m_extraColumns As Generic.List(Of extraColumn)
    Private m_ComparesWeb As Generic.List(Of CompareWeb)
    Private m_EditFields As Generic.List(Of Field)
    Private m_ViewFields As Generic.List(Of Field)
    Private m_AddMenuItemNames As Generic.List(Of AddMenuItemName)
    Private m_AddButtonParams As Generic.List(Of AddButtonParam)
    Private m_FilterColumnsName As Generic.List(Of FilterColumnName)
    Private m_SumColumn As Generic.List(Of SumColumn)
    Private m_Header As Generic.List(Of Header)

    Public Property EnumPageNameCode() As EnumPageName

        Get
            Return Me.m_EnumPageNameCode
        End Get

        Set(ByVal value As EnumPageName)
            Me.m_EnumPageNameCode = value
        End Set

    End Property

    Public Sub New()

    End Sub

    Public Property TypeReportSimplePrintButton As String

        Get
            Return m_TypeReportSimplePrintButton
        End Get

        Set(value As String)
            m_TypeReportSimplePrintButton = value
        End Set

    End Property

    Public Property ReportNameSimplePrintButton As String

        Get
            Return m_ReportNameSimplePrintButton
        End Get

        Set(value As String)
            m_ReportNameSimplePrintButton = value
        End Set

    End Property

    Public Property Parent() As config

        Get
            Return m_Parent
        End Get

        Set(ByVal value As config)
            Me.m_Parent = value
        End Set

    End Property

    Public Property DBName() As String

        Get
            Return m_DBName
        End Get

        Set(ByVal value As String)
            m_DBName = value
        End Set

    End Property

    Public Property Where() As String

        Get
            Return m_Where
        End Get

        Set(ByVal value As String)
            m_Where = value
        End Set

    End Property

    Public Property OrderBy() As String

        Get
            Return m_OrderBy
        End Get

        Set(ByVal value As String)
            m_OrderBy = value
        End Set

    End Property

    Public Property SelectRowChkBoxColumnName() As String

        Get
            Return m_SelectRowChkBoxColumnName
        End Get

        Set(ByVal value As String)
            m_SelectRowChkBoxColumnName = value
        End Set

    End Property

    Public Property LinesPerPage() As Integer

        Get
            Return m_LinesPerPage
        End Get

        Set(ByVal value As Integer)
            m_LinesPerPage = value
        End Set

    End Property

    Public Property Name() As String

        Get
            Return m_Name
        End Get

        Set(ByVal value As String)
            m_Name = value
        End Set

    End Property

    Public Property HasCancelButton() As Boolean

        Get
            Return m_HasCancelButton
        End Get

        Set(ByVal value As Boolean)
            m_HasCancelButton = value
        End Set

    End Property

    Public Property HasSimplePrintButton() As Boolean

        Get
            Return m_HasSimplePrintButton
        End Get

        Set(ByVal value As Boolean)
            m_HasSimplePrintButton = value
        End Set

    End Property

    Public Property HasReportButton() As Boolean

        Get
            Return m_HasReportButton
        End Get

        Set(ByVal value As Boolean)
            m_HasReportButton = value
        End Set

    End Property

    Public Property HasAddButton() As Boolean

        Get
            Return m_HasAddButton
        End Get

        Set(ByVal value As Boolean)
            m_HasAddButton = value
        End Set

    End Property

    Public Property HasEditButton() As Boolean

        Get
            Return m_HasEditButton
        End Get

        Set(ByVal value As Boolean)
            m_HasEditButton = value
        End Set

    End Property

    Public Property HasDelButton() As Boolean

        Get
            Return m_HasDelButton
        End Get

        Set(ByVal value As Boolean)
            m_HasDelButton = value
        End Set

    End Property

    Public Property HasAllDeleteButton() As Boolean

        Get
            Return m_HasAllDeleteButton
        End Get

        Set(ByVal value As Boolean)
            m_HasAllDeleteButton = value
        End Set

    End Property

    Public Property HasImportFileButton() As Boolean

        Get
            Return Me.m_HasImportFileButton
        End Get

        Set(ByVal value As Boolean)
            Me.m_HasImportFileButton = value
        End Set

    End Property

    Public Property HasSearchButton() As Boolean

        Get
            Return m_HasSearchButton
        End Get

        Set(ByVal value As Boolean)
            m_HasSearchButton = value
        End Set

    End Property

    ''' <summary>
    ''' Get a dummy myHyperLinkField for SelectRowChkBox
    ''' </summary>
    ''' <param name="dt">datatable</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function GetSelectRowChkBoxColumn(ByRef dt As Data.DataTable) As myHyperLinkField
        Dim sSelectRowColumn(0) As String
        Dim n As New myHyperLinkField

        sSelectRowColumn(0) = m_NameSelectionColumn
        dt.Columns.Add(m_NameSelectionColumn)

        n.ControlStyle.CssClass = "label-text"
        n.ItemStyle.BorderStyle = BorderStyle.None
        n.HeaderStyle.BorderStyle = BorderStyle.None
        n.ItemStyle.CssClass = "hideOnPrint"
        n.HeaderStyle.CssClass = "hideOnPrint"
        n.DataNavigateUrlFields = sSelectRowColumn

        Return n
    End Function

    ''' <summary>
    ''' Get a HyperLinkFieldAtmill for the delete data into database
    ''' </summary>
    ''' <param name="dt">datatable</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function GetDeleteColumn(ByRef dt As Data.DataTable, m_target As String) As myHyperLinkField
        Dim sDeleteColumn(0) As String
        Dim n As New myHyperLinkField

        sDeleteColumn(0) = m_NameDeleteColumn
        dt.Columns.Add(m_NameDeleteColumn)

        n.ImageUrl = "~/image/delete.png"
        Try
            n.Text = Me.Parent.GetEntryByKeyName("Delete").GetValue
        Catch ex As Exception
            n.Text = "No entry key found for value (Delete)"
        End Try

        n.ControlStyle.CssClass = "label-text"
        n.ItemStyle.BorderStyle = BorderStyle.None
        n.HeaderStyle.BorderStyle = BorderStyle.None
        n.ItemStyle.CssClass = "hideOnPrint"
        n.HeaderStyle.CssClass = "hideOnPrint"
        n.DataNavigateUrlFields = sDeleteColumn
        If m_target <> String.Empty Then
            n.Target = m_target
        End If
        Return n
    End Function

    ''' <summary>
    ''' Get a HyperLinkFieldAtmill for edit data into database
    ''' </summary>
    ''' <param name="dt">datatable</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function GetEditColumn(ByRef dt As Data.DataTable) As myHyperLinkField
        Dim sEditColumn(0) As String
        Dim n As New myHyperLinkField

        sEditColumn(0) = m_NameEditColumn
        dt.Columns.Add(m_NameEditColumn)

        n.ImageUrl = "~/image/edit.png"
        Try
            n.Text = Me.Parent.GetEntryByKeyName("Edit").GetValue
        Catch ex As Exception
            n.Text = "No entry key found for value (Edit)"
        End Try

        n.ControlStyle.CssClass = "label-text"
        n.ItemStyle.BorderStyle = BorderStyle.None
        n.HeaderStyle.BorderStyle = BorderStyle.None
        n.ItemStyle.CssClass = "hideOnPrint"
        n.HeaderStyle.CssClass = "hideOnPrint"
        n.DataNavigateUrlFields = sEditColumn
        Return n
    End Function

    ''' <summary>
    ''' Get a HyperLinkFieldAtmill for print data
    ''' </summary>
    ''' <param name="dt">datatable</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function GetPrintColumn(ByRef dt As Data.DataTable) As myHyperLinkField
        Dim sPrintColumn(0) As String
        Dim n As New myHyperLinkField

        sPrintColumn(0) = m_NamePrintColumn
        dt.Columns.Add(m_NamePrintColumn)

        n.ImageUrl = "~/image/print.png"
        Try
            n.Text = Me.Parent.GetEntryByKeyName("IMG_PRINT").GetValue
        Catch ex As Exception
            n.Text = "No entry key found for value (IMG_PRINT)"
        End Try

        n.ControlStyle.CssClass = "label-text"
        n.ItemStyle.BorderStyle = BorderStyle.None
        n.HeaderStyle.BorderStyle = BorderStyle.None
        n.ItemStyle.CssClass = "hideOnPrint"
        n.HeaderStyle.CssClass = "hideOnPrint"
        n.DataNavigateUrlFields = sPrintColumn
        Return n
    End Function

    Public Property extraColumns() As Generic.List(Of extraColumn)

        Get
            Return m_extraColumns
        End Get

        Set(ByVal value As Generic.List(Of extraColumn))
            m_extraColumns = value
        End Set

    End Property

    Public Property FilterColumnsName() As Generic.List(Of FilterColumnName)

        Get
            Return m_FilterColumnsName
        End Get

        Set(ByVal value As Generic.List(Of FilterColumnName))
            m_FilterColumnsName = value
        End Set

    End Property

    Public Property SumColumns() As Generic.List(Of SumColumn)

        Get
            Return m_SumColumn
        End Get

        Set(ByVal value As Generic.List(Of SumColumn))
            m_SumColumn = value
        End Set

    End Property

    Public Property Headers() As Generic.List(Of Header)

        Get
            Return m_Header
        End Get

        Set(ByVal value As Generic.List(Of Header))
            m_Header = value
        End Set

    End Property

    Public Property AddMenuItemNames() As Generic.List(Of AddMenuItemName)

        Get
            Return m_AddMenuItemNames
        End Get

        Set(ByVal value As Generic.List(Of AddMenuItemName))
            m_AddMenuItemNames = value
        End Set

    End Property

    Public Property EditFields() As Generic.List(Of Field)

        Get
            Return m_EditFields
        End Get

        Set(ByVal value As Generic.List(Of Field))
            m_EditFields = value
        End Set

    End Property

    Public Property ViewFields() As Generic.List(Of Field)

        Get
            Return m_ViewFields
        End Get

        Set(ByVal value As Generic.List(Of Field))
            m_ViewFields = value
        End Set

    End Property

    Public ReadOnly Property HasQueryFields() As Boolean

        Get
            If Me.m_ViewFields IsNot Nothing Then
                For Each f As Field In Me.m_ViewFields
                    If f.QueryField Then
                        Return True
                    End If
                Next
            End If
            Return False
        End Get

    End Property

    Public Property ComparesWeb() As Generic.List(Of CompareWeb)

        Get
            Return m_ComparesWeb
        End Get

        Set(ByVal value As Generic.List(Of CompareWeb))
            m_ComparesWeb = value
        End Set

    End Property

    Public Property AddButtonParams() As Generic.List(Of AddButtonParam)

        Get
            Return m_AddButtonParams
        End Get

        Set(ByVal value As Generic.List(Of AddButtonParam))
            m_AddButtonParams = value
        End Set

    End Property

    Public Function GetSqlSelect() As String
        If Me.DBName = String.Empty Then
            Return String.Empty
        End If

        Dim ret_val As String = "SELECT "

        If (Current.Session("AllData") Is Nothing OrElse Current.Session("AllData").ToString() = String.Empty) Then
            ret_val &= " TOP (100) "
        End If

        ret_val &= " * FROM " & Me.DBName

        If Me.Where <> String.Empty Then
            ret_val &= " WHERE " & Me.Where
        End If

        If Me.OrderBy <> String.Empty Then
            ret_val &= " ORDER BY " & Me.OrderBy
        End If

        Return ret_val
    End Function

    Public Function GetSqlDeleteAll() As String
        Dim ret_val As String = "DELETE FROM " & Me.DBName
        Return ret_val
    End Function

    Public Function GetSqlSelectFullTable(ByVal sql_where As String, ByVal sort_column As String, ByVal sort_direction As SortDirection) As String
        If Me.DBName = String.Empty Then
            Return String.Empty
        End If

        Dim ret_val As String = "SELECT * FROM " &
            "(SELECT ROW_NUMBER() OVER(ORDER BY "

        ' sicurezza
        If sort_column <> String.Empty Then
            ret_val &= sort_column
        Else
            ret_val &= "ID"
        End If

        If sort_direction = SortDirection.Ascending Then
            ret_val &= " ASC "
        Else
            ret_val &= " DESC "
        End If

        ret_val &= ") AS RowNum, "

        ret_val &= " * FROM " & Me.DBName

        If sql_where IsNot Nothing AndAlso sql_where <> String.Empty Then
            If Not ret_val.Contains("WHERE") Then
                ret_val &= " WHERE "
            Else
                ret_val &= " AND "
            End If
            ret_val &= sql_where
        End If

        If Me.Where <> String.Empty Then
            If Not ret_val.Contains("WHERE") Then
                ret_val &= " WHERE "
            Else
                ret_val &= " AND "
            End If
            ret_val &= Me.Where
        End If

        ret_val &= ") AS DerivedTableName "

        Return ret_val
    End Function

    Public Function GetSqlSelect(ByVal sql_where As String, ByVal row_num_start As Integer, ByVal row_num_stop As Integer, ByVal sort_column As String, ByVal sort_direction As SortDirection) As String
        If Me.DBName = String.Empty Then
            Return String.Empty
        End If

        Dim ret_val As String = GetSqlSelectFullTable(sql_where, sort_column, sort_direction)
        ret_val &= " WHERE RowNum BETWEEN " & row_num_start & " AND " & row_num_stop & " ORDER BY RowNum"

        Return ret_val
    End Function

    Public Function GetSqlSelectSum(ByVal sql_where As String, ByVal sum_column As String) As String
        If Me.DBName = String.Empty Then
            Return String.Empty
        End If

        Dim ret_val As String = "SELECT SUM(" & sum_column & ") FROM " & Me.DBName

        If sql_where IsNot Nothing AndAlso sql_where <> String.Empty Then
            If Not ret_val.Contains("WHERE") Then
                ret_val &= " WHERE "
            Else
                ret_val &= " AND "
            End If
            ret_val &= sql_where
        End If

        If Me.Where <> String.Empty Then
            If Not ret_val.Contains("WHERE") Then
                ret_val &= " WHERE "
            Else
                ret_val &= " AND "
            End If
            ret_val &= Me.Where
        End If

        Return ret_val
    End Function

    Public Function GetSqlCount(ByVal sql_where As String, ByVal sort_column As String) As String
        If Me.DBName = String.Empty Then
            Return String.Empty
        End If

        Dim ret_val As String = "SELECT TOP(1) * FROM " &
            "(SELECT ROW_NUMBER() OVER(ORDER BY "

        ' sicurezza
        If sort_column <> String.Empty Then
            ret_val &= sort_column
        Else
            ret_val &= "ID"
        End If

        ret_val &= ") AS RowNum "
        ret_val &= " FROM " & Me.DBName

        If sql_where IsNot Nothing AndAlso sql_where <> String.Empty Then
            If Not ret_val.Contains("WHERE") Then
                ret_val &= " WHERE "
            Else
                ret_val &= " AND "
            End If
            ret_val &= sql_where
        End If

        If Me.Where <> String.Empty Then
            If Not ret_val.Contains("WHERE") Then
                ret_val &= " WHERE "
            Else
                ret_val &= " AND "
            End If
            ret_val &= Me.Where
        End If

        ret_val &= ") AS DerivedTableName"
        ret_val &= " ORDER BY RowNum DESC"

        Return ret_val
    End Function

    Public Function GetSqlSelectWithViewFieldsColumns(ByVal sql_where As String) As String
        Dim ret_val As String = "SELECT "
        If Me.extraColumns IsNot Nothing Then
            For Each exC As extraColumn In Me.extraColumns
                For Each pm As extraColumnParam In exC.Parameters
                    ret_val &= pm.FieldDb & ","
                Next
            Next
        End If
        For Each s As Field In Me.ViewFields
            If s.VisibleField Then
                ret_val &= s.FieldDb & ","
            End If
        Next

        'Tolgo la , all'ultimo campo inserito
        ret_val = ret_val.Substring(0, ret_val.Length - 1)

        ret_val &= " FROM " & Me.DBName

        If sql_where IsNot Nothing AndAlso sql_where <> String.Empty Then
            ret_val &= sql_where
        End If

        If Me.Where <> String.Empty Then
            If Not ret_val.Contains("WHERE") Then
                ret_val &= " WHERE "
            Else
                ret_val &= " AND "
            End If
            ret_val &= Me.Where
        End If

        If Me.OrderBy <> String.Empty Then
            ret_val &= " ORDER BY " & Me.OrderBy
        End If

        Return ret_val
    End Function

    ''' <summary>
    ''' Return an array of strings to create the extra columns
    ''' Return a dt with the new columns. It's a byRef
    ''' </summary>
    ''' <param name="dt">datatable</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function GetExtraColumns(ByRef dt As Data.DataTable) As String()
        Dim sExtraColums(0) As String
        Dim counter As Integer = 0

        'Gestione colonne extra
        If Me.extraColumns IsNot Nothing Then

            If Me.extraColumns.Count > 0 Then
                ReDim sExtraColums(Me.extraColumns.Count - 1)
            End If

            counter = 0
            For Each exCol As extraColumn In Me.extraColumns
                dt.Columns.Add(exCol.Name)
                sExtraColums(counter) = exCol.Name
                counter += 1
            Next
        End If

        Return sExtraColums
    End Function

    ''' <summary>
    ''' Return an array of strings to create the unit columns
    ''' Return a dt with the new columns. It's a byRef
    ''' </summary>
    ''' <param name="dt">datatable</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function GetUnitColumns(ByRef dt As Data.DataTable) As String()
        Dim sUnitColumns(0) As String
        Dim counter As Integer = 0
        Dim cCountUnitColumn As New Generic.List(Of String)

        If Me.ViewFields IsNot Nothing Then
            'Gestione colonne unit
            For Each vf As Field In Me.ViewFields
                If vf.UnitAsp <> String.Empty And vf.UnitDb <> String.Empty Then
                    If Not cCountUnitColumn.Contains(vf.UnitAsp) Then
                        cCountUnitColumn.Add(vf.UnitAsp)
                    End If
                End If
            Next
        End If

        If cCountUnitColumn.Count > 0 Then
            ReDim sUnitColumns(cCountUnitColumn.Count - 1)
        End If

        counter = 0
        If Me.ViewFields IsNot Nothing Then
            For Each vf As Field In Me.ViewFields
                If vf.UnitAsp <> String.Empty And vf.UnitDb <> String.Empty Then
                    If Not dt.Columns.Contains(vf.UnitAsp) Then
                        dt.Columns.Add(vf.UnitAsp)
                        sUnitColumns(counter) = vf.UnitAsp
                        counter += 1
                    End If
                End If
            Next
        End If
        Return sUnitColumns
    End Function

    ''' <summary>
    ''' Return an array of strings to create the led columns
    ''' Return a dt with the new columns. It's a byRef
    ''' </summary>
    ''' <param name="dt">datatable</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function GetLedColumns(ByRef dt As Data.DataTable) As String()
        Dim sLedColumns(0) As String
        Dim counter As Integer = 0
        Dim cCountLedColumn As New Generic.List(Of String)

        If Me.ViewFields IsNot Nothing Then
            'Gestione colonne unit
            For Each vf As Field In Me.ViewFields
                If vf.GetFieldType = EnumFieldType.FieldLed Then
                    If Not cCountLedColumn.Contains(vf.FieldDb) Then
                        cCountLedColumn.Add(vf.FieldDb)
                    End If
                End If
            Next
        End If
        If cCountLedColumn.Count > 0 Then
            ReDim sLedColumns(cCountLedColumn.Count - 1)
        End If

        counter = 0
        If Me.ViewFields IsNot Nothing Then
            For Each vf As Field In Me.ViewFields
                If vf.GetFieldType = EnumFieldType.FieldLed Then
                    If Not dt.Columns.Contains(vf.FieldDb) Then
                        dt.Columns.Add(vf.FieldDb)
                        sLedColumns(counter) = vf.FieldDb
                        counter += 1
                    End If
                End If
            Next
        End If
        Return sLedColumns
    End Function

    ''' <summary>
    ''' Create a path for the extra column with the parameters and
    ''' ste this path into dr column name
    ''' </summary>
    ''' <param name="exCol">extraColumn</param>
    ''' <param name="dr">datarow</param>
    ''' <remarks></remarks>
    Public Sub SetPathDataForExtraColumns(ByVal exCol As extraColumn, ByVal from_page As String, ByRef dr As Data.DataRow)

        exCol.View = True
        If exCol.FieldDBValueOnly <> String.Empty AndAlso exCol.FieldDBViewOnly <> String.Empty Then
            If dr.Item(exCol.FieldDBViewOnly).ToString.ToUpper <> exCol.FieldDBValueOnly.ToUpper Then
                exCol.View = False
            End If
        End If
        Dim sStringa As String = String.Empty
        Dim sStringa_Params As String = String.Empty
        Dim pChar As String = String.Empty
        Dim sql_where As String = String.Empty

        If exCol.ScriptName.Contains("?") Then
            pChar = "&"
        Else
            pChar = "?"
        End If

        ' aggiungo la pagina da cui provengo sse è diversa da quella dove verrò ridiretto
        Dim objValueCollection As System.Collections.Specialized.NameValueCollection = System.Web.HttpUtility.ParseQueryString(exCol.ScriptName)
        If objValueCollection.Get("pagename") <> from_page Then
            sStringa &= pChar & "from=" & from_page
            pChar = "&"
        End If

        ' aggiungo gli ExtraQueryString se presenti
        If exCol.ExtraQueryString <> String.Empty Then
            sStringa &= pChar & exCol.ExtraQueryString
            pChar = "&"
        End If

        'Creo la stringa where da utilizzare poi durante la fase di ricerca
        For Each p As extraColumnParam In exCol.Parameters
            sStringa_Params &= p.FieldDb & "=" & dr.Item(p.FieldDb).ToString & "&"

            If sql_where = String.Empty Then
                sStringa &= pChar & "sql_where="
            Else
                sql_where &= " AND "
            End If

            sql_where &= p.FieldDb & "='" & dr.Item(p.FieldDb).ToString & "'"

        Next

        If sql_where <> String.Empty Then
            sStringa &= sql_where & "&" & sStringa_Params
        End If

        If exCol.View Then
            dr.Item(exCol.Name) = exCol.ScriptName & sStringa
        Else
            dr.Item(exCol.Name) = String.Empty
        End If

    End Sub

    ''' <summary>
    ''' Create a path for delete row, you need the ID coulmns for delete
    ''' </summary>
    ''' <param name="dr">datarow</param>
    ''' <param name="sPageName">Table or View of the database</param>
    ''' <remarks></remarks>
    Public Sub SetPathForDeleteColumns(ByRef dr As Data.DataRow, ByVal sPageName As String, ByVal asp_page As String, ByVal menuname As String, ByVal m_List As Generic.List(Of AddButtonParam), ByVal mControl As String, sql_where As String, mTopMenuName As String, m_addpathstring As String)
        Dim sPath As String = "~/" & asp_page & "?topmenuname=" & mTopMenuName & "&control=" & mControl & "&deleted=yes&pagename=" & sPageName & "&menuname=" & menuname & "&Id=" & dr.Item("ID").ToString

        If m_List IsNot Nothing Then
            For Each p As AddButtonParam In m_List
                If Not sPath.Contains(p.Parameter & "=") Then
                    If Current.Request(p.Parameter) IsNot Nothing Then
                        sPath &= "&" & p.Parameter & "=" & Current.Request(p.Parameter).ToString
                    End If
                End If
            Next
        End If

        If Me.FilterColumnsName IsNot Nothing Then
            For Each p As FilterColumnName In Me.FilterColumnsName
                If Not sPath.Contains(p.FieldDB & "=") Then
                    Try
                        sPath &= "&" & p.FieldDB & "=" & Current.Request(p.FieldDB).ToString
                    Catch
                    End Try
                End If
            Next
        End If
        If sql_where <> String.Empty Then
            sPath &= "&sql_where=" & sql_where
        End If

        If m_addpathstring <> String.Empty Then
            sPath &= m_addpathstring
        End If

        dr.Item(costanti.m_NameDeleteColumn) = sPath
    End Sub

    ''' <summary>
    ''' Create a path for edit row, you need the ID coulmns for delete
    ''' </summary>
    ''' <param name="dr">datarow</param>
    ''' <param name="sPageName">Table or View of the database</param>
    ''' <remarks></remarks>
    Public Sub SetPathForEditColumns(ByRef dr As Data.DataRow, ByVal sPageName As String, ByVal asp_page As String, ByVal menuname As String, ByVal m_List As Generic.List(Of AddButtonParam), mTopMenuName As String)
        dr.Item(costanti.m_NameEditColumn) = GetPathStringForEdit(sPageName, asp_page, menuname, m_List, dr.Item("ID").ToString, mTopMenuName)
    End Sub

    Public Function GetPathStringForEdit(ByVal sPageName As String, ByVal asp_page As String, ByVal menuname As String, ByVal m_List As Generic.List(Of AddButtonParam), ByVal Id As String, mTopMenuName As String) As String
        Dim sPath As String = "~/" & asp_page & "?topmenuname=" & mTopMenuName & "&control=edit&pagename=" & sPageName & "&menuname=" & menuname & "&Id=" & Id

        If m_List IsNot Nothing Then
            For Each p As AddButtonParam In m_List
                If Not sPath.Contains(p.Parameter) Then
                    If Current.Request(p.Parameter) IsNot Nothing Then
                        sPath &= "&" & p.Parameter & "=" & Current.Request(p.Parameter).ToString
                    End If
                End If
            Next
        End If
        Return sPath
    End Function

    Public Function GetPathStringForView(ByVal sPageName As String, ByVal asp_page As String, ByVal menuname As String, ByVal m_List As Generic.List(Of AddButtonParam), ByVal Id As String, mTopMenuName As String) As String
        Dim sPath As String = "~/" & asp_page & "?topmenuname=" & mTopMenuName & "&control=view&pagename=" & sPageName & "&menuname=" & menuname & "&Id=" & Id

        If m_List IsNot Nothing Then
            For Each p As AddButtonParam In m_List
                If Not sPath.Contains(p.Parameter) Then
                    If Current.Request(p.Parameter) IsNot Nothing Then
                        sPath &= "&" & p.Parameter & "=" & Current.Request(p.Parameter).ToString
                    End If
                End If
            Next
        End If
        Return sPath
    End Function

    Public Function IsCompareValidated(ByVal m_root As System.Web.UI.Control) As Boolean
        Dim bPassed As Boolean = True
        For Each cm As CompareWeb In ComparesWeb
            If cm.CompareType = "RequiredFieldValidator" Then
                If Not CType(tools.FindControlRecursive(m_root, cm.Id), RequiredFieldValidator).IsValid Then
                    bPassed = False
                End If
            End If
        Next
        Return bPassed
    End Function

    Public Function IsAddMenuItemNameType(ByVal m_Type As EnumAddMenuBound) As Boolean

        For Each m As AddMenuItemName In Me.m_AddMenuItemNames
            If m.MenuBound = m_Type Then
                Return True
            End If
        Next
        Return False
    End Function

    Public Function ToJSON() As String
        Dim screen_JSON As String = String.Empty
        Dim is_first As Boolean = True

        screen_JSON = "{""ScreenName"":""" & m_Name & """," &
                    """DBName"":""" & m_DBName & """," &
                    """HasEditButton"":""" & m_HasEditButton & """," &
                    """HasAddButton"":""" & m_HasAddButton & """," &
                    """HasDelButton"":""" & m_HasDelButton & """," &
                    """HasAllDelButton"":""" & m_HasAllDeleteButton & """," &
                    """HasSearchButton"":""" & m_HasSearchButton & """," &
                    """LinesPerPage"":""" & m_LinesPerPage & """," &
                    """OrderBy"":""" & m_OrderBy & """," &
                    """ViewFields"":"

        If Not m_ViewFields Is Nothing Then
            screen_JSON &= "["
            For Each vf As Field In m_ViewFields
                If is_first Then
                    is_first = False
                Else
                    screen_JSON &= ","
                End If
                screen_JSON &= "{""PropertyName"":""" & vf.PropertyName & """," &
                    """queryField"":""" & vf.QueryField & """," &
                    """FieldName"":""" & vf.FieldName & """," &
                    """visibleField"":""" & vf.VisibleField & """," &
                    """fieldDb"":""" & vf.FieldDb & """," &
                    """IsSqlExcluded"":""" & vf.IsSqlExcluded & """," &
                    """FieldType"":""" & vf.FieldType & """"
                If vf.ObjectList IsNot Nothing Then
                    screen_JSON &= ",""SelectColumns"":""" & vf.ObjectList.SelectColumns & """" &
                        ",""ValueColumn"":""" & vf.ObjectList.ValueColumn & """" &
                        ",""From"":""" & vf.ObjectList.From & """"
                    If vf.ObjectList.Where <> String.Empty Then
                        screen_JSON &= ",""Where"":""" & vf.ObjectList.Where & """"
                    End If
                    If vf.ObjectList.OrderBy <> String.Empty Then
                        screen_JSON &= ",""OrderBy"":""" & vf.ObjectList.OrderBy & """"
                    End If
                End If
                If vf.ObjectChkList IsNot Nothing Then
                    screen_JSON &= ",""SelectColumns"":""" & vf.ObjectChkList.SelectColumns & """" &
                        ",""ValueColumn"":""" & vf.ObjectChkList.ValueColumn & """" &
                        ",""From"":""" & vf.ObjectChkList.From & """"
                    If vf.ObjectChkList.Where <> String.Empty Then
                        screen_JSON &= ",""Where"":""" & vf.ObjectChkList.Where & """"
                    End If
                    If vf.ObjectChkList.OrderBy <> String.Empty Then
                        screen_JSON &= ",""OrderBy"":""" & vf.ObjectChkList.OrderBy & """"
                    End If
                End If
                screen_JSON &= "}"
            Next
            is_first = True
            screen_JSON &= "]"
        Else
            screen_JSON &= """null"""
        End If

        screen_JSON &= ",""EditFields"":"
        If Not m_EditFields Is Nothing Then
            screen_JSON &= "["
            For Each ef As Field In m_EditFields
                If is_first Then
                    is_first = False
                Else
                    screen_JSON &= ","
                End If
                screen_JSON &= "{""PropertyName"":""" & ef.PropertyName & """," &
                    """updateField"":""" & ef.UpdateField & """," &
                    """FieldName"":""" & ef.FieldName & """," &
                    """insertField"":""" & ef.InsertField & """," &
                    """fieldDb"":""" & ef.FieldDb & """," &
                    """IsReadOnly"":""" & ef.IsReadOnly & """," &
                    """IsHidden"":""" & ef.IsHidden & """," &
                    """IsSqlExcluded"":""" & ef.IsSqlExcluded & """," &
                    """IsVisibleButNotEditable"":""" & ef.IsVisibleButNotEditable & """," &
                    """FieldType"":""" & ef.FieldType & """"
                If ef.ObjectList IsNot Nothing Then
                    screen_JSON &= ",""SelectColumns"":""" & ef.ObjectList.SelectColumns & """" &
                        ",""ValueColumn"":""" & ef.ObjectList.ValueColumn & """" &
                        ",""From"":""" & ef.ObjectList.From & """"
                    If ef.ObjectList.Where <> String.Empty Then
                        screen_JSON &= ",""Where"":""" & ef.ObjectList.Where & """"
                    End If
                    If ef.ObjectList.OrderBy <> String.Empty Then
                        screen_JSON &= ",""OrderBy"":""" & ef.ObjectList.OrderBy & """"
                    End If
                End If
                If ef.ObjectChkList IsNot Nothing Then
                    screen_JSON &= ",""SelectColumns"":""" & ef.ObjectChkList.SelectColumns & """" &
                        ",""ValueColumn"":""" & ef.ObjectChkList.ValueColumn & """" &
                        ",""From"":""" & ef.ObjectChkList.From & """"
                    If ef.ObjectChkList.Where <> String.Empty Then
                        screen_JSON &= ",""Where"":""" & ef.ObjectChkList.Where & """"
                    End If
                    If ef.ObjectChkList.OrderBy <> String.Empty Then
                        screen_JSON &= ",""OrderBy"":""" & ef.ObjectChkList.OrderBy & """"
                    End If
                End If
                screen_JSON &= "}"
            Next
            is_first = True
            screen_JSON &= "]"
        Else
            screen_JSON &= """null"""
        End If

        screen_JSON &= "}"
        Return screen_JSON
    End Function

End Class

Public Class extraColumn
    Private m_Name As String
    Private m_Parameters As Generic.List(Of extraColumnParam)
    Private m_ScriptName As String
    Private m_ImageSrc As String
    Private m_ImageToolTip As String
    Private m_ParentMenu As Screen
    Private m_FieldDBViewOnly As String = String.Empty
    Private m_FieldDBValueOnly As String = String.Empty
    Private m_View As Boolean = True
    Private m_RequiredAccessLevel As EnumAccessLevel
    Private m_ExtraQueryString As String = String.Empty
    Private m_Visible As Boolean = False

    Public Sub New()

    End Sub

    Public Property FieldDBValueOnly() As String

        Get
            Return m_FieldDBValueOnly
        End Get

        Set(ByVal value As String)
            m_FieldDBValueOnly = value
        End Set

    End Property

    Public Property FieldDBViewOnly() As String

        Get
            Return m_FieldDBViewOnly
        End Get

        Set(ByVal value As String)
            m_FieldDBViewOnly = value
        End Set

    End Property

    Public Property View() As Boolean

        Get
            Return Me.m_View
        End Get

        Set(ByVal value As Boolean)
            Me.m_View = value
        End Set

    End Property

    Public Property ImageToolTip() As String

        Get
            Return m_ImageToolTip
        End Get

        Set(ByVal value As String)
            m_ImageToolTip = value
        End Set

    End Property

    Public Property Name() As String

        Get
            Return m_Name
        End Get

        Set(ByVal value As String)
            m_Name = value
        End Set

    End Property

    Public Property ScriptName() As String

        Get
            Return m_ScriptName
        End Get

        Set(ByVal value As String)
            m_ScriptName = value
        End Set

    End Property

    Public Property ImageSrc() As String

        Get
            Return m_ImageSrc
        End Get

        Set(ByVal value As String)
            m_ImageSrc = value
        End Set

    End Property

    Public Property ParentMenu() As Screen

        Get
            Return m_ParentMenu
        End Get

        Set(ByVal value As Screen)
            m_ParentMenu = value
        End Set

    End Property

    Public Property Parameters() As Generic.List(Of extraColumnParam)

        Get
            Return m_Parameters
        End Get

        Set(ByVal value As Generic.List(Of extraColumnParam))
            m_Parameters = value
        End Set

    End Property

    Public Property RequiredAccessLevel As EnumAccessLevel

        Get
            Return m_RequiredAccessLevel
        End Get

        Set(value As EnumAccessLevel)
            m_RequiredAccessLevel = value
        End Set

    End Property

    Public Property ExtraQueryString As String

        Get
            Return m_ExtraQueryString
        End Get

        Set(value As String)
            m_ExtraQueryString = value
        End Set

    End Property

    Public Property Visible() As Boolean

        Get
            Return Me.m_Visible
        End Get

        Set(ByVal value As Boolean)
            Me.m_Visible = value
        End Set

    End Property

End Class

Public Class extraColumnParam

    Private m_FieldDb As String

    Public Sub New()

    End Sub

    Public Property FieldDb() As String

        Get
            Return m_FieldDb
        End Get

        Set(ByVal value As String)
            m_FieldDb = value
        End Set

    End Property

End Class

Public Class CompareWeb
    Private m_ModeType As EnumCompareType
    Private m_CompareType As String
    Private m_ValidationGroup As String
    Private m_EnableClientScript As Boolean
    Private m_ControlToValidate As String
    Private m_ControlToCompare As String
    Private m_DisplayType As ValidatorDisplay
    Private m_MinimumValue As String
    Private m_MaximumValue As String
    Private m_ParentMenu As Screen
    Private m_ErrorMessage As String
    Private m_FieldName As String
    Private m_Id As String
    Private m_ValidationDataType As ValidationDataType

    Public Property Id() As String

        Get
            Return Me.m_Id
        End Get

        Set(ByVal value As String)
            Me.m_Id = value
        End Set

    End Property

    Public Property ParentMenu() As Screen

        Get
            Return m_ParentMenu
        End Get

        Set(ByVal value As Screen)
            m_ParentMenu = value
        End Set

    End Property

    Public Property ValidationDataType_object() As ValidationDataType

        Get
            Return m_ValidationDataType
        End Get

        Set(ByVal value As ValidationDataType)
            m_ValidationDataType = value
        End Set

    End Property

    Public Property CompareType() As String

        Get
            Return m_CompareType
        End Get

        Set(ByVal value As String)
            m_CompareType = value
        End Set

    End Property

    Public Property ModeType() As EnumCompareType

        Get
            Return m_ModeType
        End Get

        Set(ByVal value As EnumCompareType)
            m_ModeType = value
        End Set

    End Property

    Public Property ValidationGroup() As String

        Get
            Return m_ValidationGroup
        End Get

        Set(ByVal value As String)
            m_ValidationGroup = value
        End Set

    End Property

    Public Property EnableClientScript() As Boolean

        Get
            Return m_EnableClientScript
        End Get

        Set(ByVal value As Boolean)
            m_EnableClientScript = value
        End Set

    End Property

    Public Property ControlToValidate() As String

        Get
            Return m_ControlToValidate
        End Get

        Set(ByVal value As String)
            m_ControlToValidate = value
        End Set

    End Property

    Public Property ControlToCompare() As String

        Get
            Return m_ControlToCompare
        End Get

        Set(ByVal value As String)
            m_ControlToCompare = value
        End Set

    End Property

    Public Property MaximumValue() As String

        Get
            Return m_MaximumValue
        End Get

        Set(ByVal value As String)
            m_MaximumValue = value
        End Set

    End Property

    Public Property MinimumValue() As String

        Get
            Return m_MinimumValue
        End Get

        Set(ByVal value As String)
            m_MinimumValue = value
        End Set

    End Property

    Public Property ErrorMessage() As String

        Get
            Return m_ErrorMessage
        End Get

        Set(ByVal value As String)
            m_ErrorMessage = value
        End Set

    End Property

    Public Property FieldName() As String

        Get
            Return m_FieldName
        End Get

        Set(ByVal value As String)
            m_FieldName = value
        End Set

    End Property

    Public Property DisplayType() As ValidatorDisplay

        Get
            Return m_DisplayType
        End Get

        Set(ByVal value As ValidatorDisplay)
            m_DisplayType = value
        End Set

    End Property

End Class

Public Class Field
    Private m_ObjectNumber As NumberAtmill = Nothing
    Private m_ObjectList As myList = Nothing
    Private m_ObjectCalendar As myCalendar = Nothing
    Private m_ObjectChkList As ChkmyList = Nothing
    Private m_Type As String
    Private m_Value As String
    Private m_FieldDb As String
    Private m_FieldName As String
    Private m_DateFormat As EnumDateFormats
    Private m_FixedRuntimeValue As String
    Private m_ParentMenu As Screen
    Private m_UnitDb As String
    Private m_UnitAsp As String
    Private m_HAlign As System.Web.UI.WebControls.HorizontalAlign
    Private m_nDecimal As Integer
    Private m_IsReadOnly As Boolean
    Private m_IsHidden As Boolean
    Private m_IsSqlExcluded As Boolean
    Private m_IsVisibleButNotEditable As Boolean
    Private m_ValidationGroup As String
    Private m_PropertyName As String
    Private m_UpdateField As Boolean
    Private m_InsertField As Boolean
    Private m_QueryField As Boolean
    Private m_VisibleField As Boolean
    Private m_EnableKeyEntry As Boolean
    Private m_ImageLed As String
    Private m_GetDataFrom As EnumGetDataFrom
    Private m_TextBefore As String
    Private m_TextAfter As String
    Private m_RadioButtonAspFilter As String
    Private m_CallEvent As String
    Private m_QueryFieldName As String
    Private m_HideIfEmpty As Boolean
    Private m_TimeSpanFormatDb As String
    Private m_TimeSpanFormatAsp As String

    Public Sub New(ByVal Value As String)
        m_Value = Value
    End Sub

    Public Property TimeSpanFormatAsp() As String

        Get
            Return m_TimeSpanFormatAsp
        End Get

        Set(ByVal value As String)
            m_TimeSpanFormatAsp = value
        End Set

    End Property

    Public Property TimeSpanFormatDb() As String

        Get
            Return m_TimeSpanFormatDb
        End Get

        Set(ByVal value As String)
            m_TimeSpanFormatDb = value
        End Set

    End Property

    Public Property HideIfEmpty() As Boolean

        Get
            Return m_HideIfEmpty
        End Get

        Set(ByVal value As Boolean)
            m_HideIfEmpty = value
        End Set

    End Property

    Public Property QueryFieldName() As String

        Get
            Return m_QueryFieldName
        End Get

        Set(ByVal value As String)
            m_QueryFieldName = value
        End Set

    End Property

    Public Property CallEvent() As String

        Get
            Return m_CallEvent
        End Get

        Set(ByVal value As String)
            m_CallEvent = value
        End Set

    End Property

    Public Property RadioButtonAspFilter() As String

        Get
            Return m_RadioButtonAspFilter
        End Get

        Set(ByVal value As String)
            m_RadioButtonAspFilter = value
        End Set

    End Property

    Public Property UpdateField() As Boolean

        Get
            Return m_UpdateField
        End Get

        Set(ByVal value As Boolean)
            m_UpdateField = value
        End Set

    End Property

    Public Property InsertField() As Boolean

        Get
            Return m_InsertField
        End Get

        Set(ByVal value As Boolean)
            m_InsertField = value
        End Set

    End Property

    Public Property QueryField() As Boolean

        Get
            Return m_QueryField
        End Get

        Set(ByVal value As Boolean)
            m_QueryField = value
        End Set

    End Property

    Public Property VisibleField() As Boolean

        Get
            Return m_VisibleField
        End Get

        Set(ByVal value As Boolean)
            m_VisibleField = value
        End Set

    End Property

    Public Property EnableKeyEntry() As Boolean

        Get
            Return m_EnableKeyEntry
        End Get

        Set(ByVal value As Boolean)
            m_EnableKeyEntry = value
        End Set

    End Property

    Public Property ObjectChkList() As ChkmyList

        Get
            Return m_ObjectChkList
        End Get

        Set(ByVal value As ChkmyList)
            Me.m_ObjectChkList = value
        End Set

    End Property

    Public Property PropertyName() As String

        Get
            Return m_PropertyName
        End Get

        Set(ByVal value As String)
            m_PropertyName = value
        End Set

    End Property

    Public Property IsVisibleButNotEditable() As Boolean

        Get
            Return m_IsVisibleButNotEditable
        End Get

        Set(ByVal value As Boolean)
            m_IsVisibleButNotEditable = value
        End Set

    End Property

    Public Property IsSqlExcluded() As Boolean

        Get
            Return m_IsSqlExcluded
        End Get

        Set(ByVal value As Boolean)
            m_IsSqlExcluded = value
        End Set

    End Property

    Public Property IsReadOnly() As Boolean

        Get
            Return m_IsReadOnly
        End Get

        Set(ByVal value As Boolean)
            m_IsReadOnly = value
        End Set

    End Property

    Public Property IsHidden() As Boolean

        Get
            Return m_IsHidden
        End Get

        Set(ByVal value As Boolean)
            m_IsHidden = value
        End Set

    End Property

    Public Property DateFormat() As EnumDateFormats

        Get
            Return m_DateFormat
        End Get

        Set(ByVal value As EnumDateFormats)
            m_DateFormat = value
        End Set

    End Property

    Public Property ObjectNumber() As NumberAtmill

        Get
            Return m_ObjectNumber
        End Get

        Set(ByVal value As NumberAtmill)
            Me.m_ObjectNumber = value
        End Set

    End Property

    Public Property ObjectList() As myList

        Get
            Return m_ObjectList
        End Get

        Set(ByVal value As myList)
            Me.m_ObjectList = value
        End Set

    End Property

    Public Property ObjectCalendar() As myCalendar

        Get
            Return m_ObjectCalendar
        End Get

        Set(ByVal value As myCalendar)
            Me.m_ObjectCalendar = value
        End Set

    End Property

    Public ReadOnly Property GetValue() As String

        Get
            Return m_Value
        End Get

    End Property

    Public Property FieldType() As String

        Get
            Return m_Type
        End Get

        Set(ByVal value As String)
            m_Type = value
        End Set

    End Property

    Public Property ImageLed() As String

        Get
            Return m_ImageLed
        End Get

        Set(ByVal value As String)
            m_ImageLed = value
        End Set

    End Property

    Public Property GetDataFrom() As EnumGetDataFrom

        Get
            Return m_GetDataFrom
        End Get

        Set(ByVal value As EnumGetDataFrom)
            m_GetDataFrom = value
        End Set

    End Property

    Public Property TextBefore() As String

        Get
            Return m_TextBefore
        End Get

        Set(ByVal value As String)
            m_TextBefore = value
        End Set

    End Property

    Public Property TextAfter() As String

        Get
            Return m_TextAfter
        End Get

        Set(ByVal value As String)
            m_TextAfter = value
        End Set

    End Property

    ' GetFieldType
    ' recupera dal config.xml l'attributo FieldType del Field e restituisce il valore corrispondente nell'EnumFieldType
    ' possibilità:
    ' NUMBER, STRING, LIST, TDATE, PASSWORD, AUTO, CHKLIST,
    ' CHECKBOXYESNO, CHECKBOXONOFF, CHECKBOXONEZERO, WSTRING, LINK
    '
    Public ReadOnly Property GetFieldType() As EnumFieldType

        Get
            Select Case Me.m_Type.ToUpper
                Case "NUMBER"
                    Return EnumFieldType.FieldNumber
                Case "LABEL"
                    Return EnumFieldType.FieldLabel
                Case "RADIOBUTTON"
                    Return EnumFieldType.FieldRadioButton
                Case "STRING"
                    Return EnumFieldType.FieldString
                Case "LIST"
                    Return EnumFieldType.FieldList
                Case "TDATE"
                    Return EnumFieldType.FieldCalendar
                Case "PASSWORD"
                    Return EnumFieldType.FieldPassword
                Case "AUTO"
                    Return EnumFieldType.FieldAuto
                Case "CHKLIST"
                    Return EnumFieldType.FieldChkList
                Case "CHECKBOXYESNO"
                    Return EnumFieldType.FieldCheckBoxYesNo
                Case "CHECKBOXONOFF"
                    Return EnumFieldType.FieldCheckBoxOnOff
                Case "CHECKBOXONEZERO"
                    Return EnumFieldType.FieldCheckBoxOneZero
                Case "WSTRING"
                    Return EnumFieldType.FieldWString
                Case "LINK"
                    Return EnumFieldType.FieldLink
                Case "LED"
                    Return EnumFieldType.FieldLed
                Case Else
                    Return Nothing
            End Select
        End Get

    End Property

    Public Property nDecimal() As Integer

        Get
            Return m_nDecimal
        End Get

        Set(ByVal value As Integer)
            m_nDecimal = value
        End Set

    End Property

    Public Property UnitAsp() As String

        Get
            Return m_UnitAsp
        End Get

        Set(ByVal value As String)
            m_UnitAsp = value
        End Set

    End Property

    Public Property UnitDb() As String

        Get
            Return m_UnitDb
        End Get

        Set(ByVal value As String)
            m_UnitDb = value
        End Set

    End Property

    Public Property ValidationGroup() As String

        Get
            Return m_ValidationGroup
        End Get

        Set(ByVal value As String)
            m_ValidationGroup = value
        End Set

    End Property

    Public Property FieldName() As String

        Get
            Return m_FieldName
        End Get

        Set(ByVal value As String)
            m_FieldName = value
        End Set

    End Property

    Public Property FixedRuntimeValue() As String

        Get
            Return Me.m_FixedRuntimeValue
        End Get

        Set(ByVal value As String)
            Me.m_FixedRuntimeValue = value
        End Set

    End Property

    Public Property FieldDb() As String

        Get
            Return m_FieldDb
        End Get

        Set(ByVal value As String)
            m_FieldDb = value
        End Set

    End Property

    Public Property ParentMenu() As Screen

        Get
            Return m_ParentMenu
        End Get

        Set(ByVal value As Screen)
            m_ParentMenu = value
        End Set

    End Property

    Public Property HAlign() As System.Web.UI.WebControls.HorizontalAlign

        Get
            Return Me.m_HAlign
        End Get

        Set(ByVal value As System.Web.UI.WebControls.HorizontalAlign)
            Me.m_HAlign = value
        End Set

    End Property

End Class

Public Class AddMenuItemName
    Private mNameLink As String
    Private mNameMenu As String
    Private mMenuBound As EnumAddMenuBound
    Private mName As String
    Private mPath As String
    Private mParentMenu As Screen
    Private mListParameters As Generic.List(Of AddMenuItemNameParameter)

    Public Property Name() As String

        Get
            Return Me.mName
        End Get

        Set(ByVal value As String)
            Me.mName = value
        End Set

    End Property

    Public Property Path() As String

        Get
            Return Me.mPath
        End Get

        Set(ByVal value As String)
            Me.mPath = value
        End Set

    End Property

    Public Property MenuBound() As EnumAddMenuBound

        Get
            Return Me.mMenuBound
        End Get

        Set(ByVal value As EnumAddMenuBound)
            Me.mMenuBound = value
        End Set

    End Property

    Public Property NameMenu() As String

        Get
            Return Me.mNameMenu
        End Get

        Set(ByVal value As String)
            Me.mNameMenu = value
        End Set

    End Property

    Public Property NameLink() As String

        Get
            Return Me.mNameLink
        End Get

        Set(ByVal value As String)
            Me.mNameLink = value
        End Set

    End Property

    Public Property ParentMenu() As Screen

        Get
            Return Me.mParentMenu
        End Get

        Set(ByVal value As Screen)
            Me.mParentMenu = value
        End Set

    End Property

    Public ReadOnly Property GetNavigateUrl() As String

        Get
            Dim ret_val As String = String.Empty
            If NameMenu <> String.Empty Then
                ret_val = Me.mParentMenu.Parent.GetMenuByMenuName(Me.NameMenu).GetMenuItemsLinkFromItemName(Me.NameLink)
            Else
                ret_val = Me.mPath
            End If

            Return ret_val
        End Get

    End Property

    Public Property ListParameters() As Generic.List(Of AddMenuItemNameParameter)

        Get
            Return Me.mListParameters
        End Get

        Set(ByVal value As Generic.List(Of AddMenuItemNameParameter))
            Me.mListParameters = value
        End Set

    End Property

End Class

Public Class AddMenuItemNameParameter
    Private m_FieldDB As String

    Public Property FieldDB() As String

        Get
            Return Me.m_FieldDB
        End Get

        Set(ByVal value As String)
            Me.m_FieldDB = value
        End Set

    End Property

End Class

Public Class AddButtonParam
    Private m_Param As String
    Private mParentMenu As Screen

    Public Sub New()

    End Sub

    Public Property Parameter() As String

        Get
            Return m_Param
        End Get

        Set(ByVal value As String)
            m_Param = value
        End Set

    End Property

    Public Property ParentMenu() As Screen

        Get
            Return Me.mParentMenu
        End Get

        Set(ByVal value As Screen)
            Me.mParentMenu = value
        End Set

    End Property

End Class

Public Class FilterColumnName
    Private m_FieldDB As String
    Private mParentMenu As Screen

    Public Sub New()

    End Sub

    Public Property FieldDB() As String

        Get
            Return m_FieldDB
        End Get

        Set(ByVal value As String)
            m_FieldDB = value
        End Set

    End Property

    Public Property ParentMenu() As Screen

        Get
            Return Me.mParentMenu
        End Get

        Set(ByVal value As Screen)
            Me.mParentMenu = value
        End Set

    End Property

End Class

Public Class SumColumn
    Private m_Field As String
    Private m_FieldName As String
    Private m_UnitDB As String
    Private m_UnitASP As String
    Private m_TimeSpanFormatDB As String
    Private m_TimeSpanFormatASP As String
    Private mParentMenu As Screen

    Public Sub New()

    End Sub

    Public Property FieldName() As String

        Get
            Return m_FieldName
        End Get

        Set(ByVal value As String)
            m_FieldName = value
        End Set

    End Property

    Public Property Field() As String

        Get
            Return m_Field
        End Get

        Set(ByVal value As String)
            m_Field = value
        End Set

    End Property

    Public Property UnitDB() As String

        Get
            Return m_UnitDB
        End Get

        Set(ByVal value As String)
            m_UnitDB = value
        End Set

    End Property

    Public Property UnitASP() As String

        Get
            Return m_UnitASP
        End Get

        Set(ByVal value As String)
            m_UnitASP = value
        End Set

    End Property

    Public Property TimeSpanFormatASP() As String

        Get
            Return m_TimeSpanFormatASP
        End Get

        Set(ByVal value As String)
            m_TimeSpanFormatASP = value
        End Set

    End Property

    Public Property TimeSpanFormatDB() As String

        Get
            Return m_TimeSpanFormatDB
        End Get

        Set(ByVal value As String)
            m_TimeSpanFormatDB = value
        End Set

    End Property

    Public Property ParentMenu() As Screen

        Get
            Return Me.mParentMenu
        End Get

        Set(ByVal value As Screen)
            Me.mParentMenu = value
        End Set

    End Property

End Class

Public Class Header
    Private m_MasterScreenName As String
    Private m_MasterFilterColumn As String
    Private m_MasterFilterRuntimeValue As String
    Private m_ViewFieldList As Generic.List(Of Field)
    Private m_ParentMenu As Screen
    Private m_ScreenMaster As Screen

    Public Sub New()

    End Sub

    Public Property ParentMenu() As Screen

        Get
            Return Me.m_ParentMenu
        End Get

        Set(ByVal value As Screen)
            Me.m_ParentMenu = value
        End Set

    End Property

    Public ReadOnly Property ScreenMaster() As Screen

        Get
            m_ScreenMaster = Me.m_ParentMenu.Parent.GetScreenByScreenName(Me.MasterScreenName)
            Return m_ScreenMaster
        End Get

    End Property

    Public Property MasterScreenName() As String

        Get
            Return m_MasterScreenName
        End Get

        Set(ByVal value As String)
            m_MasterScreenName = value
        End Set

    End Property

    Public Property MasterFilterColumn() As String

        Get
            Return m_MasterFilterColumn
        End Get

        Set(ByVal value As String)
            m_MasterFilterColumn = value
        End Set

    End Property

    Public Property MasterFilterRuntimeValue() As String

        Get
            Return m_MasterFilterRuntimeValue
        End Get

        Set(ByVal value As String)
            m_MasterFilterRuntimeValue = value
        End Set

    End Property

    Public Property ViewFieldList() As Generic.List(Of Field)

        Get
            Return m_ViewFieldList
        End Get

        Set(ByVal value As Generic.List(Of Field))
            m_ViewFieldList = value
        End Set

    End Property

End Class