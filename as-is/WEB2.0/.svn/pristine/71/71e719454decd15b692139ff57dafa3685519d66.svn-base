﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class SystemUser
    Inherits myDbBaseClass

    Private m_Id As Long
    Private m_UserName As String
    Private m_Password As String
    Private m_Name As String

    Private m_scr As Screen
    Private m_root As Control

    Private m_DataString As String

    Public Sub New(ByVal scr As Screen, ByVal data_string As String, ByVal Id As Long)
        MyBase.New("SYSTEM_USERS")
        Me.m_scr = scr
        Me.m_root = Nothing
        Me.m_DataString = data_string
        Me.CreateObjectFromSocket(Id)
    End Sub
    Public Sub New(ByVal scr As Screen, ByVal name As String, ByVal user_name As String, ByVal password As String, ByVal Id As Long)
        MyBase.New("SYSTEM_USERS")
        Me.m_scr = scr
        Me.m_root = Nothing
        Me.m_Id = Id
        Me.m_Name = name
        Me.m_UserName = user_name
        Me.m_Password = password
        Me.m_DataString = String.Empty
    End Sub

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("SYSTEM_USERS")
        Me.m_scr = scr
        Me.m_root = root
        Me.m_DataString = String.Empty
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As SystemUser
        Me.IdUser = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "UserName"
                            Me.UserName = CType(m_Control, myTextBox).Text
                        Case "Password"
                            Me.Password = CType(m_Control, myTextBox).Text
                        Case "Name"
                            Me.Name = CType(m_Control, myTextBox).Text
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Private Function CreateObjectFromSocket(ByVal Id As Long) As SystemUser
        Me.IdUser = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Select Case s.PropertyName
                    Case "UserName"
                        Me.UserName = GetProprietyValue(s.FieldDb)
                    Case "Password"
                        Me.Password = GetProprietyValue(s.FieldDb)
                    Case "Name"
                        Me.Name = GetProprietyValue(s.FieldDb)
                End Select
            End If
        Next
        Return Me
    End Function

    Private Function GetProprietyValue(ByVal field_db_name As String) As String
        Dim to_return As String = String.Empty
        Dim data_splitted = Split(m_DataString, ws_separatore_lv4)
        Dim data_struct As List(Of String()) = New List(Of String())

        For Each single_data As String In data_splitted
            If String.Compare(single_data, String.Empty) <> 0 Then
                data_struct.Add(Split(single_data, ws_separatore_lv5))
            End If
        Next

        For Each single_data As String() In data_struct
            If String.Compare(single_data(0), field_db_name) = 0 Then
                to_return = single_data(1)
                Exit For
            End If
        Next
        Return to_return
    End Function

    Public Property IdUser() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property Name() As String
        Get
            Return Me.m_Name
        End Get
        Set(ByVal value As String)
            Me.m_Name = value.Trim
        End Set
    End Property

    Public Property UserName() As String
        Get
            Return Me.m_UserName
        End Get
        Set(ByVal value As String)
            Me.m_UserName = value.Trim
        End Set
    End Property

    Public Property Password() As String
        Get
            Return Me.m_Password
        End Get
        Set(ByVal value As String)
            Me.m_Password = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdUser & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True

            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdUser))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio system users."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If

                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio system users sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If Me.IdUser = m_InvalidId Then
                Me.IdUser = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("ID") = Me.IdUser
            drNodo("USER_NAME") = Me.TrimStringToMaxLen(Me.UserName, "USER_NAME")
            drNodo("PASSWORD") = Me.TrimStringToMaxLen(Me.Password, "PASSWORD")
            drNodo("NAME") = Me.TrimStringToMaxLen(Me.Name, "NAME")

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento system users: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try

        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class