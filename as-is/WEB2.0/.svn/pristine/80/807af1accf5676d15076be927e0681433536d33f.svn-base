﻿Option Strict On

Public Module costanti
    Public Const m_Debugging As Boolean = False ' Settato nel global.asax in base ai dati di registro
    Public Const m_Log_Debugging As Boolean = False ' Settato nel global.asax in base ai dati di registro

    'Generic constant
    Public Const m_ConfigXml As String = "config\config.xml"

    Public Const m_ConfigXmlLanguages As String = "config\languages.xml"
    Public Const m_NameDeleteColumn As String = "DELETE_COLUMN"
    Public Const m_NameEditColumn As String = "EDIT_COLUMN"
    Public Const m_NamePrintColumn As String = "PRINT_COLUMN"
    Public Const m_NameSelectionColumn As String = "SELECTION_COLUMN"
    Public Const m_DefaultLinesPerPage As Integer = 20
    Public Const m_DefaultDecimal As Integer = 0
    Public Const m_InvalidScreen As String = "NULL_SCREEN"
    Public Const m_InvalidId As Long = -1
    Public Const m_InvalidInteger As Integer = -1
    Public Const m_IntegerZero As Integer = 0
    Public Const m_GuestIdGroup As Long = 1
    Public Const m_SystemIdUser As Long = 1
    Public Const m_InvalidDblValue As Double = -1
    Public m_InvalidDateTime As DateTime = New DateTime(1901, 1, 1)
    Public Const m_LongZero As Long = 0
    Public Const m_DoubleZero As Double = 0.0
    Public Const m_SystemUser As Long = 6
    Public Const m_ManualCounter As Integer = 1
    Public Const m_FieldDataDA As String = "DA"
    Public Const m_FieldDataA As String = "A"
    Public Const m_ODBCSqlDateTimeFormat As String = "yyyy-MM-dd HH:mm:ss"
    Public Const m_MaxLotDepth As Integer = 20

    Public Const m_InvalidMaxReferenceBatchSize As Double = 10000000

    Public Const m_StringOff As String = "OFF"
    Public Const m_StringOn As String = "ON"

    Public Const m_StringYes As String = "YES"
    Public Const m_StringNo As String = "NO"

    Public Const m_StringUno As String = "1"
    Public Const m_StringZero As String = "0"

    Public Const m_ConvFactorQuant As Integer = 1       ' 1T = 1T
    Public Const m_DecimalsForProducts As Integer = 2
    Public Const m_DecimalsForPower As Integer = 0     ' kW
    Public Const m_DecimalsForFlowrate As Integer = 2      ' Cose in T/h
    Public Const m_DecimalsForWater As Integer = 0      ' L
    Public Const m_DecimalsForPerc As Integer = 2     ' %

    Public Const m_UnitTon As Integer = 1000
    Public Const m_UnitQli As Integer = 100

    Public Const m_LogFileLinesNumberToKeep As Integer = 5000

    'Generic page constant
    Public Const a_home As String = "~/default.aspx"

    Public Const a_error As String = "~/HttpErrorPage.aspx"
    Public Const a_error_iframe As String = "~/HttpError.aspx"

    'Production Plan
    Public Const m_StatusLocked As Long = 0

    Public Const m_StatusRelease As Long = 1
    Public Const m_StatusStarting As Long = 2
    Public Const m_StatusActive As Long = 3
    Public Const m_StatusCompleted As Long = 4

    'Costanti
    Public Const m_IdWareHouse_Default As Long = 1

    Public Const m_IdProduct_Default As Long = 1

    Public Const m_RecId_NoRecipe As Long = 1

    'Processi (tabella SYSTEM_PROCESS)
    Public Const m_ProcIdProhnd As Short = 1

    Public Const m_ProcIdSyssup As Short = 3
    Public Const m_ProcIdWeb As Short = 4
    Public Const m_ProcIdRephnd As Short = 6

    'costanti WebSocket
    Public Const m_JSnull As String = "null"

    Public Const m_JStrue As String = "true"
    Public Const m_JSfalse As String = "false"
    Public Const ws_separatore_lv0 As String = "!"
    Public Const ws_separatore_lv1 As String = "&"
    Public Const ws_separatore_lv2 As String = "|"
    Public Const ws_separatore_lv3 As String = "$"
    Public Const ws_separatore_lv4 As String = "£"
    Public Const ws_separatore_lv5 As String = "§"

    'Costanti @Mobile
    Public Const m_ProfileGuestUsername As String = "guest"

    Public Const m_ProfileGuestPassword As String = "guest"
    Public Const m_SystemIdGuest As Long = 0
End Module

Public Structure InfoReport
    Dim m_ReportConfig As config
    Dim m_ReportScreen As Screen
    Dim m_ReportPageName As String
    Dim m_ReportMenuName As String
    Dim m_ReportReportName As String
    Dim m_ReportPathFile As String
    Dim m_QueryString As String
End Structure

Public Structure RecipeDefaultValue
    Const m_RadioButton As String = "1"
    Const m_Number As String = "0"
    Const m_CheckBoxOnOff = "OFF"
    Const m_List = "0"
End Structure