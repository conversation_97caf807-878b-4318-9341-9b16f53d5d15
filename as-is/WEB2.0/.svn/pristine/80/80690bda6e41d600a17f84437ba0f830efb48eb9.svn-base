﻿Option Strict On

Public Enum EnumTranslation
    Francese = 0
    Inglese = 1
    Italiano = 2
    Spagnolo = 3
    Russo = 4
    Portoghese = 5
    Tedesco = 6
    Indiano = 7
    Arabo = 8
    Altro = 9
End Enum

Public Enum EnumFieldType
    FieldNumber = 0
    FieldString = 1
    FieldList = 2
    FieldCalendar = 3
    FieldPassword = 4
    FieldAuto = 5
    FieldChkList = 6
    FieldCheckBoxYesNo = 7
    FieldCheckBoxOnOff = 8
    FieldCheckBoxOneZero = 9
    FieldWString = 10
    FieldLink = 11 'Gestisce i campi di update
    FieldLed = 12
    FieldRadioButton = 13
    FieldLabel = 14
    FieldLinkString = 15 'Viene utilizzato nelle datagridview per visualizzare un link in tabella
End Enum

Public Enum EnumNumBound
    Min = 0
    Max = 1
    MinEMax = 2
    Equal = 3
    BoundError = 999
End Enum

Public Enum EnumNumType
    NumInteger = 0
    NumDouble = 1
    NumLong = 2
    NumShort = 3
    NumError = 999
End Enum

Public Enum EnumRequest
    UploadRecipeParameters = 3
    ForceCompleteOrder = 5
    AbortActiveOrder = 6
    SynchronizeTime = 10
    EnableCpu = 22
    CreateReportForMailDistribution = 23
    CreateReportForPrintDistribution = 24
    SendHectolitricWeight = 25
    ConfirmParcel = 26
    ConfirmShipment = 27
    TagWrite = 28
    UpdateActivePagesFroMobile = 29
    SetLogLevelForThread = 30
    ForceCheckAllCyclesControls = 31
    ForceBuildAllProcessRecipes = 32
    ImportDataFromERP = 33
    ExportDataToERP = 34
End Enum

Public Enum EnumPageName
    InvalidPageName = 0
    SystemGroups = 1
    SystemUsers = 2
    SystemUsersGroups = 4
    SystemGroupsRights = 5
    ViewYieldsPrintouts = 6
    EquipmentsModels = 7
    Equipments = 8
    MaintenanceProcedures = 9
    ProcedureDoc = 10
    MaintProcAssignem = 13
    Cells = 15
    ProductsToCells = 16
    EquipmentsMainData = 17
    MaintenancePlanning = 18
    MaintProcReport = 19
    ProductTypes = 20
    Products = 21
    ProductionReports = 22
    Recipes = 23
    ProductionPlan = 24
    CyclesActive = 25
    FlowsActive = 26
    FlowsLogs = 27
    FlowsLogsAchived = 28
    Lots = 31
    Customers = 32
    Suppliers = 33
    Carriers = 34
    FlowLogsProductionReports = 37
    JobToCell = 38
    StockManualCorrReq = 39
    ConfirmFlowLogs = 40
    RecipeLogs = 43
    EquipmentDoc = 44
    MillFeedingsLog = 45
    ProductPerScale = 46
    Parcels = 47
    ParcelsToBeConfirmed = 48
    FlowLogsToParcel = 49
    FlowLogsToParcel999 = 50
    FlowLogsToParcel666 = 51
    Shipments = 52
    ShipmentsToBeConfirmed = 53
    FlowLogsToShipment = 54
    FlowLogsToShipment999 = 55
    FlowLogsToShipment666 = 56
    ProductsBelowLevel = 57
    BatchSizePerScale = 58
    RecipeParameters = 59
    AllowedBatchSizePerScale = 60
    FlowProductionReport = 61
    Analysis = 62
    AnalysisParams = 63
    Operations = 64
    OperationParams = 65
    Contracts = 66
    ContractParams = 67
    RecipeToProducts = 68
    TaskScheduler = 69
    TaskAutomations = 70
    Threads = 71
End Enum

Public Enum EnumAccessLevel
    System = 0 ' usato per l'utente speciale 'system'
    Other = 1
    Adding = 2
    Editing = 3
    Deleting = 4
    Accessing = 5
    Printing = 6
    Searching = 7
End Enum

Public Enum EnumTypeReport
    InstantReport = 0
    PeriodicReport = 1
    JobReport = 2
    InstantPowerReport = 3
    PeriodicPowerReport = 4
    JobPowerReport = 5
    PeriodicShipmentReport = 6
    MillFlowsReport = 7
    ShipmentReport = 8
    ProductsSummationReport = 9
    ParcelReport = 10
    MixingReport = 11
    TransferReport = 12
    CleaningReport = 13
    MillingReport = 14
    GrainsSummationReport = 15
End Enum

Public Enum EnumReportChooser
    ByDate = 0
    ByPeriod = 1
End Enum

Public Enum EnumGraphChooser
    ByDate = 0
    ByLastHours = 1
End Enum

Public Enum EnumGraphInterval
    ByHour = 0
    ByDay = 1
End Enum

Public Enum EnumGraphIndicators
    Flowrate = 1
    PercOnB1 = 2
    PercOnProducts = 3
    InitialHumidity = 4
    FinalHumidity = 5
    Loss = 6
End Enum

Public Enum EnumGetDataFrom
    Standard = 0
    OrderParamValue = 1
    OrderParamValueArchive = 2
    RecipeParamValue = 3
    RecipeParamValueArchive = 4
    AnalysisParamValue = 5
    OperationParamValue = 6
    ContractParamValue = 7
End Enum

Public Enum EnumCompareType
    Insert = 0
    Update = 1
    Both = 2
End Enum

Public Enum EnumAddMenuBound
    View = 0
    Edit = 1
    _New = 2
End Enum

Public Enum EnumTypeCalendarReport
    useCalendars = 0
    currhour = 1
    currshift = 2
    currday = 3
    currweek = 4
    currmonth = 5
    lasthour = 6
    lastshift = 7
    lastday = 8
    lastweek = 9
    lastmonth = 10
    AllData = 11
End Enum

Public Enum EnumMeasurementUnit
    Tons = 0
    Tons_h = 1
    Litri = 2
    Litri_h = 3
    Perc = 4
    Power = 5
End Enum

Public Enum EnumMonths
    January = 1
    February = 2
    March = 3
    April = 4
    May = 5
    June = 6
    July = 7
    August = 8
    September = 9
    October = 10
    November = 11
    December = 12
End Enum

Public Enum EnumLineStatus
    Lock = 0
    Idle = 1
    Starting = 2
    Active = 3
    Complete = 4
End Enum

Public Enum EnumProductionPlanControlTypes
    DestBinProd = 1
    DestBinLots = 2
    SourceBinRestTime = 3
    SourceBinExcluded = 4
    DestBinExcluded = 5
    SourceBinProd = 6
End Enum

Public Enum EnumWaitOperation
    Submit = 1
    Upload = 2
End Enum

Public Enum EnumDateFormats
    DateTime = 0
    DateOnly = 1
    TimeOnly = 2
End Enum