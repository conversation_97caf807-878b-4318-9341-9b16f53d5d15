﻿Option Strict On

Imports System.Data.OleDb

Public Class CommandInterface
    Inherits myDbBaseClass

    Private m_request_id As Long = m_InvalidId
    Private m_parameter_1 As String = String.Empty
    Private m_parameter_2 As String = String.Empty
    Private m_parameter_3 As String = String.Empty
    Private m_parameter_4 As String = String.Empty

    Public Sub New(ByVal request_id As EnumRequest, Optional ByVal parameter_1 As String = "", Optional ByVal parameter_2 As String = "",
                   Optional ByVal parameter_3 As String = "", Optional ByVal parameter_4 As String = "")
        MyBase.New("COMMAND_INTERFACE")

        Me.m_request_id = request_id
        Me.m_parameter_1 = parameter_1
        Me.m_parameter_2 = parameter_2
        Me.m_parameter_3 = parameter_3
        Me.m_parameter_4 = parameter_4
    End Sub

    Public Property RequestId() As Long
        Get
            Return Me.m_request_id
        End Get
        Set(ByVal value As Long)
            Me.m_request_id = value
        End Set
    End Property

    Public Property Parameter1() As String
        Get
            Return m_parameter_1
        End Get
        Set(value As String)
            m_parameter_1 = value.Trim
        End Set
    End Property

    Public Property Parameter2() As String
        Get
            Return m_parameter_2
        End Get
        Set(value As String)
            m_parameter_2 = value.Trim
        End Set
    End Property

    Public Property Parameter3() As String
        Get
            Return m_parameter_3
        End Get
        Set(value As String)
            m_parameter_3 = value.Trim
        End Set
    End Property

    Public Property Parameter4() As String
        Get
            Return m_parameter_4
        End Get
        Set(value As String)
            m_parameter_4 = value.Trim
        End Set
    End Property

    Public Sub PostUniqueRequest()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim dsNodo2 As New DataSet
        Dim drNodo As DataRow
        Dim DB As New OleDb.OleDbConnection
        Dim ret_val = False

        DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
        DB.Open()

        drNodo = Nothing

        sSelect = "SELECT PARAMETERS_NEEDED FROM REQUESTS WHERE ID = " & Me.RequestId

        Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
        Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
        Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

        daNodo.Fill(dsNodo, "REQUESTS")
        daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

        If dsNodo.Tables("REQUESTS").Rows.Count > 0 Then
            drNodo = dsNodo.Tables("REQUESTS").Rows(0)

            Dim param_needed As Integer = CInt(drNodo.Item(0).ToString)

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE REQUEST_ID=" & Me.RequestId & " AND DATE_DONE IS NULL"

            If param_needed >= 1 Then
                If Me.Parameter1 = String.Empty Then
                    sSelect &= " AND (PARAMETER_1 IS NULL OR PARAMETER_1 = '')"
                Else
                    sSelect &= " AND PARAMETER_1 = '" & tools.SqlStr(Me.Parameter1) & "'"
                End If
            End If

            If param_needed >= 2 Then
                If Me.Parameter2 = String.Empty Then
                    sSelect &= " AND (PARAMETER_2 IS NULL OR PARAMETER_2 = '')"
                Else
                    sSelect &= " AND PARAMETER_2 = '" & tools.SqlStr(Me.Parameter2) & "'"
                End If
            End If

            If param_needed >= 3 Then
                If Me.Parameter3 = String.Empty Then
                    sSelect &= " AND (PARAMETER_3 IS NULL OR PARAMETER_3 = '')"
                Else
                    sSelect &= " AND PARAMETER_3 = '" & tools.SqlStr(Me.Parameter3) & "'"
                End If
            End If

            If param_needed >= 4 Then
                If Me.Parameter4 = String.Empty Then
                    sSelect &= " AND (PARAMETER_4 IS NULL OR PARAMETER_4 = '')"
                Else
                    sSelect &= " AND PARAMETER_4 = '" & tools.SqlStr(Me.Parameter4) & "'"
                End If
            End If
        Else
            ' richiesta non presente nella tabella REQUESTS
        End If

        Dim selCom2 As New OleDb.OleDbCommand(sSelect, DB)
        Dim daNodo2 As New OleDb.OleDbDataAdapter(selCom2)
        Dim cmdBuilder2 As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo2)

        daNodo2.Fill(dsNodo2, Me.TableName)
        daNodo2.MissingSchemaAction = MissingSchemaAction.AddWithKey

        If dsNodo2.Tables(Me.TableName).Rows.Count = 0 Then
            Try
                Me.PostRequest()
            Catch ex As myException.myException
                Throw ex
            Finally
                DB.Close()
            End Try
        End If
    End Sub

    Private Sub PostRequest()

        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
        DB.Open()

        drNodo = Nothing

        sSelect = "SELECT * FROM " & Me.TableName

        Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
        Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
        Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

        daNodo.Fill(dsNodo, Me.TableName)
        daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

        drNodo = dsNodo.Tables(Me.TableName).NewRow
        bRigaDaCreare = True

        drNodo("REQUEST_ID") = Me.RequestId

        If Me.Parameter1 <> String.Empty Then
            drNodo("PARAMETER_1") = Me.TrimStringToMaxLen(Me.Parameter1, "PARAMETER_1")
        Else
            drNodo("PARAMETER_1") = DBNull.Value
        End If

        If Me.Parameter2 <> String.Empty Then
            drNodo("PARAMETER_2") = Me.TrimStringToMaxLen(Me.Parameter2, "PARAMETER_2")
        Else
            drNodo("PARAMETER_2") = DBNull.Value
        End If

        If Me.Parameter3 <> String.Empty Then
            drNodo("PARAMETER_3") = Me.TrimStringToMaxLen(Me.Parameter3, "PARAMETER_3")
        Else
            drNodo("PARAMETER_3") = DBNull.Value
        End If

        If Me.Parameter4 <> String.Empty Then
            drNodo("PARAMETER_4") = Me.TrimStringToMaxLen(Me.Parameter4, "PARAMETER_4")
        Else
            drNodo("PARAMETER_4") = DBNull.Value
        End If

        drNodo("DATE_DONE") = DBNull.Value

        dsNodo.Tables(Me.TableName).Rows.Add(drNodo)

        Try
            daNodo.Update(dsNodo, Me.TableName)
            dsNodo.AcceptChanges()
        Catch ex As OleDbException
            dsNodo.RejectChanges()
            Dim msg As String = "Errore durante aggiornamento command interface: " & ex.Message
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class