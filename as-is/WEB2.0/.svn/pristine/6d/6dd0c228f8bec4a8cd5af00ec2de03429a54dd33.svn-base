﻿Option Strict On

Public Enum TypeOfData
    type_string = 0
    type_integer = 1
    type_double = 2
    type_boolean = 3
End Enum

Public Enum RequestAtMobile
    CustomRequest = 0
    TagRead = 1
    TagWrite = 2
    GetTime = 3
    SyncTime = 4
    Login = 5
    Screen = 6
    GetExtra = 7

    Ping = 98
    Pong = 99
End Enum

Public Enum RequestAtMobile_timer
    CustomRequest = 1
    TagRead = 1000
    TagWrite = 1
    GetTime = 300
    SyncTime = 1
    Login = 1
    Screen = 1
    GetExtra = 1

    Ping = 1
    Pong = 1
End Enum

Public Enum IdentifyExtraAtMobile
    ScreenData = 1
    DataSettings = 2
End Enum

Public Enum RollermillsAtMobile
    MachineData = 100
    MachinesStatus = 101
    MachinesConnections = 102
    MachineValues = 103
    MachineWrite = 104
    MachineAction = 105
    MachineLockData = 106
End Enum

Public Enum RollermillsAtMobile_timer
    MachineData = 1
    MachinesStatus = 1000
    MachinesConnections = 1000
    MachineValues = 1000
    MachineWrite = 1
    MachineAction = 1
    MachineLockData = 1000
End Enum

Public Enum SupervisorAtMobile
    ScadaGroupData = 150
    ScadaCommand = 151
End Enum

Public Enum SupervisorAtMobile_timer
    ScadaGroupData = -1
    ScadaCommand = 1
End Enum

Public Enum IdentifyScadaCommand
    HornStop = 1
    HornTest = 2
End Enum

Public Enum UsersManagementsAtMobile
    UsersManagementsData = 200
    UsersManagementsAdd = 201
    UsersManagementsGetEdit = 202
    UsersManagementsSetEdit = 203
    UsersManagementsRemove = 204
End Enum

Public Enum UsersManagementsAtMobile_timer
    UsersManagementsData = 1
    UsersManagementsAdd = 1
    UsersManagementsGetEdit = 1
    UsersManagementsSetEdit = 1
    UsersManagementsRemove = 1
End Enum

Public Enum UsersManagementsExtraAtMobile
    UsersManagementsGroupsRights = 201
End Enum

Public Enum IlluminazioneAtMobile
    IlluminazioneData = 250
    IlluminazioneStatus = 251
    TurnOffZona = 252
    TurnOffArea = 253
    TurnOffEdificio = 254
End Enum

Public Enum IlluminazioneAtMobile_timer
    IlluminazioneData = 1
    IlluminazioneStatus = 1500
    TurnOffZona = 1
    TurnOffArea = 1
    TurnOffEdificio = 1
End Enum

Public Enum IpackimaAtMobile
    GroupDataRMS = 300
End Enum

Public Enum IpackimaAtMobile_timer
    GroupDataRMS = -1
End Enum

Public Enum ScreenAtMobile
    SystemUsers = 0
    SystemGroups = 1
    SystemUsersGroups = 2
    SystemGroupsRights = 3
End Enum

Public Enum MachineMenuPage
    Runtime = 0
    Settings = 1
    Alarms = 2
    Calibrate = 3
    Maintenance = 4
End Enum

Public Enum TypeResponse
    exception = -1
    ack = 0
    data = 1
End Enum

Public Enum TypeUnlock
    UserAction = 1          'azione dell'utente di rilascio volontario
    UserAdministrator = 2   'amministratore forza il rilascio di una macchina bloccata da un'altro utente
    Auto = 3                'rilascio segnato dal sistema
    System = 4              '
End Enum

Public Enum MachineActionDataCmd
    Reserve = 1
    Release = 2
    ClearLock = 3
    ClearAlarms = 4
End Enum

'----------------------------------------------------------------------------------------------------------
Public Enum DateLockFilter
    All = 1
    Mach = 2
    Dev = 3
    User = 4
    StatusLock = 5
End Enum

Public Enum ErrPermissionResponse
    DECRIPT_ERR = 0
    DATA_EXPIRE = 1
    KEY_ERR = 2
    MISS_PERMISS_DB = 3
End Enum

Public Enum TPTagsWriter
    WATCHDOG = 8000
    VIS_KM = 8001
    VIS_SELETTORE = 8002
    CMD_TEST = 8003
    CMD_REM = 8004
    CMD_ZERO = 8005

    TP_SEL_MOT_BASE = 8010
End Enum

Public Enum IdentifyGetInfo
    DefaultVal = 0
    InfoSystemUsers = 1
    InfoSystemGroups = 2
    InfoChkSystemGroups = 3
    DataSystemGroupsRights = 4
    GraphDataInstant = 5
    GraphDataPeriodic = 6
    GraphDataAvarage = 7
    GraphDataPotenza = 8
    GraphDataTensione = 9
    GraphDataVenduto = 10
    ReportConsumePeriodic = 11
End Enum

Public Enum EnumMobilePageName
    InvalidPageName = 0
    SettingsBase = 100
    SettingsExtra = 101
    SettingsReportApp = 102
    SettingsUsers_Users = 103
    SettingsUsers_GroupsUsers = 104
    SettingsUsers_Groups = 105
    SettingsUsers_GroupsRights = 106
    Warnings_ProcessWarnings = 107
    Warnings_SystemWarnings = 108
    Warnings_SystemInformations = 109
End Enum

Public Enum EnumTypeFilterQry
    uguale = 0
    maggiore_stretto = 1
    minore_stretto = 2
    maggiore_uguale = 3
    minore_uguale = 4
    diverso = 5
    like_to = 6
    between = 7
End Enum