﻿Option Strict On

Imports System.Data.OleDb

Public Class ExecutedMaintProc
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdSu As Long = m_InvalidId
    Private m_IdMpl As Long = m_InvalidId
    Private m_Intervention_report As String = String.Empty
    Private m_Cancel_flag As String = String.Empty

    Public Sub New(ByVal su_id As Long, ByVal mpl_id As Long, ByVal intervention_report As String, ByVal cancel_flag As String)
        MyBase.New("EXECUTED_MAINT_PROC")
        Me.IdExecutedMaintProc = m_InvalidId
        Me.IdSu = su_id
        Me.IdMpl = mpl_id
        Me.InterventionReport = intervention_report
        Me.CancelFlag = cancel_flag
    End Sub

    Public Property IdExecutedMaintProc() As Long
        Get
            Return m_Id
        End Get
        Set(value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdSu() As Long
        Get
            Return m_IdSu
        End Get
        Set(value As Long)
            Me.m_IdSu = value
        End Set
    End Property

    Public Property IdMpl() As Long
        Get
            Return m_IdMpl
        End Get
        Set(value As Long)
            Me.m_IdMpl = value
        End Set
    End Property

    Public Property InterventionReport() As String
        Get
            Return m_Intervention_report
        End Get
        Set(value As String)
            Me.m_Intervention_report = value.Trim
        End Set
    End Property

    Public Property CancelFlag() As String
        Get
            Return m_Cancel_flag
        End Get
        Set(value As String)
            Me.m_Cancel_flag = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdExecutedMaintProc & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdExecutedMaintProc))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio equipment"
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio equipment sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdExecutedMaintProc

            If Me.IdExecutedMaintProc = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            If Me.IdSu <> m_InvalidId Then
                drNodo("SU_ID") = Me.IdSu
            End If

            If Me.IdMpl <> m_InvalidId Then
                drNodo("MPL_ID") = Me.IdMpl
            End If

            drNodo("INTERVENTION_REPORT") = Me.TrimStringToMaxLen(Me.InterventionReport, "INTERVENTION_REPORT")
            drNodo("CANCEL_FLAG") = Me.TrimStringToMaxLen(Me.CancelFlag, "CANCEL_FLAG")

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento executed_maint_proc: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class