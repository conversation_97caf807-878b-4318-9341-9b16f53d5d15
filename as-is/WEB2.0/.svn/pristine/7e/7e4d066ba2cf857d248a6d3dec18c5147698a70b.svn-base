﻿Imports System.Web.UI.WebControls
Imports WebDataBaseLayer

Public Class myCheckBoxList
    Inherits System.Web.UI.WebControls.CheckBoxList

    Private m_Tag As Object

    Public Property Tag() As Object
        Get
            Return m_Tag
        End Get
        Set(ByVal value As Object)
            Me.m_Tag = value
        End Set
    End Property

End Class

Public Class ChkmyList

    Private m_IsQueryField As Boolean
    Private m_OrderBy As String
    Private m_Where As String
    Private m_SelectColumns As String
    Private m_ValueColumn As String
    Private m_From As String
    Private m_EventsFieldName As String
    Private m_EventWhere As String
    Private m_HasEvents As Boolean
    Private m_Parent As Object
    Private m_HasParentEvent As Boolean
    Private m_SelectedItemColumn As String
    Private m_EventParentName As String

    Public Sub New()

    End Sub

    Public Property SelectedItemColumn() As String
        Get
            Return Me.m_SelectedItemColumn
        End Get
        Set(ByVal value As String)
            Me.m_SelectedItemColumn = value
        End Set
    End Property

    Public Property EventParentName() As String
        Get
            Return m_EventParentName
        End Get
        Set(ByVal value As String)
            m_EventParentName = value
        End Set
    End Property

    Public Property EventWhere() As String
        Get
            Return Me.m_EventWhere
        End Get
        Set(ByVal value As String)
            Me.m_EventWhere = value
        End Set
    End Property

    Public Property Parent() As Object
        Get
            Return Me.m_Parent
        End Get
        Set(ByVal value As Object)
            Me.m_Parent = value
        End Set
    End Property

    Public Property EventsFieldName() As String
        Get
            Return Me.m_EventsFieldName
        End Get
        Set(ByVal value As String)
            Me.m_EventsFieldName = value
        End Set
    End Property

    Public Property HasEvents() As Boolean
        Get
            Return Me.m_HasEvents
        End Get
        Set(ByVal value As Boolean)
            Me.m_HasEvents = value
        End Set
    End Property

    Public Property IsQueryField() As Boolean
        Get
            Return Me.m_IsQueryField
        End Get
        Set(ByVal value As Boolean)
            Me.m_IsQueryField = value
        End Set
    End Property

    Public Property HasParentEvent() As Boolean
        Get
            Return Me.m_HasParentEvent
        End Get
        Set(ByVal value As Boolean)
            Me.m_HasParentEvent = value
        End Set
    End Property

    Public Property From() As String
        Get
            Return Me.m_From
        End Get
        Set(ByVal value As String)
            Me.m_From = value
        End Set
    End Property

    Public Property ValueColumn() As String
        Get
            Return Me.m_ValueColumn
        End Get
        Set(ByVal value As String)
            Me.m_ValueColumn = value
        End Set
    End Property

    Public Property SelectColumns() As String
        Get
            Return Me.m_SelectColumns
        End Get
        Set(ByVal value As String)
            Me.m_SelectColumns = value
        End Set
    End Property

    Public Property OrderBy() As String
        Get
            Return Me.m_OrderBy
        End Get
        Set(ByVal value As String)
            Me.m_OrderBy = value
        End Set
    End Property

    Public Property Where() As String
        Get
            Return Me.m_Where
        End Get
        Set(ByVal value As String)
            Me.m_Where = value
        End Set
    End Property

    ''' <summary>
    ''' Disegna il CheckBoxmyList In base ai dati passati in argomento
    ''' </summary>
    ''' <param name="FieldName"></param>
    ''' <param name="dt_result"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function DrawObject(ByVal FieldName As String, ByVal dt_result As Data.DataTable, ByVal m_ReadOnly As Boolean) As myCheckBoxList
        If Me IsNot Nothing Then
            Dim sSelect As String
            Dim dt As Data.DataTable
            Dim ddl As New myCheckBoxList

            ddl.ID = FieldName
            ddl.Tag = Me.EventsFieldName
            ddl.CssClass = "ddl-text"
            ddl.RepeatColumns = 2
            ddl.RepeatDirection = RepeatDirection.Horizontal
            ddl.Enabled = Not m_ReadOnly
            If Not Me.HasParentEvent Then

                sSelect = "SELECT " & Me.SelectColumns & "," & Me.ValueColumn & " FROM " & Me.From
                If Me.Where <> String.Empty Then
                    sSelect &= " WHERE " & Me.Where
                End If
                If Me.OrderBy <> String.Empty Then
                    sSelect &= " ORDER BY " & Me.OrderBy
                End If

                dt = DataBase.ExecuteSQL_DataTable(sSelect, True)

                If dt Is Nothing Then
                    Return ddl
                End If

                If Me.HasEvents And Me.EventsFieldName <> String.Empty Then
                    ddl.AutoPostBack = True
                End If

                ddl.DataSource = dt
                ddl.DataTextField = Me.SelectColumns
                ddl.DataValueField = Me.ValueColumn
                ddl.EnableViewState = True
                ddl.DataBind()
            End If

            If Me.HasEvents And Me.EventsFieldName <> String.Empty Then
                AddHandler ddl.SelectedIndexChanged, AddressOf showListSelection
            End If

            If dt_result IsNot Nothing Then
                For Each dr As Data.DataRow In dt_result.Rows
                    For Each lst As ListItem In ddl.Items
                        If lst.Value = dr(Me.SelectedItemColumn).ToString Then
                            lst.Selected = True
                            Exit For
                        End If
                    Next

                Next
            End If

            Return ddl
        End If
        Return Nothing
    End Function

    ''' <summary>
    ''' Draw the CheckBoxmyList for the list.
    ''' </summary>
    ''' <param name="c">TableCell</param>
    ''' <param name="qf">QueryFiled</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    '''
    Public Overloads Function SetDataBound(ByVal c As TableCell, ByVal qf As Field, ByVal m_ReadOnly As Boolean) As TableCell
        If Me IsNot Nothing Then
            c.Controls.Add(DrawObject(qf.FieldDb, Nothing, m_ReadOnly))
        End If
        Return c
    End Function

    ''' <summary>
    ''' Draw the CheckBoxmyList for the list.
    ''' </summary>
    ''' <param name="c">TableCell</param>
    ''' <param name="uf">UpdateFiled</param>
    ''' <param name="dt">DataTable Results</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Overloads Function SetDataBound(ByVal c As System.Web.UI.HtmlControls.HtmlTableCell, ByVal uf As Field, ByVal dt As Data.DataTable, ByVal m_ReadOnly As Boolean) As System.Web.UI.HtmlControls.HtmlTableCell
        If Me IsNot Nothing Then
            c.Controls.Add(DrawObject(uf.FieldDb, dt, m_ReadOnly))
        End If
        Return c
    End Function

    ''' <summary>
    ''' Draw the CheckBoxmyList for the list.
    ''' </summary>
    ''' <param name="c">HtmlTableCell</param>
    ''' <param name="in_f">InsertField</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Overloads Function SetDataBound(ByVal c As System.Web.UI.HtmlControls.HtmlTableCell, ByVal in_f As Field, ByVal m_ReadOnly As Boolean) As System.Web.UI.HtmlControls.HtmlTableCell
        If Me IsNot Nothing Then
            c.Controls.Add(DrawObject(in_f.FieldDb, Nothing, m_ReadOnly))
        End If
        Return c
    End Function

    ''' <summary>
    ''' Compila il CheckBoxmyList assoggettato ad evento da un'altro CheckBoxmyList principale
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    ''' <remarks></remarks>
    Private Sub showListSelection(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim ddl As myCheckBoxList = CType(sender, myCheckBoxList)

        Dim scr As Screen

        If Me.IsQueryField Then
            scr = CType(Me.Parent, Field).ParentMenu

            For Each qf As Field In scr.ViewFields
                If qf.FieldName = ddl.Tag Then
                    Me.SetEventOnject(ddl, qf.ObjectList)
                End If
            Next
        End If
    End Sub

    ''' <summary>
    ''' Popola il CheckBoxmyList successivo al SelectIndexChange del suo chiamante
    ''' </summary>
    ''' <param name="ParentObject"></param>
    ''' <param name="ObjList"></param>
    ''' <remarks></remarks>
    Public Sub SetEventOnject(ByVal ParentObject As myCheckBoxList, ByVal ObjList As myList)
        Dim sSelect As String
        Dim dt As Data.DataTable = Nothing
        Dim ddl_control As myCheckBoxList

        If ParentObject.SelectedValue.ToString <> String.Empty Then
            sSelect = "SELECT " & ObjList.SelectColumns & "," & Me.ValueColumn & " FROM " & ObjList.From
            If Me.Where <> String.Empty Then
                sSelect &= " WHERE " & ObjList.Where
                If Me.EventWhere <> String.Empty Then
                    sSelect &= " AND " & Me.EventWhere & "='" & ParentObject.SelectedValue.ToString & "'"
                End If
            Else
                If Me.EventWhere <> String.Empty Then
                    sSelect &= " WHERE " & Me.EventWhere & "='" & ParentObject.SelectedValue.ToString & "'"
                End If
            End If
            If Me.OrderBy <> String.Empty Then
                sSelect &= " ORDER BY " & ObjList.OrderBy
            End If

            dt = DataBase.ExecuteSQL_DataTable(sSelect, True)

            If dt Is Nothing Then
                Exit Sub
            End If

        End If

        ddl_control = CType(tools.FindControlRecursive(ParentObject.Page, CType(ObjList.Parent, Field).FieldDb), myCheckBoxList)

        If ddl_control Is Nothing Then
            Exit Sub
        End If

        ddl_control.Tag = ObjList.EventsFieldName

        If Me.HasEvents And Me.EventsFieldName <> String.Empty Then
            ddl_control.AutoPostBack = True
        End If

        ddl_control.DataSource = dt
        If dt IsNot Nothing Then
            ddl_control.DataValueField = ObjList.ValueColumn
            ddl_control.DataTextField = ObjList.SelectColumns
        Else
            ddl_control.SelectedIndex = -1
            ddl_control.Items.Clear()
            showListSelection(ddl_control, New System.EventArgs)
        End If
        ddl_control.DataBind()

    End Sub

End Class