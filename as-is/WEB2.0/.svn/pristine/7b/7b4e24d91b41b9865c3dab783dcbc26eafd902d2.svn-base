﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class ProcedureDoc
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdMaintenanceProcedures As Long = m_InvalidId
    Private m_Link As String = String.Empty
    Private m_Description As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("PROCEDURE_DOCS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As ProcedureDoc
        Me.IdProcedureDoc = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "IdMaintenanceProcedures"
                            Try
                                Me.IdMaintenanceProcedures = Long.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.IdMaintenanceProcedures = costanti.m_InvalidId
                            End Try
                        Case "Description"
                            Me.Description = CType(m_Control, myTextBox).Text
                        Case "Link"
                            Me.Link = CType(m_Control, myTextBox).Text
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdProcedureDoc() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdMaintenanceProcedures() As Long
        Get
            Return Me.m_IdMaintenanceProcedures
        End Get
        Set(ByVal value As Long)
            Me.m_IdMaintenanceProcedures = value
        End Set
    End Property

    Public Property Description() As String
        Get
            Return Me.m_Description
        End Get
        Set(ByVal value As String)
            Me.m_Description = value.Trim
        End Set
    End Property

    Public Property Link() As String
        Get
            Return Me.m_Link
        End Get
        Set(ByVal value As String)
            Me.m_Link = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdProcedureDoc & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True

            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdProcedureDoc))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio  documenti procedura."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If

                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio  documenti procedura sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If drNodo("ID") Is DBNull.Value OrElse Me.IdProcedureDoc = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            drNodo("DESCRIPTION") = Me.TrimStringToMaxLen(Me.Description, "DESCRIPTION")
            drNodo("LINK") = Me.TrimStringToMaxLen(Me.Link, "LINK")
            drNodo("MP_ID") = Me.IdMaintenanceProcedures

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento documenti procedura: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try

        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class