﻿Public Class GroupDataObj
    Private m_groups As Integer()
    Private m_baggings As Integer()
    Private m_cells As Integer()
    Private m_dm As Integer()
    Private m_dosers As Integer()
    Private m_weigher As Integer()
    Private m_rm As Integer()
    Private m_tags As Integer()

    Public Sub New()
        With Me

        End With
    End Sub

    Public Sub New(ByVal groups As Integer(), ByVal baggings As Integer(), ByVal cells As Integer(), ByVal dm As Integer(), ByVal dosers As Integer(), ByVal weigher As Integer(), ByVal rm As Integer(), ByVal tags As Integer())
        With Me
            .m_groups = groups
            .m_baggings = baggings
            .m_cells = cells
            .m_dm = dm
            .m_dosers = dosers
            .m_weigher = weigher
            .m_rm = rm
            .m_tags = tags
        End With
    End Sub

    Public Property GROUPS() As Integer()
        Get
            Return m_groups
        End Get
        Set(ByVal value As Integer())
            Me.m_groups = value
        End Set
    End Property
    Public Property BAGGINGS() As Integer()
        Get
            Return m_baggings
        End Get
        Set(ByVal value As Integer())
            Me.m_baggings = value
        End Set
    End Property
    Public Property CELLS() As Integer()
        Get
            Return m_cells
        End Get
        Set(ByVal value As Integer())
            Me.m_cells = value
        End Set
    End Property
    Public Property DM() As Integer()
        Get
            Return m_dm
        End Get
        Set(ByVal value As Integer())
            Me.m_dm = value
        End Set
    End Property
    Public Property DOSERS() As Integer()
        Get
            Return m_dosers
        End Get
        Set(ByVal value As Integer())
            Me.m_dosers = value
        End Set
    End Property
    Public Property WEIGHER() As Integer()
        Get
            Return m_weigher
        End Get
        Set(ByVal value As Integer())
            Me.m_weigher = value
        End Set
    End Property
    Public Property RM() As Integer()
        Get
            Return m_rm
        End Get
        Set(ByVal value As Integer())
            Me.m_rm = value
        End Set
    End Property
    Public Property TAGS() As Integer()
        Get
            Return m_tags
        End Get
        Set(ByVal value As Integer())
            Me.m_tags = value
        End Set
    End Property
End Class