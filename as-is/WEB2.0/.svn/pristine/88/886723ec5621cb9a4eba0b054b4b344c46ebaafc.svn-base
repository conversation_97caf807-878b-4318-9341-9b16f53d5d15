﻿Imports System.Web.UI.WebControls
Imports WebDataBaseLayer

#Region "TextBox Numerici"

Public Class NumberAtmill

    Private m_NumType As String
    Private m_NumBound As String
    Private m_Value As Object

    Public Sub New()

    End Sub

    Public Property Value() As Object
        Get
            If Not IsNothing(Me.m_Value) Then
                Select Case Me.m_NumType.ToUpper
                    Case "INTEGER"
                        Return Integer.Parse(Me.m_Value.ToString)
                    Case "DOUBLE"
                        Return tools.WebInputDoubleParse(Me.m_Value.ToString, 1)
                    Case "LONG"
                        Return Long.Parse(Me.m_Value.ToString)
                    Case "SHORT"
                        Return Short.Parse(Me.m_Value.ToString)
                End Select
            End If
            Return Me.m_Value
        End Get
        Set(ByVal value As Object)
            Select Case Me.m_NumType.ToUpper
                Case "INTEGER"
                    Me.m_Value = Integer.Parse(value.ToString)
                Case "DOUBLE"
                    Me.m_Value = tools.WebInputDoubleParse(value.ToString, 1)
                Case "LONG"
                    Me.m_Value = Long.Parse(value.ToString)
                Case "SHORT"
                    Me.m_Value = Short.Parse(value.ToString)
            End Select
        End Set
    End Property

    Public Property NumType() As String
        Get
            Return Me.m_NumType
        End Get
        Set(ByVal value As String)
            Me.m_NumType = value
        End Set
    End Property

    Public Property NumBound() As String
        Get
            Return Me.m_NumBound
        End Get
        Set(ByVal value As String)
            Me.m_NumBound = value
        End Set
    End Property

    Public ReadOnly Property GetNumType() As EnumNumType
        Get
            Select Case Me.m_NumType.ToUpper
                Case "INTEGER"
                    Return EnumNumType.NumInteger
                Case "DOUBLE"
                    Return EnumNumType.NumDouble
                Case "LONG"
                    Return EnumNumType.NumLong
                Case "SHORT"
                    Return EnumNumType.NumShort
                Case Else
                    Return EnumNumType.NumError
            End Select
        End Get
    End Property

    Public ReadOnly Property GetNumBound() As EnumNumBound
        Get
            Select Case Me.m_NumBound.ToUpper
                Case "EQUAL"
                    Return EnumNumBound.Equal
                Case "MAX"
                    Return EnumNumBound.Max
                Case "MIN"
                    Return EnumNumBound.Min
                Case "MINMAX"
                    Return EnumNumBound.MinEMax
                Case Else
                    Return EnumNumBound.BoundError
            End Select
        End Get
    End Property

    ''' <summary>
    ''' Draw the textbox for the number. If I use the MINMAX option I draw 2 textbox
    ''' </summary>
    ''' <param name="c">TableCell</param>
    ''' <param name="m_config">Config object</param>
    ''' <param name="FieldName">FieldName from config.xml</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Function SetDataBoundToCell(ByVal c As System.Web.UI.HtmlControls.HtmlTableCell, ByVal m_config As config, ByVal FieldName As String) As System.Web.UI.HtmlControls.HtmlTableCell
        Dim l As Label
        If Me IsNot Nothing Then
            Select Case Me.GetNumBound
                Case EnumNumBound.MinEMax

                    Dim t_min As New TextBox
                    t_min.ID = FieldName & "_MIN"
                    t_min.Attributes.Add("type", "number")
                    t_min.Attributes.Add("step", "any")
                    t_min.CssClass = "textbox-text-right"
                    c.Controls.Add(t_min)

                    l = New Label
                    l.Text = "&nbsp;" & m_config.GetEntryByKeyName("MIN").GetValue & "&nbsp;&nbsp;"
                    l.CssClass = "label-text"
                    c.Controls.Add(l)

                    Dim t_max As New TextBox
                    t_max.ID = FieldName & "_MAX"
                    t_max.Attributes.Add("type", "number")
                    t_max.Attributes.Add("step", "any")
                    t_max.CssClass = "textbox-text-right"
                    c.Controls.Add(t_max)

                    l = New Label
                    l.Text = "&nbsp;" & m_config.GetEntryByKeyName("MAX").GetValue
                    l.CssClass = "label-text"
                    c.Controls.Add(l)

                Case EnumNumBound.Min

                    Dim t As New TextBox
                    t.ID = FieldName & "_MIN"
                    t.Attributes.Add("type", "number")
                    t.Attributes.Add("step", "any")
                    t.CssClass = "textbox-text-right"
                    c.Controls.Add(t)

                    l = New Label
                    l.Text = "&nbsp;" & m_config.GetEntryByKeyName("MIN").GetValue
                    l.CssClass = "label-text"
                    c.Controls.Add(l)

                Case EnumNumBound.Max

                    Dim t As New TextBox
                    t.ID = FieldName & "_MAX"
                    t.Attributes.Add("type", "number")
                    t.Attributes.Add("step", "any")
                    t.CssClass = "textbox-text-right"
                    c.Controls.Add(t)

                    l = New Label
                    l.Text = "&nbsp;" & m_config.GetEntryByKeyName("MAX").GetValue
                    l.CssClass = "label-text"
                    c.Controls.Add(l)

                Case EnumNumBound.Equal
                    Dim t As New TextBox
                    t.ID = FieldName
                    t.Attributes.Add("type", "number")
                    t.Attributes.Add("step", "any")
                    t.CssClass = "textbox-text-right"
                    c.Controls.Add(t)
            End Select
        End If
        Return c
    End Function

End Class

#End Region

#Region "Gestione DropDownList"

Public Class myDropDownList
    Inherits DropDownList

    Private m_Tag As Object
    Private m_FieldObject As Field

    Public Property FieldObject() As Field
        Get
            Return m_FieldObject
        End Get
        Set(ByVal value As Field)
            m_FieldObject = value
        End Set
    End Property

    Public Property Tag() As Object
        Get
            Return m_Tag
        End Get
        Set(ByVal value As Object)
            Me.m_Tag = value
        End Set
    End Property

End Class

Public Class myList
    Private m_IsReadOnly As Boolean
    Private m_OrderBy As String
    Private m_Where As String
    Private m_SelectColumns As String
    Private m_ValueColumn As String
    Private m_From As String
    Private m_EventsFieldName As String
    Private m_EventWhere As String
    Private m_AddNull As Boolean
    Private m_HasEvents As Boolean
    Private m_Parent As Object
    Private m_HasParentEvent As Boolean
    Private m_EventParentName As String

    Public Sub New()

    End Sub

    Public Property EventWhere() As String
        Get
            Return Me.m_EventWhere
        End Get
        Set(ByVal value As String)
            Me.m_EventWhere = value
        End Set
    End Property

    Public Property Parent() As Object
        Get
            Return Me.m_Parent
        End Get
        Set(ByVal value As Object)
            Me.m_Parent = value
        End Set
    End Property

    Public Property EventParentName() As String
        Get
            Return Me.m_EventParentName
        End Get
        Set(ByVal value As String)
            Me.m_EventParentName = value
        End Set
    End Property

    Public Property EventsFieldName() As String
        Get
            Return Me.m_EventsFieldName
        End Get
        Set(ByVal value As String)
            Me.m_EventsFieldName = value
        End Set
    End Property

    Public Property HasEvents() As Boolean
        Get
            Return Me.m_HasEvents
        End Get
        Set(ByVal value As Boolean)
            Me.m_HasEvents = value
        End Set
    End Property

    Public Property IsReadOnly() As Boolean
        Get
            Return Me.m_IsReadOnly
        End Get
        Set(ByVal value As Boolean)
            Me.m_IsReadOnly = value
        End Set
    End Property

    Public Property HasParentEvent() As Boolean
        Get
            Return Me.m_HasParentEvent
        End Get
        Set(ByVal value As Boolean)
            Me.m_HasParentEvent = value
        End Set
    End Property

    Public Property AddNull() As Boolean
        Get
            Return Me.m_AddNull
        End Get
        Set(ByVal value As Boolean)
            Me.m_AddNull = value
        End Set
    End Property

    Public Property From() As String
        Get
            Return Me.m_From
        End Get
        Set(ByVal value As String)
            Me.m_From = value
        End Set
    End Property

    Public Property ValueColumn() As String
        Get
            Return Me.m_ValueColumn
        End Get
        Set(ByVal value As String)
            Me.m_ValueColumn = value
        End Set
    End Property

    Public Property SelectColumns() As String
        Get
            Return Me.m_SelectColumns
        End Get
        Set(ByVal value As String)
            Me.m_SelectColumns = value
        End Set
    End Property

    Public Property OrderBy() As String
        Get
            Return Me.m_OrderBy
        End Get
        Set(ByVal value As String)
            Me.m_OrderBy = value
        End Set
    End Property

    Public Property Where() As String
        Get
            Return Me.m_Where
        End Get
        Set(ByVal value As String)
            Me.m_Where = value
        End Set
    End Property

    ''' <summary>
    ''' Disegna il myDropDownList In base ai dati passati in argomento
    ''' </summary>
    ''' <param name="FieldName"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function DrawObject(ByVal obj As Field, ByVal FieldName As String, ByVal value As String, ByVal m_ReadOnly As Boolean, ByVal dr As DataRow) As myDropDownList
        If Me IsNot Nothing Then
            Dim sSelect As String
            Dim dt As Data.DataTable
            Dim ddl As New myDropDownList

            ddl.ID = FieldName
            ddl.Tag = Me.EventsFieldName
            ddl.FieldObject = obj
            ddl.CssClass = "ddl-text"
            ddl.AutoPostBack = False
            If Me.Parent IsNot Nothing Then
                ddl.Visible = Not CType(Me.Parent, Field).IsHidden
            Else
                ddl.Visible = True
            End If

            If m_ReadOnly OrElse Me.IsReadOnly Then
                ddl.Enabled = False
            End If

            If Not Me.HasParentEvent Then
                Dim sComp() As String
                Dim sSelectComp As String = String.Empty
                sComp = Me.SelectColumns.Split(",")

                If sComp.Count > 1 Then
                    For Each s As String In sComp
                        If sSelectComp <> String.Empty Then
                            sSelectComp &= " + ' - ' + "
                        End If
                        sSelectComp &= " case when " & s & " is null then '' else convert(nvarchar(max)," & s & ") end "
                    Next
                End If

                If sSelectComp <> String.Empty Then
                    sSelectComp &= " as Componente "
                End If

                sSelect = "SELECT " & Me.SelectColumns
                If Not Me.SelectColumns.Contains(Me.ValueColumn) Then
                    sSelect &= "," & Me.ValueColumn
                End If
                If sSelectComp <> String.Empty Then
                    sSelect &= "," & sSelectComp
                End If
                sSelect &= " FROM " & Me.From
                If Me.Where <> String.Empty Then
                    sSelect &= " WHERE " & Me.Where
                End If

                If Me.OrderBy <> String.Empty Then
                    sSelect &= " ORDER BY " & Me.OrderBy
                End If

                dt = DataBase.ExecuteSQL_DataTable(sSelect, True)

                If dt Is Nothing Then
                    Return ddl
                End If

                If Me.AddNull Then
                    Dim r As Data.DataRow
                    r = dt.NewRow
                    r.Item(0) = String.Empty
                    dt.Rows.InsertAt(r, 0)
                End If

                If Me.HasEvents And Me.EventsFieldName <> String.Empty Then
                    ddl.AutoPostBack = True
                End If

                ddl.DataSource = dt
                If sSelectComp <> String.Empty Then
                    ddl.DataTextField = "Componente"
                Else
                    Dim sSetColum As String = Me.SelectColumns
                    If sSetColum.Contains("DISTINCT") Then
                        sSetColum = sSetColum.Substring(sSetColum.IndexOf("DISTINCT") + "DISTINCT".Length)
                    End If
                    ddl.DataTextField = sSetColum.Trim
                End If

                ddl.DataValueField = Me.ValueColumn
                ddl.EnableViewState = True
                ddl.DataBind()
            ElseIf dr IsNot Nothing Then
                Dim sComp() As String
                Dim sSelectComp As String = String.Empty
                sComp = Me.SelectColumns.Split(",")

                If sComp.Count > 1 Then
                    For Each s As String In sComp
                        If sSelectComp <> String.Empty Then
                            sSelectComp &= " + ' - ' + "
                        End If
                        sSelectComp &= s
                    Next
                End If

                If sSelectComp <> String.Empty Then
                    sSelectComp &= " as Componente "
                End If

                sSelect = "SELECT " & Me.SelectColumns & "," & Me.ValueColumn
                If sSelectComp <> String.Empty Then
                    sSelect &= "," & sSelectComp
                End If
                sSelect &= " FROM " & Me.From
                If Me.Where <> String.Empty Then
                    sSelect &= " WHERE " & Me.Where
                End If

                If Me.HasParentEvent Then
                    If Me.Where <> String.Empty Then
                        sSelect &= " AND " & Me.EventParentName & "='" & dr(Me.EventParentName).ToString & "' "
                    Else
                        sSelect &= " WHERE " & Me.EventParentName & "='" & dr(Me.EventParentName).ToString & "' "
                    End If
                End If

                If Me.OrderBy <> String.Empty Then
                    sSelect &= " ORDER BY " & Me.OrderBy
                End If

                dt = DataBase.ExecuteSQL_DataTable(sSelect, True)

                If dt Is Nothing Then
                    Return ddl
                End If

                If Me.AddNull Then
                    Dim r As Data.DataRow
                    r = dt.NewRow
                    r.Item(0) = String.Empty
                    dt.Rows.InsertAt(r, 0)
                End If

                If Me.HasEvents And Me.EventsFieldName <> String.Empty Then
                    ddl.AutoPostBack = True
                End If

                ddl.DataSource = dt
                If sSelectComp <> String.Empty Then
                    ddl.DataTextField = "Componente"
                Else
                    Dim sSetColum As String = Me.SelectColumns
                    If sSetColum.Contains("DISTINCT") Then
                        sSetColum = sSetColum.Substring(sSetColum.IndexOf("DISTINCT") + "DISTINCT".Length)
                    End If
                    ddl.DataTextField = sSetColum.Trim
                End If

                ddl.DataValueField = Me.ValueColumn
                ddl.EnableViewState = True
                ddl.DataBind()
            End If

            If Me.HasEvents And Me.EventsFieldName <> String.Empty Then
                AddHandler ddl.SelectedIndexChanged, AddressOf showListSelection
            End If

            If value <> String.Empty Then
                ddl.SelectedValue = value
            End If

            Return ddl

        End If
        Return Nothing
    End Function

    ''' <summary>
    ''' Draw the myDropDownList for the list.
    ''' </summary>
    ''' <param name="c">TableCell</param>
    ''' <param name="qf">QueryFiled</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Overloads Function SetDataBound(ByVal c As TableCell, ByVal qf As Field, ByVal m_ReadOnly As Boolean, ByVal dr As DataRow) As TableCell
        If Me IsNot Nothing Then
            c.Controls.Add(DrawObject(qf, qf.FieldDb, String.Empty, m_ReadOnly, dr))
        End If
        Return c
    End Function

    ''' <summary>
    ''' Draw the myDropDownList for the list.
    ''' </summary>
    ''' <param name="c">TableCell</param>
    ''' <param name="uf">UpdateFiled</param>
    ''' <param name="value"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Overloads Function SetDataBound(ByVal c As TableCell, ByVal uf As Field, ByVal value As String, ByVal m_ReadOnly As Boolean, ByVal dr As DataRow) As TableCell
        If Me IsNot Nothing Then
            c.Controls.Add(DrawObject(uf, uf.FieldDb, value, m_ReadOnly, dr))
        End If
        Return c
    End Function

    ''' <summary>
    ''' Draw the myDropDownList for the list.
    ''' </summary>
    ''' <param name="c">HtmlTableCell</param>
    ''' <param name="in_f">InsertField</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Overloads Function SetDataBound(ByVal c As System.Web.UI.HtmlControls.HtmlTableCell, ByVal in_f As Field, ByVal value As String, ByVal m_ReadOnly As Boolean, ByVal dr As DataRow) As System.Web.UI.HtmlControls.HtmlTableCell
        If Me IsNot Nothing Then
            c.Controls.Add(DrawObject(in_f, in_f.FieldDb, value, m_ReadOnly, dr))
        End If
        Return c
    End Function

    ''' <summary>
    ''' Compila il myDropDownList assoggettato ad evento da un'altro myDropDownList principale
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    ''' <remarks></remarks>
    Private Sub showListSelection(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim scr As Screen

        If TypeOf sender Is myDropDownList Then
            Dim s_control As myDropDownList = CType(sender, myDropDownList)

            'If TypeOf s_control.ObjectAtmill Is myDropDownList Then
            Dim ddl As myDropDownList = CType(sender, myDropDownList)

            If TypeOf Me.Parent Is Field Then
                scr = CType(Me.Parent, Field).ParentMenu

                If CType(Me.Parent, Field).QueryField Then
                    For Each qf As Field In scr.ViewFields
                        If qf.FieldDb = ddl.Tag Then
                            Me.SetEventObject(ddl, qf.ObjectList)
                        End If
                    Next
                End If

                If CType(Me.Parent, Field).UpdateField Or CType(Me.Parent, Field).InsertField Then
                    For Each qf As Field In scr.EditFields
                        If qf.FieldDb = ddl.Tag Then
                            Me.SetEventObject(ddl, qf.ObjectList)
                        End If
                    Next
                End If
            End If
        End If

    End Sub

    ''' <summary>
    ''' Popola il myDropDownList successivo al SelectIndexChange del suo chiamante
    ''' </summary>
    ''' <param name="ParentObject"></param>
    ''' <param name="ObjList"></param>
    ''' <remarks></remarks>
    Private Sub SetEventObject(ByVal ParentObject As myDropDownList, ByVal ObjList As myList)
        Dim sSelect As String
        Dim dt As Data.DataTable = Nothing
        Dim ddl_control As myDropDownList = Nothing
        Dim sComp() As String
        Dim sSelectComp As String = String.Empty

        If ParentObject.SelectedValue.ToString <> String.Empty Then
            sComp = ObjList.SelectColumns.Split(",")

            If sComp.Count > 1 Then
                For Each s As String In sComp
                    If sSelectComp <> String.Empty Then
                        sSelectComp &= " + ' - ' + "
                    End If
                    sSelectComp &= s
                Next
            End If

            If sSelectComp <> String.Empty Then
                sSelectComp &= " as Componente "
            End If

            sSelect = "SELECT " & ObjList.SelectColumns & "," & ObjList.ValueColumn
            If sSelectComp <> String.Empty Then
                sSelect &= "," & sSelectComp
            End If
            sSelect &= " FROM " & ObjList.From

            If ObjList.Where <> String.Empty Then
                sSelect &= " WHERE " & ObjList.Where
                If ObjList.EventWhere <> String.Empty Then
                    sSelect &= " AND " & ObjList.EventWhere & "='" & ParentObject.SelectedValue.ToString & "'"
                End If
            Else
                If ObjList.EventWhere <> String.Empty Then
                    sSelect &= " WHERE " & ObjList.EventWhere & "='" & ParentObject.SelectedValue.ToString & "'"
                End If
            End If

            If (ObjList.Where <> String.Empty OrElse ObjList.EventWhere <> String.Empty) Then
                sSelect &= " AND " & ObjList.EventParentName & "='" & ParentObject.SelectedValue.ToString & "'"
            Else
                sSelect &= " WHERE " & ObjList.EventParentName & "='" & ParentObject.SelectedValue.ToString & "'"
            End If

            If ObjList.OrderBy <> String.Empty Then
                sSelect &= " ORDER BY " & ObjList.OrderBy
            End If

            dt = DataBase.ExecuteSQL_DataTable(sSelect, True)

            If dt Is Nothing Then
                Exit Sub
            End If

            If Me.AddNull Then
                Dim r As Data.DataRow
                r = dt.NewRow
                r.Item(0) = String.Empty
                dt.Rows.InsertAt(r, 0)
            End If

        End If

        If TypeOf ObjList.Parent Is Field Then
            ddl_control = CType(tools.FindControlRecursive(ParentObject.Page, CType(ObjList.Parent, Field).FieldDb), myDropDownList)
        End If

        If ddl_control Is Nothing Then
            Exit Sub
        End If

        ddl_control.Tag = ObjList.EventsFieldName

        If Me.HasEvents And Me.EventsFieldName <> String.Empty Then
            ddl_control.AutoPostBack = True
        End If

        ddl_control.DataSource = dt
        If dt IsNot Nothing Then
            ddl_control.DataValueField = ObjList.ValueColumn

            If sSelectComp <> String.Empty Then
                ddl_control.DataTextField = "Componente"
            Else
                Dim sSetColum As String = ObjList.SelectColumns
                If sSetColum.Contains("DISTINCT") Then
                    sSetColum = sSetColum.Substring(sSetColum.IndexOf("DISTINCT") + "DISTINCT".Length)
                End If
                ddl_control.DataTextField = sSetColum.Trim
            End If
        Else
            ddl_control.SelectedIndex = -1
            ddl_control.Items.Clear()
            showListSelection(ddl_control, New System.EventArgs)
        End If
        ddl_control.DataBind()

    End Sub

End Class

#End Region

#Region "Calendar"

Public Class myCalendar
    Inherits TextBox

    Private m_FieldObject As Field

    Public Property FieldObject() As Field
        Get
            Return m_FieldObject
        End Get
        Set(ByVal value As Field)
            m_FieldObject = value
        End Set
    End Property

    Public Sub New()

    End Sub

    ''' <summary>
    ''' Draw the myDropDownList for the list.
    ''' </summary>
    ''' <param name="c">HtmlTableCell</param>
    ''' <param name="uf">UpdateFiled</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Overloads Function SetDataBound(ByVal c As System.Web.UI.HtmlControls.HtmlTableCell, ByVal predicate As String, ByVal uf As Field, ByVal m_value As String, ByVal m_ReadOnly As Boolean) As System.Web.UI.HtmlControls.HtmlTableCell
        If Me IsNot Nothing Then
            DrawObject(c, predicate, uf.FieldDb, m_value, m_ReadOnly, uf)
        End If
        Return c
    End Function

    ''' <summary>
    ''' Disegna il myCalendar In base ai dati passati in argomento
    ''' </summary>
    ''' <param name="c"></param>
    ''' <param name="FieldDb"></param>
    ''' <param name="m_value"></param>
    ''' <remarks></remarks>
    Private Sub DrawObject(ByVal c As System.Web.UI.HtmlControls.HtmlTableCell, ByVal predicate As String, ByVal FieldDb As String, ByVal m_value As String, ByVal m_ReadOnly As Boolean, ByVal uf As Field)
        If Me IsNot Nothing Then
            Dim l As Label

            Dim data As String = String.Empty
            Dim ora As String = "0"
            Dim minuto As String = "0"
            Dim data_tempo As String = String.Empty
            Dim without_data As String
            Dim split_data() As String

            If m_value IsNot Nothing AndAlso m_value <> String.Empty Then
                If m_value.ToLower = "now" Then
                    ' sulla Save() della classe ci sarà il confronto su m_InvalidDateTime
                    data = m_InvalidDateTime.Date
                    ora = "0"
                    minuto = "0"
                Else
                    data = m_value.Substring(0, m_value.IndexOf(" "))
                    without_data = m_value.Substring(data.Length, m_value.Length - data.Length)

                    If without_data.Contains(".") Then
                        split_data = without_data.Trim.Split(".")
                    ElseIf without_data.Contains(":") Then
                        split_data = without_data.Trim.Split(":")
                    Else
                        split_data = without_data.Trim.Split(".")
                    End If

                    ora = split_data(0)
                    minuto = split_data(1)
                End If

                data_tempo = data & " " & ora & ":" & minuto
            End If

            If uf.QueryField Then
                Dim cmbSegno As New DropDownList
                cmbSegno = tools.PopolaSegno(cmbSegno)
                cmbSegno.CssClass = "ddl-text"
                cmbSegno.DataBind()
                cmbSegno.Enabled = False
                cmbSegno.ID = FieldDb & "_DATE_" & predicate & "_SEGNO"
                Select Case predicate
                    Case costanti.m_FieldDataDA
                        cmbSegno.Text = ">="
                    Case costanti.m_FieldDataA
                        cmbSegno.Text = "<="
                End Select
                cmbSegno.Visible = True
                c.Controls.Add(cmbSegno)

                l = New Label
                l.Text = "&nbsp;"
                l.CssClass = "label-text"
                c.Controls.Add(l)
            End If

            Dim cal As New myCalendar
            cal.ID = FieldDb & "_DATE_" & predicate
            cal.Attributes.Add("type", "text")
            cal.Attributes.Add("datetimepicker", "step:10;")
            cal.Visible = Not uf.IsHidden
            cal.CssClass = "text DateTimePicker_jq"

            If m_value IsNot Nothing AndAlso m_value <> String.Empty Then
                cal.Text = data_tempo
            End If

            If m_ReadOnly OrElse uf.IsReadOnly OrElse uf.IsVisibleButNotEditable Then
                cal.ReadOnly = True
                cal.Enabled = False
            End If

            c.Controls.Add(cal)
        End If
    End Sub

End Class

#End Region