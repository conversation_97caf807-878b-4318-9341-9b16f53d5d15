﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class TaskScheduler
    Inherits myDbBaseClass

    Private m_id As Long = m_InvalidId
    Private m_description As String = String.Empty
    Private m_hour As Integer
    Private m_mon As String
    Private m_tue As String
    Private m_wed As String
    Private m_thu As String
    Private m_fri As String
    Private m_sat As String
    Private m_sun As String

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("TASK_SCHEDULER")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As TaskScheduler
        Me.Id = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "Description"
                            Me.Description = CType(m_Control, myTextBox).Text
                    End Select
                ElseIf TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "Hour"
                            Try
                                Me.Hour = Integer.Parse(CType(m_Control, myDropDownList).SelectedValue)
                            Catch
                                Me.Hour = Date.Now.Hour
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myCheckBox Then
                    Select Case s.PropertyName
                        Case "Mon"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.Mon = m_StringUno
                            Else
                                Me.Mon = m_StringZero
                            End If

                        Case "Tue"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.Tue = m_StringUno
                            Else
                                Me.Tue = m_StringZero
                            End If

                        Case "Wed"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.Wed = m_StringUno
                            Else
                                Me.Wed = m_StringZero
                            End If

                        Case "Thu"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.Thu = m_StringUno
                            Else
                                Me.Thu = m_StringZero
                            End If

                        Case "Fri"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.Fri = m_StringUno
                            Else
                                Me.Fri = m_StringZero
                            End If

                        Case "Sat"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.Sat = m_StringUno
                            Else
                                Me.Sat = m_StringZero
                            End If

                        Case "Sun"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.Sun = m_StringUno
                            Else
                                Me.Sun = m_StringZero
                            End If
                    End Select
                End If
            End If
        Next

        Return Me
    End Function

    Public Property Id() As Long
        Get
            Return Me.m_id
        End Get
        Set(ByVal value As Long)
            Me.m_id = value
        End Set
    End Property

    Public Property Description() As String
        Get
            Return Me.m_description
        End Get
        Set(ByVal value As String)
            Me.m_description = value
        End Set
    End Property

    Public Property Hour() As Integer
        Get
            Return Me.m_hour
        End Get
        Set(ByVal value As Integer)
            Me.m_hour = value
        End Set
    End Property

    Public Property Mon() As String
        Get
            Return Me.m_mon
        End Get
        Set(ByVal value As String)
            Me.m_mon = value
        End Set
    End Property

    Public Property Tue() As String
        Get
            Return Me.m_tue
        End Get
        Set(ByVal value As String)
            Me.m_tue = value
        End Set
    End Property

    Public Property Wed() As String
        Get
            Return Me.m_wed
        End Get
        Set(ByVal value As String)
            Me.m_wed = value
        End Set
    End Property

    Public Property Thu() As String
        Get
            Return Me.m_thu
        End Get
        Set(ByVal value As String)
            Me.m_thu = value
        End Set
    End Property

    Public Property Fri() As String
        Get
            Return Me.m_fri
        End Get
        Set(ByVal value As String)
            Me.m_fri = value
        End Set
    End Property

    Public Property Sat() As String
        Get
            Return Me.m_sat
        End Get
        Set(ByVal value As String)
            Me.m_sat = value
        End Set
    End Property

    Public Property Sun() As String
        Get
            Return Me.m_sun
        End Get
        Set(ByVal value As String)
            Me.m_sun = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.Id & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.Id))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio di task scheduler."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio di task scheduler sulla select del recupero dati."
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.Id

            If Me.Id = m_InvalidId Then
                Me.Id = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.Id
            End If

            drNodo("HOUR") = Me.Hour
            drNodo("MON") = Me.Mon
            drNodo("TUE") = Me.Tue
            drNodo("WED") = Me.Wed
            drNodo("THU") = Me.Thu
            drNodo("FRI") = Me.Fri
            drNodo("SAT") = Me.Sat
            drNodo("SUN") = Me.Sun

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim m_error As String = "Errore durante aggiornamento di task scheduler. Controllare i dati immessi e riprovare."
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, m_error)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class