﻿Option Strict On

Imports UsersGUI
Imports System.IO

Public Class externalSource
    Public Shared Function GetAppSetting(ByVal key As String) As String
        Try
            Return System.Configuration.ConfigurationManager.AppSettings(key).ToString()
        Catch ex As Exception
            wsLogs.WriteError("ExternalSource not found on config file!")
            Return String.Empty
        End Try
    End Function

    Private Shared Function GetPathByCode(ByVal code As String) As String
        Dim path_xml_file As String = String.Empty
        Select Case code
            Case "pvhnd"
                path_xml_file = GetAppSetting("ExternalSource_" & code)
            Case Else
                path_xml_file = String.Empty
        End Select
        Return path_xml_file
    End Function

    Public Shared Function GetMyXmlDoc() As myXmlDocument
        Dim to_return As myXmlDocument = Nothing
        Dim xml_path As String = GetAppSetting("MyConfig")
        Dim xml_file As New FileInfo(xml_path)

        If xml_file.Exists Then
            to_return = New myXmlDocument(xml_path)
        Else
            to_return = Nothing
        End If

        Return to_return
    End Function

    Public Shared Function GetExternalSource(ByVal path As String) As myXmlDocument
        Dim to_return As myXmlDocument = Nothing

        Dim xml_file As New FileInfo(path)
        If xml_file.Exists Then
            to_return = New myXmlDocument(path)
        Else
            to_return = Nothing
        End If

        Return to_return
    End Function

    'restituisce un determinato nodo del config.xml
    Public Shared Function GetNodeValue(ByVal code As String, ByVal node_name As String) As String
        Dim xml_path As String = GetPathByCode(code)
        Dim xml_doc As myXmlDocument
        Dim to_return As String = String.Empty

        If xml_path <> String.Empty Then
            xml_doc = MobileTools.externalSource.GetExternalSource(xml_path)
            For Each xmlNode As System.Xml.XmlNode In xml_doc.SelectNodes("//ExtrenalSettings")
                to_return = xmlNode.SelectSingleNode(node_name, xml_doc.GetXmlManager).InnerText.Trim
            Next
        End If
        Return to_return
    End Function
    Public Shared Function GetNodeValue(ByVal xml_doc As myXmlDocument, ByVal node_name As String) As String
        Dim to_return As String = String.Empty

        For Each xmlNode As System.Xml.XmlNode In xml_doc.SelectNodes("//ExtrenalSettings")
            to_return = xmlNode.SelectSingleNode(node_name, xml_doc.GetXmlManager).InnerText.Trim
        Next
        Return to_return
    End Function

    'imposta un determinato nodo del config.xml
    Public Shared Function SetNodeValue(ByVal code As String, ByVal node_name As String, ByVal value_node As String) As Boolean
        Dim xml_path As String = GetPathByCode(code)
        Dim xml_doc As myXmlDocument
        Dim esito As Boolean = False

        If xml_path <> String.Empty Then
            xml_doc = MobileTools.externalSource.GetExternalSource(xml_path)
            For Each xmlNode As System.Xml.XmlNode In xml_doc.SelectNodes("//ExtrenalSettings")
                xmlNode.SelectSingleNode(node_name, xml_doc.GetXmlManager).InnerText = value_node
            Next
            xml_doc.GetXmlDoc.Save(xml_path)
            esito = True
        End If
        Return esito
    End Function

    'verifico la correttezza della directory in ingresso, se non esiste la completo
    Public Shared Sub controlDirectory(ByVal direc As String)
        Dim end_control As Boolean = False
        Dim direc_miss As List(Of String) = New List(Of String)
        Dim direc_control As String = direc

        Try
            While (Not end_control) And (direc_control.Length > 0)
                If (Not System.IO.Directory.Exists(direc_control)) Then
                    direc_miss.Add(Path.GetFileName(direc_control.ToString))
                    direc_control = Path.GetDirectoryName(direc_control)
                Else
                    end_control = True
                End If
            End While

            direc_miss.Reverse()
            For Each drct As String In direc_miss
                direc_control = Path.Combine(direc_control, drct)
                System.IO.Directory.CreateDirectory(direc_control)
            Next
        Catch ex As Exception
            wsLogs.WriteError("Directory error! Please contact the assistance..")
        End Try
    End Sub

End Class