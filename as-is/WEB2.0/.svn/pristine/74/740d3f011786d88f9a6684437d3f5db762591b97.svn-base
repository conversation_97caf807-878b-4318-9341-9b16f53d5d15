﻿Option Strict On

Imports System.Web.UI.WebControls

Public Class myImageButton
    Inherits ImageButton

    Private m_Tag As Object

    Public Property Tag() As Object
        Get
            Return Me.m_Tag
        End Get
        Set(ByVal value As Object)
            If value IsNot Nothing Then
                Me.m_Tag = value
            Else
                Me.m_Tag = New Language(EnumTranslation.Italiano, "0")
            End If
        End Set
    End Property
End Class