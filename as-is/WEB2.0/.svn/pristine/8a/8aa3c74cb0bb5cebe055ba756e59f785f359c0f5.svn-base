﻿Option Strict On

Imports UsersGUI

'output
Public Class SocketResponseObj
    Private m_result As Boolean
    Private m_exception As String
    Private m_info As String
    Private m_data As String

    Public Sub New()
        With Me
            .m_result = False
            .m_exception = Nothing
            .m_info = Nothing
            .m_data = Nothing
        End With
    End Sub

    Public Sub New(ByVal result As Boolean)
        With Me
            .m_result = result
            .m_exception = Nothing
            .m_info = Nothing
            .m_data = Nothing
        End With
    End Sub

    Public Property RESULT() As Boolean

        Get
            Return m_result
        End Get

        Set(ByVal value As Boolean)
            Me.m_result = value
        End Set

    End Property

    Public Property EXCEPTION() As String

        Get
            Return m_exception
        End Get

        Set(ByVal value As String)
            Me.m_exception = value
        End Set

    End Property

    Public Property INFO() As String

        Get
            Return m_info
        End Get

        Set(ByVal value As String)
            Me.m_info = value
        End Set

    End Property

    Public Property DATA() As String

        Get
            Return m_data
        End Get

        Set(ByVal value As String)
            Me.m_data = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = "{"
        'UsersGUI.tools.JSONstringify(Me, json_to_return)
        json_to_return &= """RESULT"":" & m_result.ToString.ToLower

        If Not IsNothing(m_exception) Then
            json_to_return &= ",""EXCEPTION"":""" & m_exception & """"
        Else
            json_to_return &= ",""EXCEPTION"":null"
        End If

        If Not IsNothing(m_info) Then
            json_to_return &= ",""INFO"":""" & m_info & """"
        Else
            json_to_return &= ",""INFO"":null"
        End If

        If Not IsNothing(m_data) Then
            json_to_return &= ",""DATA"":" & m_data & ""
        Else
            json_to_return &= ",""DATA"":null"
        End If

        json_to_return &= "}"

        Return json_to_return
    End Function

End Class

'input
'output
Public Class SocketGetExtraObj
    Private m_identify As Integer
    Private m_identify_string As String
    Private m_query As SocketQueryObj
    Private m_extrascreen As ExtraScreenObj
    Private m_target As String
    Private m_callback As String
    Private m_result As Boolean
    Private m_return_to_client As Object

    Public Sub New()
        With Me
            .m_identify = m_InvalidInteger
            .m_identify_string = String.Empty
            .m_query = New SocketQueryObj()
            .m_extrascreen = New ExtraScreenObj()
            .m_target = String.Empty
            .m_callback = String.Empty
            .m_result = False
            .m_return_to_client = Nothing
        End With
    End Sub

    Public Property Identify() As Integer

        Get
            Return m_identify
        End Get

        Set(ByVal value As Integer)
            Me.m_identify = value
        End Set

    End Property

    Public Property IdentifyString() As String

        Get
            Return m_identify_string
        End Get

        Set(ByVal value As String)
            Me.m_identify_string = value
        End Set

    End Property

    Public Property Query() As SocketQueryObj

        Get
            Return m_query
        End Get

        Set(ByVal value As SocketQueryObj)
            Me.m_query = value
        End Set

    End Property

    Public Property ExtraScreen() As ExtraScreenObj

        Get
            Return m_extrascreen
        End Get

        Set(ByVal value As ExtraScreenObj)
            Me.m_extrascreen = value
        End Set

    End Property

    Public Property Target() As String

        Get
            Return m_target
        End Get

        Set(ByVal value As String)
            Me.m_target = value
        End Set

    End Property

    Public Property Callback() As String

        Get
            Return m_callback
        End Get

        Set(ByVal value As String)
            Me.m_callback = value
        End Set

    End Property

    Public Property Result() As Boolean

        Get
            Return m_result
        End Get

        Set(ByVal value As Boolean)
            Me.m_result = value
        End Set

    End Property

    Public Property ReturnToClient() As Object

        Get
            Return m_return_to_client
        End Get

        Set(ByVal value As Object)
            Me.m_return_to_client = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = String.Empty

        UsersGUI.tools.JSONstringify(Me, json_to_return)

        Return json_to_return
    End Function

End Class

'input
'output
Public Class ExtraScreenObj
    Private m_selectcol As String
    Private m_valuecol As String
    Private m_from As String
    Private m_where As String
    Private m_orderby As String

    Public Sub New()
        With Me
            .m_selectcol = String.Empty
            .m_valuecol = String.Empty
            .m_from = String.Empty
            .m_where = String.Empty
            .m_orderby = String.Empty
        End With
    End Sub

    Public Property SelectCol() As String

        Get
            Return m_selectcol
        End Get

        Set(ByVal value As String)
            Me.m_selectcol = value
        End Set

    End Property

    Public Property ValueCol() As String

        Get
            Return m_valuecol
        End Get

        Set(ByVal value As String)
            Me.m_valuecol = value
        End Set

    End Property

    Public Property From() As String

        Get
            Return m_from
        End Get

        Set(ByVal value As String)
            Me.m_from = value
        End Set

    End Property

    Public Property Where() As String

        Get
            Return m_where
        End Get

        Set(ByVal value As String)
            Me.m_where = value
        End Set

    End Property

    Public Property OrderBy() As String

        Get
            Return m_orderby
        End Get

        Set(ByVal value As String)
            Me.m_orderby = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = String.Empty

        UsersGUI.tools.JSONstringify(Me, json_to_return)

        Return json_to_return
    End Function

End Class

'input
'output
Public Class SocketLoginObj
    Private m_user_name As String
    Private m_password As String
    Private m_user_id As Integer
    Private m_group_id As List(Of Integer)
    Private m_accesses_level As String

    Public Sub New()
        With Me
            .m_user_name = String.Empty
            .m_password = String.Empty
            .m_group_id = New List(Of Integer)
        End With
    End Sub

    Public Sub New(ByVal user_name As String, ByVal password As String)
        With Me
            .m_user_name = user_name
            .m_password = password
            .m_group_id = New List(Of Integer)
        End With
    End Sub

    Public Property USERNAME() As String

        Get
            Return m_user_name
        End Get

        Set(ByVal value As String)
            Me.m_user_name = value
        End Set

    End Property

    Public Property PASSWORD() As String

        Get
            Return m_password
        End Get

        Set(ByVal value As String)
            Me.m_password = value
        End Set

    End Property

    Public Property USERID() As Integer

        Get
            Return m_user_id
        End Get

        Set(ByVal value As Integer)
            Me.m_user_id = value
        End Set

    End Property

    Public Property GROUPID() As List(Of Integer)

        Get
            Return m_group_id
        End Get

        Set(ByVal value As List(Of Integer))
            Me.m_group_id = value
        End Set

    End Property

    Public Property ACCESSESLEVEL() As String

        Get
            Return m_accesses_level
        End Get

        Set(ByVal value As String)
            Me.m_accesses_level = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = String.Empty

        UsersGUI.tools.JSONstringify(Me, json_to_return)

        Return json_to_return
    End Function

End Class

'TODO non ancora usato
Public Class SocketQueryObj
    Private m_select As String
    Private m_from As String
    Private m_where As String
    Private m_orderby As String
    Private m_groupby As String

    Public Sub New()
        With Me
            .m_select = String.Empty
            .m_from = String.Empty
            .m_where = String.Empty
            .m_orderby = String.Empty
            .m_groupby = String.Empty
        End With
    End Sub

    Public Property qSELECT() As String

        Get
            Return m_select
        End Get

        Set(ByVal value As String)
            Me.m_select = value
        End Set

    End Property

    Public Property qFROM() As String

        Get
            Return m_from
        End Get

        Set(ByVal value As String)
            Me.m_from = value
        End Set

    End Property

    Public Property qWHERE() As String

        Get
            Return m_where
        End Get

        Set(ByVal value As String)
            Me.m_where = value
        End Set

    End Property

    Public Property qORDERBY() As String

        Get
            Return m_orderby
        End Get

        Set(ByVal value As String)
            Me.m_orderby = value
        End Set

    End Property

    Public Property qGROUPBY() As String

        Get
            Return m_groupby
        End Get

        Set(ByVal value As String)
            Me.m_groupby = value
        End Set

    End Property

End Class

'input
Public Class SocketMachineValuesObj
    Private m_menu As Integer
    Private m_user As Integer
    Private m_machine As Integer
    Private m_query As String

    Public Sub New()
        With Me
            .m_menu = m_InvalidId
            .m_user = m_InvalidId
            .m_machine = m_InvalidId
            .m_query = String.Empty
        End With
    End Sub

    Public Property MENU() As Integer

        Get
            Return m_menu
        End Get

        Set(ByVal value As Integer)
            Me.m_menu = value
        End Set

    End Property

    Public Property USER() As Integer

        Get
            Return m_user
        End Get

        Set(ByVal value As Integer)
            Me.m_user = value
        End Set

    End Property

    Public Property MACHINE() As Integer

        Get
            Return m_machine
        End Get

        Set(ByVal value As Integer)
            Me.m_machine = value
        End Set

    End Property

    Public Property QUERY() As String

        Get
            Return m_query
        End Get

        Set(ByVal value As String)
            Me.m_query = value
        End Set

    End Property

End Class

'input
Public Class SocketScadaGroupObj
    Private m_page_id As Integer

    Public Sub New()
        With Me
            .m_page_id = m_InvalidId
        End With
    End Sub

    Public Property PAGE_ID() As Integer

        Get
            Return m_page_id
        End Get

        Set(ByVal value As Integer)
            Me.m_page_id = value
        End Set

    End Property

End Class

Public Class SocketIpackimaGroupObj
    Private m_page_id As Integer

    Public Sub New()
        With Me
            .m_page_id = m_InvalidId
        End With
    End Sub

    Public Property PAGE_ID() As Integer

        Get
            Return m_page_id
        End Get

        Set(ByVal value As Integer)
            Me.m_page_id = value
        End Set

    End Property

End Class

'input
Public Class SocketWriteTagsObj
    Private m_tags_id As String
    Private m_value As String

    Public Sub New()
        With Me
            .m_tags_id = m_InvalidId.ToString
            .m_value = String.Empty
        End With
    End Sub

    Public Property TAGS_ID() As String

        Get
            Return m_tags_id
        End Get

        Set(ByVal value As String)
            Me.m_tags_id = value
        End Set

    End Property

    Public Property VALUE() As String

        Get
            Return m_value
        End Get

        Set(ByVal value As String)
            Me.m_value = value
        End Set

    End Property

End Class

'input
Public Class SocketMachineWriteObj
    Private m_menu As Integer
    Private m_machine As Integer
    Private m_tags_type As Integer
    Private m_value As String

    Public Sub New()
        With Me
            .m_menu = m_InvalidId
            .m_machine = m_InvalidId
            .m_tags_type = m_InvalidId
            .m_value = String.Empty
        End With
    End Sub

    Public Property MENU() As Integer

        Get
            Return m_menu
        End Get

        Set(ByVal value As Integer)
            Me.m_menu = value
        End Set

    End Property

    Public Property MACHINE() As Integer

        Get
            Return m_machine
        End Get

        Set(ByVal value As Integer)
            Me.m_machine = value
        End Set

    End Property

    Public Property TAG_TYPE_ID() As Integer

        Get
            Return m_tags_type
        End Get

        Set(ByVal value As Integer)
            Me.m_tags_type = value
        End Set

    End Property

    Public Property VALUE() As String

        Get
            Return m_value
        End Get

        Set(ByVal value As String)
            Me.m_value = value
        End Set

    End Property

End Class

'input
Public Class SocketMachineActionObj
    Private m_user_id As Integer
    Private m_machine_id As Integer
    Private m_cmd As Integer
    Private m_type_unlock As Integer

    Public Sub New()
        With Me
            .m_user_id = m_InvalidId
            .m_machine_id = m_InvalidId
            .m_cmd = Nothing
            .m_type_unlock = Nothing
        End With
    End Sub

    Public Sub New(ByVal user_id As Integer, ByVal machine_id As Integer, ByVal cmd As Integer, Optional ByVal type_unlock As Integer = Nothing)
        With Me
            .m_user_id = user_id
            .m_machine_id = machine_id
            .m_cmd = cmd
            .m_type_unlock = type_unlock
        End With
    End Sub

    Public Property USERID() As Integer

        Get
            Return m_user_id
        End Get

        Set(ByVal value As Integer)
            Me.m_user_id = value
        End Set

    End Property

    Public Property MACHINEID() As Integer

        Get
            Return m_machine_id
        End Get

        Set(ByVal value As Integer)
            Me.m_machine_id = value
        End Set

    End Property

    Public Property CMD() As Integer

        Get
            Return m_cmd
        End Get

        Set(ByVal value As Integer)
            Me.m_cmd = value
        End Set

    End Property

    Public Property TYPEUNLOCK() As Integer

        Get
            Return m_type_unlock
        End Get

        Set(ByVal value As Integer)
            Me.m_type_unlock = value
        End Set

    End Property

End Class

'input
'output
Public Class SocketScreenObj
    Private m_orderby As String
    Private m_dbname As String
    Private m_linesperpage As Integer
    Private m_screenname As String

    Private m_hasreportbutton As Boolean
    Private m_hassimpleprintbutton As Boolean
    Private m_hasdelbutton As Boolean
    Private m_hasaddbutton As Boolean
    Private m_haseditbutton As Boolean
    Private m_hassearchbutton As Boolean
    Private m_enumpagenamecode As String

    Private m_extraColumns As Generic.List(Of extraColumn)
    Private m_EditFields As Generic.List(Of SocketFieldObj)
    Private m_ViewFields As Generic.List(Of SocketFieldObj)

    Public Sub New()
        With Me
            .m_orderby = Nothing
            .m_dbname = Nothing
            .m_linesperpage = Nothing
            .m_screenname = Nothing

            .m_hasreportbutton = Nothing
            .m_hassimpleprintbutton = Nothing
            .m_hasdelbutton = Nothing
            .m_hasaddbutton = Nothing
            .m_haseditbutton = Nothing
            .m_hassearchbutton = Nothing
            .m_enumpagenamecode = Nothing

            .m_extraColumns = New List(Of extraColumn)
            .m_EditFields = New List(Of SocketFieldObj)
            .m_ViewFields = New List(Of SocketFieldObj)
        End With
    End Sub

    Public Sub New(ByVal screenname As String)
        With Me
            .m_orderby = Nothing
            .m_dbname = Nothing
            .m_linesperpage = m_InvalidInteger
            .m_screenname = screenname

            .m_hasreportbutton = False
            .m_hassimpleprintbutton = False
            .m_hasdelbutton = False
            .m_hasaddbutton = False
            .m_haseditbutton = False
            .m_hassearchbutton = False
            .m_enumpagenamecode = Nothing

            .m_extraColumns = New List(Of extraColumn)
            .m_EditFields = New List(Of SocketFieldObj)
            .m_ViewFields = New List(Of SocketFieldObj)
        End With
    End Sub

    Public Property OrderBy() As String

        Get
            Return m_orderby
        End Get

        Set(ByVal value As String)
            Me.m_orderby = value
        End Set

    End Property

    Public Property DBName() As String

        Get
            Return m_dbname
        End Get

        Set(ByVal value As String)
            Me.m_dbname = value
        End Set

    End Property

    Public Property LinesPerPage() As Integer

        Get
            Return m_linesperpage
        End Get

        Set(ByVal value As Integer)
            Me.m_linesperpage = value
        End Set

    End Property

    Public Property ScreenName() As String

        Get
            Return m_screenname
        End Get

        Set(ByVal value As String)
            Me.m_screenname = value
        End Set

    End Property

    Public Property HasReportButton() As Boolean

        Get
            Return m_hasreportbutton
        End Get

        Set(ByVal value As Boolean)
            Me.m_hasreportbutton = value
        End Set

    End Property

    Public Property HasSimplePrintButton() As Boolean

        Get
            Return m_hassimpleprintbutton
        End Get

        Set(ByVal value As Boolean)
            Me.m_hassimpleprintbutton = value
        End Set

    End Property

    Public Property HasDelButton() As Boolean

        Get
            Return m_hasdelbutton
        End Get

        Set(ByVal value As Boolean)
            Me.m_hasdelbutton = value
        End Set

    End Property

    Public Property HasAddButton() As Boolean

        Get
            Return m_hasaddbutton
        End Get

        Set(ByVal value As Boolean)
            Me.m_hasaddbutton = value
        End Set

    End Property

    Public Property HasEditButton() As Boolean

        Get
            Return m_haseditbutton
        End Get

        Set(ByVal value As Boolean)
            Me.m_haseditbutton = value
        End Set

    End Property

    Public Property HasSearchButton() As Boolean

        Get
            Return m_hassearchbutton
        End Get

        Set(ByVal value As Boolean)
            Me.m_hassearchbutton = value
        End Set

    End Property

    Public Property EnumPageNameCode() As String

        Get
            Return m_enumpagenamecode
        End Get

        Set(ByVal value As String)
            Me.m_enumpagenamecode = value
        End Set

    End Property

    Public Property extraColumns() As Generic.List(Of extraColumn)

        Get
            Return m_extraColumns
        End Get

        Set(ByVal value As Generic.List(Of extraColumn))
            m_extraColumns = value
        End Set

    End Property

    Public Property EditFields() As Generic.List(Of SocketFieldObj)

        Get
            Return m_EditFields
        End Get

        Set(ByVal value As Generic.List(Of SocketFieldObj))
            m_EditFields = value
        End Set

    End Property

    Public Property ViewFields() As Generic.List(Of SocketFieldObj)

        Get
            Return m_ViewFields
        End Get

        Set(ByVal value As Generic.List(Of SocketFieldObj))
            m_ViewFields = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = String.Empty

        UsersGUI.tools.JSONstringify(Me, json_to_return)

        Return json_to_return
    End Function

End Class

'output
Public Class SocketFieldObj

    'per ViewField & EditField
    Private m_FieldDB As String

    Private m_FieldName As String

    'Private m_Summable As Boolean
    Private m_DateFormat As EnumDateFormats

    Private m_FieldType As String
    Private m_IsReadOnly As Boolean
    Private m_IsHidden As Boolean
    Private m_IsSQLExcluded As Boolean
    Private m_PropertyName As String
    Private m_queryField As Boolean
    Private m_visibleField As Boolean

    'per EditField
    Private m_UpdateField As Boolean

    Private m_InsertField As Boolean

    'se FieldType = Number
    Private m_NumType As String

    Private m_NumBound As String

    'se FieldType = List || Chk
    Private m_ValueColumn As String

    Private m_SelectColumns As String
    Private m_From As String
    Private m_AddNull As Boolean
    Private m_Where As String

    Public Sub New()
        With Me
            .m_FieldDB = String.Empty
            .m_FieldName = String.Empty
            '.m_Summable = False
            .m_DateFormat = EnumDateFormats.DateTime
            .m_FieldType = String.Empty
            .m_IsReadOnly = False
            .m_IsHidden = False
            .m_IsSQLExcluded = False
            .m_PropertyName = String.Empty
            .m_queryField = False
            .m_visibleField = False
            .m_UpdateField = False
            .m_InsertField = False
            .m_NumType = String.Empty
            .m_NumBound = String.Empty
            .m_ValueColumn = String.Empty
            .m_SelectColumns = String.Empty
            .m_From = String.Empty
            .m_AddNull = False
            .m_Where = String.Empty
        End With
    End Sub

    Public Sub New(ByVal i_field As UsersGUI.Field)
        With Me
            .m_FieldDB = i_field.FieldDb
            .m_FieldName = i_field.FieldName
            '.m_Summable = False
            .m_DateFormat = i_field.DateFormat
            .m_FieldType = i_field.FieldType
            .m_IsReadOnly = i_field.IsReadOnly
            .m_IsHidden = i_field.IsHidden
            .m_IsSQLExcluded = i_field.IsSqlExcluded
            .m_PropertyName = i_field.PropertyName
            .m_queryField = i_field.QueryField
            .m_visibleField = i_field.VisibleField
            .m_UpdateField = i_field.UpdateField
            .m_InsertField = i_field.InsertField

            If String.Compare(i_field.FieldType.ToLower, "number") = 0 Then
                .m_NumType = i_field.ObjectNumber.NumType
                .m_NumBound = i_field.ObjectNumber.NumBound
            Else
                .m_NumType = String.Empty
                .m_NumBound = String.Empty
            End If

            If String.Compare(i_field.FieldType.ToLower, "chklist") = 0 Then
                .m_ValueColumn = i_field.ObjectChkList.ValueColumn
                .m_SelectColumns = i_field.ObjectChkList.SelectColumns
                .m_From = i_field.ObjectChkList.From
                .m_AddNull = False
                .m_Where = i_field.ObjectChkList.Where
            ElseIf String.Compare(i_field.FieldType.ToLower, "list") = 0 Then
                .m_ValueColumn = i_field.ObjectList.ValueColumn
                .m_SelectColumns = i_field.ObjectList.SelectColumns
                .m_From = i_field.ObjectList.From
                .m_AddNull = i_field.ObjectList.AddNull
                .m_Where = i_field.ObjectList.Where
            ElseIf String.Compare(i_field.FieldType.ToLower, "Number") = 0 Then
                .m_ValueColumn = String.Empty
                .m_SelectColumns = String.Empty
                .m_From = String.Empty
                .m_AddNull = False
                .m_Where = String.Empty
            End If

        End With
    End Sub

    Public Property FieldDB() As String

        Get
            Return m_FieldDB
        End Get

        Set(ByVal value As String)
            Me.m_FieldDB = value
        End Set

    End Property

    Public Property FieldName() As String

        Get
            Return m_FieldName
        End Get

        Set(ByVal value As String)
            Me.m_FieldName = value
        End Set

    End Property

    'Public Property Summable() As Boolean
    '    Get
    '        Return m_Summable
    '    End Get
    '    Set(ByVal value As Boolean)
    '        Me.m_Summable = value
    '    End Set
    'End Property
    Public Property DateFormat() As EnumDateFormats

        Get
            Return m_DateFormat
        End Get

        Set(ByVal value As EnumDateFormats)
            Me.m_DateFormat = value
        End Set

    End Property

    Public Property FieldType() As String

        Get
            Return m_FieldType
        End Get

        Set(ByVal value As String)
            Me.m_FieldType = value
        End Set

    End Property

    Public Property IsReadOnly() As Boolean

        Get
            Return m_IsReadOnly
        End Get

        Set(ByVal value As Boolean)
            Me.m_IsReadOnly = value
        End Set

    End Property

    Public Property IsHidden() As Boolean

        Get
            Return m_IsHidden
        End Get

        Set(ByVal value As Boolean)
            Me.m_IsHidden = value
        End Set

    End Property

    Public Property IsSQLExcluded() As Boolean

        Get
            Return m_IsSQLExcluded
        End Get

        Set(ByVal value As Boolean)
            m_IsSQLExcluded = value
        End Set

    End Property

    Public Property PropertyName() As String

        Get
            Return m_PropertyName
        End Get

        Set(ByVal value As String)
            m_PropertyName = value
        End Set

    End Property

    Public Property queryField() As Boolean

        Get
            Return m_queryField
        End Get

        Set(ByVal value As Boolean)
            m_queryField = value
        End Set

    End Property

    Public Property visibleField() As Boolean

        Get
            Return m_visibleField
        End Get

        Set(ByVal value As Boolean)
            m_visibleField = value
        End Set

    End Property

    Public Property updateField() As Boolean

        Get
            Return m_UpdateField
        End Get

        Set(ByVal value As Boolean)
            m_UpdateField = value
        End Set

    End Property

    Public Property insertField() As Boolean

        Get
            Return m_InsertField
        End Get

        Set(ByVal value As Boolean)
            m_InsertField = value
        End Set

    End Property

    Public Property NumType() As String

        Get
            Return m_NumType
        End Get

        Set(ByVal value As String)
            Me.m_NumType = value
        End Set

    End Property

    Public Property NumBound() As String

        Get
            Return m_NumBound
        End Get

        Set(ByVal value As String)
            Me.m_NumBound = value
        End Set

    End Property

    Public Property ValueColumn() As String

        Get
            Return m_ValueColumn
        End Get

        Set(ByVal value As String)
            Me.m_ValueColumn = value
        End Set

    End Property

    Public Property SelectColumns() As String

        Get
            Return m_SelectColumns
        End Get

        Set(ByVal value As String)
            Me.m_SelectColumns = value
        End Set

    End Property

    Public Property From() As String

        Get
            Return m_From
        End Get

        Set(ByVal value As String)
            Me.m_From = value
        End Set

    End Property

    Public Property AddNull() As Boolean

        Get
            Return m_AddNull
        End Get

        Set(ByVal value As Boolean)
            Me.m_AddNull = value
        End Set

    End Property

    Public Property Where() As String

        Get
            Return m_Where
        End Get

        Set(ByVal value As String)
            Me.m_Where = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = String.Empty

        UsersGUI.tools.JSONstringify(Me, json_to_return)

        Return json_to_return
    End Function

End Class

'input
'output
Public Class SocketUsersManagementObj
    Private m_screenname As String
    Private m_index As Integer
    Private m_orderby As String
    Private m_orderdir As String
    Private m_filter As String
    Private m_values As String

    Public Sub New()
        With Me
            .m_screenname = String.Empty
            .m_index = m_InvalidId
            .m_orderby = String.Empty
            .m_orderdir = String.Empty
            .m_filter = String.Empty
            .m_values = Nothing
        End With
    End Sub

    Public Sub New(ByVal screen_name As String)
        With Me
            .m_screenname = screen_name
            .m_index = m_InvalidId
            .m_orderby = String.Empty
            .m_orderdir = String.Empty
            .m_filter = String.Empty
            .m_values = Nothing
        End With
    End Sub

    Public Property ScreenName() As String

        Get
            Return m_screenname
        End Get

        Set(ByVal value As String)
            Me.m_screenname = value
        End Set

    End Property

    Public Property Index() As Integer

        Get
            Return m_index
        End Get

        Set(ByVal value As Integer)
            Me.m_index = value
        End Set

    End Property

    Public Property OrderBy() As String

        Get
            Return m_orderby
        End Get

        Set(ByVal value As String)
            Me.m_orderby = value
        End Set

    End Property

    Public Property OrderDir() As String

        Get
            Return m_orderdir
        End Get

        Set(ByVal value As String)
            Me.m_orderdir = value
        End Set

    End Property

    Public Property Filter() As String

        Get
            Return m_filter
        End Get

        Set(ByVal value As String)
            Me.m_filter = value
        End Set

    End Property

    Public Property Values() As String

        Get
            Return m_values
        End Get

        Set(ByVal value As String)
            Me.m_values = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = "{"

        'UsersGUI.tools.JSONstringify(Me, json_to_return)
        json_to_return &= """ScreenName"":""" & m_screenname & """"
        json_to_return &= ", ""Index"":" & m_index

        If Not IsNothing(m_orderby) Then
            json_to_return &= ",""OrderBy"":""" & m_orderby & """"
        Else
            json_to_return &= ",""OrderBy"":null"
        End If

        If Not IsNothing(m_orderdir) Then
            json_to_return &= ",""OrderDir"":""" & m_orderdir & """"
        Else
            json_to_return &= ",""OrderDir"":null"
        End If

        If Not IsNothing(m_filter) Then
            json_to_return &= ",""Filter"":""" & m_filter & """"
        Else
            json_to_return &= ",""Filter"":null"
        End If

        If Not IsNothing(m_values) Then
            json_to_return &= ",""Values"":" & m_values & ""
        Else
            json_to_return &= ",""Values"":null"
        End If

        json_to_return &= "}"

        Return json_to_return
    End Function

End Class

'input
Public Class SocketUsersManagementActionObj
    Private m_screenname As String
    Private m_screencod As Integer
    Private m_id As Integer
    Private m_values As List(Of SocketUsersManagementValueObj)

    Public Sub New(ByVal screen_name As String, ByVal screen_cod As Integer)
        With Me
            .m_screenname = screen_name
            .m_screencod = screen_cod
            .m_id = m_InvalidId
            .m_values = New List(Of SocketUsersManagementValueObj)
        End With
    End Sub

    Public Sub New()
        With Me
            .m_screenname = String.Empty
            .m_id = m_InvalidId
            .m_values = New List(Of SocketUsersManagementValueObj)
        End With
    End Sub

    Public Property ScreenName() As String

        Get
            Return m_screenname
        End Get

        Set(ByVal value As String)
            Me.m_screenname = value
        End Set

    End Property

    Public Property ScreenCod() As Integer

        Get
            Return m_screencod
        End Get

        Set(ByVal value As Integer)
            Me.m_screencod = value
        End Set

    End Property

    Public Property Id() As Integer

        Get
            Return m_id
        End Get

        Set(ByVal value As Integer)
            Me.m_id = value
        End Set

    End Property

    Public Property Values() As List(Of SocketUsersManagementValueObj)

        Get
            Return m_values
        End Get

        Set(ByVal value As List(Of SocketUsersManagementValueObj))
            Me.m_values = value
        End Set

    End Property

End Class

Public Class SocketUsersManagementValueObj
    Private m_fielddb As String
    Private m_value As String

    Public Sub New()
        With Me
            .m_fielddb = String.Empty
            .m_value = String.Empty
        End With
    End Sub

    Public Property FieldDB() As String

        Get
            Return m_fielddb
        End Get

        Set(ByVal value As String)
            Me.m_fielddb = value
        End Set

    End Property

    Public Property Value() As String

        Get
            Return m_value
        End Get

        Set(ByVal value As String)
            Me.m_value = value
        End Set

    End Property

End Class

'input
'output
Public Class SocketScadaCommandObj
    Private m_identify As Integer
    Private m_data As String
    Private m_result As Boolean
    Private m_return_to_client As Object

    Public Sub New()
        With Me
            .m_identify = m_InvalidInteger
            .m_data = String.Empty
        End With
    End Sub

    Public Property Identify() As Integer

        Get
            Return m_identify
        End Get

        Set(ByVal value As Integer)
            Me.m_identify = value
        End Set

    End Property

    Public Property Data() As String

        Get
            Return m_data
        End Get

        Set(ByVal value As String)
            Me.m_data = value
        End Set

    End Property

    Public Property Result() As Boolean

        Get
            Return m_result
        End Get

        Set(ByVal value As Boolean)
            Me.m_result = value
        End Set

    End Property

    Public Property ReturnToClient() As Object

        Get
            Return m_return_to_client
        End Get

        Set(ByVal value As Object)
            Me.m_return_to_client = value
        End Set

    End Property

    Public Function GetJSON() As String
        Dim json_to_return As String = String.Empty

        UsersGUI.tools.JSONstringify(Me, json_to_return)

        Return json_to_return
    End Function

End Class