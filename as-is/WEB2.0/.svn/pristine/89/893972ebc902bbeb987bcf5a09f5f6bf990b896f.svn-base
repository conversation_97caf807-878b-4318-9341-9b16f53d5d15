﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class Supplier
    Inherits myDbBaseClass

    Private m_id As Long = m_InvalidId
    Private m_id_erp As String = String.Empty
    Private m_name As String = String.Empty
    Private m_full_name As String = String.Empty
    Private m_address As String = String.Empty
    Private m_zip_code As String = String.Empty
    Private m_city As String = String.Empty
    Private m_country As String = String.Empty
    Private m_telephone As String = String.Empty
    Private m_email As String = String.Empty
    Private m_is_obsolete As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("SUPPLIERS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As Supplier
        Me.IdSupplier = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "IdErp"
                            Me.IdErp = CType(m_Control, myTextBox).Text
                        Case "Name"
                            Me.Name = CType(m_Control, myTextBox).Text
                        Case "FullName"
                            Me.FullName = CType(m_Control, myTextBox).Text
                        Case "Address"
                            Me.Address = CType(m_Control, myTextBox).Text
                        Case "ZipCode"
                            Me.ZipCode = CType(m_Control, myTextBox).Text
                        Case "City"
                            Me.City = CType(m_Control, myTextBox).Text
                        Case "Country"
                            Me.Country = CType(m_Control, myTextBox).Text
                        Case "Telephone"
                            Me.Telephone = CType(m_Control, myTextBox).Text
                        Case "Email"
                            Me.Email = CType(m_Control, myTextBox).Text
                    End Select
                ElseIf TypeOf m_Control Is myCheckBox Then
                    Select Case s.PropertyName
                        Case "IsObsolete"
                            Try
                                Me.IsObsolete = m_StringNo
                                If CType(m_Control, myCheckBox).Checked Then
                                    Me.IsObsolete = m_StringYes
                                End If
                            Catch
                                Me.IsObsolete = m_StringNo
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdSupplier() As Long
        Get
            Return Me.m_id
        End Get
        Set(ByVal value As Long)
            Me.m_id = value
        End Set
    End Property

    Public Property IdErp() As String
        Get
            Return Me.m_id_erp
        End Get
        Set(ByVal value As String)
            Me.m_id_erp = value.Trim
        End Set
    End Property

    Public Property Name() As String
        Get
            Return Me.m_name
        End Get
        Set(ByVal value As String)
            Me.m_name = value.Trim
        End Set
    End Property

    Public Property FullName() As String
        Get
            Return Me.m_full_name
        End Get
        Set(ByVal value As String)
            Me.m_full_name = value.Trim
        End Set
    End Property

    Public Property Address() As String
        Get
            Return Me.m_address
        End Get
        Set(ByVal value As String)
            Me.m_address = value.Trim
        End Set
    End Property

    Public Property ZipCode() As String
        Get
            Return Me.m_zip_code
        End Get
        Set(ByVal value As String)
            Me.m_zip_code = value.Trim
        End Set
    End Property

    Public Property City() As String
        Get
            Return Me.m_city
        End Get
        Set(ByVal value As String)
            Me.m_city = value.Trim
        End Set
    End Property

    Public Property Country() As String
        Get
            Return Me.m_country
        End Get
        Set(ByVal value As String)
            Me.m_country = value.Trim
        End Set
    End Property

    Public Property Telephone() As String
        Get
            Return Me.m_telephone
        End Get
        Set(ByVal value As String)
            Me.m_telephone = value.Trim
        End Set
    End Property

    Public Property Email() As String
        Get
            Return Me.m_email
        End Get
        Set(ByVal value As String)
            Me.m_email = value.Trim
        End Set
    End Property

    Public Property IsObsolete() As String
        Get
            Return Me.m_is_obsolete
        End Get
        Set(ByVal value As String)
            Me.m_is_obsolete = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdSupplier & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdSupplier))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio supplier."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio supplier sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdSupplier
            If Me.IdSupplier = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            If Me.IdErp <> String.Empty Then
                drNodo("ID_ERP") = Me.TrimStringToMaxLen(Me.Name, "ID_ERP")
            Else
                drNodo("ID_ERP") = DBNull.Value
            End If

            If Me.Name <> String.Empty Then
                drNodo("NAME") = Me.TrimStringToMaxLen(Me.Name, "NAME")
            Else
                drNodo("NAME") = DBNull.Value
            End If

            If Me.FullName <> String.Empty Then
                drNodo("FULL_NAME") = Me.TrimStringToMaxLen(Me.FullName, "FULL_NAME")
            Else
                drNodo("FULL_NAME") = DBNull.Value
            End If

            If Me.Address <> String.Empty Then
                drNodo("ADDRESS") = Me.TrimStringToMaxLen(Me.Address, "ADDRESS")
            Else
                drNodo("ADDRESS") = DBNull.Value
            End If

            If Me.ZipCode <> String.Empty Then
                drNodo("ZIP_CODE") = Me.TrimStringToMaxLen(Me.ZipCode, "ZIP_CODE")
            Else
                drNodo("ZIP_CODE") = DBNull.Value
            End If

            If Me.City <> String.Empty Then
                drNodo("CITY") = Me.TrimStringToMaxLen(Me.City, "CITY")
            Else
                drNodo("CITY") = DBNull.Value
            End If

            If Me.Country <> String.Empty Then
                drNodo("COUNTRY") = Me.TrimStringToMaxLen(Me.Country, "COUNTRY")
            Else
                drNodo("COUNTRY") = DBNull.Value
            End If

            If Me.Telephone <> String.Empty Then
                drNodo("TELEPHONE") = Me.TrimStringToMaxLen(Me.Telephone, "TELEPHONE")
            Else
                drNodo("TELEPHONE") = DBNull.Value
            End If

            If Me.Email <> String.Empty Then
                drNodo("EMAIL") = Me.TrimStringToMaxLen(Me.Email, "EMAIL")
            Else
                drNodo("EMAIL") = DBNull.Value
            End If

            If Me.IsObsolete.ToUpper = m_StringYes Then
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringYes, "IS_OBSOLETE")
            Else
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringNo, "IS_OBSOLETE")
            End If

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento supplier: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class