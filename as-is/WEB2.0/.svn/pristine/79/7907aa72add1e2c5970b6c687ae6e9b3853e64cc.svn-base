﻿Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class PoNumber
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_Number As String = String.Empty
    Private m_Status As Integer = 0
    Private m_InsertDate As DateTime = m_InvalidDateTime
    Private m_InsertUser As Long = m_InvalidId
    Private m_ClosedDate As DateTime = m_InvalidDateTime
    Private m_ClosedUser As Long = m_SystemIdUser
    Private m_Notes As String = String.Empty
    Private m_UpdateDate As DateTime = m_InvalidDateTime
    Private m_UpdateUser As Long = m_SystemIdUser

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("PO_NUMBER_LIST")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As PoNumber
        Me.IdPoNumber = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "Status"
                            Try
                                Me.Status = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.Status = 0
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "Number"
                            Dim s_val_digit As Integer = 20
                            Dim s_val As String = String.Empty
                            Dim dt_read_val As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable("SELECT * FROM SYSTEM_PARAMETERS WHERE PARAMETER_NAME='MAX_PO_NUMBER_DIGIT'", False)
                            If (dt_read_val IsNot Nothing AndAlso dt_read_val.Rows.Count > 0) Then
                                s_val_digit = Integer.Parse(dt_read_val.Rows(0)("PARAMETER_VALUE").ToString())
                            End If

                            Dim x As Integer = 0
                            For x = 1 To s_val_digit - CType(m_Control, myTextBox).Text.Length
                                s_val &= "0"
                            Next
                            s_val &= CType(m_Control, myTextBox).Text
                            Me.Number = s_val
                        Case "Notes"
                            Me.Notes = CType(m_Control, myTextBox).Text
                        Case "InsertUser"
                            If (CType(m_Control, myTextBox).Text <> String.Empty) Then
                                Me.InsertUser = Long.Parse(CType(m_Control, myTextBox).Text)
                            Else
                                Me.InsertUser = m_InvalidId
                            End If
                        Case "ClosedUser"
                            If (CType(m_Control, myTextBox).Text <> String.Empty) Then
                                Me.ClosedUser = Long.Parse(CType(m_Control, myTextBox).Text)
                            Else
                                Me.ClosedUser = m_InvalidId
                            End If
                    End Select
                ElseIf m_Control Is Nothing Then
                    Select Case s.PropertyName
                        Case "InsertDate"
                            Try
                                Me.InsertDate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.InsertDate = m_InvalidDateTime
                            End Try
                        Case "ClosedDate"
                            Try
                                Me.ClosedDate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.ClosedDate = m_InvalidDateTime
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdPoNumber() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property Status() As Long
        Get
            Return Me.m_Status
        End Get
        Set(ByVal value As Long)
            Me.m_Status = value
        End Set
    End Property

    Public Property Number() As String
        Get
            Return Me.m_Number
        End Get
        Set(ByVal value As String)
            Me.m_Number = value
        End Set
    End Property

    Public Property Notes() As String
        Get
            Return Me.m_Notes
        End Get
        Set(ByVal value As String)
            Me.m_Notes = value
        End Set
    End Property

    Public Property InsertDate() As DateTime
        Get
            Return Me.m_InsertDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_InsertDate = value
        End Set
    End Property

    Public Property InsertUser() As Long
        Get
            Return Me.m_InsertUser
        End Get
        Set(ByVal value As Long)
            Me.m_InsertUser = value
        End Set
    End Property

    Public Property ClosedDate() As DateTime
        Get
            Return Me.m_ClosedDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_ClosedDate = value
        End Set
    End Property

    Public Property ClosedUser() As Long
        Get
            Return Me.m_ClosedUser
        End Get
        Set(ByVal value As Long)
            Me.m_ClosedUser = value
        End Set
    End Property

    Public Property UpdateDate() As DateTime
        Get
            Return Me.m_UpdateDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_UpdateDate = value
        End Set
    End Property

    Public Property UpdateUser() As Long
        Get
            Return Me.m_UpdateUser
        End Get
        Set(ByVal value As Long)
            Me.m_UpdateUser = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdPoNumber & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdPoNumber))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio PoNumber."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio PoNumber sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            If Me.Number <> String.Empty Then
                drNodo("NUMBER") = Me.Number
            End If

            If Me.Notes <> String.Empty Then
                drNodo("NOTES") = Me.Notes
            Else
                drNodo("NOTES") = DBNull.Value
            End If

            drNodo("STATUS") = Me.Status

            If Me.Status = EnumStatusPONumbers.Closed Then
                'Sono sicuramente nella fase di chiusura, se qui in manuale
                'se un operatore decide di chiudere un PO_NUMBERS allora gestisco le logiche di chiusura
                If (Me.ClosedUser <> m_InvalidId) Then
                    drNodo("CLOSED_USER") = Me.ClosedUser
                End If
                If Me.ClosedDate <> m_InvalidDateTime Then
                    drNodo("CLOSED_DATE") = Me.ClosedDate
                Else
                    Me.ClosedDate = tools.ConvertDateTimeToDB(Now, m_scr.Parent.GetLanguage().FormatDateTime)
                    drNodo("CLOSED_DATE") = Me.ClosedDate
                End If
            Else
                drNodo("CLOSED_USER") = DBNull.Value
                drNodo("CLOSED_DATE") = DBNull.Value
            End If

            If (InsertDate = m_InvalidDateTime) Then
                Me.InsertDate = tools.ConvertDateTimeToDB(Now, m_scr.Parent.GetLanguage().FormatDateTime)
                drNodo("INSERT_USER") = Me.InsertUser
            End If

            drNodo("UPDATE_USER") = Me.UpdateUser
            Me.UpdateDate = tools.ConvertDateTimeToDB(Now, m_scr.Parent.GetLanguage().FormatDateTime)
            drNodo("UPDATE_DATE") = Me.UpdateDate

            drNodo("UPDATE_FROM_FLOW") = 0

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento PoNumber: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class