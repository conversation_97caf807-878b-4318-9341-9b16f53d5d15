﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class Product
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdProductType As Long = m_InvalidId
    Private m_Name As String = String.Empty
    Private m_Barcode As String = String.Empty
    Private m_Version As String = String.Empty
    Private m_MedicatedFlag As String = String.Empty
    Private m_IsObsolete As String = String.Empty
    Private m_SpecificWeight As Double = m_DoubleZero
    Private m_ProLevel As Double = m_DoubleZero
    Private m_ProDate As DateTime = m_InvalidDateTime
    Private m_HectolitricWeight As Double = m_DoubleZero
    Private m_ShortName As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("PRODUCTS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As Product
        Me.IdProduct = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdProductType"
                            Try
                                Me.IdProductType = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdProductType = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myCheckBox Then
                    Select Case s.PropertyName
                        Case "IsObsolete"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.IsObsolete = costanti.m_StringYes
                            Else
                                Me.IsObsolete = m_StringNo
                            End If
                        Case "MedicatedFlag"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.MedicatedFlag = costanti.m_StringYes
                            Else
                                Me.MedicatedFlag = m_StringNo
                            End If
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "Name"
                            Me.Name = CType(m_Control, myTextBox).Text
                        Case "Version"
                            Me.Version = CType(m_Control, myTextBox).Text
                        Case "Barcode"
                            Me.Barcode = CType(m_Control, myTextBox).Text
                        Case "ProLevel"
                            Me.ProLevel = tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                        Case "SpecificWeight"
                            Me.SpecificWeight = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                        Case "HectolitricWeight"
                            Me.HectolitricWeight = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                        Case "ShortName"
                            Me.ShortName = CType(m_Control, myTextBox).Text
                    End Select
                ElseIf m_Control Is Nothing Then
                    Select Case s.PropertyName
                        Case "ProDate"
                            Try
                                Me.ProDate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.ProDate = m_InvalidDateTime
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdProduct() As Long
        Get
            Return Me.m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdProductType() As Long
        Get
            Return Me.m_IdProductType
        End Get
        Set(ByVal value As Long)
            Me.m_IdProductType = value
        End Set
    End Property

    Public Property Barcode() As String
        Get
            Return Me.m_Barcode
        End Get
        Set(ByVal value As String)
            Me.m_Barcode = value.Trim
        End Set
    End Property

    Public Property Version() As String
        Get
            Return Me.m_Version
        End Get
        Set(ByVal value As String)
            Me.m_Version = value.Trim
        End Set
    End Property

    Public Property IsObsolete() As String
        Get
            Return Me.m_IsObsolete
        End Get
        Set(ByVal value As String)
            Me.m_IsObsolete = value.Trim
        End Set
    End Property

    Public Property MedicatedFlag() As String
        Get
            Return Me.m_MedicatedFlag
        End Get
        Set(ByVal value As String)
            Me.m_MedicatedFlag = value.Trim
        End Set
    End Property

    Public Property Name() As String
        Get
            Return Me.m_Name
        End Get
        Set(ByVal value As String)
            Me.m_Name = value.Trim
        End Set
    End Property

    Public Property ProDate() As DateTime
        Get
            Return Me.m_ProDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_ProDate = value
        End Set
    End Property

    Public Property ProLevel() As Double
        Get
            Return Me.m_ProLevel
        End Get
        Set(ByVal value As Double)
            Me.m_ProLevel = value
        End Set
    End Property

    Public Property SpecificWeight() As Double
        Get
            Return Me.m_SpecificWeight
        End Get
        Set(ByVal value As Double)
            Me.m_SpecificWeight = value
        End Set
    End Property

    Public Property HectolitricWeight() As Double
        Get
            Return Me.m_HectolitricWeight
        End Get
        Set(ByVal value As Double)
            Me.m_HectolitricWeight = value
        End Set
    End Property

    Public Property ShortName() As String
        Get
            Return Me.m_ShortName
        End Get
        Set(ByVal value As String)
            Me.m_ShortName = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdProduct & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True

            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdProduct))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio del prodotto."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If

                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio del prodotto sulla select del recupero dati."
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdProduct

            If Me.IdProduct = m_InvalidId Then
                Me.IdProduct = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.IdProduct
            End If

            drNodo("NAME") = Me.TrimStringToMaxLen(Me.Name, "NAME")
            drNodo("SHORT_NAME") = Me.TrimStringToMaxLen(Me.ShortName, "SHORT_NAME")
            drNodo("VERSION") = Me.TrimStringToMaxLen(Me.Version, "VERSION")

            If Me.Barcode <> String.Empty Then
                drNodo("BARCODE") = Me.TrimStringToMaxLen(Me.Barcode, "BARCODE")
            Else
                drNodo("BARCODE") = DBNull.Value
            End If

            If Me.IsObsolete.ToUpper = m_StringYes Then
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringYes, "IS_OBSOLETE")
            Else
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringNo, "IS_OBSOLETE")
            End If

            If Me.MedicatedFlag.ToUpper = m_StringYes Then
                drNodo("MEDICATED_FLAG") = Me.TrimStringToMaxLen(m_StringYes, "MEDICATED_FLAG")
            Else
                drNodo("MEDICATED_FLAG") = Me.TrimStringToMaxLen(m_StringNo, "MEDICATED_FLAG")
            End If

            Me.SpecificWeight = Math.Round(Me.SpecificWeight, 3)
            drNodo("SPECIFIC_WEIGHT") = Me.SpecificWeight

            Me.HectolitricWeight = Math.Round(Me.HectolitricWeight, 3)
            drNodo("HECTOLITRIC_WEIGHT") = Me.HectolitricWeight

            drNodo("PRO_LEVEL") = Me.ProLevel

            If Me.ProDate <> m_InvalidDateTime Then
                drNodo("PRO_DATE") = Me.ProDate
            Else
                drNodo("PRO_DATE") = Now.ToString(m_FormatDateTimeToSqlServer)
            End If

            drNodo("PT_ID") = Me.IdProductType

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim m_error As String = "Errore durante aggiornamento del prodotto. Controllare i dati immessi e riprovare."
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, m_error)
                Throw myExc
            End Try

        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub
End Class