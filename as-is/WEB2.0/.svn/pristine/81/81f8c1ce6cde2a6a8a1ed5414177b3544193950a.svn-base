﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI
Imports System.Web.UI.WebControls

Public Class Product
    Inherits myDbBaseClass

    Private m_id As Long = m_InvalidId
    Private m_id_erp As String = String.Empty
    Private m_pt_id As Long = m_InvalidId
    Private m_name As String = String.Empty
    Private m_short_name As String = String.Empty
    Private m_hectolitric_weight As Double = m_DoubleZero
    Private m_is_obsolete As String = String.Empty
    Private m_last_update As DateTime = m_InvalidDateTime
    Private m_creation_date As DateTime = m_InvalidDateTime

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("PRODUCTS")
        Me.m_scr = scr
        Me.m_root = root
        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As Product
        Me.IdProduct = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdProductType"
                            Try
                                Me.IdProductType = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdProductType = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myCheckBox Then
                    Select Case s.PropertyName
                        Case "IsObsolete"
                            If CType(m_Control, myCheckBox).Checked Then
                                Me.IsObsolete = costanti.m_StringYes
                            Else
                                Me.IsObsolete = m_StringNo
                            End If
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "Name"
                            Me.Name = CType(m_Control, myTextBox).Text
                        Case "ShortName"
                            Me.ShortName = CType(m_Control, myTextBox).Text
                        Case "HectolitricWeight"
                            Me.HectolitricWeight = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal)
                    End Select
                ElseIf m_Control Is Nothing Then
                    Select Case s.PropertyName
                        Case "CreationDate"
                            Try
                                Me.CreationDate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.CreationDate = m_InvalidDateTime
                            End Try
                        Case "LastUpdate"
                            Try
                                Me.LastUpdate = Date.Parse(CType(tools.FindControlRecursive(m_root, s.FieldDb & "_DATE_DA"), TextBox).Text)
                            Catch
                                Me.LastUpdate = m_InvalidDateTime
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdProduct() As Long
        Get
            Return Me.m_id
        End Get
        Set(ByVal value As Long)
            Me.m_id = value
        End Set
    End Property

    Public Property IdErp() As String
        Get
            Return Me.m_id_erp
        End Get
        Set(ByVal value As String)
            Me.m_id_erp = value.Trim
        End Set
    End Property

    Public Property IdProductType() As Long
        Get
            Return Me.m_pt_id
        End Get
        Set(ByVal value As Long)
            Me.m_pt_id = value
        End Set
    End Property

    Public Property Name() As String
        Get
            Return Me.m_name
        End Get
        Set(ByVal value As String)
            Me.m_name = value.Trim
        End Set
    End Property

    Public Property ShortName() As String
        Get
            Return Me.m_short_name
        End Get
        Set(ByVal value As String)
            Me.m_short_name = value.Trim
        End Set
    End Property

    Public Property HectolitricWeight() As Double
        Get
            Return Me.m_hectolitric_weight
        End Get
        Set(ByVal value As Double)
            Me.m_hectolitric_weight = value
        End Set
    End Property

    Public Property IsObsolete() As String
        Get
            Return Me.m_is_obsolete
        End Get
        Set(ByVal value As String)
            Me.m_is_obsolete = value.Trim
        End Set
    End Property

    Public Property LastUpdate() As DateTime
        Get
            Return Me.m_last_update
        End Get
        Set(ByVal value As DateTime)
            Me.m_last_update = value
        End Set
    End Property

    Public Property CreationDate() As DateTime
        Get
            Return Me.m_creation_date
        End Get
        Set(ByVal value As DateTime)
            Me.m_creation_date = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdProduct & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdProduct))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio del prodotto."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio del prodotto sulla select del recupero dati."
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdProduct
            If Me.IdProduct = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            If Me.IdErp <> String.Empty Then
                drNodo("ID_ERP") = Me.TrimStringToMaxLen(Me.Name, "ID_ERP")
            Else
                drNodo("ID_ERP") = DBNull.Value
            End If

            drNodo("PT_ID") = Me.IdProductType

            drNodo("NAME") = Me.TrimStringToMaxLen(Me.Name, "NAME")
            drNodo("SHORT_NAME") = Me.TrimStringToMaxLen(Me.ShortName, "SHORT_NAME")

            drNodo("HECTOLITRIC_WEIGHT") = Math.Round(Me.HectolitricWeight, 3)

            If Me.IsObsolete.ToUpper = m_StringYes Then
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringYes, "IS_OBSOLETE")
            Else
                drNodo("IS_OBSOLETE") = Me.TrimStringToMaxLen(m_StringNo, "IS_OBSOLETE")
            End If

            If Me.CreationDate = m_InvalidDateTime Then
                drNodo("CREATION_DATE") = Now.ToString(m_FormatDateTimeToSqlServer)
            End If

            drNodo("LAST_UPDATE") = Now.ToString(m_FormatDateTimeToSqlServer)

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim m_error As String = "Errore durante aggiornamento del prodotto. Controllare i dati immessi e riprovare."
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, m_error)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class