﻿Option Strict On

Imports System.Data.OleDb

Public Class MaintProcExecReq
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdSu As Long = m_InvalidId
    Private m_IdMpa As Long = m_InvalidId
    Private m_Intervention_report As String = String.Empty

    Public Sub New(ByVal su_id As Long, ByVal mpa_id As Long, ByVal intervention_report As String)
        MyBase.New("MAINT_PROC_EXEC_REQ")
        Me.IdMaintProcExecReq = m_InvalidId
        Me.IdSu = su_id
        Me.IdMpa = mpa_id
        Me.InterventionReport = intervention_report
    End Sub

    Public Property IdMaintProcExecReq() As Long
        Get
            Return m_Id
        End Get
        Set(value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdSu() As Long
        Get
            Return m_IdSu
        End Get
        Set(value As Long)
            Me.m_IdSu = value
        End Set
    End Property

    Public Property IdMpa() As Long
        Get
            Return m_IdMpa
        End Get
        Set(value As Long)
            Me.m_IdMpa = value
        End Set
    End Property

    Public Property InterventionReport() As String
        Get
            Return m_Intervention_report
        End Get
        Set(value As String)
            Me.m_Intervention_report = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdMaintProcExecReq & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdMaintProcExecReq))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio equipment"
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio equipment sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdMaintProcExecReq

            If Me.IdMaintProcExecReq = m_InvalidId Then
                drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            End If

            If Me.IdSu <> m_InvalidId Then
                drNodo("SU_ID") = Me.IdSu
            End If

            If Me.IdMpa <> m_InvalidId Then
                drNodo("MPA_ID") = Me.IdMpa
            End If

            drNodo("INTERVENTION_REPORT") = Me.TrimStringToMaxLen(Me.InterventionReport, "INTERVENTION_REPORT")

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento maint_proc_exeq_req: " & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class