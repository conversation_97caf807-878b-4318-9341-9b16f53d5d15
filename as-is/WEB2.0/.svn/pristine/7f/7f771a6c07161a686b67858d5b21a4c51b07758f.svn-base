﻿Imports System.IO
Imports System.Xml

Public Class config

    Private m_Entry As Generic.List(Of Entry)
    Private m_Menu As Generic.List(Of MenuAtmill)
    Private m_Screen As Generic.List(Of Screen)
    Private m_PathXmlFile As String
    Private m_PathXmlFile_Languages As String
    Private xmlDoc As myXmlDocument
    Private xmlDoc_Languages As myXmlDocument
    Private m_CurrentLanguage As String
    Private m_CustomerLanguage As String
    Private m_ListLanguages As Generic.List(Of Language)
    Private m_Charts As Charts

    Public Sub New(ByVal PathXmlFile As String, ByVal PathXmlFile_Languages As String)
        m_PathXmlFile = PathXmlFile
        m_PathXmlFile_Languages = PathXmlFile_Languages
        Dim f As New FileInfo(m_PathXmlFile)
        Dim l As New FileInfo(m_PathXmlFile_Languages)
        If f.Exists AndAlso l.Exists Then
            xmlDoc = New myXmlDocument(m_PathXmlFile)
            xmlDoc_Languages = New myXmlDocument(m_PathXmlFile_Languages)

            '1. Load Language
            LoadCurrentLanguage()

            '2. Load All Data
            LoadXmlDocument()

        End If

    End Sub

    Public Sub ReLoadLanguagesXml()

        xmlDoc_Languages = New myXmlDocument(m_PathXmlFile_Languages)

        Me.m_ListLanguages.Clear()

        '1. Load Language
        LoadCurrentLanguage()

        '2. Update translations of the menus
        UpdateMenuTranslations()

    End Sub

    Public ReadOnly Property GetMenusAll() As Generic.List(Of MenuAtmill)
        Get
            Return m_Menu
        End Get
    End Property

    Public ReadOnly Property GetMenusTopLevel() As Generic.List(Of MenuAtmill)
        Get
            Dim m_ListMenu As New Generic.List(Of MenuAtmill)
            For Each m As MenuAtmill In m_Menu
                If m.IsTopLevel AndAlso m.Visible Then
                    m_ListMenu.Add(m)
                End If
            Next
            Return m_ListMenu
        End Get
    End Property

    Public ReadOnly Property GetMenusNotTopLevel() As Generic.List(Of MenuAtmill)
        Get
            Dim m_ListMenu As New Generic.List(Of MenuAtmill)
            For Each m As MenuAtmill In m_Menu
                If Not m.IsTopLevel Then
                    m_ListMenu.Add(m)
                End If
            Next
            Return m_ListMenu
        End Get
    End Property

    Public ReadOnly Property Entry() As Generic.List(Of Entry)
        Get
            Return m_Entry
        End Get
    End Property

    Public ReadOnly Property GetScreens() As Generic.List(Of Screen)
        Get
            Return m_Screen
        End Get
    End Property

    Public ReadOnly Property GetCurrentLanguage() As EnumTranslation
        Get

            Select Case m_CurrentLanguage
                Case "0"
                    Return EnumTranslation.Francese
                Case "1"
                    Return EnumTranslation.Inglese
                Case "2"
                    Return EnumTranslation.Italiano
                Case "3"
                    Return EnumTranslation.Spagnolo
                Case "4"
                    Return EnumTranslation.Russo
                Case "5"
                    Return EnumTranslation.Portoghese
                Case "6"
                    Return EnumTranslation.Tedesco
                Case "7"
                    Return EnumTranslation.Indiano
                Case "8"
                    Return EnumTranslation.Arabo
                Case "9"
                    Return EnumTranslation.Altro
                Case Else
                    Return EnumTranslation.Italiano
            End Select

        End Get
    End Property

    Public ReadOnly Property GetCustomerLanguage() As EnumTranslation
        Get

            Select Case m_CustomerLanguage
                Case "0"
                    Return EnumTranslation.Italiano
                Case "1"
                    Return EnumTranslation.Inglese
                Case "2"
                    Return EnumTranslation.Francese
                Case "3"
                    Return EnumTranslation.Spagnolo
                Case "4"
                    Return EnumTranslation.Portoghese
                Case "5"
                    Return EnumTranslation.Russo
                Case "6"
                    Return EnumTranslation.Indiano
                Case "7"
                    Return EnumTranslation.Arabo
                Case Else
                    Return EnumTranslation.Italiano
            End Select

        End Get
    End Property

    Public ReadOnly Property GetCharts() As Charts
        Get
            Return Me.m_Charts
        End Get
    End Property

    Public ReadOnly Property GetLanguage() As Language
        Get

            For Each l As Language In m_ListLanguages
                If l.Type = CType(Me.m_CurrentLanguage, EnumTranslation) Then
                    Return l
                End If
            Next

            Return Nothing
        End Get
    End Property

    Public Property SetCurrentLanguage() As String
        Get
            Return Me.m_CurrentLanguage
        End Get
        Set(ByVal value As String)
            For Each xmlNode As System.Xml.XmlNode In xmlDoc.SelectNodes("//Languages")
                xmlNode.SelectSingleNode("currentLanguage", xmlDoc.GetXmlManager).InnerText = value
            Next
            m_CurrentLanguage = value
            xmlDoc.GetXmlDoc.Save(Me.m_PathXmlFile)
        End Set
    End Property

    Public Property SetCustomerLanguage() As String
        Get
            Return Me.m_CurrentLanguage
        End Get
        Set(ByVal value As String)
            For Each xmlNode As System.Xml.XmlNode In xmlDoc.SelectNodes("//Languages")
                xmlNode.SelectSingleNode("CustomerLanguage", xmlDoc.GetXmlManager).Value = value
            Next
            m_CustomerLanguage = value
        End Set
    End Property

    Public ReadOnly Property GetEntryByKeyName(ByVal KeyName As String) As Entry
        Get
            Dim et As Entry = Nothing

            For Each e As Entry In Me.Entry
                If e Is Nothing Then
                    Exit For
                ElseIf KeyName Is Nothing Then
                    Exit For
                ElseIf e.KeyName Is Nothing Then
                    Exit For
                ElseIf e.KeyName.ToUpper = KeyName.ToUpper Then
                    et = e
                    Exit For
                End If
            Next
            If et Is Nothing Then
                et = New Entry
                et.KeyName = "NULL_NAME"
                et.Francese = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Inglese = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Italiano = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Spagnolo = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Russo = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Portoghese = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Tedesco = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Indiano = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Arabo = "Attenzione, Entry non trovata per il KeyName: " & KeyName
                et.Altro = "Attenzione, Entry non trovata per il KeyName: " & KeyName

            End If
            Return et
        End Get
    End Property

    Public ReadOnly Property GetMenuByMenuName(ByVal MenuName As String) As MenuAtmill
        Get
            Dim et As MenuAtmill = Nothing
            For Each e As MenuAtmill In Me.GetMenusAll
                If e.Name = MenuName Then
                    et = e
                    Exit For
                End If
            Next
            If et Is Nothing Then
                et = New MenuAtmill
                et.Name = "NULL_Menu"
                et.Title = "Attenzione, menu non trovato per il MenuName:" & MenuName
            End If
            Return et
        End Get
    End Property

    Public ReadOnly Property GetItemName(ByVal m_menu As MenuAtmill, ByVal m_PageName As String) As String
        Get
            Dim m_count As Integer = 0
            Dim m_menuItemLink As menuItemsLink

            m_menuItemLink = m_menu.GetMenuItemsLinkFromPageName(m_PageName)

            If m_menuItemLink IsNot Nothing Then
                For Each li As MenuItem In m_menu.menuItems
                    If li.MenuItemLink.GetValue = m_menuItemLink.GetValue Then
                        Return li.MenuItemName.GetName
                    End If
                Next
            Else
                Return "MAIN_" & m_PageName.ToUpper & "_TITLE"
            End If
            Return "GetItemName Not Found"
        End Get
    End Property

    Public ReadOnly Property GetScreenByScreenName(ByVal ScreenName As String) As Screen
        Get
            Dim et As Screen = Nothing
            If Me.GetScreens IsNot Nothing Then
                For Each e As Screen In Me.GetScreens
                    If e.Name = ScreenName Then
                        et = e
                        Exit For
                    End If
                Next
            End If

            If et Is Nothing Then
                et = New Screen() With {
                    .DBName = m_InvalidScreen,
                    .Name = m_InvalidScreen
                }
            End If

            Return et
        End Get
    End Property

    Public Sub LoadXmlDocument()
        LoadEntryKeys()
        LoadMenus()
        LoadScreens()
        LoadCharts()
    End Sub

    Public ReadOnly Property GetCommessa()
        Get
            Return xmlDoc.SelectSingleNode("//Commessa").InnerText.Trim()
        End Get
    End Property

    Private Sub LoadEntryKeys()
        If m_Entry Is Nothing Then
            m_Entry = New Generic.List(Of Entry)
        End If
        m_Entry.Clear()

        If GetLanguage().DbLanguages = True Then
            Dim sSelect As String = "SELECT * FROM LANGUAGES"
            Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)
            If (dt IsNot Nothing AndAlso dt.Rows.Count > 0) Then
                For Each dr As System.Data.DataRow In dt.Rows
                    Dim e As New Entry
                    Try
                        e.ConfigParent = Me
                        e.KeyName = dr("KeyName").ToString().Trim()
                        e.Francese = dr("Francese").ToString().Trim()
                        e.Inglese = dr("Inglese").ToString().Trim()
                        e.Italiano = dr("Italiano").ToString().Trim()
                        e.Russo = dr("Russo").ToString().Trim()
                        e.Spagnolo = dr("Spagnolo").ToString().Trim()
                        e.Portoghese = dr("Portoghese").ToString().Trim()
                        e.Tedesco = dr("Tedesco").ToString().Trim()
                        e.Indiano = dr("Indiano").ToString().Trim()
                        e.Arabo = dr("Arabo").ToString().Trim()
                        e.Altro = dr("Altro").ToString().Trim()
                    Catch ex As Exception

                    End Try

                    m_Entry.Add(e)
                Next
            End If
        Else
            For Each xmlNode As System.Xml.XmlNode In xmlDoc_Languages.SelectNodes("//entry")
                Dim e As New Entry
                Try
                    e.ConfigParent = Me
                    e.KeyName = xmlNode.SelectSingleNode("KeyName", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Francese = xmlNode.SelectSingleNode("Francese", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Inglese = xmlNode.SelectSingleNode("Inglese", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Italiano = xmlNode.SelectSingleNode("Italiano", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Russo = xmlNode.SelectSingleNode("Russo", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Spagnolo = xmlNode.SelectSingleNode("Spagnolo", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Portoghese = xmlNode.SelectSingleNode("Portoghese", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Tedesco = xmlNode.SelectSingleNode("Tedesco", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Indiano = xmlNode.SelectSingleNode("Indiano", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Arabo = xmlNode.SelectSingleNode("Arabo", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                    e.Altro = xmlNode.SelectSingleNode("Altro", xmlDoc_Languages.GetXmlManager).InnerText.Trim
                Catch ex As Exception

                End Try

                m_Entry.Add(e)
            Next
        End If

    End Sub

    Private Sub LoadCharts()

        For Each xmlNode As System.Xml.XmlNode In xmlDoc.SelectNodes("//Charts")
            m_Charts = New Charts
            Try
                m_Charts.TemplateName = xmlNode.SelectSingleNode("TemplateName", xmlDoc.GetXmlManager).InnerText.Trim
            Catch ex As Exception

            End Try
        Next
    End Sub

    Public Sub LoadCurrentLanguage()
        Dim m_DbLanguages As Boolean = False

        If m_ListLanguages Is Nothing Then
            m_ListLanguages = New Generic.List(Of Language)
        End If

        For Each xmlNode As System.Xml.XmlNode In xmlDoc.SelectNodes("//Languages")
            m_CurrentLanguage = xmlNode.SelectSingleNode("currentLanguage", xmlDoc.GetXmlManager).InnerText.Trim
            m_CustomerLanguage = xmlNode.SelectSingleNode("CustomerLanguage", xmlDoc.GetXmlManager).InnerText.Trim

            If (xmlNode.SelectSingleNode("DbLanguages", xmlDoc.GetXmlManager) IsNot Nothing AndAlso xmlNode.SelectSingleNode("DbLanguages", xmlDoc.GetXmlManager).InnerText.ToString().Trim().ToLower() = "true") Then
                m_DbLanguages = True
            Else
                m_DbLanguages = False
            End If

            For Each xmlNodeLang As System.Xml.XmlNode In xmlNode.SelectNodes("Language")
                Dim l As Language = Nothing
                Select Case xmlNodeLang.SelectSingleNode("Type", xmlDoc.GetXmlManager).InnerText.Trim
                    Case "Francese"
                        l = New Language(EnumTranslation.Francese, m_CustomerLanguage)
                    Case "Inglese"
                        l = New Language(EnumTranslation.Inglese, m_CustomerLanguage)
                    Case "Italiano"
                        l = New Language(EnumTranslation.Italiano, m_CustomerLanguage)
                    Case "Spagnolo"
                        l = New Language(EnumTranslation.Spagnolo, m_CustomerLanguage)
                    Case "Russo"
                        l = New Language(EnumTranslation.Russo, m_CustomerLanguage)
                    Case "Portoghese"
                        l = New Language(EnumTranslation.Portoghese, m_CustomerLanguage)
                    Case "Tedesco"
                        l = New Language(EnumTranslation.Tedesco, m_CustomerLanguage)
                    Case "Indiano"
                        l = New Language(EnumTranslation.Indiano, m_CustomerLanguage)
                    Case "Arabo"
                        l = New Language(EnumTranslation.Arabo, m_CustomerLanguage)
                    Case "Altro"
                        l = New Language(EnumTranslation.Altro, m_CustomerLanguage)
                    Case Else
                        Continue For
                End Select

                ' for specific customizations of cultures check the full list @ http://msdn.microsoft.com/en-us/library/ms533052(v=vs.85).aspx
                l.CurrentInfo = xmlNodeLang.SelectSingleNode("CurrentInfo", xmlDoc.GetXmlManager).InnerText.Trim
                l.ImageFlagName = xmlNodeLang.SelectSingleNode("ImgFlag", xmlDoc.GetXmlManager).InnerText.Trim
                l.FormatDateTimeCalendar = xmlNodeLang.SelectSingleNode("FormatDateTimeCalendar", xmlDoc.GetXmlManager).InnerText.Trim
                l.FormatDateTime = xmlNodeLang.SelectSingleNode("FormatDateTime", xmlDoc.GetXmlManager).InnerText.Trim
                l.FormatDate = xmlNodeLang.SelectSingleNode("FormatDate", xmlDoc.GetXmlManager).InnerText.Trim
                l.FormatTime = xmlNodeLang.SelectSingleNode("FormatTime", xmlDoc.GetXmlManager).InnerText.Trim
                l.DbLanguages = m_DbLanguages

                Try
                    l.Visible = CBool(xmlNodeLang.SelectSingleNode("Visible", xmlDoc.GetXmlManager).InnerText.Trim)
                Catch ex As Exception
                    l.Visible = False
                End Try

                Me.m_ListLanguages.Add(l)
            Next
        Next
    End Sub

    Public ReadOnly Property GetListLanguages() As Generic.List(Of Language)
        Get
            Return Me.m_ListLanguages
        End Get
    End Property

    Private Sub LoadMenus()
        If m_Menu Is Nothing Then
            m_Menu = New Generic.List(Of MenuAtmill)
        End If
        m_Menu.Clear()
        For Each xmlNodeMenus As System.Xml.XmlNode In xmlDoc.SelectNodes("//menus")
            For Each xmlNodeMenu As System.Xml.XmlNode In xmlNodeMenus.SelectNodes("menu")
                Dim m As New MenuAtmill
                Try
                    m.Name = xmlNodeMenu.SelectSingleNode("Name", xmlDoc.GetXmlManager).InnerText.Trim

                    Try
                        m.Visible = CBool(xmlNodeMenu.SelectSingleNode("Visible", xmlDoc.GetXmlManager).InnerText.Trim())
                    Catch ex As Exception
                        m.Visible = False
                    End Try

                    m.Title = Me.GetEntryByKeyName(m.Name).GetValue
                    m.IsTopLevel = CBool(xmlNodeMenu.SelectSingleNode("IsTopLevel", xmlDoc.GetXmlManager).InnerText.Trim)
                    m.Color = System.Drawing.Color.FromName(xmlNodeMenu.SelectSingleNode("Color", xmlDoc.GetXmlManager).InnerText.Trim)
                    m.MenuIcon = xmlNodeMenu.SelectSingleNode("MenuIcon", xmlDoc.GetXmlManager).InnerText.Trim

                    Try
                        m.ParentName = xmlNodeMenu.SelectSingleNode("ParentMenuName", xmlDoc.GetXmlManager).InnerText.Trim
                    Catch ex As Exception
                        m.ParentName = String.Empty
                    End Try

                    For Each xmlNodeTitleMenus As System.Xml.XmlNode In xmlNodeMenu.SelectNodes("MenuItems")
                        For Each xmlNodeMenuItem As System.Xml.XmlNode In xmlNodeTitleMenus.SelectNodes("MenuItem")
                            Dim m_i As New MenuItem
                            Dim menuItemName As New menuItemsName(xmlNodeMenuItem.SelectSingleNode("ItemName", xmlDoc.GetXmlManager).InnerText.Trim)
                            Dim menuItemsLink As New menuItemsLink(xmlNodeMenuItem.SelectSingleNode("ItemLink", xmlDoc.GetXmlManager).InnerText.Trim)

                            menuItemName.ParentMenu = m
                            menuItemName.Value = Me.GetEntryByKeyName(xmlNodeMenuItem.SelectSingleNode("ItemName", xmlDoc.GetXmlManager).InnerText.Trim).GetValue()

                            m_i.MenuItemName = menuItemName
                            m_i.MenuItemLink = menuItemsLink

                            Try
                                m_i.Visible = CBool(xmlNodeMenuItem.SelectSingleNode("Visible", xmlDoc.GetXmlManager).InnerText.Trim)
                            Catch ex As Exception
                                m_i.Visible = False
                            End Try

                            If m.menuItems Is Nothing Then
                                m.menuItems = New Generic.List(Of MenuItem)
                            End If
                            m.menuItems.Add(m_i)
                        Next
                    Next

                    For Each xmlNodeTitleMenus As System.Xml.XmlNode In xmlNodeMenu.SelectNodes("TitleMenus")
                        For Each xmlNode As System.Xml.XmlNode In xmlNodeTitleMenus.ChildNodes
                            Dim TitleMenu As New TitleMenu(xmlNode.InnerText.Trim)
                            TitleMenu.ParentMenu = m
                            TitleMenu.Value = Me.GetEntryByKeyName(xmlNode.InnerText.Trim).GetValue

                            If m.TitleMenus Is Nothing Then
                                m.TitleMenus = New Generic.List(Of TitleMenu)
                            End If
                            m.TitleMenus.Add(TitleMenu)
                        Next
                    Next

                    For Each xmlNodeNLinkTitleMenus As System.Xml.XmlNode In xmlNodeMenu.SelectNodes("NLinkTitleMenus")
                        For Each xmlNode As System.Xml.XmlNode In xmlNodeNLinkTitleMenus.ChildNodes
                            Dim NLinkTitleMenu As New NLinkTitleMenu(xmlNode.InnerText.Trim)
                            If m.NLinkTitleMenus Is Nothing Then
                                m.NLinkTitleMenus = New Generic.List(Of NLinkTitleMenu)
                            End If
                            m.NLinkTitleMenus.Add(NLinkTitleMenu)
                        Next
                    Next
                Catch ex As Exception

                End Try

                m_Menu.Add(m)
            Next

        Next

    End Sub

    ''' <summary>
    ''' Updates the translations for all the loaded menus.
    ''' </summary>
    Private Sub UpdateMenuTranslations()
        If m_Menu IsNot Nothing Then
            For Each menu As MenuAtmill In m_Menu
                menu.Title = Me.GetEntryByKeyName(menu.Name).GetValue

                If menu.menuItems IsNot Nothing Then
                    For Each menuItem As MenuItem In menu.menuItems
                        menuItem.MenuItemName.Value = Me.GetEntryByKeyName(menuItem.MenuItemName.GetName).GetValue()
                    Next
                End If

                If menu.TitleMenus IsNot Nothing Then
                    For Each titleMenu As TitleMenu In menu.TitleMenus
                        titleMenu.Value = Me.GetEntryByKeyName(titleMenu.Key).GetValue()
                    Next
                End If
            Next
        End If
    End Sub

    ''' <summary>
    ''' Se si specifica il Nome Dello Screen la funzione carica lo screen corrispondente e ne restituisce il valore, nel caso in cui
    ''' si voglia carcare lì'intero set di screens presenti nel config mettere il nome come string.empty
    ''' </summary>
    ''' <param name="sScreenName"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Sub LoadScreens()
        If m_Screen Is Nothing Then
            m_Screen = New Generic.List(Of Screen)
        End If
        Dim sc As Screen = Nothing

        m_Screen.Clear()
        For Each xmlNodeScreens As System.Xml.XmlNode In xmlDoc.SelectNodes("//screens")
            For Each xmlNodeMenu As System.Xml.XmlNode In xmlNodeScreens.ChildNodes
                sc = New Screen
                Try
                    sc.Parent = Me
                    sc.Name = xmlNodeMenu.SelectSingleNode("ScreenName", xmlDoc.GetXmlManager).InnerText.Trim

                    sc.DBName = xmlNodeMenu.SelectSingleNode("DBName", xmlDoc.GetXmlManager).InnerText.Trim

                    If (xmlNodeMenu.SelectSingleNode("LinesPerPage", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.LinesPerPage = Integer.Parse(xmlNodeMenu.SelectSingleNode("LinesPerPage", xmlDoc.GetXmlManager).InnerText.Trim)

                        If sc.LinesPerPage <= 0 Then
                            sc.LinesPerPage = costanti.m_DefaultLinesPerPage
                        End If
                    Else
                        sc.LinesPerPage = costanti.m_DefaultLinesPerPage
                    End If

                    If (xmlNodeMenu.SelectSingleNode("Where", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.Where = xmlNodeMenu.SelectSingleNode("Where", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        sc.Where = String.Empty
                    End If

                    If (xmlNodeMenu.SelectSingleNode("OrderBy", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.OrderBy = xmlNodeMenu.SelectSingleNode("OrderBy", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        sc.OrderBy = String.Empty
                    End If

                    If (xmlNodeMenu.SelectSingleNode("SelectRowChkBoxColumnName", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.SelectRowChkBoxColumnName = xmlNodeMenu.SelectSingleNode("SelectRowChkBoxColumnName", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        sc.SelectRowChkBoxColumnName = String.Empty
                    End If

                    If (xmlNodeMenu.SelectSingleNode("EnumPageNameCode", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.EnumPageNameCode = CType(xmlNodeMenu.SelectSingleNode("EnumPageNameCode", xmlDoc.GetXmlManager).InnerText.Trim, EnumPageName)
                    Else
                        sc.EnumPageNameCode = EnumPageName.InvalidPageName
                    End If

                    sc.HasAddButton = CBool(xmlNodeMenu.SelectSingleNode("HasAddButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    sc.HasDelButton = CBool(xmlNodeMenu.SelectSingleNode("HasDelButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    sc.HasEditButton = CBool(xmlNodeMenu.SelectSingleNode("HasEditButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    sc.HasReportButton = CBool(xmlNodeMenu.SelectSingleNode("HasReportButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    sc.HasSimplePrintButton = CBool(xmlNodeMenu.SelectSingleNode("HasSimplePrintButton", xmlDoc.GetXmlManager).InnerText.Trim)

                    If (xmlNodeMenu.SelectSingleNode("ReportNameSimplePrintButton", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.ReportNameSimplePrintButton = xmlNodeMenu.SelectSingleNode("ReportNameSimplePrintButton", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        sc.ReportNameSimplePrintButton = String.Empty
                    End If

                    If (xmlNodeMenu.SelectSingleNode("TypeReportSimplePrintButton", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.TypeReportSimplePrintButton = xmlNodeMenu.SelectSingleNode("TypeReportSimplePrintButton", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        sc.TypeReportSimplePrintButton = String.Empty
                    End If

                    If (xmlNodeMenu.SelectSingleNode("HasSearchButton", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.HasSearchButton = CBool(xmlNodeMenu.SelectSingleNode("HasSearchButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        sc.HasSearchButton = False
                    End If

                    If (xmlNodeMenu.SelectSingleNode("HasCancelButton", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.HasCancelButton = CBool(xmlNodeMenu.SelectSingleNode("HasCancelButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        sc.HasCancelButton = True
                    End If

                    If (xmlNodeMenu.SelectSingleNode("HasAllDeleteButton", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.HasAllDeleteButton = CBool(xmlNodeMenu.SelectSingleNode("HasAllDeleteButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        sc.HasAllDeleteButton = False
                    End If

                    If (xmlNodeMenu.SelectSingleNode("HasImportFileButton", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        sc.HasImportFileButton = CBool(xmlNodeMenu.SelectSingleNode("HasImportFileButton", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        sc.HasImportFileButton = False
                    End If

                    sc = GetEditFields(xmlNodeMenu, sc)

                    sc = GetViewFields(xmlNodeMenu, sc)

                    sc = GetExtraColumnsForScreen(xmlNodeMenu, sc)

                    sc = GetComparesForScreen(xmlNodeMenu, sc)

                    sc = GetAddMenuItemNamesForScreen(xmlNodeMenu, sc)

                    sc = GetAddButtonParamsFromScreen(xmlNodeMenu, sc)

                    sc = GetFilterColumnsNameForScreen(xmlNodeMenu, sc)

                    sc = GetSumColmunsForScreen(xmlNodeMenu, sc)

                    sc = GetHeaderForScreen(xmlNodeMenu, sc)
                Catch ex As Exception

                End Try

                m_Screen.Add(sc)

            Next

        Next

    End Sub

    ''' <summary>
    ''' Recupero i campi innestati nel tag EditFields
    ''' </summary>
    ''' <param name="m_xmlNode"></param>
    ''' <param name="sc"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetEditFields(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeField As System.Xml.XmlNode In m_xmlNode.SelectNodes("EditFields")
            For Each xmlNode As System.Xml.XmlNode In xmlNodeField.ChildNodes

                Dim in_f As Field = GetFieldObj(xmlNode, sc)

                If sc.EditFields Is Nothing Then
                    sc.EditFields = New Generic.List(Of Field)
                End If
                sc.EditFields.Add(in_f)
            Next
        Next
        Return sc
    End Function

    ''' <summary>
    ''' Recupero i campi innestati nel tag ViewFields
    ''' </summary>
    ''' <param name="m_xmlNode"></param>
    ''' <param name="sc"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetViewFields(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeField As System.Xml.XmlNode In m_xmlNode.SelectNodes("ViewFields")
            For Each xmlNode As System.Xml.XmlNode In xmlNodeField.ChildNodes

                Dim in_f As Field = GetFieldObj(xmlNode, sc)

                If sc.ViewFields Is Nothing Then
                    sc.ViewFields = New Generic.List(Of Field)
                End If
                sc.ViewFields.Add(in_f)
            Next
        Next
        Return sc
    End Function

    ''' <summary>
    ''' Recupero i dati del campo
    ''' </summary>
    ''' <param name="xmlNode"></param>
    ''' <param name="sc"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetFieldObj(ByVal xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Field
        Dim in_f As New Field(xmlNode.InnerText.Trim)
        in_f.ParentMenu = sc
        Try
            in_f.FieldType = xmlNode.SelectSingleNode("FieldType", xmlDoc.GetXmlManager).InnerText.Trim

            Select Case in_f.GetFieldType()
                Case EnumFieldType.FieldNumber
                    in_f.ObjectNumber = New NumberAtmill

                    '<NumType>Double</NumType>
                    '<NumBound>Equal</NumBound>    <NumBound>Max</NumBound>    <NumBound>Min</NumBound>    <NumBound>MinMax</NumBound>
                    '<Decimal>2</Decimal>

                    in_f.ObjectNumber.NumType = xmlNode.SelectSingleNode("NumType", xmlDoc.GetXmlManager).InnerText.Trim
                    in_f.ObjectNumber.NumBound = xmlNode.SelectSingleNode("NumBound", xmlDoc.GetXmlManager).InnerText.Trim

                    If (xmlNode.SelectSingleNode("Decimal", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.nDecimal = Integer.Parse(xmlNode.SelectSingleNode("Decimal", xmlDoc.GetXmlManager).InnerText.Trim)

                        If in_f.nDecimal < 0 Then
                            in_f.nDecimal = costanti.m_DefaultDecimal
                        End If
                    Else
                        in_f.nDecimal = costanti.m_DefaultDecimal
                    End If

                Case EnumFieldType.FieldList
                    in_f.ObjectList = New myList

                    '<SelectColumns>PT_TYPE</SelectColumns>
                    '<ValueColumn>ID</ValueColumn>
                    '<From>PRODUCT_TYPES</From>
                    '<Where>ID <> 999</Where>
                    '<AddNull>true</AddNull>
                    '<QueryFieldName>PT_ID</QueryFieldName>
                    '<AddNull>true</AddNull>

                    If (xmlNode.SelectSingleNode("AddNull", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectList.AddNull = CBool(xmlNode.SelectSingleNode("AddNull", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        in_f.ObjectList.AddNull = False
                    End If

                    If (xmlNode.SelectSingleNode("From", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectList.From = xmlNode.SelectSingleNode("From", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectList.From = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("SelectColumns", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectList.SelectColumns = xmlNode.SelectSingleNode("SelectColumns", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectList.SelectColumns = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("ValueColumn", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectList.ValueColumn = xmlNode.SelectSingleNode("ValueColumn", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectList.ValueColumn = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("Where", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectList.Where = xmlNode.SelectSingleNode("Where", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectList.Where = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("OrderBy", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectList.OrderBy = xmlNode.SelectSingleNode("OrderBy", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectList.OrderBy = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("QueryFieldName", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.QueryFieldName = xmlNode.SelectSingleNode("QueryFieldName", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.QueryFieldName = String.Empty
                    End If

                    Try
                        in_f.ObjectList.Parent = in_f
                    Catch ex As Exception
                        in_f.ObjectList.Parent = Nothing
                    End Try
                Case EnumFieldType.FieldLabel
                    in_f.FieldType = "label"
                Case EnumFieldType.FieldRadioButton
                    in_f.FieldType = "radiobutton"

                    If (xmlNode.SelectSingleNode("RadioButtonAspFilter", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.RadioButtonAspFilter = xmlNode.SelectSingleNode("RadioButtonAspFilter", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.RadioButtonAspFilter = String.Empty
                    End If
                Case EnumFieldType.FieldCalendar
                    in_f.ObjectCalendar = New myCalendar

                    '<DateFormat>0</DateFormat>

                    'DateTime = 0
                    'DateOnly = 1
                    'TimeOnly = 2

                    If (xmlNode.SelectSingleNode("DateFormat", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.DateFormat = tools.String2Enum(in_f.DateFormat.GetType, xmlNode.SelectSingleNode("DateFormat", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        in_f.DateFormat = EnumDateFormats.DateTime
                    End If

                Case EnumFieldType.FieldPassword
                    in_f.FieldType = "password"
                Case EnumFieldType.FieldWString
                    in_f.FieldType = "wstring"
                Case EnumFieldType.FieldLink
                    in_f.FieldType = "link"
                Case EnumFieldType.FieldLed
                    in_f.FieldType = "led"
                    If (xmlNode.SelectSingleNode("ImageLed", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ImageLed = xmlNode.SelectSingleNode("ImageLed", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ImageLed = String.Empty
                    End If
                    If (xmlNode.SelectSingleNode("ShowImageIfLedStringIs", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ShowImageIfLedStringIs = xmlNode.SelectSingleNode("ShowImageIfLedStringIs", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ShowImageIfLedStringIs = String.Empty
                    End If
                Case EnumFieldType.FieldAuto
                    in_f.FieldType = "auto"
                Case EnumFieldType.FieldCheckBoxYesNo
                    in_f.FieldType = "checkboxyesno"
                Case EnumFieldType.FieldCheckBoxOnOff
                    in_f.FieldType = "checkboxonoff"
                Case EnumFieldType.FieldCheckBoxOneZero
                    in_f.FieldType = "checkboxonezero"
                Case EnumFieldType.FieldChkList
                    in_f.ObjectChkList = New ChkmyList
                    in_f.ObjectChkList.IsQueryField = True

                    If (xmlNode.SelectSingleNode("SelectedItemColumn", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectChkList.SelectedItemColumn = xmlNode.SelectSingleNode("SelectedItemColumn", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectChkList.SelectedItemColumn = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("From", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectChkList.From = xmlNode.SelectSingleNode("From", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectChkList.From = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("SelectColumns", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectChkList.SelectColumns = xmlNode.SelectSingleNode("SelectColumns", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectChkList.SelectColumns = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("ValueColumn", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectChkList.ValueColumn = xmlNode.SelectSingleNode("ValueColumn", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectChkList.ValueColumn = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("Where", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectChkList.Where = xmlNode.SelectSingleNode("Where", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectChkList.Where = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("OrderBy", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.ObjectChkList.OrderBy = xmlNode.SelectSingleNode("OrderBy", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.ObjectChkList.OrderBy = String.Empty
                    End If

                    Try
                        in_f.ObjectChkList.Parent = in_f
                    Catch ex As Exception
                        in_f.ObjectChkList.Parent = Nothing
                    End Try
                Case EnumFieldType.FieldLedString
                    in_f.FieldType = "ledstring"
                    If (xmlNode.SelectSingleNode("FieldWithLed", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        in_f.FieldWithLed = xmlNode.SelectSingleNode("FieldWithLed", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        in_f.FieldWithLed = String.Empty
                    End If
                Case Else
                    in_f.FieldType = "string"
            End Select
        Catch
            in_f.FieldType = "string"
        End Try

        If (xmlNode.SelectSingleNode("FixedRuntimeValue", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.FixedRuntimeValue = xmlNode.SelectSingleNode("FixedRuntimeValue", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.FixedRuntimeValue = String.Empty
        End If

        If (xmlNode.SelectSingleNode("UnitASP", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.UnitAsp = xmlNode.SelectSingleNode("UnitASP", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.UnitAsp = String.Empty
        End If

        If (xmlNode.SelectSingleNode("UnitDB", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.UnitDb = xmlNode.SelectSingleNode("UnitDB", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.UnitDb = String.Empty
        End If

        If (xmlNode.SelectSingleNode("DynamicUnitAsp", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.DynamicUnitAsp = xmlNode.SelectSingleNode("DynamicUnitAsp", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.DynamicUnitAsp = String.Empty
        End If

        If (xmlNode.SelectSingleNode("FieldDB", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.FieldDb = xmlNode.SelectSingleNode("FieldDB", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.FieldDb = String.Empty
        End If

        If (xmlNode.SelectSingleNode("PropertyName", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.PropertyName = xmlNode.SelectSingleNode("PropertyName", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.PropertyName = String.Empty
        End If

        If (xmlNode.SelectSingleNode("ValidationGroup", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.ValidationGroup = xmlNode.SelectSingleNode("ValidationGroup", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.ValidationGroup = String.Empty
        End If

        If (xmlNode.SelectSingleNode("FieldName", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.FieldName = xmlNode.SelectSingleNode("FieldName", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.FieldName = String.Empty
        End If

        If (xmlNode.SelectSingleNode("HAlign", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.HAlign = tools.GetHorizontalAlign(xmlNode.SelectSingleNode("HAlign", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.HAlign = System.Web.UI.WebControls.HorizontalAlign.Center
        End If

        If (xmlNode.SelectSingleNode("IsHidden", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.IsHidden = CBool(xmlNode.SelectSingleNode("IsHidden", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.IsHidden = False
        End If

        If (xmlNode.SelectSingleNode("EnableKeyEntry", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.EnableKeyEntry = CBool(xmlNode.SelectSingleNode("EnableKeyEntry", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.EnableKeyEntry = False
        End If

        If (xmlNode.SelectSingleNode("IsReadOnly", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.IsReadOnly = CBool(xmlNode.SelectSingleNode("IsReadOnly", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.IsReadOnly = False
        End If

        If (xmlNode.SelectSingleNode("updateField", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.UpdateField = CBool(xmlNode.SelectSingleNode("updateField", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.UpdateField = False
        End If

        If (xmlNode.SelectSingleNode("insertField", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.InsertField = CBool(xmlNode.SelectSingleNode("insertField", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.InsertField = False
        End If

        If (xmlNode.SelectSingleNode("visibleField", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.VisibleField = CBool(xmlNode.SelectSingleNode("visibleField", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.VisibleField = False
        End If

        If (xmlNode.SelectSingleNode("queryField", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.QueryField = CBool(xmlNode.SelectSingleNode("queryField", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.QueryField = False
        End If

        If (xmlNode.SelectSingleNode("GetDataFrom", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.GetDataFrom = tools.String2Enum(in_f.GetDataFrom.GetType, xmlNode.SelectSingleNode("GetDataFrom", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.GetDataFrom = EnumGetDataFrom.Standard
        End If

        If (xmlNode.SelectSingleNode("TextBefore", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.TextBefore = xmlNode.SelectSingleNode("TextBefore", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.TextBefore = String.Empty
        End If

        If (xmlNode.SelectSingleNode("TextAfter", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.TextAfter = xmlNode.SelectSingleNode("TextAfter", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.TextAfter = String.Empty
        End If

        If (xmlNode.SelectSingleNode("CallEvent", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.CallEvent = xmlNode.SelectSingleNode("CallEvent", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.CallEvent = String.Empty
        End If

        If (xmlNode.SelectSingleNode("HideIfEmpty", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.HideIfEmpty = CBool(xmlNode.SelectSingleNode("HideIfEmpty", xmlDoc.GetXmlManager).InnerText.Trim)
        Else
            in_f.HideIfEmpty = False
        End If

        If (xmlNode.SelectSingleNode("TimeSpanFormatDB", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.TimeSpanFormatDb = xmlNode.SelectSingleNode("TimeSpanFormatDB", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.TimeSpanFormatDb = String.Empty
        End If

        If (xmlNode.SelectSingleNode("TimeSpanFormatASP", xmlDoc.GetXmlManager) IsNot Nothing) Then
            in_f.TimeSpanFormatAsp = xmlNode.SelectSingleNode("TimeSpanFormatASP", xmlDoc.GetXmlManager).InnerText.Trim
        Else
            in_f.TimeSpanFormatAsp = String.Empty
        End If

        Return in_f
    End Function

    ''' <summary>
    ''' Get extraColumns from screen
    ''' </summary>
    ''' <param name="m_xmlNode">XmlNomeMenu</param>
    ''' <param name="sc">Screen</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetExtraColumnsForScreen(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeExtraColumn As System.Xml.XmlNode In m_xmlNode.SelectNodes("extraColumns")
            For Each xmlNode As System.Xml.XmlNode In xmlNodeExtraColumn.ChildNodes
                Dim exCol As New extraColumn()
                exCol.ParentMenu = sc
                exCol.Name = xmlNode.SelectSingleNode("name", xmlDoc.GetXmlManager).InnerText.Trim
                exCol.ImageSrc = xmlNode.SelectSingleNode("imageSrc", xmlDoc.GetXmlManager).InnerText.Trim
                exCol.ScriptName = xmlNode.SelectSingleNode("scriptName", xmlDoc.GetXmlManager).InnerText.Trim
                exCol.ImageToolTip = xmlNode.SelectSingleNode("imageToolTip", xmlDoc.GetXmlManager).InnerText.Trim
                exCol.RequiredAccessLevel = tools.String2Enum(exCol.RequiredAccessLevel.GetType, xmlNode.SelectSingleNode("RequiredAccessLevel", xmlDoc.GetXmlManager).InnerText.Trim)

                If (xmlNode.SelectSingleNode("FieldDBValueOnly", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    exCol.FieldDBValueOnly = xmlNode.SelectSingleNode("FieldDBValueOnly", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    exCol.FieldDBValueOnly = String.Empty
                End If

                If (xmlNode.SelectSingleNode("FieldDBViewOnly", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    exCol.FieldDBViewOnly = xmlNode.SelectSingleNode("FieldDBViewOnly", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    exCol.FieldDBViewOnly = String.Empty
                End If

                If (xmlNode.SelectSingleNode("ExtraQueryString", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    exCol.ExtraQueryString = xmlNode.SelectSingleNode("ExtraQueryString", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    exCol.ExtraQueryString = String.Empty
                End If

                If (xmlNode.SelectSingleNode("Visible", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    exCol.Visible = CBool(xmlNode.SelectSingleNode("Visible", xmlDoc.GetXmlManager).InnerText.Trim)
                Else
                    exCol.Visible = False
                End If

                For Each xmlNodeParam As System.Xml.XmlNode In xmlNode.SelectNodes("parameters")
                    For Each xmlNodeSingleParam As System.Xml.XmlNode In xmlNodeParam.ChildNodes
                        Dim exColParam As New extraColumnParam()
                        exColParam.FieldDb = xmlNodeSingleParam.InnerText.Trim
                        If exCol.Parameters Is Nothing Then
                            exCol.Parameters = New Generic.List(Of extraColumnParam)
                        End If
                        exCol.Parameters.Add(exColParam)
                    Next
                Next
                If sc.extraColumns Is Nothing Then
                    sc.extraColumns = New Generic.List(Of extraColumn)
                End If
                sc.extraColumns.Add(exCol)
            Next
        Next
        Return sc
    End Function

    ''' <summary>
    ''' Get GetAddButtonParams from screen
    ''' </summary>
    ''' <param name="m_xmlNode">XmlNomeMenu</param>
    ''' <param name="sc">Screen</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetAddButtonParamsFromScreen(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeAddButtonParams As System.Xml.XmlNode In m_xmlNode.SelectNodes("AddButtonParams")
            Dim adbp As New AddButtonParam()
            adbp.ParentMenu = sc
            If (xmlNodeAddButtonParams.SelectSingleNode("Parameter", xmlDoc.GetXmlManager) IsNot Nothing) Then
                adbp.Parameter = xmlNodeAddButtonParams.SelectSingleNode("Parameter", xmlDoc.GetXmlManager).InnerText.Trim
            Else
                Exit For
            End If

            If sc.AddButtonParams Is Nothing Then
                sc.AddButtonParams = New Generic.List(Of AddButtonParam)
            End If
            sc.AddButtonParams.Add(adbp)
        Next
        Return sc
    End Function

    ''' <summary>
    ''' Get AddMenuItemNames from screen
    ''' </summary>
    ''' <param name="m_xmlNode">XmlNomeMenu</param>
    ''' <param name="sc">Screen</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetAddMenuItemNamesForScreen(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeExtraColumn As System.Xml.XmlNode In m_xmlNode.SelectNodes("AddMenuItemNames")
            For Each xmlNode As System.Xml.XmlNode In xmlNodeExtraColumn.ChildNodes
                Dim adin As New AddMenuItemName()
                adin.ParentMenu = sc
                If (xmlNode.SelectSingleNode("NameLink", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    adin.NameLink = xmlNode.SelectSingleNode("NameLink", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    adin.NameLink = String.Empty
                End If

                If (xmlNode.SelectSingleNode("NameMenu", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    adin.NameMenu = xmlNode.SelectSingleNode("NameMenu", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    adin.NameMenu = String.Empty
                End If

                If (xmlNode.SelectSingleNode("MenuBound", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    adin.MenuBound = tools.String2Enum(adin.MenuBound.GetType, xmlNode.SelectSingleNode("MenuBound", xmlDoc.GetXmlManager).InnerText.Trim)
                Else
                    adin.MenuBound = EnumAddMenuBound.View
                End If

                If (xmlNode.SelectSingleNode("Name", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    adin.Name = xmlNode.SelectSingleNode("Name", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    adin.Name = String.Empty
                End If

                If (xmlNode.SelectSingleNode("Path", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    adin.Path = xmlNode.SelectSingleNode("Path", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    adin.Path = String.Empty
                End If

                For Each xmlNodeParam As System.Xml.XmlNode In xmlNode.SelectNodes("Parameters")
                    For Each xmlNodeSingleParam As System.Xml.XmlNode In xmlNodeParam.ChildNodes
                        Dim mParam As New AddMenuItemNameParameter()
                        mParam.FieldDB = xmlNodeSingleParam.InnerText.Trim
                        If adin.ListParameters Is Nothing Then
                            adin.ListParameters = New Generic.List(Of AddMenuItemNameParameter)
                        End If
                        adin.ListParameters.Add(mParam)
                    Next
                Next

                If sc.AddMenuItemNames Is Nothing Then
                    sc.AddMenuItemNames = New Generic.List(Of AddMenuItemName)
                End If
                sc.AddMenuItemNames.Add(adin)
            Next
        Next
        Return sc
    End Function

    ''' <summary>
    ''' Get Compare rules from screen
    ''' </summary>
    ''' <param name="m_xmlNode"></param>
    ''' <param name="sc"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetComparesForScreen(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeCompares As System.Xml.XmlNode In m_xmlNode.SelectNodes("Compares")
            For Each xmlNode As System.Xml.XmlNode In xmlNodeCompares.ChildNodes
                Dim cw As New CompareWeb()
                cw.ParentMenu = sc
                Try

                    If (xmlNode.SelectSingleNode("ModeType", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        cw.ModeType = tools.String2Enum(cw.ModeType.GetType, xmlNode.SelectSingleNode("ModeType", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        cw.ModeType = EnumCompareType.Both
                    End If

                    cw.CompareType = xmlNode.SelectSingleNode("CompareType", xmlDoc.GetXmlManager).InnerText.Trim
                    cw.FieldName = xmlNode.SelectSingleNode("FieldName", xmlDoc.GetXmlManager).InnerText.Trim
                    If (xmlNode.SelectSingleNode("ControlToCompare", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        cw.ControlToCompare = xmlNode.SelectSingleNode("ControlToCompare", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        cw.ControlToCompare = String.Empty
                    End If

                    cw.ControlToValidate = xmlNode.SelectSingleNode("ControlToValidate", xmlDoc.GetXmlManager).InnerText.Trim
                    cw.ValidationGroup = xmlNode.SelectSingleNode("ValidationGroup", xmlDoc.GetXmlManager).InnerText.Trim
                    cw.ErrorMessage = xmlNode.SelectSingleNode("ErrorMessage", xmlDoc.GetXmlManager).InnerText.Trim
                    Select Case xmlNode.SelectSingleNode("DisplayType", xmlDoc.GetXmlManager).InnerText.Trim.ToUpper
                        Case "DYNAMIC"
                            cw.DisplayType = System.Web.UI.WebControls.ValidatorDisplay.Dynamic
                        Case "NONE"
                            cw.DisplayType = System.Web.UI.WebControls.ValidatorDisplay.None
                        Case "STATIC"
                            cw.DisplayType = System.Web.UI.WebControls.ValidatorDisplay.Static
                        Case Else
                            cw.DisplayType = System.Web.UI.WebControls.ValidatorDisplay.Dynamic
                    End Select

                    cw.Id = xmlNode.SelectSingleNode("Id", xmlDoc.GetXmlManager).InnerText.Trim
                    If (xmlNode.SelectSingleNode("EnableClientScript", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        cw.EnableClientScript = CBool(xmlNode.SelectSingleNode("EnableClientScript", xmlDoc.GetXmlManager).InnerText.Trim)
                    Else
                        cw.EnableClientScript = False
                    End If

                    If (xmlNode.SelectSingleNode("MinimumValue", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        cw.MinimumValue = xmlNode.SelectSingleNode("MinimumValue", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        cw.MinimumValue = String.Empty
                    End If

                    If (xmlNode.SelectSingleNode("MaximumValue", xmlDoc.GetXmlManager) IsNot Nothing) Then
                        cw.MaximumValue = xmlNode.SelectSingleNode("MaximumValue", xmlDoc.GetXmlManager).InnerText.Trim
                    Else
                        cw.MaximumValue = String.Empty
                    End If

                    Select Case xmlNode.SelectSingleNode("ValidationDataType", xmlDoc.GetXmlManager).InnerText.Trim.ToUpper
                        Case "CURRENCY"
                            cw.ValidationDataType_object = System.Web.UI.WebControls.ValidationDataType.Currency
                        Case "DATE"
                            cw.ValidationDataType_object = System.Web.UI.WebControls.ValidationDataType.Date
                        Case "DOUBLE"
                            cw.ValidationDataType_object = System.Web.UI.WebControls.ValidationDataType.Double
                        Case "INTEGER"
                            cw.ValidationDataType_object = System.Web.UI.WebControls.ValidationDataType.Integer
                        Case "STRING"
                            cw.ValidationDataType_object = System.Web.UI.WebControls.ValidationDataType.String
                        Case Else
                            cw.ValidationDataType_object = System.Web.UI.WebControls.ValidationDataType.String
                    End Select
                Catch ex As Exception

                End Try
                If sc.ComparesWeb Is Nothing Then
                    sc.ComparesWeb = New Generic.List(Of CompareWeb)
                End If
                sc.ComparesWeb.Add(cw)
            Next
        Next

        Return sc
    End Function

    ''' <summary>
    ''' Get FilterColumnsName from screen
    ''' </summary>
    ''' <param name="m_xmlNode"></param>
    ''' <param name="sc"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetFilterColumnsNameForScreen(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeFilterColumnsName As System.Xml.XmlNode In m_xmlNode.SelectNodes("FilterColumnsName")

            Dim m As New FilterColumnName
            If (xmlNodeFilterColumnsName.SelectSingleNode("FieldDB", xmlDoc.GetXmlManager) IsNot Nothing) Then
                m.FieldDB = xmlNodeFilterColumnsName.SelectSingleNode("FieldDB", xmlDoc.GetXmlManager).InnerText.Trim
                m.ParentMenu = sc
            End If

            If sc.FilterColumnsName Is Nothing Then
                sc.FilterColumnsName = New Generic.List(Of FilterColumnName)
            End If
            sc.FilterColumnsName.Add(m)

        Next

        Return sc
    End Function

    ''' <summary>
    ''' Get SumComuns from screen
    ''' </summary>
    ''' <param name="m_xmlNode"></param>
    ''' <param name="sc"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetSumColmunsForScreen(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeSumColmuns As System.Xml.XmlNode In m_xmlNode.SelectNodes("SumColumns")
            For Each xmlNode As System.Xml.XmlNode In xmlNodeSumColmuns.ChildNodes
                Dim m As New SumColumn
                If (xmlNode.SelectSingleNode("Field", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.Field = xmlNode.SelectSingleNode("Field", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    m.Field = String.Empty
                End If

                If (xmlNode.SelectSingleNode("FieldName", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.FieldName = xmlNode.SelectSingleNode("FieldName", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    m.FieldName = String.Empty
                End If

                If (xmlNode.SelectSingleNode("UnitDB", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.UnitDB = xmlNode.SelectSingleNode("UnitDB", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    m.UnitDB = String.Empty
                End If

                If (xmlNode.SelectSingleNode("UnitASP", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.UnitASP = xmlNode.SelectSingleNode("UnitASP", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    m.UnitASP = String.Empty
                End If

                If (xmlNode.SelectSingleNode("Decimal", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.nDecimal = Integer.Parse(xmlNode.SelectSingleNode("Decimal", xmlDoc.GetXmlManager).InnerText.Trim)

                    If m.nDecimal < 0 Then
                        m.nDecimal = costanti.m_DefaultDecimal
                    End If
                Else
                    m.nDecimal = costanti.m_DefaultDecimal
                End If

                If (xmlNode.SelectSingleNode("DynamicUnitAsp", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.DynamicUnitAsp = xmlNode.SelectSingleNode("DynamicUnitAsp", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    m.DynamicUnitAsp = String.Empty
                End If

                If (xmlNode.SelectSingleNode("TimeSpanFormatDB", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.TimeSpanFormatDB = xmlNode.SelectSingleNode("TimeSpanFormatDB", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    m.TimeSpanFormatDB = String.Empty
                End If

                If (xmlNode.SelectSingleNode("TimeSpanFormatASP", xmlDoc.GetXmlManager) IsNot Nothing) Then
                    m.TimeSpanFormatASP = xmlNode.SelectSingleNode("TimeSpanFormatASP", xmlDoc.GetXmlManager).InnerText.Trim
                Else
                    m.TimeSpanFormatASP = String.Empty
                End If

                Try
                    m.ParentMenu = sc
                Catch
                    m.ParentMenu = Nothing
                End Try

                If sc.SumColumns Is Nothing Then
                    sc.SumColumns = New Generic.List(Of SumColumn)
                End If
                sc.SumColumns.Add(m)
            Next
        Next

        Return sc
    End Function

    ''' <summary>
    ''' Get Header from screen
    ''' </summary>
    ''' <param name="m_xmlNode"></param>
    ''' <param name="sc"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function GetHeaderForScreen(ByVal m_xmlNode As System.Xml.XmlNode, ByVal sc As Screen) As Screen
        For Each xmlNodeHeader As System.Xml.XmlNode In m_xmlNode.SelectNodes("Header")
            For Each xmlNode As System.Xml.XmlNode In xmlNodeHeader.ChildNodes
                Dim m As New Header
                Try
                    m.MasterDBName = xmlNodeHeader.SelectSingleNode("MasterDBName", xmlDoc.GetXmlManager).InnerText.Trim
                    m.ParentMenu = sc

                    For Each xmlNodeField As System.Xml.XmlNode In xmlNodeHeader.SelectNodes("FilterColumns")
                        For Each xmlNodeSingleFilter As System.Xml.XmlNode In xmlNodeField.ChildNodes
                            Dim in_hf As New HeaderFilter

                            in_hf.ColumnName = xmlNodeSingleFilter.SelectSingleNode("ColumnName", xmlDoc.GetXmlManager).InnerText.Trim
                            in_hf.FilterValueParamName = xmlNodeSingleFilter.SelectSingleNode("FilterValueParamName", xmlDoc.GetXmlManager).InnerText.Trim

                            If m.FilterColumns Is Nothing Then
                                m.FilterColumns = New List(Of HeaderFilter)
                            End If
                            m.FilterColumns.Add(in_hf)
                        Next
                    Next

                    For Each xmlNodeField As System.Xml.XmlNode In xmlNodeHeader.SelectNodes("EditFields")
                        For Each xmlNodeSingleField As System.Xml.XmlNode In xmlNodeField.ChildNodes
                            Dim in_f As Field = GetFieldObj(xmlNodeSingleField, sc)
                            If m.EditFieldList Is Nothing Then
                                m.EditFieldList = New Generic.List(Of Field)
                            End If
                            m.EditFieldList.Add(in_f)
                        Next
                    Next
                Catch ex As Exception
                    Return sc
                End Try

                sc.Header = m
            Next
        Next
        Return sc
    End Function

End Class