﻿Option Strict On

Imports System.Data.OleDb

Public Class OperationParamValue
    Inherits myDbBaseClass

    Private m_id_operation As Long = m_InvalidId
    Private m_id_meta_operation_param As Long = m_InvalidId
    Private m_parameter_value As String = String.Empty

    Public Sub New()
        MyBase.New("OPERATION_PARAM_VALUES")
    End Sub

    Public Property IdOperation() As Long
        Get
            Return m_id_operation
        End Get
        Set(ByVal value As Long)
            Me.m_id_operation = value
        End Set
    End Property

    Public Property ParameterValue() As String
        Get
            Return m_parameter_value
        End Get
        Set(ByVal value As String)
            m_parameter_value = value.Trim
        End Set
    End Property

    Public Property IdMetaOperationParam() As Long
        Get
            Return m_id_meta_operation_param
        End Get
        Set(ByVal value As Long)
            Me.m_id_meta_operation_param = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE OPERATION_ID = '" & Me.IdOperation & "' AND MOP_ID = '" & Me.IdMetaOperationParam & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("OPERATION_ID={0} AND MOP_ID={1}", Me.IdOperation, Me.IdMetaOperationParam))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio operation param"
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio operation param sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("OPERATION_ID") = Me.IdOperation

            If Me.ParameterValue <> String.Empty Then
                drNodo("PARAMETER_VALUE") = Me.TrimStringToMaxLen(Me.ParameterValue, "PARAMETER_VALUE")
            Else
                drNodo("PARAMETER_VALUE") = DBNull.Value
            End If

            drNodo("MOP_ID") = Me.IdMetaOperationParam

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento operation param: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class