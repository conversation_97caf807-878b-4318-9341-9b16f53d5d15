Option Strict On

Imports System.Web.UI.WebControls

Public Class Lot
    Private mId As Integer
    Private mDescrizione As String
    Private mFiglio As Integer
    Private mImageUrl As String
    Private mQuanity As Double
    Private mCreatedDate As DateTime
    Private mUnitAsp As String
    Private mProductId As Integer
    Private mNode As TreeNode

    Public Sub New()

    End Sub

    Public Property Id() As Integer
        Get
            Return mId
        End Get
        Set(ByVal value As Integer)
            Me.mId = value
        End Set
    End Property

    Public Property Descrizione() As String
        Get
            Return Me.mDescrizione
        End Get
        Set(ByVal value As String)
            Me.mDescrizione = value
        End Set
    End Property

    Public Property IsFiglio() As Integer
        Get
            Return Me.mFiglio
        End Get
        Set(ByVal value As Integer)
            Me.mFiglio = value
        End Set
    End Property

    Public Property ProductId() As Integer
        Get
            Return Me.mProductId
        End Get
        Set(ByVal value As Integer)
            Me.mProductId = value
        End Set
    End Property

    Public Property ImageUrl() As String
        Get
            Return mImageUrl
        End Get
        Set(ByVal value As String)
            Me.mImageUrl = value
        End Set
    End Property

    Public Property CreationDate() As DateTime
        Get
            Return Me.mCreatedDate
        End Get
        Set(ByVal value As DateTime)
            Me.mCreatedDate = value
        End Set
    End Property

    Public Property Quantity() As Double
        Get
            Return Me.mQuanity
        End Get
        Set(ByVal value As Double)
            Me.mQuanity = value
        End Set
    End Property

    Public Property UnitAsp() As String
        Get
            Return Me.mUnitAsp
        End Get
        Set(ByVal value As String)
            Me.mUnitAsp = value
        End Set
    End Property

    Public Property Node() As TreeNode
        Get
            Return Me.mNode
        End Get
        Set(ByVal value As TreeNode)
            Me.mNode = value
        End Set
    End Property

End Class

Public Class CompLot
    Private mId As Integer
    Private mQuanity As Double

    Public Sub New()

    End Sub

    Public Property Id() As Integer
        Get
            Return mId
        End Get
        Set(ByVal value As Integer)
            Me.mId = value
        End Set
    End Property

    Public Property Quantity() As Double
        Get
            Return Me.mQuanity
        End Get
        Set(ByVal value As Double)
            Me.mQuanity = value
        End Set
    End Property

End Class

Public Class TreeNodeLot
    Inherits TreeNode

    Private mLot As Lot

    Public Property Lot() As Lot
        Get
            Return Me.mLot
        End Get
        Set(ByVal value As Lot)
            Me.mLot = value
        End Set
    End Property

    Public Sub New()
        Me.Collapse()
    End Sub

End Class

Public Class TreeNodeLotComparer
    Implements IComparer

    ' Compare the length of the strings, or the strings
    ' themselves, if they are the same length.
    Public Function Compare(ByVal x As Object, ByVal y As Object) _
        As Integer Implements IComparer.Compare
        Dim tx As TreeNodeLot = CType(x, TreeNodeLot)
        Dim ty As TreeNodeLot = CType(y, TreeNodeLot)

        If tx.Text.Length <> ty.Text.Length Then
            Return tx.Text.Length - ty.Text.Length
        End If
        Return String.Compare(tx.Text, ty.Text)

    End Function
End Class