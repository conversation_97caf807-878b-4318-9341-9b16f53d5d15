﻿Imports UsersGUI

Public Class myErrorMessage

    Public Shared Sub AppendCheckBinProductMsg(ByRef str_error As String, ByVal config As config, ByVal pro_id As Long, ByVal bin_prod_list As List(Of myCompositeObjects.BinProd), ByVal b_is_source_bin As Boolean,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        If b_is_source_bin Then
            str_error &= config.GetEntryByKeyName("CHECK_SOURCE_BIN_PRODUCT_1").GetValue() & " " & tools.GetProductNameFromId(pro_id)

            For Each bin_prod As myCompositeObjects.BinProd In bin_prod_list

                str_error &= config.GetEntryByKeyName("CHECK_SOURCE_BIN_PRODUCT_2").GetValue() & " " & tools.GetBinNameFromId(bin_prod.Bin) & " " &
                                    config.GetEntryByKeyName("CHECK_SOURCE_BIN_PRODUCT_3").GetValue() & " " & tools.GetProductNameFromId(bin_prod.Pro)
            Next
        Else
            str_error &= config.GetEntryByKeyName("CHECK_DEST_BIN_PRODUCT_1").GetValue() & " " & tools.GetProductNameFromId(pro_id)

            For Each bin_prod As myCompositeObjects.BinProd In bin_prod_list

                str_error &= config.GetEntryByKeyName("CHECK_DEST_BIN_PRODUCT_2").GetValue() & " " & tools.GetBinNameFromId(bin_prod.Bin) & " " &
                                    config.GetEntryByKeyName("CHECK_DEST_BIN_PRODUCT_3").GetValue() & " " & tools.GetProductNameFromId(bin_prod.Pro)
            Next
        End If

    End Sub

    Public Shared Sub AppendCustomCheckBinProductMsg(ByRef str_error As String, ByVal config As config, ByVal pro_id As Long, ByVal bin_prod_list As List(Of myCompositeObjects.BinProd), ByVal source_bin As Long,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("PRODUCT_IN_SOURCE_BIN").GetValue() & " " & tools.GetBinNameFromId(source_bin) & " is " & tools.GetProductNameFromId(pro_id)

        For Each bin_prod As myCompositeObjects.BinProd In bin_prod_list

            str_error &= config.GetEntryByKeyName("CHECK_DEST_BIN_PRODUCT_2").GetValue() & " " & tools.GetBinNameFromId(bin_prod.Bin) & " " &
                                    config.GetEntryByKeyName("CHECK_DEST_BIN_PRODUCT_3").GetValue() & " " & tools.GetProductNameFromId(bin_prod.Pro)
        Next

    End Sub

    Public Shared Sub AppendCheckBinLotsMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long, ByVal lot_list As List(Of Integer),
                                            Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)
        Dim ret_val As String = String.Empty
        Dim b_first As Boolean = True

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("CHECK_BIN_LOTS_1").GetValue() & " " & tools.GetBinNameFromId(bin_id) & " " &
                           config.GetEntryByKeyName("CHECK_BIN_LOTS_2").GetValue() & " ("

        For Each lot_id As String In lot_list
            If Not b_first Then
                str_error &= ", "
            End If

            str_error &= tools.GetLotNameFromId(lot_id)

            b_first = False
        Next

        str_error &= ")"
    End Sub

    Public Shared Sub AppendCheckBinRestTimeMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False, Optional b_show_date_expires As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("CHECK_BIN_REST_TIME_1").GetValue() & " " & tools.GetBinNameFromId(bin_id) & " " &
                    config.GetEntryByKeyName("CHECK_BIN_REST_TIME_2").GetValue()

        If (b_show_date_expires) Then
            Dim sSelectExpiredTime = "SELECT * FROM JOB_TO_CEL WHERE CEL_ID='" + bin_id.ToString() + "' ORDER BY READY_DATE DESC"
            Dim dt_expiredtime As System.Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelectExpiredTime, False)
            Dim sStringMssagePost = ""
            If (dt_expiredtime IsNot Nothing AndAlso dt_expiredtime.Rows.Count() > 0) Then
                sStringMssagePost = config.GetEntryByKeyName("EXPIRES_AT").GetValue() + ": " + dt_expiredtime.Rows(0)("READY_DATE").ToString()
            End If
            str_error &= " - " & sStringMssagePost
        End If

    End Sub

    Public Shared Sub AppendRequiredFieldMsg(ByRef str_error As String, ByVal config As config, ByVal field_name As String,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " & config.GetEntryByKeyName("EMPTY_FIELD_FOUND").GetValue()

    End Sub

    Public Shared Sub AppendTotalSourcePercMsg(ByRef str_error As String, ByVal config As config, ByVal source_perc As Double,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("TOTAL_PERCENTAGE_NOT_OK").GetValue() & " " & source_perc.ToString() & "%"

    End Sub

    Public Shared Sub AppendSingleSourcePercMsg(ByRef str_error As String, ByVal config As config, ByVal source_perc As Double,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("PERCENTAGE_NOT_OK").GetValue() & " " & source_perc.ToString() & "%"

    End Sub

    Public Shared Sub AppendDestQueueEmptyMsg(ByRef str_error As String, ByVal config As config,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("DESTINATION_NOT_OK").GetValue()

    End Sub

    Public Shared Sub AppendAllSourcesPercEmptyMsg(ByRef str_error As String, ByVal config As config,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("SOURCE_NOT_OK").GetValue()

    End Sub

    Public Shared Sub AppendDuplicateSourceInQueueMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= tools.GetBinNameFromId(bin_id) & " " & config.GetEntryByKeyName("SOURCE_PRESENT").GetValue()

    End Sub

    Public Shared Sub AppendDuplicateDestInQueueMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= tools.GetBinNameFromId(bin_id) & " " & config.GetEntryByKeyName("DESTINATION_PRESENT").GetValue()

    End Sub

    Public Shared Sub AppendLoadNotEnabledForRestingTimeMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("CHECK_BIN_LOAD_ENABLE_1").GetValue() & " " & tools.GetBinNameFromId(bin_id) & " " &
                    config.GetEntryByKeyName("CHECK_BIN_LOAD_ENABLE_2").GetValue() & " " & config.GetEntryByKeyName("CHECK_BIN_LOAD_ENABLE_3").GetValue() & " " & tools.GetRestingtimeFromBinId(config, bin_id)

    End Sub

    Public Shared Sub AppendLoadNotEnabledMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("CHECK_BIN_LOAD_ENABLE_1").GetValue() & " " & tools.GetBinNameFromId(bin_id) & " " &
                    config.GetEntryByKeyName("CHECK_BIN_LOAD_ENABLE_2").GetValue()

    End Sub

    Public Shared Sub AppendDownloadNotEnabledMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("CHECK_BIN_DOWNLOAD_ENABLE_1").GetValue() & " " & tools.GetBinNameFromId(bin_id) & " " &
                    config.GetEntryByKeyName("CHECK_BIN_DOWNLOAD_ENABLE_2").GetValue()

    End Sub

    Public Shared Sub AppendValueEqualOrLessThanZeroMsg(ByRef str_error As String, ByVal config As config, ByVal field_name As String,
                                                        Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " & config.GetEntryByKeyName("VALUE_EQUAL_OR_LESS_THAN_ZERO").GetValue()

    End Sub

    Public Shared Sub AppendValueLessThanZeroMsg(ByRef str_error As String, ByVal config As config, ByVal field_name As String,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " & config.GetEntryByKeyName("VALUE_LESS_THAN_ZERO").GetValue()

    End Sub

    Public Shared Sub AppendValueEqualOrMoreThanXxxMsg(ByRef str_error As String, ByVal config As config, ByVal field_name As String, ByVal limit_value As String,
                                                        Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " & config.GetEntryByKeyName("VALUE_EQUAL_OR_MORE_THAN_XXX").GetValue() & " " & limit_value

    End Sub

    Public Shared Sub AppendValueMoreThanXxxMsg(ByRef str_error As String, ByVal config As config, ByVal field_name As String, ByVal limit_value As String,
                                                        Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " & config.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() & " " & limit_value

    End Sub

    Public Shared Sub AppendPercentageOutLimitsMsg(ByRef str_error As String, ByVal config As config, ByVal field_name As String,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " & config.GetEntryByKeyName("PERCENTAGE_MORE_100_OR_LESS_0").GetValue()

    End Sub

    Public Shared Sub AppendMinValueGraterEqualThanMaxValue(ByRef str_error As String, ByVal config As config, ByVal field_min_name As String, ByVal field_max_name As String,
                                                            Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("MIN_VAL_GREAT_EQ_THAN_MAX_1").GetValue() & " " & field_min_name & " " &
                        config.GetEntryByKeyName("MIN_VAL_GREAT_EQ_THAN_MAX_2").GetValue() & " " & field_max_name & " " &
                        config.GetEntryByKeyName("MIN_VAL_GREAT_EQ_THAN_MAX_3").GetValue()

    End Sub

    Public Shared Sub AppendTextIsTooLong(ByRef str_error As String, ByVal config As config, ByVal field_name As String, ByVal max_len As Integer,
                                            Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)
        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " &
                        config.GetEntryByKeyName("ERR_TEXT_IS_TOO_LONG_1").GetValue() & " " & max_len & " " &
                        config.GetEntryByKeyName("ERR_TEXT_IS_TOO_LONG_2").GetValue()
    End Sub

    Public Shared Sub AppendValueAlreadyPresent(ByRef str_error As String, ByVal config As config, ByVal field_name As String, ByVal field_val As String,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)
        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & " " & field_val & " " &
                        config.GetEntryByKeyName("ERR_VALUE_ALREADY_PRESENT").GetValue()
    End Sub

    Public Shared Sub AppendDuplicateProductMsg(ByRef str_error As String, ByVal config As config, ByVal pro_id As Long,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("DUPLICATE_PRODUCT").GetValue() & ": " & tools.GetProductNameFromId(pro_id)

    End Sub

    Public Shared Sub AppendValueOutOfBounds(ByRef str_error As String, ByVal config As config, ByVal field_name As String, ByVal field_val As String, ByVal lower_bound_value As String, ByVal upper_boud_value As String,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " &
                        config.GetEntryByKeyName("VALUE_OUT_OF_BOUNDS_1").GetValue() & " " & field_val & " " &
                        config.GetEntryByKeyName("VALUE_OUT_OF_BOUNDS_2").GetValue() & " " & lower_bound_value & " " &
                        config.GetEntryByKeyName("VALUE_OUT_OF_BOUNDS_3").GetValue() & " " & upper_boud_value

    End Sub

    Public Shared Sub AppendValueUnderLowerBound(ByRef str_error As String, ByVal config As config, ByVal field_name As String, ByVal field_val As String, ByVal lower_bound_value As String,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " &
                        config.GetEntryByKeyName("VALUE_UNDER_LOWER_BOUND_1").GetValue() & " " & field_val & " " &
                        config.GetEntryByKeyName("VALUE_UNDER_LOWER_BOUND_2").GetValue() & " " & lower_bound_value

    End Sub

    Public Shared Sub AppendValueOverUpperBound(ByRef str_error As String, ByVal config As config, ByVal field_name As String, ByVal field_val As String, ByVal upper_bound_value As String,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName(field_name).GetValue() & ": " &
                        config.GetEntryByKeyName("VALUE_OVER_UPPER_BOUND_1").GetValue() & " " & field_val & " " &
                        config.GetEntryByKeyName("VALUE_OVER_UPPER_BOUND_2").GetValue() & " " & upper_bound_value

    End Sub

    Public Shared Sub AppendIngredientFoundInBinMsg(ByRef str_error As String, ByVal config As config, ByVal pro_id As Long, ByVal bin_id As Long,
                                                    Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= tools.GetProductNameFromId(pro_id) & " " & config.GetEntryByKeyName("INGREDIENT_FOUND_IN_BIN").GetValue() & " " &
            tools.GetBinNameFromId(bin_id)

    End Sub

    Public Shared Sub AppendIngredientNotFoundInBinMsg(ByRef str_error As String, ByVal config As config, ByVal pro_id As Long,
                                                        Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= tools.GetProductNameFromId(pro_id) & " " & config.GetEntryByKeyName("INGREDIENT_NOT_FOUND_IN_BIN").GetValue()

    End Sub

    Public Shared Sub AppendCheckBinProteinMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long, ByVal protein_value As Double,
                                               Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)
        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("Bin").GetValue() & " " & tools.GetBinNameFromId(bin_id) & ": " &
                    config.GetEntryByKeyName("CHECK_BIN_PROTEIN").GetValue() & " " & Math.Round(protein_value, m_DecimalsForProtein) & "%"

    End Sub

    Public Shared Sub AppendDestBinProteinMsg(ByRef str_error As String, ByVal config As config, ByVal bin_id As Long, ByVal protein_value As Double,
                                               Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)
        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("Bin").GetValue() & " " & tools.GetBinNameFromId(bin_id) & ": " &
                    config.GetEntryByKeyName("DEST_BIN_PROTEIN_ALREADY_SET").GetValue() & " " & Math.Round(protein_value, m_DecimalsForProtein) & "%"

    End Sub

    Public Shared Sub AppendImpossibleToReachTargetProteinMsg(ByRef str_error As String, ByVal config As config,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("IMPOSSIBLE_TO_REACH_TARGET_PROTEIN").GetValue()

    End Sub

    Public Shared Sub AppendImpossibleToProduceQtyMsg(ByRef str_error As String, ByVal config As config,
                                                Optional msg_prefix As String = "", Optional b_is_languages_entry As Boolean = False)

        AddNewLine(str_error)

        If msg_prefix <> String.Empty Then
            If b_is_languages_entry Then
                str_error &= config.GetEntryByKeyName(msg_prefix).GetValue()
            Else
                str_error &= msg_prefix
            End If

            str_error &= " - "
        End If

        str_error &= config.GetEntryByKeyName("IMPOSSIBLE_TO_PRODUCE_SELECTED_QTY").GetValue()

    End Sub

    Public Shared Sub AppendCustomErrorMsg(ByRef str_error As String, ByVal custom_error As String)

        AddNewLine(str_error)

        str_error &= custom_error

    End Sub

    ' aggiungo alla stringa il carattere '\n' solo se necessario
    Private Shared Sub AddNewLine(ByRef str_error As String)
        If Not str_error.EndsWith("\n") AndAlso str_error.Length() > 0 Then
            str_error &= "\n"
        End If
    End Sub

End Class