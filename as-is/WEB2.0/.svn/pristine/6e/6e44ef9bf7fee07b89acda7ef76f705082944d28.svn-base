﻿Option Strict On

Imports System.Web.HttpContext
Imports System.Web.UI.WebControls
Imports System.Globalization
Imports UsersGUI
Imports UsersGUI.costanti
Imports WebTools.tools
Imports WebDataBaseLayer

Public Class drawings

    ''' <summary>
    ''' Disegna i campi per i valori di Header
    ''' </summary>
    ''' <param name="tblHeader"></param>
    ''' <param name="scr"></param>
    ''' <param name="m_config"></param>
    ''' <param name="m_ReadOnly"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function DrawHeaderFields(ByVal tblHeader As System.Web.UI.HtmlControls.HtmlTable,
                                            ByVal scr As UsersGUI.Screen,
                                            ByVal m_config As UsersGUI.config,
                                            ByVal m_ReadOnly As Boolean) As System.Web.UI.HtmlControls.HtmlTable
        Dim m_Value As String = String.Empty
        Dim b_Suddividi As Boolean = False

        'Qui disegno la grafica della pagina nel caso fossimo nella pagina dei cicli se
        'il numero di campi supera il valore impostato disegno la pagina suddivisa in due
        'colonne.

        If scr.Name.Contains("CYCLE_") Then
            b_Suddividi = True
        End If

        Dim nCount_Column As Integer = 0
        Dim nCount_Row As Integer = 0
        For Each h As Header In scr.Headers
            tblHeader = HeaderFields(h, tblHeader, scr, m_config, True, nCount_Column, nCount_Row, b_Suddividi)
        Next
        Return tblHeader
    End Function

    ''' <summary>
    ''' Disegna i campi per i valori di UpDateFields
    ''' </summary>
    ''' <param name="tblEdit"></param>
    ''' <param name="scr"></param>
    ''' <param name="m_config"></param>
    ''' <param name="dt"></param>
    ''' <param name="m_ReadOnly"></param>
    ''' <param name="m_ref_id">reference id. Sarà PPL_ID nel caso di production plan/order param values, PRT_ID nel caso di produciton report/order param values archive</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function DrawUpdateFields(ByVal tblEdit As System.Web.UI.HtmlControls.HtmlTable,
                                            ByVal scr As UsersGUI.Screen,
                                            ByVal m_config As UsersGUI.config,
                                            ByVal dt As Data.DataTable,
                                            ByVal m_ReadOnly As Boolean,
                                            ByVal m_ref_id As Long) As System.Web.UI.HtmlControls.HtmlTable
        Dim m_Value As String = String.Empty
        Dim b_Suddividi As Boolean = False

        'Qui disegno la grafica della pagina nel caso fossimo nella pagina dei cicli se
        'il numero di campi supera il valore impostato disegno la pagina suddivisa in due
        'colonne.

        If scr.Name.StartsWith("CYCLE_") OrElse scr.Name.StartsWith("RECIPE_PARAMS_") OrElse
            scr.Name.StartsWith("ANALYSIS_PARAMS_") OrElse scr.Name.StartsWith("OPERATION_PARAMS_") OrElse
            scr.Name.StartsWith("CONTRACT_PARAMS_") Then
            b_Suddividi = True
        End If

        Dim nCount_Column As Integer = 0
        Dim nCount_Row As Integer = 0
        Dim has_text_before As Boolean = False

        ' disegno prima tutti i field visibili, e poi quelli hidden
        ' in questo modo evito colori male assortiti nel risultato finale
        For Each uf As Field In scr.EditFields
            If uf.UpdateField AndAlso Not uf.IsHidden Then
                tblEdit = DrawFields(uf, tblEdit, scr, m_config, dt, m_ReadOnly, m_ref_id, nCount_Column, nCount_Row, b_Suddividi, False, has_text_before)
            End If
        Next

        For Each uf As Field In scr.EditFields
            If uf.UpdateField AndAlso uf.IsHidden Then
                tblEdit = DrawFields(uf, tblEdit, scr, m_config, dt, m_ReadOnly, m_ref_id, nCount_Column, nCount_Row, b_Suddividi, False, has_text_before)
            End If
        Next

        Return tblEdit
    End Function

    ''' <summary>
    ''' Disegna i campi per i valori di InsertFields
    ''' </summary>
    ''' <param name="tblNew"></param>
    ''' <param name="scr"></param>
    ''' <param name="m_config"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function DrawInsertFields(ByVal tblNew As System.Web.UI.HtmlControls.HtmlTable, ByVal scr As UsersGUI.Screen, ByVal m_config As UsersGUI.config) As System.Web.UI.HtmlControls.HtmlTable
        Dim m_Value As String = String.Empty
        Dim b_Suddividi As Boolean = False

        'Qui disegno la grafica della pagina nel caso fossimo nella pagina dei cicli se
        'il numero di campi supera il valore impostato disegno la pagina suddivisa in due
        'colonne.

        If scr.Name.Contains("CYCLE_") Then
            b_Suddividi = True
        End If

        Dim nCount_Column As Integer = 0
        Dim nCount_Row As Integer = 0
        Dim has_text_before As Boolean = False

        ' disegno prima tutti i field visibili, e poi quelli hidden
        ' in questo modo evito colori male assortiti nel risultato finale
        For Each in_f As Field In scr.EditFields
            If in_f.InsertField AndAlso Not in_f.IsHidden Then
                tblNew = DrawFields(in_f, tblNew, scr, m_config, Nothing, False, costanti.m_InvalidId, nCount_Column, nCount_Row, b_Suddividi, False, has_text_before)
            End If
        Next

        For Each in_f As Field In scr.EditFields
            If in_f.InsertField AndAlso in_f.IsHidden Then
                tblNew = DrawFields(in_f, tblNew, scr, m_config, Nothing, False, costanti.m_InvalidId, nCount_Column, nCount_Row, b_Suddividi, False, has_text_before)
            End If
        Next
        Return tblNew
    End Function

    ''' <summary>
    ''' Disegna la tabella per la ricerca
    ''' </summary>
    ''' <param name="tblQuery"></param>
    ''' <param name="scr"></param>
    ''' <param name="m_config"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function DrawQueryFields(ByVal tblQuery As System.Web.UI.HtmlControls.HtmlTable, ByVal scr As UsersGUI.Screen, ByVal m_config As UsersGUI.config) As System.Web.UI.HtmlControls.HtmlTable
        Dim m_Value As String = String.Empty
        Dim b_Suddividi As Boolean = False

        'Qui disegno la grafica della pagina nel caso fossimo nella pagina dei cicli se
        'il numero di campi supera il valore impostato disegno la pagina suddivisa in due
        'colonne.

        If scr.Name.Contains("CYCLE_") Then
            b_Suddividi = True
        End If

        Dim nCount_Column As Integer = 0
        Dim nCount_Row As Integer = 0
        Dim has_text_before As Boolean = False
        If scr.ViewFields IsNot Nothing Then

            ' disegno prima tutti i field visibili, e poi quelli hidden
            ' in questo modo evito colori male assortiti nel risultato finale
            For Each in_f As Field In scr.ViewFields
                If in_f.QueryField AndAlso Not in_f.IsHidden Then
                    tblQuery = DrawFields(in_f, tblQuery, scr, m_config, Nothing, False, costanti.m_InvalidId, nCount_Column, nCount_Row, b_Suddividi, True, has_text_before)
                End If
            Next

            For Each in_f As Field In scr.ViewFields
                If in_f.QueryField AndAlso in_f.IsHidden Then
                    tblQuery = DrawFields(in_f, tblQuery, scr, m_config, Nothing, False, costanti.m_InvalidId, nCount_Column, nCount_Row, b_Suddividi, True, has_text_before)
                End If
            Next
        End If

        Return tblQuery
    End Function

    ''' <summary>
    ''' Disegna i campi
    ''' </summary>
    ''' <param name="uf"></param>
    ''' <param name="tblEdit"></param>
    ''' <param name="scr"></param>
    ''' <param name="m_config"></param>
    ''' <param name="dt"></param>
    ''' <param name="m_ReadOnly"></param>
    ''' <param name="m_ref_id">reference id. Sarà PPL_ID nel caso di production plan/order param values, PRT_ID nel caso di produciton report/order param values archive</param>
    ''' <param name="nCount_Column"></param>
    ''' <param name="nCount_Row"></param>
    ''' <param name="b_Suddividi"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Shared Function DrawFields(ByVal uf As Field,
                                ByVal tblEdit As System.Web.UI.HtmlControls.HtmlTable,
                                ByVal scr As UsersGUI.Screen,
                                ByVal m_config As UsersGUI.config,
                                ByVal dt As Data.DataTable,
                                ByVal m_ReadOnly As Boolean,
                                ByVal m_ref_id As Long,
                                ByRef nCount_Column As Integer,
                                ByRef nCount_Row As Integer,
                                ByVal b_Suddividi As Boolean,
                                ByVal b_CallQueryField As Boolean,
                                ByRef b_text_before As Boolean) As System.Web.UI.HtmlControls.HtmlTable

        Dim m_Value As String = String.Empty
        Dim b_old_text_before As Boolean = False
        Dim row_class As String = String.Empty

        Dim m_language As UsersGUI.Language

        b_old_text_before = SetTextBefore(m_config, uf, nCount_Column, tblEdit)

        If nCount_Column Mod 2 = 0 Then
            If Not b_text_before Then
                ' incremento
                nCount_Column = 0
                nCount_Row += 1

                ' il field precedente non ha un text before, quindi creo una nuova riga
                Dim row As New System.Web.UI.HtmlControls.HtmlTableRow

                If nCount_Row Mod 2 = 0 OrElse uf.IsHidden Then
                    nCount_Row = 0
                End If

                If uf.IsHidden Then
                    row.Attributes("class") &= " elementHide"
                End If

                row_class = row.Attributes("class")

                tblEdit.Rows.Add(row)

            End If
        Else
            ' ho un text before, ne tengo traccia per riuscire a mettere
            ' il prossimo field sulla stessa riga
            b_text_before = b_old_text_before
        End If

        If b_Suddividi Then
            nCount_Column += 1
        End If

        Dim tc As System.Web.UI.HtmlControls.HtmlTableCell

        Select Case uf.GetFieldType
            Case EnumFieldType.FieldList
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each dr As Data.DataRow In dt.Rows
                        tc = New System.Web.UI.HtmlControls.HtmlTableCell

                        m_Value = GetValueFromField(uf, dr, m_ref_id).ToString

                        tc.Controls.Add(GetAtmillList(m_config, uf, m_Value, m_ReadOnly, dr))

                        If uf.QueryField Then
                            tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                        End If

                        SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)

                        'Non sono una checklistbox quindi, prendo il primo valore
                        Exit For
                    Next
                Else
                    tc = New System.Web.UI.HtmlControls.HtmlTableCell
                    tc.Controls.Add(GetAtmillList(m_config, uf, Nothing, m_ReadOnly, Nothing))

                    If uf.QueryField Then
                        tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                    End If

                    SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
                End If
            Case EnumFieldType.FieldCheckBoxYesNo
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

                    m_Value = GetValueFromField(uf, dt.Rows(0), m_ref_id).ToString

                    tc.Controls.Add(GetCheckBoxYesNo(m_config, uf, m_Value, m_ReadOnly))
                Else
                    tc.Controls.Add(GetCheckBoxYesNo(m_config, uf, Nothing, m_ReadOnly))
                End If

                If uf.QueryField Then
                    tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                End If

                SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
            Case EnumFieldType.FieldCheckBoxOnOff
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

                    m_Value = GetValueFromField(uf, dt.Rows(0), m_ref_id).ToString

                    tc.Controls.Add(GetCheckBoxOnOff(m_config, uf, m_Value, m_ReadOnly))
                Else
                    tc.Controls.Add(GetCheckBoxOnOff(m_config, uf, Nothing, m_ReadOnly))
                End If

                If uf.QueryField Then
                    tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                End If

                SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
            Case EnumFieldType.FieldCheckBoxOneZero
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

                    m_Value = GetValueFromField(uf, dt.Rows(0), m_ref_id).ToString

                    tc.Controls.Add(GetCheckBoxZeroUno(m_config, uf, m_Value, m_ReadOnly))
                Else
                    tc.Controls.Add(GetCheckBoxZeroUno(m_config, uf, Nothing, m_ReadOnly))
                End If

                If uf.QueryField Then
                    tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                End If

                SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
            Case EnumFieldType.FieldChkList
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb & costanti.m_FieldDataDA, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                If dt IsNot Nothing Then
                    tc = GetAtmillChkList(tc, uf, dt, m_ReadOnly)
                Else
                    tc = GetAtmillChkList(tc, uf, Nothing, m_ReadOnly)
                End If

                If uf.QueryField Then
                    tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                End If

                SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
            Case EnumFieldType.FieldCalendar
                ' from
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    Try
                        m_language = m_config.GetLanguage()

                        m_Value = UsersGUI.tools.ConvertDateTimeFromDB(GetValueFromField(uf, dt.Rows(0), m_ref_id).ToString, m_language.FormatDateTimeCalendar)
                    Catch ex As Exception
                        m_Value = GetValueFromField(uf, dt.Rows(0), m_ref_id).ToString
                    End Try

                    tc = GetAtmillCalendar(tc, costanti.m_FieldDataDA, uf, m_Value, m_ReadOnly)
                Else
                    tc = GetAtmillCalendar(tc, costanti.m_FieldDataDA, uf, Nothing, m_ReadOnly)
                End If
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                If uf.QueryField Then
                    tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                End If

                If Not uf.QueryField Then
                    SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
                Else
                    SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit, costanti.m_FieldDataDA)
                End If

                ' to
                If uf.QueryField Then
                    Dim row2 As New System.Web.UI.HtmlControls.HtmlTableRow
                    row2.Attributes("class") = row_class
                    tblEdit.Rows.Add(row2)

                    Dim tc2 As System.Web.UI.HtmlControls.HtmlTableCell
                    tc2 = New System.Web.UI.HtmlControls.HtmlTableCell
                    tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc2)

                    tc2 = New System.Web.UI.HtmlControls.HtmlTableCell

                    tc2 = GetAtmillCalendar(tc2, costanti.m_FieldDataA, uf, Nothing, m_ReadOnly)

                    tc2.Attributes.Add("ReferenceColumn", uf.FieldDb)

                    SetTextAfter(m_config, uf, nCount_Column, tc2, tblEdit, costanti.m_FieldDataA)
                End If

            Case EnumFieldType.FieldLabel
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf, True))
                tc.ColSpan = 4
                nCount_Column = 2
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)
            Case EnumFieldType.FieldLink

                'Gestiamo le text-box
                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each dr As Data.DataRow In dt.Rows
                        m_Value = GetValueFromField(uf, dr, m_ref_id).ToString

                        tc.Controls.Add(GetTextBox(m_config, uf, m_Value, m_ReadOnly))

                        If uf.UnitAsp IsNot Nothing AndAlso uf.UnitAsp <> String.Empty AndAlso Not uf.IsHidden Then
                            tc.Controls.Add(GetLabel(m_config, uf.FieldDb & "_" & uf.UnitAsp, uf.UnitAsp))
                        End If

                        If uf.QueryField Then
                            tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                        End If

                        SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
                        'Non sono una checklistbox quindi, prendo il primo valore
                        Exit For
                    Next
                Else
                    tc.Controls.Add(GetTextBox(m_config, uf, Nothing, m_ReadOnly))
                    If uf.UnitAsp IsNot Nothing AndAlso uf.UnitAsp <> String.Empty AndAlso Not uf.IsHidden Then
                        tc.Controls.Add(GetLabel(m_config, uf.FieldDb & "_" & uf.UnitAsp, uf.UnitAsp))
                    End If

                    If uf.QueryField Then
                        tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                    End If

                    SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
                End If
                Dim btnUpdate As New Button
                btnUpdate.ID = "ID_btnUpload"
                btnUpdate.CssClass = "btnGeneral"
                btnUpdate.Text = m_config.GetEntryByKeyName("UploadFile").GetValue
                btnUpdate.OnClientClick = "window.open('UploadFiles.aspx','UploadPopUp', 'width=400,height=200,status=yes'); return false;"
                'btnUpdate.Attributes.Add("OnClientClick", "window.open('UploadFiles.aspx','UploadPopUp', 'width=640,height=480,status=yes'); return false;")
                tc.Controls.Add(btnUpdate)

                'tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf, True))
                'tc.ColSpan = 4
                'nCount_Column = 2
                'tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)
            Case EnumFieldType.FieldRadioButton
                tc = New System.Web.UI.HtmlControls.HtmlTableCell

                tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                tc = New System.Web.UI.HtmlControls.HtmlTableCell

                Dim s_filter As String() = Split(uf.RadioButtonAspFilter, ":")
                If s_filter.Length > 1 Then
                    If s_filter(0) = "CONFIG" Then
                        Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                        Dim n As Integer = 0
                        Dim i As Integer = 0

                        For i = 1 To n_ripetizioni

                            m_Value = "False"
                            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                                m_Value = tools.GetValueFromField(uf, dt.Rows(0), m_ref_id).ToString

                                If m_Value = i.ToString Then
                                    m_Value = "1"
                                Else
                                    m_Value = "0"
                                End If
                            End If

                            Dim fil As Field

                            If uf.FixedRuntimeValue IsNot Nothing AndAlso uf.FixedRuntimeValue.ToUpper.Contains("QUERYSTRING") Then
                                Dim _start As Integer = uf.FixedRuntimeValue.IndexOf("""") + 1
                                Dim _stop As Integer = uf.FixedRuntimeValue.LastIndexOf("""") - _start
                                Dim sStringa As String = uf.FixedRuntimeValue.Substring(_start, _stop)

                                fil = UsersGUI.tools.GetNewObject(uf.FieldName & "_" & i, uf.FieldName, uf.FieldType,
                                                                 Current.Request.QueryString(sStringa), uf.UnitAsp, uf.UnitDb,
                                                                 uf.GetFieldType, uf.IsReadOnly, uf.CallEvent, uf.IsHidden, uf.InsertField,
                                                                 uf.IsSqlExcluded, uf.UpdateField, uf.RadioButtonAspFilter)
                            Else
                                fil = UsersGUI.tools.GetNewObject(uf.FieldName & "_" & i, uf.FieldName, uf.FieldType,
                                                                 uf.FixedRuntimeValue, uf.UnitAsp, uf.UnitDb,
                                                                 uf.GetFieldType, uf.IsReadOnly, uf.CallEvent, uf.IsHidden, uf.InsertField,
                                                                 uf.IsSqlExcluded, uf.UpdateField, uf.RadioButtonAspFilter)

                            End If

                            Dim div_rb As New System.Web.UI.HtmlControls.HtmlGenericControl("div")
                            div_rb.ID = String.Format("div_rb_{0}_{1}", uf.FieldName, n + 1)
                            div_rb.Attributes.Add("name", "div_rb")
                            div_rb.Controls.Add(WebTools.drawings.GetRadioButton(m_config, fil, uf.FieldName, m_config.GetEntryByKeyName(s_filter(1) & "_" & s_filter(2) & "_" & n + 1).GetValue, m_Value, m_ReadOnly))

                            tc.Controls.Add(div_rb)

                            n += 1
                        Next
                    ElseIf s_filter(0) = "SQL" Then

                    End If

                End If

                If uf.QueryField Then
                    tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                End If

                SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
            Case Else
                If b_CallQueryField Then
                    'Viene chiamata dalla funzione che crea la maschera per i dati di query.
                    'In questo caso si costruiscono i campi in modo differente
                    tc = New System.Web.UI.HtmlControls.HtmlTableCell
                    tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                    tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)
                    If uf.GetFieldType = EnumFieldType.FieldNumber Then
                        'If uf.ObjectNumber.GetNumBound = EnumNumBound.MinEMax Then
                        tc = New System.Web.UI.HtmlControls.HtmlTableCell
                        tc = uf.ObjectNumber.SetDataBoundToCell(tc, m_config, uf.FieldDb)

                        If uf.QueryField Then
                            tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                        End If

                        tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)
                        'End If
                    ElseIf uf.GetFieldType = EnumFieldType.FieldString Then
                        tc = New System.Web.UI.HtmlControls.HtmlTableCell
                        tc.Controls.Add(GetTextBox(m_config, uf, Nothing, m_ReadOnly))

                        If uf.QueryField Then
                            tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                        End If

                        tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)
                    End If
                Else

                    'Gestiamo le text-box
                    tc = New System.Web.UI.HtmlControls.HtmlTableCell
                    tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                    tblEdit.Rows(tblEdit.Rows.Count - 1).Cells.Add(tc)

                    tc = New System.Web.UI.HtmlControls.HtmlTableCell
                    If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                        For Each dr As Data.DataRow In dt.Rows
                            m_Value = GetValueFromField(uf, dr, m_ref_id).ToString

                            tc.Controls.Add(GetTextBox(m_config, uf, m_Value, m_ReadOnly))

                            If uf.UnitAsp IsNot Nothing AndAlso uf.UnitAsp <> String.Empty AndAlso Not uf.IsHidden Then
                                tc.Controls.Add(GetLabel(m_config, uf.FieldDb & "_" & uf.UnitAsp, uf.UnitAsp))
                            End If

                            If uf.QueryField Then
                                tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                            End If

                            SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
                            'Non sono una checklistbox quindi, prendo il primo valore
                            Exit For
                        Next
                    Else
                        tc.Controls.Add(GetTextBox(m_config, uf, Nothing, m_ReadOnly))
                        If uf.UnitAsp IsNot Nothing AndAlso uf.UnitAsp <> String.Empty AndAlso Not uf.IsHidden Then
                            tc.Controls.Add(GetLabel(m_config, uf.FieldDb & "_" & uf.UnitAsp, uf.UnitAsp))
                        End If

                        If uf.QueryField Then
                            tc.Attributes.Add("ReferenceColumn", uf.FieldDb)
                        End If

                        SetTextAfter(m_config, uf, nCount_Column, tc, tblEdit)
                    End If
                End If
        End Select
        Return tblEdit
    End Function

    Private Shared Function HeaderFields(ByVal h As Header,
                               ByVal tblHeader As System.Web.UI.HtmlControls.HtmlTable,
                               ByVal scr As UsersGUI.Screen,
                               ByVal m_config As UsersGUI.config,
                               ByVal m_ReadOnly As Boolean,
                               ByRef nCount_Column As Integer,
                               ByRef nCount_Row As Integer,
                               ByVal b_Suddividi As Boolean) As System.Web.UI.HtmlControls.HtmlTable

        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable
        Dim m_Value As String = String.Empty

        sSelect = "SELECT * FROM " & h.MasterScreenName & " WHERE 1=1"
        If h.MasterFilterColumn <> String.Empty Then
            sSelect &= " AND " & h.MasterFilterColumn & "='" & Current.Request.QueryString(h.MasterFilterRuntimeValue) & "'"
        End If

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each uf As Field In h.ViewFieldList

            If h.ViewFieldList.Count > 0 Then

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each dr As Data.DataRow In dt.Rows
                        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
                        tblHeader.Rows.Add(row)
                        Dim tc As System.Web.UI.HtmlControls.HtmlTableCell

                        tc = New System.Web.UI.HtmlControls.HtmlTableCell
                        tc.Controls.Add(GetLabel(m_config, "lbl_" & uf.FieldDb, uf))
                        tblHeader.Rows(tblHeader.Rows.Count - 1).Cells.Add(tc)

                        tc = New System.Web.UI.HtmlControls.HtmlTableCell
                        m_Value = dr.Item(uf.FieldDb).ToString

                        tc.Controls.Add(GetLabel(m_config, String.Empty, m_Value))
                        If uf.UnitAsp IsNot Nothing AndAlso uf.UnitAsp <> String.Empty AndAlso Not uf.IsHidden Then
                            tc.Controls.Add(GetLabel(m_config, uf.FieldDb & "_" & uf.UnitAsp, uf.UnitAsp))
                        End If

                        SetTextAfter(m_config, uf, nCount_Column, tc, tblHeader)
                        Exit For
                    Next
                End If
            End If
        Next

        Return tblHeader
    End Function

    ''' <summary>
    '''
    ''' </summary>
    ''' <param name="tbl"></param>
    ''' <param name="scr"></param>
    ''' <param name="m_config"></param>
    ''' <param name="m_Type">Insert or update or both</param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function DrawCompares(ByVal tbl As System.Web.UI.HtmlControls.HtmlTable, ByVal scr As UsersGUI.Screen, ByVal m_config As UsersGUI.config, ByVal m_type As EnumCompareType) As System.Web.UI.HtmlControls.HtmlTable
        If scr.ComparesWeb IsNot Nothing Then
            'Creo le regole di controllo nel caso vengano specificate
            For Each cw As CompareWeb In scr.ComparesWeb
                If cw.ModeType = m_type OrElse cw.ModeType = EnumCompareType.Both Then
                    tbl.Rows.Add(New System.Web.UI.HtmlControls.HtmlTableRow)
                    Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
                    Select Case cw.CompareType.ToUpper
                        Case "CompareValidator".ToUpper
                            Dim CompVal As New CompareValidator
                            CompVal.Display = cw.DisplayType
                            CompVal.ID = cw.Id
                            CompVal.EnableClientScript = cw.EnableClientScript
                            CompVal.ControlToCompare = cw.ControlToCompare
                            CompVal.ControlToValidate = cw.ControlToValidate
                            CompVal.ValidationGroup = cw.ValidationGroup
                            CompVal.ErrorMessage = m_config.GetEntryByKeyName(cw.FieldName).GetValue() & ": " & m_config.GetEntryByKeyName(cw.ErrorMessage).GetValue()
                            tc.Controls.Add(CompVal)
                        Case "RequiredFieldValidator".ToUpper
                            Dim CompVal As New RequiredFieldValidator
                            CompVal.Display = cw.DisplayType
                            CompVal.ID = cw.Id
                            CompVal.EnableClientScript = cw.EnableClientScript
                            CompVal.ControlToValidate = cw.ControlToValidate
                            CompVal.ValidationGroup = cw.ValidationGroup
                            CompVal.ErrorMessage = m_config.GetEntryByKeyName(cw.FieldName).GetValue() & ": " & m_config.GetEntryByKeyName(cw.ErrorMessage).GetValue()
                            tc.Controls.Add(CompVal)
                        Case "RangeValidator".ToUpper
                            Dim CompVal As New RangeValidator
                            CompVal.Display = cw.DisplayType
                            CompVal.ID = cw.Id
                            CompVal.EnableClientScript = cw.EnableClientScript
                            CompVal.ControlToValidate = cw.ControlToValidate
                            CompVal.ValidationGroup = cw.ValidationGroup
                            CompVal.MaximumValue = cw.MaximumValue
                            CompVal.MinimumValue = cw.MinimumValue
                            CompVal.Type = cw.ValidationDataType_object
                            CompVal.ErrorMessage = m_config.GetEntryByKeyName(cw.FieldName).GetValue() & ": " & m_config.GetEntryByKeyName(cw.ErrorMessage).GetValue()
                            tc.Controls.Add(CompVal)
                    End Select
                    tbl.Rows(tbl.Rows.Count - 1).Cells.Add(tc)
                End If
            Next
        End If
        Return tbl
    End Function

    Private Shared Function SetTextBefore(ByVal m_config As UsersGUI.config,
                                          ByVal f As Field,
                                          ByRef nCount_Column As Integer,
                                          ByRef table As System.Web.UI.HtmlControls.HtmlTable) As Boolean

        Dim ret_val As Boolean = False

        If Not f.IsHidden Then
            Select Case f.TextBefore
                Case String.Empty
                    ' non devo fare niente

                Case "NewRow"
                    ' aggiungo una riga vuota
                    Dim n_row As New System.Web.UI.HtmlControls.HtmlTableRow
                    table.Rows.Add(n_row)

                    If nCount_Column Mod 2 <> 0 Then
                        nCount_Column = 0
                    End If

                Case Else
                    ' aggiungo la stringa in una nuova riga
                    Dim tc = New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim lbl As New Label
                    lbl.Text = m_config.GetEntryByKeyName(f.TextBefore).GetValue
                    lbl.CssClass = "label-text-section"
                    If nCount_Column Mod 2 <> 0 Then
                        nCount_Column = 0
                    End If

                    tc.Controls.Add(lbl)

                    Dim n_row As New System.Web.UI.HtmlControls.HtmlTableRow
                    n_row.Attributes("class") = "rowSection"
                    n_row.Cells.Add(tc)
                    table.Rows.Add(n_row)

                    ret_val = True
            End Select
        End If

        Return ret_val
    End Function

    Private Shared Sub SetTextAfter(ByVal m_config As UsersGUI.config,
                                    ByVal f As Field,
                                    ByRef nCount_Column As Integer,
                                    ByVal tc As System.Web.UI.HtmlControls.HtmlTableCell,
                                    ByRef table As System.Web.UI.HtmlControls.HtmlTable,
                                    Optional ByVal predicate As String = "")

        'aggiungo un ID a tutti i field (predicate e usato solo sui filtri di calendario)
        If predicate = String.Empty Then
            tc.ID = f.FieldDb & "_td"
        Else
            tc.ID = f.FieldDb & "_" & predicate & "_td"
        End If

        Select Case f.TextAfter
            Case String.Empty
                ' aggiungo la table cell alla tabella
                table.Rows(table.Rows.Count - 1).Cells.Add(tc)

            Case "NewRow"
                If nCount_Column Mod 2 <> 0 Then
                    nCount_Column = 2
                End If
                ' aggiungo la table cell in una nuova riga
                table.Rows(table.Rows.Count - 1).Cells.Add(tc)
            Case Else
                ' aggiungo la table cell alla tabella
                table.Rows(table.Rows.Count - 1).Cells.Add(tc)

                If Not f.IsHidden Then
                    ' aggiungo la stringa in una nuova riga
                    Dim tc2 = New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim lbl As New Label
                    lbl.Text = m_config.GetEntryByKeyName(f.TextAfter).GetValue
                    lbl.CssClass = "label-text-section"
                    tc2.Controls.Add(lbl)

                    If nCount_Column Mod 2 <> 0 Then
                        nCount_Column = 0
                    End If

                    Dim n_row As New System.Web.UI.HtmlControls.HtmlTableRow
                    n_row.Attributes("class") = "rowSection"
                    n_row.Cells.Add(tc2)
                    table.Rows.Add(n_row)
                End If

        End Select
    End Sub

    Public Shared Function GetLabel(ByVal m_config As UsersGUI.config, ByVal name As String, ByVal obj As Field, Optional ByVal bBold As Boolean = False) As System.Web.UI.WebControls.Label
        Dim l As New Label
        If name <> String.Empty Then
            l.ID = name
        End If
        l.Text = m_config.GetEntryByKeyName(obj.FieldName).GetValue() & ":&nbsp;"
        l.Visible = Not obj.IsHidden
        l.CssClass = "label-text"
        l.Font.Bold = bBold
        Return l
    End Function

    Public Shared Function GetLabel(ByVal m_config As UsersGUI.config, ByVal name As String, ByVal m_value As String) As System.Web.UI.WebControls.Label
        Dim l As New Label
        If name <> String.Empty Then
            l.ID = name
        End If
        l.Text = "&nbsp;" & m_value
        l.CssClass = "label-text"
        Return l
    End Function

    Public Shared Function GetTextBox(ByVal m_config As UsersGUI.config, ByVal obj As Field, ByVal m_Value As String, ByVal m_ReadOnly As Boolean) As System.Web.UI.WebControls.TextBox
        Dim t As New myTextBox
        t.ID = obj.FieldDb
        t.FieldObject = obj
        t.ValidationGroup = obj.ValidationGroup
        t.TextMode = TextBoxMode.SingleLine

        If obj.GetFieldType = EnumFieldType.FieldNumber Then
            t.CssClass = "text-right"
            t.Attributes.Add("type", "number")
            t.Attributes.Add("step", "any")
        Else
            t.CssClass = "text"
        End If
        t.CssClass &= " input-wide"

        t.Text = String.Empty
        Select Case obj.GetFieldType
            Case EnumFieldType.FieldPassword
                t.TextMode = TextBoxMode.Password
            Case EnumFieldType.FieldWString
                t.TextMode = TextBoxMode.MultiLine
                t.CssClass = "textbox-text"
        End Select
        If obj.EnableKeyEntry Then
            t.Text = m_config.GetEntryByKeyName(m_Value).GetValue
        ElseIf m_Value IsNot Nothing Then
            If obj.GetFieldType = EnumFieldType.FieldNumber Then
                Try
                    If IsNumeric(m_Value) Then
                        t.Text = UsersGUI.tools.WebInputDoubleParse(m_Value, obj.nDecimal).ToString(CultureInfo.CreateSpecificCulture("en-gb"))
                    End If
                Catch ex As Exception
                    t.Text = String.Empty
                End Try
            Else
                Try
                    t.Text = m_Value
                Catch ex As Exception
                    t.Text = String.Empty
                End Try
            End If
        Else
            If obj.FixedRuntimeValue IsNot Nothing AndAlso obj.FixedRuntimeValue.ToUpper.Contains("QUERYSTRING") Then
                Dim _start As Integer = obj.FixedRuntimeValue.IndexOf("""") + 1
                Dim _stop As Integer = obj.FixedRuntimeValue.LastIndexOf("""") - _start
                Dim sStringa As String = obj.FixedRuntimeValue.Substring(_start, _stop)

                t.Text = Current.Request.QueryString(sStringa)
            Else
                t.Text = obj.FixedRuntimeValue
            End If

        End If
        If m_ReadOnly OrElse obj.IsReadOnly Then
            t.ReadOnly = True
            t.Enabled = False
        End If
        If obj.GetFieldType = EnumFieldType.FieldPassword Then
            t.TextMode = TextBoxMode.Password
        End If
        t.Visible = Not obj.IsHidden

        If obj.CallEvent <> String.Empty Then
            Dim ge As New GenericsEvents.GenericsEvents(m_config, t, obj)
            ge.CallEvent(t, obj)
        End If

        Return t
    End Function

    Public Shared Function GetCheckBoxYesNo(ByVal m_config As UsersGUI.config, ByVal obj As Field, ByVal m_Value As String, ByVal m_ReadOnly As Boolean) As System.Web.UI.WebControls.CheckBox
        Dim t As New myCheckBox

        t.ID = obj.FieldDb
        t.FieldObject = obj
        t.ValidationGroup = obj.ValidationGroup
        t.CssClass = "rb"
        t.Checked = False
        If m_ReadOnly OrElse obj.IsReadOnly Then
            t.Enabled = False
        End If

        t.Checked = False

        If m_Value IsNot Nothing AndAlso m_Value.Trim.ToUpper = m_StringYes.Trim Then
            t.Checked = True
        End If

        t.Visible = Not obj.IsHidden

        If obj.CallEvent <> String.Empty Then
            Dim ge As New GenericsEvents.GenericsEvents(m_config, t, obj)
            ge.CallEvent(t, obj)
        End If

        Return t
    End Function

    Public Shared Function GetCheckBoxOnOff(ByVal m_config As UsersGUI.config, ByVal obj As Field, ByVal m_Value As String, ByVal m_ReadOnly As Boolean) As System.Web.UI.WebControls.CheckBox
        Dim t As New myCheckBox

        t.ID = obj.FieldDb
        t.FieldObject = obj
        t.ValidationGroup = obj.ValidationGroup
        t.CssClass = "rb"
        t.Checked = False
        If m_ReadOnly OrElse obj.IsReadOnly Then
            t.Enabled = False
        End If

        t.Checked = False

        If m_Value IsNot Nothing AndAlso m_Value.Trim.ToUpper = m_StringOn.Trim Then
            t.Checked = True
        End If

        t.Visible = Not obj.IsHidden

        If obj.CallEvent <> String.Empty Then
            Dim ge As New GenericsEvents.GenericsEvents(m_config, t, obj)
            ge.CallEvent(t, obj)
        End If

        Return t
    End Function

    Public Shared Function GetRadioButton(ByVal m_config As UsersGUI.config, ByVal obj As Field, ByVal m_GroupName As String, ByVal m_Text As String, ByVal m_Value As String, ByVal m_ReadOnly As Boolean) As System.Web.UI.WebControls.RadioButton
        Dim t As New myRadioButton

        t.ID = obj.FieldDb
        t.FieldObject = obj
        t.ValidationGroup = obj.ValidationGroup
        t.CssClass = "rb"
        t.Checked = False
        If m_ReadOnly OrElse obj.IsReadOnly Then
            t.Enabled = False
        End If

        t.Checked = False

        If m_Value <> String.Empty AndAlso CBool(m_Value) Then
            t.Checked = True
        End If
        t.Text = m_Text
        t.GroupName = m_GroupName
        t.Visible = Not obj.IsHidden

        If obj.CallEvent <> String.Empty Then
            Dim ge As New GenericsEvents.GenericsEvents(m_config, t, obj)
            ge.CallEvent(t, obj)
        End If

        Return t
    End Function

    Public Shared Function GetCheckBoxZeroUno(ByVal m_config As UsersGUI.config, ByVal obj As Field, ByVal m_Value As String, ByVal m_ReadOnly As Boolean) As System.Web.UI.WebControls.CheckBox
        Dim t As New myCheckBox

        t.ID = obj.FieldDb
        t.FieldObject = obj
        t.ValidationGroup = obj.ValidationGroup
        t.CssClass = "rb"
        t.Checked = False
        If m_ReadOnly OrElse obj.IsReadOnly Then
            t.Enabled = False
        End If

        t.Checked = False

        If m_Value IsNot Nothing AndAlso m_Value.ToUpper = "1" Then
            t.Checked = True
        End If

        t.Visible = Not obj.IsHidden

        If obj.CallEvent <> String.Empty Then
            Dim ge As New GenericsEvents.GenericsEvents(m_config, t, obj)
            ge.CallEvent(t, obj)
        End If

        Return t
    End Function

    Public Shared Function GetAtmillCalendar(ByVal tc As System.Web.UI.HtmlControls.HtmlTableCell, ByVal predicate As String, ByVal uf As Field, ByVal m_value As String, ByVal m_ReadOnly As Boolean) As System.Web.UI.HtmlControls.HtmlTableCell
        Return uf.ObjectCalendar.SetDataBound(tc, predicate, uf, m_value, m_ReadOnly)
    End Function

    Public Shared Function GetAtmillList(ByVal m_config As UsersGUI.config, ByVal obj As Field, ByVal value As String, ByVal m_ReadOnly As Boolean, ByVal dr As DataRow) As System.Web.UI.WebControls.DropDownList
        Dim sSelect As String
        Dim dt As Data.DataTable
        Dim ddl As New myDropDownList

        ddl.ID = obj.FieldDb
        ddl.FieldObject = obj
        ddl.CssClass = "ddl-text"
        ddl.AutoPostBack = False
        If obj.ObjectList.Parent IsNot Nothing Then
            ddl.Visible = Not CType(obj.ObjectList.Parent, Field).IsHidden
        Else
            ddl.Visible = True
        End If

        If m_ReadOnly OrElse obj.ObjectList.IsReadOnly Then
            ddl.Enabled = False
        End If

        Dim sComp() As String
        Dim sSelectComp As String = String.Empty
        sComp = obj.ObjectList.SelectColumns.Split(Char.Parse(","))

        If sComp.Count > 1 Then
            For Each s As String In sComp
                If sSelectComp <> String.Empty Then
                    sSelectComp &= " + ' - ' + "
                End If
                sSelectComp &= " case when " & s & " is null then '' else convert(nvarchar(max)," & s & ") end "
            Next
        End If

        If sSelectComp <> String.Empty Then
            sSelectComp &= " as Componente "
        End If

        If obj.ObjectList.SelectColumns <> String.Empty AndAlso obj.ObjectList.From <> String.Empty Then
            sSelect = "SELECT " & obj.ObjectList.SelectColumns
            If Not obj.ObjectList.SelectColumns.Contains(obj.ObjectList.ValueColumn) Then
                sSelect &= "," & obj.ObjectList.ValueColumn
            End If
            If sSelectComp <> String.Empty Then
                sSelect &= "," & sSelectComp
            End If
            sSelect &= " FROM " & obj.ObjectList.From
            If obj.ObjectList.Where <> String.Empty Then
                sSelect &= " WHERE " & obj.ObjectList.Where
            End If

            If obj.ObjectList.OrderBy <> String.Empty Then
                sSelect &= " ORDER BY " & obj.ObjectList.OrderBy
            End If

            dt = DataBase.ExecuteSQL_DataTable(sSelect, True)

            If dt Is Nothing Then
                Return ddl
            End If

            If obj.ObjectList.AddNull Then
                Dim r As Data.DataRow
                r = dt.NewRow
                r.Item(0) = String.Empty
                dt.Rows.InsertAt(r, 0)
            End If

            ' Handle EnableKeyEntry option on the field
            If obj.EnableKeyEntry Then
                For Each row As Data.DataRow In dt.Rows
                    Try
                        If row.Item(obj.ObjectList.SelectColumns).ToString().Trim().Length > 0 Then
                            row.Item(obj.ObjectList.SelectColumns) = m_config.GetEntryByKeyName(row.Item(obj.ObjectList.SelectColumns).ToString).GetValue()
                        End If
                    Catch ex As Exception
                        Exit For
                    End Try
                Next
            End If

            ddl.DataSource = dt
            If sSelectComp <> String.Empty Then
                ddl.DataTextField = "Componente"
            Else
                Dim sSetColum As String = obj.ObjectList.SelectColumns
                If sSetColum.Contains("DISTINCT") Then
                    sSetColum = sSetColum.Substring(sSetColum.IndexOf("DISTINCT") + "DISTINCT".Length)
                End If
                ddl.DataTextField = sSetColum.Trim
            End If

            ddl.DataValueField = obj.ObjectList.ValueColumn
            ddl.EnableViewState = True
            ddl.DataBind()
        End If

        If obj.CallEvent <> String.Empty Then
            Dim ge As New GenericsEvents.GenericsEvents(m_config, ddl, obj)
            ge.CallEvent(ddl, obj)
        End If

        If value <> String.Empty Then
            ddl.SelectedValue = value
        End If

        Return ddl
    End Function

    Public Shared Function GetAtmillChkList(ByVal tc As System.Web.UI.HtmlControls.HtmlTableCell, ByVal uf As Field, ByVal dt As DataTable, ByVal m_ReadOnly As Boolean) As System.Web.UI.HtmlControls.HtmlTableCell
        Return uf.ObjectChkList.SetDataBound(tc, uf, dt, m_ReadOnly)
    End Function

End Class