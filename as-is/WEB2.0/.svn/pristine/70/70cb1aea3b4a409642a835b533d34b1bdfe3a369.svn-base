﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class ProductionPlan
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdRecipe As Long = m_InvalidId
    Private m_IdRecipeLog As Long = m_InvalidId
    Private m_LinePosition As Long = m_InvalidId
    Private m_OrderStatus As Long = m_InvalidId
    Private m_Counter As Long = m_InvalidId
    Private m_working_time As Long = m_IntegerZero
    Private m_ProducedAmount As Double = m_DoubleZero
    Private m_StartDate As DateTime = m_InvalidDateTime
    Private m_StopDate As DateTime = m_InvalidDateTime
    Private m_IdCycle As Integer = m_IntegerZero
    Private m_ControlsOK As String = m_StringNo
    Private m_RecipeExecutable As String = String.Empty

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New()
        MyBase.New("PRODUCTION_PLAN")
    End Sub

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long)
        MyBase.New("PRODUCTION_PLAN")
        Me.m_scr = scr
        Me.m_root = root

        If Id <> m_InvalidId Then
            Me.m_Id = Id
            LoadData()
        End If

        Me.CreateObjectFromControl(Id)

        ' recupero sempre il recipe_log_id attuale per la ricetta selezionata
        Me.IdRecipeLog = GetActualRecipeLogIdForRecipeId(Me.IdRecipe)
    End Sub

    Private Sub LoadData()
        Dim sSelect As String = String.Empty
        Dim dt As Data.DataTable

        sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.m_Id & "'"
        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As DataRow In dt.Rows
            Me.IdProductionPlan = Me.m_Id
            Me.Counter = Long.Parse(dr.Item("COUNTER").ToString)
            Me.IdCycle = Integer.Parse(dr.Item("CYC_ID").ToString)
            Me.IdRecipe = Long.Parse(dr.Item("REC_ID").ToString)
            Me.LinePosition = Long.Parse(dr.Item("LINE_POSITION").ToString)
            Me.ControlsOK = dr.Item("CONTROLS_OK").ToString

            Try
                Me.WorkingTime = Integer.Parse(dr.Item("WORKING_TIME").ToString)
            Catch ex As Exception
                Me.WorkingTime = m_IntegerZero
            End Try

            Try
                Me.ProducedAmount = Double.Parse(dr.Item("PRODUCED_AMOUNT").ToString)
            Catch ex As Exception
                Me.ProducedAmount = m_DoubleZero
            End Try

        Next

    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As ProductionPlan
        Me.IdProductionPlan = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdRecipe"
                            Try
                                Me.IdRecipe = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.IdRecipe = m_InvalidId
                            End Try
                        Case "OrderStatus"
                            Try
                                Me.OrderStatus = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.OrderStatus = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "IdCycle"
                            Try
                                Me.IdCycle = Integer.Parse(CType(m_Control, myTextBox).Text)
                            Catch
                                Me.IdCycle = m_IntegerZero
                            End Try
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdProductionPlan() As Long
        Get
            Return m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdRecipe() As Long
        Get
            Return m_IdRecipe
        End Get
        Set(ByVal value As Long)
            Me.m_IdRecipe = value
        End Set
    End Property

    Public Property IdRecipeLog() As Long
        Get
            Return m_IdRecipeLog
        End Get
        Set(ByVal value As Long)
            Me.m_IdRecipeLog = value
        End Set
    End Property

    Public Property LinePosition() As Long
        Get
            Return m_LinePosition
        End Get
        Set(ByVal value As Long)
            Me.m_LinePosition = value
        End Set
    End Property

    Public Property OrderStatus() As Long
        Get
            Return m_OrderStatus
        End Get
        Set(ByVal value As Long)
            Me.m_OrderStatus = value
        End Set
    End Property

    Public Property Counter() As Long
        Get
            Return m_Counter
        End Get
        Set(ByVal value As Long)
            Me.m_Counter = value
        End Set
    End Property

    Public Property WorkingTime() As Long
        Get
            Return m_working_time
        End Get
        Set(ByVal value As Long)
            Me.m_working_time = value
        End Set
    End Property

    Public Property ProducedAmount() As Double
        Get
            Return m_ProducedAmount
        End Get
        Set(ByVal value As Double)
            Me.m_ProducedAmount = value
        End Set
    End Property

    Public Property StartDate() As DateTime
        Get
            Return m_StartDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_StartDate = value
        End Set
    End Property

    Public Property StopDate() As DateTime
        Get
            Return m_StopDate
        End Get
        Set(ByVal value As DateTime)
            Me.m_StopDate = value
        End Set
    End Property

    Public Property IdCycle() As Integer
        Get
            Return m_IdCycle
        End Get
        Set(ByVal value As Integer)
            Me.m_IdCycle = value
        End Set
    End Property

    Public Property ControlsOK() As String
        Get
            Return m_ControlsOK
        End Get
        Set(ByVal value As String)
            Me.m_ControlsOK = value.Trim
        End Set
    End Property

    Public Property RecipeExecutable() As String
        Get
            Return m_RecipeExecutable
        End Get
        Set(ByVal value As String)
            Me.m_RecipeExecutable = value.Trim
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare, bRigaDaAggiornare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            If (WebDataBaseLayer.DataBase.GetSVNRevision() > 15479) Then
                sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdProductionPlan & "'"
            Else
                ' Old code <= 15479
                If tools.IsControlled(Me.IdCycle) Then
                    sSelect = "SELECT * FROM " & Me.TableName & " WHERE CYC_ID = '" & Me.IdCycle & "'"
                    bRigaDaAggiornare = True
                Else
                    sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdProductionPlan & "'"
                End If
            End If

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True
            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdProductionPlan))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio production plan."
                        Dim myExc As New myException.myException(System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If
                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio production plan sulla select ex:" & ex.Message
                    Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = Me.IdProductionPlan
            If Me.IdProductionPlan = m_InvalidId Then
                Me.IdProductionPlan = tools.GetNextId(Me.TableName, "ID")
                drNodo("ID") = Me.IdProductionPlan
            End If

            If bRigaDaCreare Then
                drNodo("COUNTER") = Me.Counter
                If Me.Counter = m_InvalidId Then
                    Me.Counter = tools.GetNextId(Me.TableName, "COUNTER")
                    drNodo("COUNTER") = Me.Counter
                End If

                drNodo("LINE_POSITION") = Me.LinePosition
                If Me.LinePosition = m_InvalidId Then
                    Me.LinePosition = tools.GetNextId(Me.TableName, "LINE_POSITION")
                    drNodo("LINE_POSITION") = Me.LinePosition
                End If
            End If

            If bRigaDaAggiornare Then
                Me.Counter = tools.GetNextId(Me.TableName, "COUNTER")
                drNodo("COUNTER") = Me.Counter
            End If

            If Me.IdRecipe <> m_InvalidId Then
                drNodo("REC_ID") = Me.IdRecipe
            Else
                drNodo("REC_ID") = m_RecId_NoRecipe 'Setto a 1 la ricetta nel caso sia un ciclo che non la prevede
            End If

            If Me.IdRecipeLog <> m_InvalidId Then
                drNodo("RECIPE_LOG_ID") = Me.IdRecipeLog

                If Me.RecipeExecutable.ToUpper = m_StringYes Then
                    drNodo("RECIPE_EXECUTABLE") = Me.TrimStringToMaxLen(m_StringYes, "RECIPE_EXECUTABLE")
                ElseIf Me.RecipeExecutable.ToUpper = m_StringNo Then
                    drNodo("RECIPE_EXECUTABLE") = Me.TrimStringToMaxLen(m_StringNo, "RECIPE_EXECUTABLE")
                Else
                    drNodo("RECIPE_EXECUTABLE") = DBNull.Value
                End If
            Else
                drNodo("RECIPE_LOG_ID") = DBNull.Value
                drNodo("RECIPE_EXECUTABLE") = DBNull.Value
            End If

            If Me.OrderStatus <> m_InvalidId Then
                drNodo("ORDER_STATUS") = Me.OrderStatus
            Else
                drNodo("ORDER_STATUS") = costanti.m_StatusRelease
            End If

            drNodo("WORKING_TIME") = Me.WorkingTime
            drNodo("PRODUCED_AMOUNT") = Me.ProducedAmount
            drNodo("START_DATE") = DBNull.Value
            drNodo("STOP_DATE") = DBNull.Value
            drNodo("CYC_ID") = Me.IdCycle

            If Me.ControlsOK.ToUpper = m_StringYes Then
                drNodo("CONTROLS_OK") = Me.TrimStringToMaxLen(m_StringYes, "CONTROLS_OK")
            Else
                drNodo("CONTROLS_OK") = Me.TrimStringToMaxLen(m_StringNo, "CONTROLS_OK")
            End If

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento production plan:" & ex.Message
                Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try
        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

    Private Function GetActualRecipeLogIdForRecipeId(ByVal rec_id As Long) As Long
        Dim ret_val As Long = m_InvalidId
        Dim sql_select As String = String.Empty
        Dim dt As Data.DataTable

        If rec_id <> m_RecId_NoRecipe AndAlso rec_id <> m_InvalidId Then
            sql_select = "SELECT MAX(ID) FROM RECIPE_LOGS WHERE REC_ID = " & rec_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sql_select, False)

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows
                    Long.TryParse(dr(0).ToString, ret_val)
                Next
            Else
                ' should never happen
                ret_val = m_InvalidId
            End If
        Else
            ' caso dei cicli senza ricetta
            ret_val = m_InvalidId
        End If

        Return ret_val
    End Function

End Class