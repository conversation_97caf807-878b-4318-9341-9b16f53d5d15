﻿Option Strict On

Imports System.Data.OleDb
Imports System.Web.UI

Public Class StockManualCorrReq
    Inherits myDbBaseClass

    Private m_Id As Long = m_InvalidId
    Private m_IdLotType As Long = m_InvalidId
    Private m_IdProduct As Long = m_InvalidId
    Private m_IdCel As Long = m_InvalidId
    Private m_Amount As Double = m_InvalidDblValue
    Private m_IdUser As Long = m_InvalidId

    Private m_scr As Screen
    Private m_root As Control

    Public Sub New(ByVal scr As Screen, ByVal root As Control, ByVal Id As Long, ByVal user_id As Long)
        MyBase.New("STOCK_MANUAL_CORR_REQ")
        Me.m_scr = scr
        Me.m_root = root
        Me.m_IdUser = user_id

        Me.CreateObjectFromControl(Id)
    End Sub

    Private Function CreateObjectFromControl(ByVal Id As Long) As StockManualCorrReq
        Me.IdStockManualCorrReq = Id
        For Each s As Field In m_scr.EditFields
            If s.GetFieldType <> EnumFieldType.FieldAuto Then
                Dim m_Control As Control
                m_Control = tools.FindControlRecursive(m_root, s.FieldDb)
                If TypeOf m_Control Is myDropDownList Then
                    Select Case s.PropertyName
                        Case "IdCel"
                            Try
                                Me.m_IdCel = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.m_IdCel = m_InvalidId
                            End Try
                        Case "IdProduct"
                            Try
                                Me.m_IdProduct = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.m_IdProduct = m_InvalidId
                            End Try
                        Case "IdLotType"
                            Try
                                Me.m_IdLotType = Long.Parse(CType(m_Control, myDropDownList).SelectedValue.ToString)
                            Catch
                                Me.m_IdLotType = m_InvalidId
                            End Try
                    End Select
                ElseIf TypeOf m_Control Is myTextBox Then
                    Select Case s.PropertyName
                        Case "Amount"
                            Me.m_Amount = UsersGUI.tools.WebInputDoubleParse(CType(m_Control, myTextBox).Text, s.nDecimal) * UsersGUI.tools.GetConversionFactorToDB(s.UnitDb, s.UnitAsp)
                    End Select
                End If
            End If
        Next
        Return Me
    End Function

    Public Property IdStockManualCorrReq() As Long
        Get
            Return m_Id
        End Get
        Set(ByVal value As Long)
            Me.m_Id = value
        End Set
    End Property

    Public Property IdProduct() As Long
        Get
            Return m_IdProduct
        End Get
        Set(ByVal value As Long)
            Me.m_IdProduct = value
        End Set
    End Property

    Public Property IdUser() As Long
        Get
            Return m_IdUser
        End Get
        Set(ByVal value As Long)
            Me.m_IdUser = value
        End Set
    End Property

    Public Property Amount() As Double
        Get
            Return m_Amount
        End Get
        Set(ByVal value As Double)
            Me.m_Amount = value
        End Set
    End Property

    Public Property IdLotType() As Long
        Get
            Return m_IdLotType
        End Get
        Set(ByVal value As Long)
            Me.m_IdLotType = value
        End Set
    End Property

    Public Property IdCell() As Long
        Get
            Return m_IdCel
        End Get
        Set(ByVal value As Long)
            Me.m_IdCel = value
        End Set
    End Property

    Public Sub Save()
        Dim sSelect As String
        Dim dsNodo As New DataSet
        Dim drNodo As DataRow
        Dim bRigaDaCreare As Boolean
        Dim DB As New OleDb.OleDbConnection

        Try
            DB.ConnectionString = WebDataBaseLayer.DataBase.GetStringConnection
            DB.Open()

            drNodo = Nothing

            sSelect = "SELECT * FROM " & Me.TableName & " WHERE ID = '" & Me.IdStockManualCorrReq & "'"

            Dim selCom As New OleDb.OleDbCommand(sSelect, DB)
            Dim daNodo As New OleDb.OleDbDataAdapter(selCom)
            Dim cmdBuilder As OleDb.OleDbCommandBuilder = New OleDb.OleDbCommandBuilder(daNodo)

            daNodo.Fill(dsNodo, Me.TableName)
            daNodo.MissingSchemaAction = MissingSchemaAction.AddWithKey

            ' Crea una nuova riga o ritrova la riga del DB
            If dsNodo.Tables(0).Rows.Count = 0 Then

                drNodo = dsNodo.Tables(Me.TableName).NewRow
                bRigaDaCreare = True

            Else
                Dim arrResults As DataRow()
                Try
                    arrResults = dsNodo.Tables(Me.TableName).Select(String.Format("ID={0}", Me.IdStockManualCorrReq))
                    If arrResults.Length > 0 Then
                        drNodo = arrResults(0)
                    Else
                        Dim msg As String = "Errore durante il salvataggio stock manual corr req."
                        Dim myExc As New myException.myException(Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                        Throw myExc
                    End If

                Catch ex As myException.myException
                    Throw ex
                Catch ex As Exception
                    Dim msg As String = "Errore durante il salvataggio stock manual corr req sulla select ex: " & ex.Message
                    Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                    Throw myExc
                End Try
            End If

            drNodo("ID") = tools.GetNextId(Me.TableName, "ID")
            drNodo("NEW_LTE_ID") = Me.IdLotType
            drNodo("PRO_ID") = Me.IdProduct
            drNodo("CEL_ID") = Me.IdCell
            drNodo("AMOUNT") = Me.Amount
            drNodo("SU_ID") = Me.IdUser

            If bRigaDaCreare Then
                dsNodo.Tables(Me.TableName).Rows.Add(drNodo)
            End If

            Try
                daNodo.Update(dsNodo, Me.TableName)
                dsNodo.AcceptChanges()
            Catch ex As OleDbException
                dsNodo.RejectChanges()
                Dim msg As String = "Errore durante aggiornamento stock manual corr req: " & ex.Message
                Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name, msg)
                Throw myExc
            End Try

        Catch ex As myException.myException
            Throw ex
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, Me.GetType().Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        Finally
            DB.Close()
        End Try
    End Sub

End Class