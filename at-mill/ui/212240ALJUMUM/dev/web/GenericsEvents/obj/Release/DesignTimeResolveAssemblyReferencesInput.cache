   .winmd.dll.exe 
   ?C:\ATMILL\212240ALJUMUM\target\Web\My Project\Application.myapp?C:\ATMILL\212240ALJUMUM\target\Web\My Project\Settings.settingsKC:\ATMILL\212240ALJUMUM\dev\web\CommonDefines\bin\Release\CommonDefines.dll;C:\ATMILL\WEB2.0\LogTools\LogTools\bin\Release\LogTools.dll8C:\ATMILL\WEB2.0\myException\bin\Release\myException.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dll;C:\ATMILL\WEB2.0\UsersGUI\UsersGUI\bin\Release\UsersGUI.dllSC:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\bin\Release\WebDataBaseLayer.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName};C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\bin\Release\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}dC:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\obj\Release\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         