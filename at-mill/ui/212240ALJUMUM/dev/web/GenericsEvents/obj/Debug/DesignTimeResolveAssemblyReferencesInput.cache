   .winmd.dll.exe 
   WC:\development\OCRIM\AtMill\as-is\212240ALJUMUM\target\Web\My Project\Application.myappWC:\development\OCRIM\AtMill\as-is\212240ALJUMUM\target\Web\My Project\Settings.settingsaC:\development\OCRIM\AtMill\as-is\212240ALJUMUM\dev\web\CommonDefines\bin\Debug\CommonDefines.dllQC:\development\OCRIM\AtMill\as-is\WEB2.0\LogTools\LogTools\bin\Debug\LogTools.dllNC:\development\OCRIM\AtMill\as-is\WEB2.0\myException\bin\Debug\myException.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dllfC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.Linq.dllQC:\development\OCRIM\AtMill\as-is\WEB2.0\UsersGUI\UsersGUI\bin\Debug\UsersGUI.dlliC:\development\OCRIM\AtMill\as-is\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\bin\Debug\WebDataBaseLayer.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName}QC:\development\OCRIM\AtMill\as-is\212240ALJUMUM\dev\web\GenericsEvents\bin\Debug\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}zC:\development\OCRIM\AtMill\as-is\212240ALJUMUM\dev\web\GenericsEvents\obj\Debug\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         