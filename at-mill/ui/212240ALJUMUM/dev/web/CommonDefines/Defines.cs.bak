﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonDefines
{
    public static class Defines
    {
        //define generiche
        public const long MAX_DIGIT_BATCH_NUMBER = 10;

        public const long ID_CUSTOMER_UNKNOWN = 1;

        public const long ID_SUPPLIER_UNKNOWN = 1;
        public const long ID_CARRIER_UNKNOWN = 1;

        public const int PROD_NAME_BIN_SIZE = 8; //Lunghezza del nome prodotto sulle celle

        public const int META_RECIPE_PARAMETER_TYPE_ALL = 0; // MetaRecipeParamType All (usato per l'upload di tutti i parametri)

        public const int MODE_LINEAR = 1;
        public const int MODE_CIRCULAR = 2;
        public const int MODE_CONTINUOUS = 3;

        //Generic Events
        public const int NumberOfDecimalsForQuantity = 2;

        // define di commessa
        public const double MIN_UMIDITA_PERC = 5.0; // umidità min DM

        public const double MAX_UMIDITA_PERC = 25.0; // umidità max DM
        public const int MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN = 5;

        public static int DELETE_FILE_EXPORT_DATA_AFTER_DAY = 7;

        public static class ReservedInvalidId
        {
            public const long InvalidId = -1;
        }

        public class PoNumberValidationFlag
        {
            public const int InProgress = 0;
            public const int NotValidated = 1;
            public const int NotDelivered = 2;
            public const int Delivered = 3;
        }

        public class SSPlantCapacityTons
        {
            // TODO
        }

        public static class SSSourceNumber
        {
            public static int C02_FirstCleaning = 5;
            public static int C03_SecondTempering = 5;
            public static int C06_FlourTransportRV501 = 5;
            public static int C07_FlourTransportRV502 = 5;
            public static int C08_FlourBaggingBS501 = 2;
            public static int C09_FlourBaggingBS502 = 2;
            public static int C11_FlourBaggingBS601 = 1;
            public static int C14_FlourBaggingBS503 = 2;

            // TODO
        }

        public enum SSBins
        {
            // TODO
        }

        public static class SSWebSourceSet
        {
            // TODO
        }

        public static class SSWebDestSet
        {
            // TODO
        }

        public static class SSWebCustomRadioButton
        {
            // TODO
        }

        public static class SSCyclesToPaths
        {
            // TODO
        }

        public static class SSProductTypes
        {
            public const long System = 999;
            // TODO
        }

        public enum SSExportDataToERP
        {
            // nothing to do
            NothingToDo = 999
        }

        public enum SSBorderDatabaseAction
        {
            Add = 1,
            Edit = 2,
            Delete = 3
        }

        public enum SSCycles
        {
            C01_Intake = 1,
            C02_FirstCleaning = 2,
            C03_SecondTempering = 3,
            C04_SecondCleaningAndMilling = 4,
            C06_FlourTransportRV501 = 6,
            C07_FlourTransportRV502 = 7,
            C08_FlourBaggingBS501 = 8,
            C09_FlourBaggingBS502 = 9,
            C10_BranRecycle = 10,
            C11_FlourBaggingBS601 = 11,
            C12_ScreeningsGrinding = 12,
            C13_ScreeningsTransport = 13,
            C14_FlourBaggingBS503 = 14
        }

        public static class SSProducts
        {
            public const long Unknown = 1;
            public const long Water = 2;
            public const long Screenings = 3;
            public const long Power = 4;
            // TODO
        }

        public static class SSDestinationSlots
        {
            // TODO
        }

        public static class SSRecipeParamInputDecimals
        {
            // TODO
        }

        public static class SSParamOutputDecimals
        {
            // TODO
        }

        public static class SSMaxRepeatOfIngredients
        {
            // TODO
        }

        public static class SSMaxNumOfIngredients
        {
            public static int C02_FirstCleaning = 5;
            public static int C03_SecondTempering = 5;
            public static int C06_FlourTransportRV501 = 5;
            public static int C07_FlourTransportRV502 = 5;
            public static int C08_FlourBaggingBS501 = 2;
            public static int C09_FlourBaggingBS502 = 2;
            public static int C11_FlourBaggingBS601 = 1;
            public static int C14_FlourBaggingBS503 = 2;
            // TODO
        }

        public static class SSMaxNumOfSourcesPerIngredient
        {
            public static int C02_FirstCleaning = 5;
            public static int C03_SecondTempering = 5;
            public static int C06_FlourTransportRV501 = 5;
            public static int C07_FlourTransportRV502 = 5;
            public static int C08_FlourBaggingBS501 = 2;
            public static int C09_FlourBaggingBS502 = 2;
            public static int C11_FlourBaggingBS601 = 1;
            public static int C14_FlourBaggingBS503 = 2;
            // TODO
        }

        public static class SSObjectPhysicalLimitsKg
        {
            // TODO
        }

        public static class SSMetaOperationParams
        {
            // TODO
        }

        public static class SSOperationParamValues
        {
            // TODO
        }

        public enum SSLotTypes
        {
            H = 1
            //TODO
        }

        public enum SSFlowFamilies
        {
            Screenings = 1
            // TODO
        }

        public enum SSFlows
        {
            Screenings = 1
            // TODO
        }

        public enum SSScales
        {
            // TODO
        }

        public enum SSYieldsReports
        {
            // TODO
        }

        public enum SSMetaRecipe
        {
            None = 1,
            R2_FirstCleaning = 2,
            R3_SecondTempering = 3,
            R4_SecondCleaningAndMilling = 4,
        }

        public enum SSPercUIDM
        {
            MinDM201 = 5,
            MaxDM201 = 19,
            MinDM202 = 5,
            MaxDM202 = 19,
        }

        public enum SSPercUFDM
        {
            MinDM201 = 5,
            MaxDM201 = 19,
            MinDM202 = 5,
            MaxDM202 = 19,
        }
    }
}