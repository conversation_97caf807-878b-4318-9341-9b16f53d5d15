﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonDefines
{
    public static class Defines
    {
        //define generiche
        public const long MAX_DIGIT_BATCH_NUMBER = 10;

        public const long ID_CUSTOMER_UNKNOWN = 1;

        public const long ID_SUPPLIER_UNKNOWN = 1;
        public const long ID_CARRIER_UNKNOWN = 1;

        public const int PROD_NAME_BIN_SIZE = 8; //Lunghezza del nome prodotto sulle celle

        public const int META_RECIPE_PARAMETER_TYPE_ALL = 0; // MetaRecipeParamType All (usato per l'upload di tutti i parametri)

        public const int MODE_LINEAR = 1;
        public const int MODE_CIRCULAR = 2;
        public const int MODE_CONTINUOUS = 3;

        //Generic Events
        public const int NumberOfDecimalsForQuantity = 2;

        // define di commessa
        public const double MIN_UMIDITA_PERC = 5.0; // umidità min DM

        public const double MAX_UMIDITA_PERC = 25.0; // umidità max DM
        public const int MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN = 5;

        public static int DELETE_FILE_EXPORT_DATA_AFTER_DAY = 7;

        public static class ReservedInvalidId
        {
            public const long InvalidId = -1;
        }

        public class PoNumberValidationFlag
        {
            public const int InProgress = 0;
            public const int NotValidated = 1;
            public const int NotDelivered = 2;
            public const int Delivered = 3;
        }

        public class SSPlantCapacityTons
        {
        }

        public static class SSSourceNumber
        {
            public static int C01_Intake = 1;
            public static int C02_FirstCleaning = 5;
            public static int C03_SecondTempering = 5;
            public static int C04_SecondCleaningAndMilling = 4;
            public static int C06_FlourTransportRV501 = 5;
            public static int C07_FlourTransportRV502 = 5;
            public static int C08_FlourBaggingBS501 = 2;
            public static int C09_FlourBaggingBS502 = 2;
            public static int C10_BranRecycle = 5;
            public static int C11_FlourBaggingBS601 = 1;
            public static int C12_ScreeningsGrinding = 1;
            public static int C13_ScreeningsTransport = 1;
            public static int C14_FlourBaggingBS503 = 2;
        }

        public enum SSBins
        {
            SL201 = 201,
            SL202 = 202,
            SL203 = 203,
            SL204 = 204,
            SL205 = 205,
            SL206 = 206,
            SL207 = 207,
            SL208 = 208,
            SL209 = 209,
            SL210 = 210,
            SL211 = 211,
            SL212 = 212,
            SL213 = 213,
            SL214 = 214,
            SL215 = 215,
            SL216 = 216,
            Buffer_B1 = 298,
            Mill = 299,
            SL301_SL302 = 301,
            SL401 = 401,
            SL501 = 501,
            SL502 = 502,
            SL503 = 503,
            SL504 = 504,
            SL505 = 505,
            SL506 = 506,
            SL507 = 507,
            SL508 = 508,
            SL509 = 509,
            SL510 = 510,
            SL511 = 511,
            SL512 = 512,
            SL513 = 513,
            SL514 = 514,
            SL515 = 515,
            SL516 = 516,
            SL517 = 517,
            SL518 = 518,
            SL519 = 519,
            SL520 = 520,
            SL521 = 521,
            SL522 = 522,
            SL523 = 523,
            SL524 = 524,
            SL525 = 525,
            SL526 = 526,
            SL1501 = 551,
            SL501A = 552,
            SL1504 = 553,
            SL504A = 554,
            SL601 = 601,
            SL602 = 602,
            SL603 = 603,
            SL604 = 604,
            SL605 = 605,
            SL606 = 606,
            SMC_Virtual_Bin = 1000,
            CC_Intake = 1001,
            SL1208 = 1002,
            Buffer_Screenings = 1099,
            Buffer_WG201_C02 = 1201,
            First_cleaning_C02 = 1211,
            DM201 = 1221,
            Buffer_C03 = 1301,
            DM202 = 1321,
            Buffer_C04 = 1401,
            Second_cleaning_C04 = 1411,
            BS501 = 1501,
            BS502 = 1502,
            BS503 = 1503,
            BS601 = 1601,
            BLEx = 1699,
            Buffer_C06 = 1706,
            Buffer_C07 = 1707,
            Buffer_C08 = 1708,
            Buffer_C09 = 1709,
            Buffer_C10 = 1710,
            Buffer_C14 = 1714,
        }

        public static class SSWebSourceSet
        {
        }

        public static class SSWebDestSet
        {
        }

        public static class SSWebCustomRadioButton
        {
        }

        public static class SSCyclesToPaths
        {
        }

        public static class SSProductTypes
        {
            public const long System = 999;
        }

        public enum SSExportDataToERP
        {
            NothingToDo = 999
        }

        public enum SSBorderDatabaseAction
        {
            Add = 1,
            Edit = 2,
            Delete = 3
        }

        public enum SSCycles
        {
            C01_Intake = 1,
            C02_FirstCleaning = 2,
            C03_SecondTempering = 3,
            C04_SecondCleaningAndMilling = 4,
            C06_FlourTransportRV501 = 6,
            C07_FlourTransportRV502 = 7,
            C08_FlourBaggingBS501 = 8,
            C09_FlourBaggingBS502 = 9,
            C10_BranRecycle = 10,
            C11_FlourBaggingBS601 = 11,
            C12_ScreeningsGrinding = 12,
            C13_ScreeningsTransport = 13,
            C14_FlourBaggingBS503 = 14
        }

        public static class SSProducts
        {
            public const long Unknown = 1;
            public const long Water = 2;
            public const long Screenings = 3;
            public const long Power = 4;
        }

        public static class SSDestinationLoadMode
        {
            public const int C04_SecondCleaningAndMilling = 4;
            public const int C04_SecondCleaningAndMilling_LoadMode_F1 = 1;
            public const int C04_SecondCleaningAndMilling_LoadMode_F2 = 2;
            public const int C04_SecondCleaningAndMilling_LoadMode_F3 = 3;
            public const int C04_SecondCleaningAndMilling_LoadMode_BRAN = 4;
        }

        public static class SSDestinationSlots
        {
            public static class C04_SecondCleaningAndMilling
            {
                public const int F1 = 5; //lunghezza coda destinazione SCADA
                public const int F2 = 5;
                public const int F3 = 5;
                public const int BRAN = 5;
            }
        }

        public static class SSRecipeParamRange
        {
            public const int R4_min_set_point = 1;
            public const int R4_max_set_point = 1000;
        }

        public static class SSMaxRepeatOfIngredients
        {
        }

        public static class SSMaxNumOfIngredients
        {
            public static int C01_Intake = 1;
            public static int C02_FirstCleaning = 5;
            public static int C03_SecondTempering = 5;
            public static int C04_SecondCleaningAndMilling = 4;
            public static int C04_SecondCleaningAndMilling_SET_POINT = 6;
            public static int C06_FlourTransportRV501 = 5;
            public static int C07_FlourTransportRV502 = 5;
            public static int C08_FlourBaggingBS501 = 2;
            public static int C09_FlourBaggingBS502 = 2;
            public static int C10_BranRecycle = 5;
            public static int C11_FlourBaggingBS601 = 1;
            public static int C12_ScreeningsGrinding = 1;
            public static int C13_ScreeningsTransport = 1;
            public static int C14_FlourBaggingBS503 = 2;
        }

        public static class SSMaxNumOfSourcesPerIngredient
        {
            public static int C01_Intake = 1;
            public static int C02_FirstCleaning = 5;
            public static int C03_SecondTempering = 5;
            public static int C04_SecondCleaningAndMilling = 4;
            public static int C06_FlourTransportRV501 = 5;
            public static int C07_FlourTransportRV502 = 5;
            public static int C08_FlourBaggingBS501 = 2;
            public static int C09_FlourBaggingBS502 = 2;
            public static int C10_BranRecycle = 5;
            public static int C11_FlourBaggingBS601 = 1;
            public static int C12_ScreeningsGrinding = 1;
            public static int C13_ScreeningsTransport = 1;
            public static int C14_FlourBaggingBS503 = 2;
        }

        public static class SSObjectPhysicalLimitsKg
        {
        }

        public static class SSMetaOperationParams
        {
        }

        public static class SSOperationParamValues
        {
        }

        public enum SSLotTypes
        {
            H = 1
        }

        public enum SSFlowFamilies
        {
            Screenings = 1
        }

        public enum SSFlows
        {
            Screenings = 1
        }

        public enum SSScales
        {
        }

        public enum SSYieldsReports
        {
        }

        public enum SSMetaRecipe
        {
            None = 1,
            R2_FirstCleaning = 2,
            R3_SecondTempering = 3,
            R4_SecondCleaningAndMilling = 4,
        }

        public enum SSPercUIDM
        {
            MinDM201 = 5,
            MaxDM201 = 19,
            MinDM202 = 5,
            MaxDM202 = 19,
        }

        public enum SSPercUFDM
        {
            MinDM201 = 5,
            MaxDM201 = 19,
            MinDM202 = 5,
            MaxDM202 = 19,
        }
    }
}