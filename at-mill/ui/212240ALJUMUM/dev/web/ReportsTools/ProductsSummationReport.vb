﻿Option Strict On

Imports UsersGUI
Imports ReportsTools.myReports
Imports CommonDefines.Defines

Public Class ProductsSummationReport

#Region "Configuration"

    Public Class ProductsSummationConfig

        Public Shared FromCycleId As New Dictionary(Of SSCycles, ReportFlowReference)

        Public Shared ProductsFromCycleId As New Dictionary(Of SSCycles, ReportFlowReference)

        Public Shared FromB1 As New ReportFlowReference()

        Public Shared Products As New ReportFlowReference()

        Public Shared DecimalPlaces As Integer = 3  ' Relative to values in tons
    End Class

#End Region

#Region "Constants and classes"

    Private Class ProductSum
        Public Property ProductID As Integer = m_InvalidInteger
        Public Property ProductName As String = String.Empty
        Public Property Sum As Double = 0

        Public Function GetNameObject() As String
            Return "{""id"": """ & Me.ProductID & """, ""val"": """ & Me.ProductName & """}"
        End Function

        Public Function GetValueObject() As String
            Return "{""id"": """ & Me.ProductID & """, ""val"": " & Tools.ConvertValueToJs(Math.Round(Me.Sum, ProductsSummationConfig.DecimalPlaces)) & "}"
        End Function

    End Class

#End Region

#Region "Methods"

    Public Shared Function GetProductsSummationScript(ByVal mConfig As UsersGUI.config, ByVal dateStart As DateTime, ByVal dateStop As DateTime, ByVal currHour As Boolean, ByVal isRefresh As Boolean, ByVal FromIdCycle As Integer) As String
        Dim script As String = String.Empty

        Try
            ' Handle current hour datetimes
            If currHour Then
                dateStart = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                dateStop = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, Now.Minute, Now.Second)
            End If

            For Each kvp As KeyValuePair(Of SSCycles, ReportFlowReference) In ProductsSummationConfig.FromCycleId
                Dim IdCycle As Integer = kvp.Key
                Dim Flows As ReportFlowReference = kvp.Value

                Dim IdProductCycles As Integer = 0
                Dim ProductsFlows As ReportFlowReference = Nothing

                If IdCycle <> FromIdCycle Then
                    Continue For
                End If

                For Each kProdvp As KeyValuePair(Of SSCycles, ReportFlowReference) In ProductsSummationConfig.ProductsFromCycleId

                    If kProdvp.Key <> FromIdCycle Then
                        Continue For
                    End If

                    IdProductCycles = kProdvp.Key
                    ProductsFlows = kProdvp.Value

                Next

                ' Retrieve B1 weight
                Dim B1 As Double = 0
                Dim query As String = "SELECT SUM(WEIGHT) AS B1 FROM FLOW_LOGS INNER JOIN FLOWS ON FLOW_LOGS.FLO_ID = FLOWS.ID" &
                " WHERE FLOW_LOGS.START_TIME >= " & UsersGUI.tools.PrepareDateForSQL(dateStart) & If(Not currHour, " AND FLOW_LOGS.STOP_TIME <= " & UsersGUI.tools.PrepareDateForSQL(dateStop), "") &
                    " AND (FLOW_LOGS.FLO_ID IN (" & If(Flows.FlowIds IsNot Nothing AndAlso Flows.FlowIds.Length > 0, String.Join(",", Flows.FlowIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
                    " OR FLOWS.FFL_ID IN(" & If(Flows.FlowFamilyIds IsNot Nothing AndAlso Flows.FlowFamilyIds.Length > 0, String.Join(",", Flows.FlowFamilyIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & "))"
                Dim result As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                If result.Rows.Count > 0 Then
                    B1 = If(Not IsDBNull(result.Rows(0).Item("B1")), Double.Parse(result.Rows(0).Item("B1").ToString()) / 1000, 0)
                End If

                ' Retrieve products weights, grouped by PRO_ID
                Dim products As New List(Of ProductSum)
                query = "SELECT PRODUCTS.ID AS PRODUCT_ID, PRODUCTS.NAME AS PRODUCT_NAME, SUM(WEIGHT) AS PRODUCT_SUM" &
                " FROM FLOW_LOGS INNER JOIN FLOWS ON FLOW_LOGS.FLO_ID = FLOWS.ID JOIN PRODUCTS ON FLOW_LOGS.PRO_ID = PRODUCTS.ID" &
                " WHERE FLOW_LOGS.START_TIME >= " & UsersGUI.tools.PrepareDateForSQL(dateStart) & If(Not currHour, " AND FLOW_LOGS.STOP_TIME <= " & UsersGUI.tools.PrepareDateForSQL(dateStop), "") &
                    " AND (FLOW_LOGS.FLO_ID IN (" & If(ProductsFlows.FlowIds IsNot Nothing AndAlso ProductsFlows.FlowIds.Length > 0, String.Join(",", ProductsFlows.FlowIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
                    " OR FLOWS.FFL_ID IN(" & If(ProductsFlows.FlowFamilyIds IsNot Nothing AndAlso ProductsFlows.FlowFamilyIds.Length > 0, String.Join(",", ProductsFlows.FlowFamilyIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & "))" &
                " GROUP BY PRODUCTS.ID, PRODUCTS.NAME" &
                " ORDER BY PRODUCTS.NAME"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                script = CreateJsScript(mConfig, dateStart, dateStop, result, products, isRefresh, B1, CType(IdCycle, SSCycles))

            Next
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Private Shared Function CreateJsScript(ByVal mConfig As UsersGUI.config, ByVal dateStart As DateTime, ByVal dateStop As DateTime, result As DataTable, products As List(Of ProductSum), ByVal isRefresh As Boolean, B1 As Double, cycleid As SSCycles) As String
        Dim script As String = String.Empty

        For Each row As Data.DataRow In result.Rows
            products.Add(New ProductSum() With {
            .ProductID = CInt(row.Item("PRODUCT_ID")),
            .ProductName = row.Item("PRODUCT_NAME").ToString,
            .Sum = If(IsDBNull(row.Item("PRODUCT_SUM")), 0, Double.Parse(row.Item("PRODUCT_SUM").ToString()) / 1000)
        })
        Next

        ' Translate to JSON
        If Not isRefresh Then
            script &= "nameJSON = {"
            script &= """top"": [" &
                "{""id"": ""B1"", ""val"": """ & myReports.GetTranslationForJSON("B1", mConfig) & """}" &
            "],"
            script &= """product"": [" &
                "{""name"": ""tb_a"", " &
                " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(Of ProductSum, String)(products.ToArray(), Function(x) x.GetNameObject())) &
                    "]}," &
                "{""name"": ""tot""," &
                " ""sum"": [""tb_a""], ""trad"": """ & myReports.GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
            "]"
            script &= "};"
            script &= "sessionStorage.setItem(""nameJSON"", JSON.stringify(nameJSON));" &
            "initializeReport( " & GetIdReport(cycleid) & ", " & EnumTypeReport.ProductsSummationReport & ");"
        Else
            script &= "valueJSON = {"
            script &= """top"": [" &
                    "{""id"": ""B1"", ""val"": " & Tools.ConvertValueToJs(Math.Round(B1, 3)) & "}," &
                    "{""id"": ""dateFrom"", ""val"": """ & dateStart.ToString(mConfig.GetLanguage().FormatDateTime) & """}," &
                    "{""id"": ""dateTo"", ""val"": """ & dateStop.ToString(mConfig.GetLanguage().FormatDateTime) & """}" &
                "],"
            script &= """product"": [" &
                    "{""name"": ""tb_a"", " &
                    " ""elem"": [" &
                            String.Join(",", Array.ConvertAll(Of ProductSum, String)(products.ToArray(), Function(x) x.GetValueObject())) &
                        "]}" &
                "]"
            script &= "};"
            script &= "sessionStorage.setItem(""valueJSON"", JSON.stringify(valueJSON));" &
                "updateReport();"
        End If

        Return script
    End Function

    Public Shared Function GetProductsSummationScript(ByVal mConfig As UsersGUI.config, ByVal dateStart As DateTime, ByVal dateStop As DateTime, ByVal currHour As Boolean, ByVal isRefresh As Boolean) As String
        Dim script As String = String.Empty

        Try
            ' Handle current hour datetimes
            If currHour Then
                dateStart = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, 0, 0)
                dateStop = New DateTime(Now.Year, Now.Month, Now.Day, Now.Hour, Now.Minute, Now.Second)
            End If

            ' Retrieve B1 weight
            Dim B1 As Double = 0
            Dim query As String = "SELECT SUM(WEIGHT) AS B1 FROM FLOW_LOGS INNER JOIN FLOWS ON FLOW_LOGS.FLO_ID = FLOWS.ID" &
            " WHERE FLOW_LOGS.START_TIME >= " & UsersGUI.tools.PrepareDateForSQL(dateStart) & If(Not currHour, " AND FLOW_LOGS.STOP_TIME <= " & UsersGUI.tools.PrepareDateForSQL(dateStop), "") &
            " AND (FLOW_LOGS.FLO_ID IN (" & If(ProductsSummationConfig.FromB1.FlowIds IsNot Nothing AndAlso ProductsSummationConfig.FromB1.FlowIds.Length > 0, String.Join(",", ProductsSummationConfig.FromB1.FlowIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
            " OR FLOWS.FFL_ID IN(" & If(ProductsSummationConfig.FromB1.FlowFamilyIds IsNot Nothing AndAlso ProductsSummationConfig.FromB1.FlowFamilyIds.Length > 0, String.Join(",", ProductsSummationConfig.FromB1.FlowFamilyIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & "))"
            Dim result As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            If result.Rows.Count > 0 Then
                B1 = If(Not IsDBNull(result.Rows(0).Item("B1")), Double.Parse(result.Rows(0).Item("B1").ToString()) / 1000, 0)
            End If

            ' Retrieve products weights, grouped by PRO_ID
            Dim products As New List(Of ProductSum)
            query = "SELECT PRODUCTS.ID AS PRODUCT_ID, PRODUCTS.NAME AS PRODUCT_NAME, SUM(WEIGHT) AS PRODUCT_SUM" &
            " FROM FLOW_LOGS INNER JOIN FLOWS ON FLOW_LOGS.FLO_ID = FLOWS.ID JOIN PRODUCTS ON FLOW_LOGS.PRO_ID = PRODUCTS.ID" &
            " WHERE FLOW_LOGS.START_TIME >= " & UsersGUI.tools.PrepareDateForSQL(dateStart) & If(Not currHour, " AND FLOW_LOGS.STOP_TIME <= " & UsersGUI.tools.PrepareDateForSQL(dateStop), "") &
            " AND (FLOW_LOGS.FLO_ID IN (" & If(ProductsSummationConfig.Products.FlowIds IsNot Nothing AndAlso ProductsSummationConfig.Products.FlowIds.Length > 0, String.Join(",", ProductsSummationConfig.Products.FlowIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
            " OR FLOWS.FFL_ID IN(" & If(ProductsSummationConfig.Products.FlowFamilyIds IsNot Nothing AndAlso ProductsSummationConfig.Products.FlowFamilyIds.Length > 0, String.Join(",", ProductsSummationConfig.Products.FlowFamilyIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & "))" &
            " GROUP BY PRODUCTS.ID, PRODUCTS.NAME" &
            " ORDER BY PRODUCTS.NAME"
            result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            script = CreateJsScript(mConfig, dateStart, dateStop, result, products, isRefresh, B1, 0)
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Private Shared Function GetIdReport(CycleId As SSCycles) As String
        Dim IdReport As String = "9" 'Report standard per PRODUCTS_SUMMATION

        'Creare le condizioni nel caso in cui sia necessario chiamamre una configurazione specifica per questo report

        Return IdReport
    End Function

#End Region

End Class