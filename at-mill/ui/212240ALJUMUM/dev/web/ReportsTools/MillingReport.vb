﻿Option Strict On

Imports UsersGUI
Imports ReportsTools.myReports
Imports CommonDefines.Defines

Public Class MillingReport

#Region "Configuration"

    Public Class MillingConfig
        Public Shared B1FlowsReference As New ReportFlowReference()

        Public Shared InputFlowsReference As New ReportFlowReference()

        Public Shared ProductsFlowFamilies As New List(Of FlowFamilyCycleId)()

        Public Shared SubProductsFlowFamilies As New List(Of FlowFamilyCycleId)()

        Public Shared FlowFamilyTranslations As New Dictionary(Of SSFlowFamilies, FlowFamilyTranslationsGroup)
    End Class

#End Region

#Region "Constants and classes"

    Public Class FlowFamilyTranslationsGroup
        Public CycleId As Long = 0
        Public Description As String = String.Empty
    End Class

    Public Class FlowFamilyCycleId
        Public CycleId As Long = 0
        Public FlowFamilyId As Integer = 0
    End Class

    Private Class MillingData
        Property SourceIds As String = String.Empty
        Property Source As String = String.Empty
        Property Destinations As String = String.Empty
        Property ProductIds As String = String.Empty
        Property Products As String = String.Empty
        Property FlowFamilyId As Integer = m_InvalidInteger
        Property WG As String = String.Empty
        Property Weight As Double = m_InvalidDblValue

        Public Function InputToNameJSON() As String
            Return "{ ""id"": ""P" & Me.SourceIds & """, ""val"": """ & Me.WG & """ }"
        End Function

        Public Function OutputToNameJSON() As String
            Return "{ ""id"": ""P" & Me.ProductIds & """, ""val"": """ & Me.WG & """ }"
        End Function

        Public Function InputToValueJSON() As String
            Return "{ ""id"": ""P" & Me.SourceIds & """, ""val"": [""" & Me.Products & """, """ & Me.Source & """," & Tools.ConvertValueToJs(Me.Weight) & "] }"
        End Function

        Public Function OutputToValueJSON() As String
            Return "{ ""id"": ""P" & Me.ProductIds & """, ""val"": [" & If(Me.Products <> String.Empty, """" & Me.Products & """", "null") & ", " & If(Me.Destinations <> String.Empty, """" & Me.Destinations & """", "null") & "," & Tools.ConvertValueToJs(Me.Weight) & "]}"
        End Function

    End Class

#End Region

#Region "Methods"

    Public Shared Function GetMillingScript(ByVal mConfig As UsersGUI.config, ByVal jobId As Integer) As String
        Dim script As String = String.Empty

        Try
            Dim dateFrom As Date
            Dim dateTo As Date
            Dim productDescription As String
            Dim productId As Integer
            Dim recipeDescription As String = String.Empty
            Dim jobTotal As Double

            Dim cycleId As SSCycles
            Dim recipeLogId As Integer = m_InvalidInteger

            Dim b1 As Double
            Dim emtSeconds As Long
            Dim dailyPlantCapacity As Integer

            Dim query As String
            Dim result As DataTable

            Dim inputData As New List(Of MillingData)
            Dim productsData As New List(Of MillingData)
            Dim subProductsData As New List(Of MillingData)

            Dim currData As MillingData

            ' 0 - Get info about the job
            query = "SELECT CYC_ID, RECIPE_LOG_ID, PRODUCED_AMOUNT, RECIPE_DESCRIPTION, PRO_ID, PRODUCTS.NAME AS PRODUCT_NAME, START_DATE, STOP_DATE, WORKING_TIME FROM PRODUCTION_REPORTS INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID WHERE COUNTER = " & jobId
            result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            If result.Rows.Count > 0 Then
                cycleId = CType(result.Rows(0).Item("CYC_ID"), SSCycles)
                recipeLogId = If(IsDBNull(result.Rows(0).Item("RECIPE_LOG_ID")), m_InvalidInteger, CInt(result.Rows(0).Item("RECIPE_LOG_ID")))
                recipeDescription = myReports.ParseTranslationForJSON(result.Rows(0).Item("RECIPE_DESCRIPTION").ToString())
                jobTotal = Double.Parse(result.Rows(0).Item("PRODUCED_AMOUNT").ToString())
                productDescription = myReports.ParseTranslationForJSON(result.Rows(0).Item("PRODUCT_NAME").ToString())
                productId = CInt(result.Rows(0).Item("PRO_ID"))
                emtSeconds = CInt(result.Rows(0).Item("WORKING_TIME"))
                dateFrom = CDate(result.Rows(0).Item("START_DATE"))
                dateTo = CDate(result.Rows(0).Item("STOP_DATE"))

                dailyPlantCapacity = YieldsConfig.GetPlantCapacityByCycleId(cycleId)

                ' 1 - Initialize productsData and subProductsData with the FFL_IDs in the configuration (so that all WGs are always shown, with a weight of 0 if no flows are present)
                For Each flow_family_cycle_id As FlowFamilyCycleId In MillingConfig.ProductsFlowFamilies
                    If (flow_family_cycle_id.CycleId = cycleId) Then
                        productsData.Add(New MillingData() With {
                        .Weight = 0,
                        .FlowFamilyId = flow_family_cycle_id.FlowFamilyId,
                        .WG = GetTranslationForJSON(MillingConfig.FlowFamilyTranslations(CType(flow_family_cycle_id.FlowFamilyId, SSFlowFamilies)).Description, mConfig)})

                    End If
                Next

                For Each flow_family_cycle_id As FlowFamilyCycleId In MillingConfig.SubProductsFlowFamilies
                    If (flow_family_cycle_id.CycleId = cycleId) Then
                        subProductsData.Add(New MillingData() With {
                    .Weight = 0,
                    .FlowFamilyId = flow_family_cycle_id.FlowFamilyId,
                    .WG = GetTranslationForJSON(MillingConfig.FlowFamilyTranslations(CType(flow_family_cycle_id.FlowFamilyId, SSFlowFamilies)).Description, mConfig)
                })
                    End If
                Next

                ' 2 - Analyze flows
                ' 2a -  Entering the mill
                query = "SELECT FFL_ID, STRING_AGG(SOURCE_CELL, '') AS SOURCE_CELL, STRING_AGG(DESCR_SOURCECELL, ',') WITHIN GROUP (ORDER BY DESCR_SOURCECELL) AS CELL_DESCRIPTION, SUM(WEIGHT) AS WEIGHT, STRING_AGG(NAME, ',') AS PRODUCT_NAMES FROM VIEW_FLOW_LOGS" &
                    " WHERE COUNTER = " & jobId & " AND" &
                    " (" &
                        "FFL_ID IN (" & If(MillingConfig.InputFlowsReference.FlowFamilyIds IsNot Nothing AndAlso MillingConfig.InputFlowsReference.FlowFamilyIds.Length > 0, String.Join(",", MillingConfig.InputFlowsReference.FlowFamilyIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
                        " OR FLO_ID IN (" & If(MillingConfig.InputFlowsReference.FlowIds IsNot Nothing, String.Join(",", MillingConfig.InputFlowsReference.FlowIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
                    ")" &
                    " GROUP BY FFL_ID" &
                    " ORDER BY SUM(WEIGHT) DESC"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                For Each row As DataRow In result.Rows
                    inputData.Add(New MillingData() With {
                        .Products = String.Join(", ", row.Item("PRODUCT_NAMES").ToString().Split(","c).Distinct()),
                        .SourceIds = row.Item("SOURCE_CELL").ToString,
                        .Source = String.Join(", ", row.Item("CELL_DESCRIPTION").ToString().Split(","c).Distinct()),
                        .WG = GetTranslationForJSON(MillingConfig.FlowFamilyTranslations(CType(row.Item("FFL_ID"), SSFlowFamilies)).Description, mConfig),
                        .Weight = CDbl(row.Item("WEIGHT").ToString()) / 1000 ' kgs -> tons
                    })
                Next

                ' 2b -  B1
                query = "SELECT SUM(WEIGHT) AS WEIGHT FROM VIEW_FLOW_LOGS" &
                    " WHERE COUNTER = " & jobId & " AND" &
                    " (" &
                        "FFL_ID IN (" & If(MillingConfig.B1FlowsReference.FlowFamilyIds IsNot Nothing AndAlso MillingConfig.B1FlowsReference.FlowFamilyIds.Length > 0, String.Join(",", MillingConfig.B1FlowsReference.FlowFamilyIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
                        " OR FLO_ID IN (" & If(MillingConfig.B1FlowsReference.FlowIds IsNot Nothing, String.Join(",", MillingConfig.B1FlowsReference.FlowIds.Cast(Of Integer)), m_InvalidInteger.ToString()) & ")" &
                    ")"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                ' SUM(WEIGHT) always returns a row (either null or not)
                If Not IsDBNull(result.Rows(0).Item("WEIGHT")) Then
                    b1 = Double.Parse(result.Rows(0).Item("WEIGHT").ToString()) / 1000 ' kgs -> tons
                End If

                ' 2c -  Exiting the mill (products)
                query = "SELECT FFL_ID, STRING_AGG(PRO_ID, ',') AS PRO_ID, STRING_AGG(NAME, ',') AS PRODUCT_NAMES, SUM(WEIGHT) AS WEIGHT, STRING_AGG(DESCR_DESTCELL, ',') WITHIN GROUP (ORDER BY DESCR_DESTCELL) AS DESTINATIONS FROM VIEW_FLOW_LOGS" &
                " WHERE COUNTER = " & jobId & " AND" &
                " FFL_ID IN (" & If(MillingConfig.ProductsFlowFamilies IsNot Nothing AndAlso MillingConfig.ProductsFlowFamilies.Count > 0, String.Join(",", MillingConfig.ProductsFlowFamilies.Cast(Of FlowFamilyCycleId).ToList().Select(Function(FlowFamilyCycleId) FlowFamilyCycleId.FlowFamilyId)), m_InvalidInteger.ToString()) & ")" &
                " GROUP BY FFL_ID" &
                " ORDER BY DESTINATIONS ASC"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                For Each row As DataRow In result.Rows
                    currData = productsData.Find(Function(x) x.FlowFamilyId = CInt(row.Item("FFL_ID")))

                    If currData IsNot Nothing Then
                        currData.ProductIds = row.Item("PRO_ID").ToString
                        currData.Products = String.Join(", ", row.Item("PRODUCT_NAMES").ToString().Split(","c).Distinct())
                        currData.Destinations = String.Join(", ", row.Item("DESTINATIONS").ToString().Split(","c).Distinct())
                        currData.Weight = CDbl(row.Item("WEIGHT").ToString()) / 1000 ' kgs -> tons
                    End If
                Next

                ' 2d -  Exiting the mill (sub-products)
                query = "SELECT FFL_ID, STRING_AGG(PRO_ID, ',') AS PRO_ID, STRING_AGG(NAME, ',') AS PRODUCT_NAMES, SUM(WEIGHT) AS WEIGHT, STRING_AGG(DESCR_DESTCELL, ',') WITHIN GROUP (ORDER BY DESCR_DESTCELL) AS DESTINATIONS FROM VIEW_FLOW_LOGS" &
                    " WHERE COUNTER = " & jobId & " AND" &
                    " FFL_ID IN (" & If(MillingConfig.SubProductsFlowFamilies IsNot Nothing AndAlso MillingConfig.SubProductsFlowFamilies.Count > 0, String.Join(",", MillingConfig.SubProductsFlowFamilies.Cast(Of FlowFamilyCycleId).ToList().Select(Function(FlowFamilyCycleId) FlowFamilyCycleId.FlowFamilyId)), m_InvalidInteger.ToString()) & ")" &
                    " GROUP BY FFL_ID" &
                    " ORDER BY DESTINATIONS ASC"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                For Each row As DataRow In result.Rows
                    currData = subProductsData.Find(Function(x) x.FlowFamilyId = CInt(row.Item("FFL_ID")))

                    If currData IsNot Nothing Then
                        currData.ProductIds = row.Item("PRO_ID").ToString
                        currData.Products = String.Join(", ", row.Item("PRODUCT_NAMES").ToString().Split(","c).Distinct())
                        currData.Destinations = String.Join(", ", row.Item("DESTINATIONS").ToString().Split(","c).Distinct())
                        currData.Weight = CDbl(row.Item("WEIGHT").ToString()) / 1000 ' kgs -> tons
                    End If
                Next

                ' 4 - Translate to JSON
                script &= "nameJSON = {"
                script &= """top"": [" &
                            "{ ""id"": ""productDescription"", ""val"": """ & GetTranslationForJSON("Product", mConfig) & """ }," &
                            "{ ""id"": ""recipeDescription"", ""val"": """ & GetTranslationForJSON("Recipe description", mConfig) & """ }," &
                            "{ ""id"": ""jobTotal"", ""val"": """ & GetTranslationForJSON("JOB_TOTAL", mConfig) & """ }," &
                            "{ ""id"": ""lot"", ""val"": """ & GetTranslationForJSON("Lotto", mConfig) & """ }," &
                            "{ ""id"": ""cycle"", ""val"": """ & GetTranslationForJSON("CYCLE_NAME", mConfig) & """ }" &
                        "],"
                script &= """product"": [" &
                "{""name"": ""tb_a"", " &
                " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(inputData.ToArray(), Function(v) v.InputToNameJSON())) &
                    "]}," &
                "{""name"": ""tot_a""," &
                " ""sum"": [""tb_a""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}," &
                "{""name"": ""tb_b"", " &
                " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(productsData.ToArray(), Function(v) v.OutputToNameJSON())) &
                    "]}," &
                "{""name"": ""tot_b""," &
                " ""sum"": [""tb_b""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT_PRODUCT", mConfig) & """, ""style"": ""sub_total""}," &
                "{""name"": ""tb_c"", " &
                " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(subProductsData.ToArray(), Function(v) v.OutputToNameJSON())) &
                    "]}," &
                "{""name"": ""tot_c""," &
                " ""sum"": [""tb_c""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT_BY_PRODUCTS", mConfig) & """, ""style"": ""sub_total""}," &
                "{""name"": ""tot""," &
                " ""sum"": [""tb_a"", ""tb_b"", ""tb_c""], ""trad"": """ & GetTranslationForJSON("YIELDS_TOT", mConfig) & """, ""style"": ""total""}" &
            "]"
                script &= "};"
                script &= "sessionStorage.setItem(""nameJSON"", JSON.stringify(nameJSON));" &
                "initializeReport( " & GetIdReport(cycleId) & ", " & EnumTypeReport.MillingReport & ");"

                script &= "valueJSON = {"
                script &= """top"": [" &
                    "{ ""id"": ""job"", ""val"": " & jobId & " }," &
                    "{ ""id"": ""dateFrom"", ""val"": """ & dateFrom.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                    "{ ""id"": ""dateTo"", ""val"": """ & dateTo.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                    "{ ""id"": ""productDescription"", ""val"": """ & productDescription & """ }," &
                    "{ ""id"": ""recipeDescription"", ""val"": " & If(recipeLogId.Equals(m_InvalidInteger) OrElse recipeDescription.Trim().Equals(String.Empty), "null", """" & recipeDescription & """") & " }," &
                    "{ ""id"": ""jobTotal"", ""val"": " & Tools.ConvertValueToJs(Math.Round(jobTotal / 1000, 3)) & " }," &
                    "{ ""id"": ""cycle"", ""val"": """ & GetTranslationForJSON("MAIN_CYCLE_" & CInt(cycleId) & "_TITLE", mConfig) & """ }," &
                    "{ ""id"": ""EMT"", ""val"": """ & Tools.ConvertValueToJs(emtSeconds) & """}," &
                    "{ ""id"": ""B1"", ""val"": """ & Math.Round(b1, 2).ToString().Replace(",", ".") & """}," &
                    "{ ""id"": ""plant_capacity_day"", ""val"": " & Tools.ConvertValueToJs(dailyPlantCapacity) & "}," &
                    "{ ""id"": ""lot"", ""val"": " & GetLotValueLinkForJob(cycleId, jobId) & "}" &
                "],"
                script &= """product"": [" &
                    "{""name"": ""tb_a"", " &
                    " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(inputData.ToArray(), Function(v) v.InputToValueJSON())) &
                    "]}," &
                    "{""name"": ""tb_b"", " &
                    " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(productsData.ToArray(), Function(v) v.OutputToValueJSON())) &
                    "]}," &
                    "{""name"": ""tb_c"", " &
                    " ""elem"": [" &
                        String.Join(",", Array.ConvertAll(subProductsData.ToArray(), Function(v) v.OutputToValueJSON())) &
                    "]}" &
                "]"
                script &= "};"
                script &= "sessionStorage.setItem(""valueJSON"", JSON.stringify(valueJSON));" &
                "updateReport();"
            End If
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Private Shared Function GetIdReport(CycleId As SSCycles) As String
        Dim IdReport As String = "14" 'Report standard per MILLING

        'Creare le condizioni nel caso in cui sia necessario chiamamre una configurazione specifica per questo report

        Return IdReport
    End Function

#End Region

End Class