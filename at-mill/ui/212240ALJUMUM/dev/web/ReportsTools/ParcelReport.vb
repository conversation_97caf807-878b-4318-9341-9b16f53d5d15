﻿Option Strict On

Imports UsersGUI
Imports System.Text.RegularExpressions
Imports ReportsTools.myReports
Imports CommonDefines.Defines

Public Class ParcelReport

    Private Shared supplier As String = String.Empty
    Private Shared carrier_in As String = String.Empty

#Region "Configuration"

    Public Class ParcelConfig

    End Class

#End Region

#Region "Constants and classes"

    Private Class ParcelData
        Property ProductId As Integer
        Property Product As String
        Property Weight As Double

    End Class

#End Region

#Region "Methods"

    Public Shared Function GetParcelScript(ByVal mConfig As UsersGUI.config, ByVal jobId As Integer) As String
        Dim script As String = String.Empty

        Try
            Dim parcelDate As Date
            Dim recipeDescription As String = String.Empty
            Dim jobTotal As Double

            Dim cycleId As SSCycles
            Dim recipeLogId As Integer = m_InvalidInteger

            Dim parcelData As New List(Of ParcelData)

            ' update according to changes to tables SUPPLIERS and CARRIERS
            Dim parcelOrigin As String = String.Empty
            Dim carrier As String = String.Empty

            Dim query As String
            Dim result As DataTable

            ' Recover data
            ' 1 - Get info about the job
            query = "SELECT CYC_ID, RECIPE_LOG_ID, PRODUCED_AMOUNT, RECIPE_DESCRIPTION, PRO_ID, PRODUCTS.NAME AS PRODUCT_NAME, START_DATE, STOP_DATE FROM PRODUCTION_REPORTS INNER JOIN PRODUCTS ON PRO_ID = PRODUCTS.ID WHERE COUNTER = " & jobId
            result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

            If result.Rows.Count > 0 Then
                cycleId = CType(result.Rows(0).Item("CYC_ID"), SSCycles)
                recipeLogId = If(IsDBNull(result.Rows(0).Item("RECIPE_LOG_ID")), m_InvalidInteger, CInt(result.Rows(0).Item("RECIPE_LOG_ID")))
                recipeDescription = result.Rows(0).Item("RECIPE_DESCRIPTION").ToString()
                jobTotal = Double.Parse(result.Rows(0).Item("PRODUCED_AMOUNT").ToString())
                parcelDate = CDate(result.Rows(0).Item("STOP_DATE"))

                ' 2 - Analyze flows
                query = "SELECT FLOW_LOGS.PRO_ID, PRODUCTS.NAME AS PRODUCT_NAME, SUM(WEIGHT) AS WEIGHT" &
                    " FROM FLOW_LOGS" &
                        " INNER JOIN PRODUCTS ON FLOW_LOGS.PRO_ID = PRODUCTS.ID" &
                    " WHERE FLOW_LOGS.COUNTER = " & jobId &
                    " GROUP BY FLOW_LOGS.PRO_ID, PRODUCTS.NAME"
                result = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(query, False)

                Dim singleData As ParcelData

                For Each row As DataRow In result.Rows
                    ' 2a - Save data from flows for the product
                    singleData = New ParcelData() With {
                    .ProductId = CInt(row.Item("PRO_ID")),
                    .Product = row.Item("PRODUCT_NAME").ToString(),
                    .Weight = Double.Parse(row.Item("WEIGHT").ToString()) / 1000      ' kgs => tons
                }

                    parcelData.Add(singleData)
                Next

                ' 3 - Recupero dati anagrafici
                Tools.GetIntakeData(jobId, supplier, carrier_in)

                ' 4 - Translate to JSON
                script &= "nameJSON = {"
                script &= """top"": [" &
                            "{ ""id"": ""jobTotal"", ""val"": """ & GetTranslationForJSON("JOB_TOTAL", mConfig) & """ }," &
                            "{ ""id"": ""parcelOrigin"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("PARCEL_ORIGIN", mConfig) & """ }," &
                            "{ ""id"": ""parcelDate"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("PARCEL_DATE", mConfig) & """ }," &
                            "{ ""id"": ""productDescription"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Product", mConfig) & """ }," &
                            "{ ""id"": ""lot"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Lotto", mConfig) & """ }," &
                            "{ ""id"": ""carrier"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Carrier", mConfig) & """ }" &
                        "],"

                script &= """intake"": ["
                If (supplier <> String.Empty) Then
                    script &= "{ ""id"": ""titleintake"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Intake", mConfig).ToUpper() & "<hr>"" },"
                    script &= "{ ""id"": ""intakeOrigin"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Supplier", mConfig) & ":"" },"
                    script &= "{ ""id"": ""carrier"", ""val"": """ & ReportsTools.myReports.GetTranslationForJSON("Carrier", mConfig) & ":"" }"
                Else
                    script &= "{ ""id"": ""titleintake"", ""val"": """ & String.Empty & """ },"
                    script &= "{ ""id"": ""intakeOrigin"", ""val"": """ & String.Empty & """ },"
                    script &= "{ ""id"": ""carrier"", ""val"": """ & String.Empty & """ }"
                End If
                script &= "],"

                script &= "};"
                script &= "sessionStorage.setItem(""nameJSON"", JSON.stringify(nameJSON));" &
                "initializeReport( " & GetIdReport(cycleId) & ", " & EnumTypeReport.ParcelReport & ");"

                script &= "valueJSON = {"
                script &= """top"": [" &
                    "{ ""id"": ""job"", ""val"": " & jobId & " }," &
                    "{ ""id"": ""parcelDate"", ""val"": """ & parcelDate.ToString(mConfig.GetLanguage().FormatDateTime) & """ }," &
                    "{ ""id"": ""recipeDescription"", ""val"": " & If(recipeLogId.Equals(m_InvalidInteger) OrElse recipeDescription.Trim().Equals(String.Empty), "null", """" & recipeDescription & """") & " }," &
                    "{ ""id"": ""jobTotal"", ""val"": " & If(parcelData.Count > 0, Tools.ConvertValueToJs(Math.Round(jobTotal / 1000, 3)), "null") & " }," &
                    "{ ""id"": ""productDescription"", ""val"": " & If(parcelData.Count > 0, """" & myReports.ParseTranslationForJSON(parcelData(0).Product) & """", "null") & " }," &
                    "{ ""id"": ""lot"", ""val"": " & GetLotValueLinkForJob(cycleId, jobId) & " }," &
                    "{ ""id"": ""parcelOrigin"", ""val"": " & If(parcelOrigin <> String.Empty, """" & parcelOrigin & """", "null") & " }," &
                    "{ ""id"": ""carrier"", ""val"": " & If(carrier <> String.Empty, """" & carrier & """", "null") & " }" &
                "],"

                'Intake
                script &= """intake"": ["
                If (supplier <> String.Empty) Then
                    script &= "{ ""id"": ""intakeOrigin"", ""val"": " & If(supplier <> String.Empty, """" & supplier & """", "null") & " }," &
                    "{ ""id"": ""carrier"", ""val"": " & If(carrier_in <> String.Empty, """" & carrier_in & """", "null") & " }"
                Else
                    script &= "{ ""id"": ""intakeOrigin"", ""val"":"""" }," &
                              "{ ""id"": ""carrier"", ""val"":"""" }"
                End If
                script &= "],"

                script &= "};"
                script &= "sessionStorage.setItem(""valueJSON"", JSON.stringify(valueJSON));" &
                "updateReport();"
            End If
        Catch ex As Exception
            Dim myExc As New myException.myException(ex, System.Reflection.Assembly.GetExecutingAssembly.GetName.Name, System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name, System.Reflection.MethodBase.GetCurrentMethod().Name)
            Throw myExc
        End Try

        Return script
    End Function

    Private Shared Function GetIdReport(CycleId As SSCycles) As String
        Dim IdReport As String = "10" 'Report standard per PARCEL

        'Creare le condizioni nel caso in cui sia necessario chiamamre una configurazione specifica per questo report
        ' nothing to do

        Return IdReport
    End Function

#End Region

End Class