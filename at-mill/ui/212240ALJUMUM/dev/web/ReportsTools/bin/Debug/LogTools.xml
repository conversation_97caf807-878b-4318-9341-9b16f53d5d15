﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
LogTools
</name>
</assembly>
<members>
<member name="T:LogTools.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:LogTools.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:LogTools.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="M:LogTools.Tools.LogError(System.String[])">
 <summary>
 Logs to the exception file
 </summary>
 <param name="text"></param>
</member>
<member name="M:LogTools.Tools.LogDebug(System.String[])">
 <summary>
 Logs to the debug file if Application("Debug") = True
 </summary>
 <param name="text"></param>
</member>
<member name="M:LogTools.Tools.LogToFile(System.Collections.Generic.List{System.String},LogTools.Config.Severity)">
 <summary>
 Handles writing to file and rotating the lines in the file
 </summary>
 <param name="text"></param>
 <param name="severity"></param>
</member>
</members>
</doc>
