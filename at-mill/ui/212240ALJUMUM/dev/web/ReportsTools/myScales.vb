﻿Option Strict On

Public Class myScales
    Private m_plant_capacity_day As Integer

    Private m_intake As New myScaleDoubleVal
    Private m_B0 As New myScaleDoubleVal
    Private m_screenings As New myScaleDoubleVal
    Private m_B1 As New myScaleDoubleVal
    Private m_prod_1 As New myScaleDoubleVal
    Private m_prod_2 As New myScaleDoubleVal
    Private m_prod_3 As New myScaleDoubleVal
    Private m_prod_4 As New myScaleDoubleVal
    Private m_prod_5 As New myScaleDoubleVal
    Private m_prod_6 As New myScaleDoubleVal
    Private m_prod_7 As New myScaleDoubleVal
    Private m_prod_8 As New myScaleDoubleVal
    Private m_prod_9 As New myScaleDoubleVal
    Private m_prod_10 As New myScaleDoubleVal
    Private m_byprod_1 As New myScaleDoubleVal
    Private m_byprod_2 As New myScaleDoubleVal
    Private m_byprod_3 As New myScaleDoubleVal
    Private m_byprod_4 As New myScaleDoubleVal
    Private m_byprod_5 As New myScaleDoubleVal
    Private m_byprod_6 As New myScaleDoubleVal
    Private m_byprod_7 As New myScaleDoubleVal
    Private m_byprod_8 As New myScaleDoubleVal
    Private m_byprod_9 As New myScaleDoubleVal
    Private m_byprod_10 As New myScaleDoubleVal
    Private m_water_1 As New myScaleDoubleVal
    Private m_water_2 As New myScaleDoubleVal
    Private m_water_3 As New myScaleDoubleVal
    Private m_water_4 As New myScaleDoubleVal
    Private m_water_5 As New myScaleDoubleVal
    Private m_water_6 As New myScaleDoubleVal
    Private m_swb_1 As New myScaleDoubleVal
    Private m_swb_2 As New myScaleDoubleVal
    Private m_swb_3 As New myScaleDoubleVal
    Private m_swb_4 As New myScaleDoubleVal
    Private m_swb_5 As New myScaleDoubleVal
    Private m_swb_6 As New myScaleDoubleVal
    Private m_swb_7 As New myScaleDoubleVal
    Private m_swb_8 As New myScaleDoubleVal
    Private m_swb_9 As New myScaleDoubleVal
    Private m_swb_10 As New myScaleDoubleVal
    Private m_swb_11 As New myScaleDoubleVal
    Private m_swb_12 As New myScaleDoubleVal
    Private m_swb_13 As New myScaleDoubleVal
    Private m_swb_14 As New myScaleDoubleVal
    Private m_swb_15 As New myScaleDoubleVal
    Private m_swb_16 As New myScaleDoubleVal
    Private m_swb_17 As New myScaleDoubleVal
    Private m_swb_18 As New myScaleDoubleVal
    Private m_swb_19 As New myScaleDoubleVal
    Private m_swb_20 As New myScaleDoubleVal

    Private m_date_start As Date
    Private m_date_stop As Date
    Private m_last_read As Date

    Private m_job_id As Integer

    Private m_recipe_name As String

    Private m_emt As TimeSpan
    Private m_period_length As TimeSpan

    Public Sub New()

    End Sub

    Public Property PlantCapacityDay As Integer
        Get
            Return Me.m_plant_capacity_day
        End Get
        Set(value As Integer)
            Me.m_plant_capacity_day = value
        End Set
    End Property

    Public Property Intake As Double
        Get
            Return Me.m_intake.ValueDouble
        End Get
        Set(value As Double)
            Me.m_intake.ValueDouble = value
        End Set
    End Property

    Public Property B0 As Double
        Get
            Return Me.m_B0.ValueDouble
        End Get
        Set(value As Double)
            Me.m_B0.ValueDouble = value
        End Set
    End Property

    Public Property Screenings As Double
        Get
            Return Me.m_screenings.ValueDouble
        End Get
        Set(value As Double)
            Me.m_screenings.ValueDouble = value
        End Set
    End Property

    Public Property B1 As Double
        Get
            Return Me.m_B1.ValueDouble
        End Get
        Set(value As Double)
            Me.m_B1.ValueDouble = value
        End Set
    End Property

    Public Property Prod_1 As Double
        Get
            Return Me.m_prod_1.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_1.ValueDouble = value
        End Set
    End Property

    Public Property Prod_2 As Double
        Get
            Return Me.m_prod_2.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_2.ValueDouble = value
        End Set
    End Property

    Public Property Prod_3 As Double
        Get
            Return Me.m_prod_3.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_3.ValueDouble = value
        End Set
    End Property

    Public Property Prod_4 As Double
        Get
            Return Me.m_prod_4.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_4.ValueDouble = value
        End Set
    End Property

    Public Property Prod_5 As Double
        Get
            Return Me.m_prod_5.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_5.ValueDouble = value
        End Set
    End Property

    Public Property Prod_6 As Double
        Get
            Return Me.m_prod_6.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_6.ValueDouble = value
        End Set
    End Property

    Public Property Prod_7 As Double
        Get
            Return Me.m_prod_7.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_7.ValueDouble = value
        End Set
    End Property

    Public Property Prod_8 As Double
        Get
            Return Me.m_prod_8.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_8.ValueDouble = value
        End Set
    End Property

    Public Property Prod_9 As Double
        Get
            Return Me.m_prod_9.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_9.ValueDouble = value
        End Set
    End Property

    Public Property Prod_10 As Double
        Get
            Return Me.m_prod_10.ValueDouble
        End Get
        Set(value As Double)
            Me.m_prod_10.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_1 As Double
        Get
            Return Me.m_byprod_1.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_1.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_2 As Double
        Get
            Return Me.m_byprod_2.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_2.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_3 As Double
        Get
            Return Me.m_byprod_3.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_3.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_4 As Double
        Get
            Return Me.m_byprod_4.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_4.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_5 As Double
        Get
            Return Me.m_byprod_5.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_5.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_6 As Double
        Get
            Return Me.m_byprod_6.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_6.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_7 As Double
        Get
            Return Me.m_byprod_7.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_7.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_8 As Double
        Get
            Return Me.m_byprod_8.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_8.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_9 As Double
        Get
            Return Me.m_byprod_9.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_9.ValueDouble = value
        End Set
    End Property

    Public Property Byprod_10 As Double
        Get
            Return Me.m_byprod_10.ValueDouble
        End Get
        Set(value As Double)
            Me.m_byprod_10.ValueDouble = value
        End Set
    End Property

    Public Property Water1 As Double
        Get
            Return Me.m_water_1.ValueDouble
        End Get
        Set(value As Double)
            Me.m_water_1.ValueDouble = value
        End Set
    End Property

    Public Property Water2 As Double
        Get
            Return Me.m_water_2.ValueDouble
        End Get
        Set(value As Double)
            Me.m_water_2.ValueDouble = value
        End Set
    End Property

    Public Property Water3 As Double
        Get
            Return Me.m_water_3.ValueDouble
        End Get
        Set(value As Double)
            Me.m_water_3.ValueDouble = value
        End Set
    End Property

    Public Property Water4 As Double
        Get
            Return Me.m_water_4.ValueDouble
        End Get
        Set(value As Double)
            Me.m_water_4.ValueDouble = value
        End Set
    End Property

    Public Property Water5 As Double
        Get
            Return Me.m_water_5.ValueDouble
        End Get
        Set(value As Double)
            Me.m_water_5.ValueDouble = value
        End Set
    End Property

    Public Property Water6 As Double
        Get
            Return Me.m_water_6.ValueDouble
        End Get
        Set(value As Double)
            Me.m_water_6.ValueDouble = value
        End Set
    End Property

    Public Property SWB1 As Double
        Get
            Return Me.m_swb_1.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_1.ValueDouble = value
        End Set
    End Property

    Public Property SWB2 As Double
        Get
            Return Me.m_swb_2.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_2.ValueDouble = value
        End Set
    End Property

    Public Property SWB3 As Double
        Get
            Return Me.m_swb_3.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_3.ValueDouble = value
        End Set
    End Property

    Public Property SWB4 As Double
        Get
            Return Me.m_swb_4.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_4.ValueDouble = value
        End Set
    End Property

    Public Property SWB5 As Double
        Get
            Return Me.m_swb_5.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_5.ValueDouble = value
        End Set
    End Property

    Public Property SWB6 As Double
        Get
            Return Me.m_swb_6.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_6.ValueDouble = value
        End Set
    End Property

    Public Property SWB7 As Double
        Get
            Return Me.m_swb_7.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_7.ValueDouble = value
        End Set
    End Property

    Public Property SWB8 As Double
        Get
            Return Me.m_swb_8.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_8.ValueDouble = value
        End Set
    End Property

    Public Property SWB9 As Double
        Get
            Return Me.m_swb_9.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_9.ValueDouble = value
        End Set
    End Property

    Public Property SWB10 As Double
        Get
            Return Me.m_swb_10.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_10.ValueDouble = value
        End Set
    End Property

    Public Property SWB11 As Double
        Get
            Return Me.m_swb_11.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_11.ValueDouble = value
        End Set
    End Property

    Public Property SWB12 As Double
        Get
            Return Me.m_swb_12.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_12.ValueDouble = value
        End Set
    End Property

    Public Property SWB13 As Double
        Get
            Return Me.m_swb_13.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_13.ValueDouble = value
        End Set
    End Property

    Public Property SWB14 As Double
        Get
            Return Me.m_swb_14.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_14.ValueDouble = value
        End Set
    End Property

    Public Property SWB15 As Double
        Get
            Return Me.m_swb_15.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_15.ValueDouble = value
        End Set
    End Property

    Public Property SWB16 As Double
        Get
            Return Me.m_swb_16.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_16.ValueDouble = value
        End Set
    End Property

    Public Property SWB17 As Double
        Get
            Return Me.m_swb_17.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_17.ValueDouble = value
        End Set
    End Property

    Public Property SWB18 As Double
        Get
            Return Me.m_swb_18.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_18.ValueDouble = value
        End Set
    End Property

    Public Property SWB19 As Double
        Get
            Return Me.m_swb_19.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_19.ValueDouble = value
        End Set
    End Property

    Public Property SWB20 As Double
        Get
            Return Me.m_swb_20.ValueDouble
        End Get
        Set(value As Double)
            Me.m_swb_20.ValueDouble = value
        End Set
    End Property

    Public Property DateStart As DateTime
        Get
            Return Me.m_date_start
        End Get
        Set(value As DateTime)
            Me.m_date_start = value
        End Set
    End Property

    Public Property DateStop As DateTime
        Get
            Return Me.m_date_stop
        End Get
        Set(value As DateTime)
            Me.m_date_stop = value
        End Set
    End Property

    Public Property LastRead As DateTime
        Get
            Return Me.m_last_read
        End Get
        Set(value As DateTime)
            Me.m_last_read = value
        End Set
    End Property

    Public Property JobId As Integer
        Get
            Return Me.m_job_id
        End Get
        Set(value As Integer)
            Me.m_job_id = value
        End Set
    End Property

    Public Property RecipeName As String
        Get
            Return Me.m_recipe_name
        End Get
        Set(value As String)
            Me.m_recipe_name = value
        End Set
    End Property

    Public Property Emt As TimeSpan
        Get
            Return Me.m_emt
        End Get
        Set(value As TimeSpan)
            Me.m_emt = value
        End Set
    End Property

    Public Property PeriodLenght As TimeSpan
        Get
            Return Me.m_period_length
        End Get
        Set(value As TimeSpan)
            Me.m_period_length = value
        End Set
    End Property

End Class

Friend Class myScaleDoubleVal
    Private m_double_val As Double

    Public Property ValueDouble As Double
        Get
            Return m_double_val
        End Get
        Set(value As Double)
            m_double_val = value
        End Set
    End Property

End Class

Friend Class myScaleIntAndDoubleVal
    Inherits myScaleDoubleVal

    Private m_integer_val As Long

    Public Property ValueInteger As Long
        Get
            Return m_integer_val
        End Get
        Set(value As Long)
            m_integer_val = value
        End Set
    End Property

End Class