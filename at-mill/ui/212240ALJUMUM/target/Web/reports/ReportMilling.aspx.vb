﻿Imports UsersGUI

Partial Class _ReportMilling
    Inherits myWebPage

    Private mConfig As UsersGUI.config
    Private m_counter As Long = m_InvalidId

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim script As String = String.Empty

        mConfig = CType(Application("Config"), UsersGUI.config)

        script &= ReportsTools.myReports.GetReportsTranslations(mConfig)

        If Current.Request("typereport") Is Nothing Then
            Exit Sub
        End If

        If Current.Request.QueryString("counter") IsNot Nothing AndAlso Current.Request.QueryString("counter") <> String.Empty Then
            Long.TryParse(Current.Request.QueryString("counter"), m_counter)
        End If

        script &= ReportsTools.MillingReport.GetMillingScript(mConfig, m_counter)

        myScript.InvokeJS(<PERSON><PERSON>Page, script)
    End Sub

End Class