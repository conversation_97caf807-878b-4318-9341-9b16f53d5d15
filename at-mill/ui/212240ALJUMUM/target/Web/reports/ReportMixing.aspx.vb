﻿Imports UsersGUI

Partial Class _ReportMixing
    Inherits myWebPage

    Private mConfig As UsersGUI.config
    Private mTypeReport As EnumTypeReport
    Private m_counter As Long = m_InvalidId

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim script As String = String.Empty

        mConfig = CType(Application("Config"), UsersGUI.config)

        script &= ReportsTools.myReports.GetReportsTranslations(mConfig)

        If Current.Request("typereport") Is Nothing Then
            Exit Sub
        End If
        mTypeReport = Current.Request("typereport").ToString

        If Current.Request.QueryString("counter") IsNot Nothing AndAlso Current.Request.QueryString("counter") <> String.Empty Then
            Long.TryParse(Current.Request.QueryString("counter"), m_counter)
        End If

        script &= ReportsTools.MixingReports.GetMixingNameScript(mConfig)

        script &= ReportsTools.MixingReports.GetMixingValueScript(m_counter)

        myScript.InvokeJS(<PERSON><PERSON>Page, script)
    End Sub

End Class