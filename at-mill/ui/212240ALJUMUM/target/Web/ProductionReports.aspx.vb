﻿Imports ReportsTools
Imports UsersGUI
Imports WebTools.tools

Partial Class _ProductionReports
    Inherits myWebPage

    Public mPageName As String
    Private mMenuName As String
    Private mTypeReport As EnumTypeReport?
    Public scr As Screen
    Public m_config As config
    Public m_ProductId As String
    Public m_LotId As String
    Public mCounter As Long = m_InvalidInteger
    Public mCycleId As Integer

    Private Class CycleParam
        Public AspName As String
        Public ParameterValue As String
    End Class

    Private Class RecipeParam
        Public AspName As String
        Public FieldValue As String
    End Class

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        m_config = CType(Application("Config"), config)

        ' Controlla se è necessario fare un update delle risorse del JS
        myScript.UpdateJSResources(m_config)

        myScript.InvokeJS(Me.Page, "ShowUCCont();")

        If m_config Is Nothing Then
            Exit Sub
        End If

        If Current.Request("pagename") Is Nothing Then
            Exit Sub
        End If
        mPageName = Current.Request("pagename").ToString

        If Current.Request("CYC_ID") IsNot Nothing Then
            mCycleId = Integer.Parse(Current.Request("CYC_ID").ToString())

            ' Concatena al pagename l'id del ciclo
            mPageName &= mCycleId
        Else
            Exit Sub
        End If

        If Current.Request("COUNTER") IsNot Nothing Then
            mCounter = Long.Parse(Current.Request("COUNTER").ToString())
        Else
            Exit Sub
        End If

        If Current.Request("menuname") Is Nothing Then
            Exit Sub
        End If
        mMenuName = Current.Request("menuname").ToString

        CheckPageDB(m_config, mMenuName, mPageName)
        If Not CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
            lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
            Me.dView.Visible = False
            Me.dError.Visible = True
            myScript.InvokeJS(Me.Page, "myLoader.stop();")
            Exit Sub
        End If

        If Not IsPostBack() Then
            If WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Printing) Then
                DrawPrintReportButton()
            End If

            DrawBackButton()
        End If

        LoadIFrame()
    End Sub

    Private Sub LoadIFrame()
        Dim frameUrl As String = String.Empty
        Dim recipeLogId As Integer = m_InvalidInteger
        Dim productionReportId As Long = m_InvalidInteger

        Select Case mCycleId
            Case Else
                mTypeReport = myReports.GetReportTypeByCycleId(mCycleId)
        End Select

        'Se true usa il sql server reporting services, se false usa javascript
        If (System.Configuration.ConfigurationManager.AppSettings("UseSSRSReport") Is Nothing OrElse
            System.Configuration.ConfigurationManager.AppSettings("UseSSRSReport").ToLower() = "false") Then

            Select Case mTypeReport
                Case EnumTypeReport.ShipmentReport, EnumTypeReport.ParcelReport
                    frameUrl = "reports/ReportShipmentParcel.aspx"
                Case EnumTypeReport.TransferReport
                    frameUrl = "reports/ReportTransfer.aspx"
                Case EnumTypeReport.CleaningReport
                    frameUrl = "reports/ReportCleaning.aspx"
                Case EnumTypeReport.MixingReport
                    frameUrl = "reports/ReportMixing.aspx"
                Case EnumTypeReport.MillingReport
                    frameUrl = "reports/ReportMilling.aspx"
            End Select

            frameUrl &= "?typereport=" & mTypeReport & "&counter=" & mCounter

            ' entry del frame
            IFrameReport.Attributes.Add("src", frameUrl)
        Else

            Select Case mTypeReport
                Case EnumTypeReport.ShipmentReport
                    frameUrl = "SSRSReports.aspx"
                    frameUrl &= "?typereport=" & EnumSSRSTypeReport.ShipmentReport & "&counter=" & mCounter & "&pagename=" & mPageName & "&menuname=" & mMenuName
                    Response.Redirect(frameUrl)
                    Response.End()
                Case EnumTypeReport.ParcelReport
                    frameUrl = "SSRSReports.aspx"
                    frameUrl &= "?typereport=" & EnumSSRSTypeReport.ParcelReport & "&counter=" & mCounter & "&pagename=" & mPageName & "&menuname=" & mMenuName
                    Response.Redirect(frameUrl)
                    Response.End()
                Case EnumTypeReport.TransferReport
                    frameUrl = "SSRSReports.aspx"
                    frameUrl &= "?typereport=" & EnumSSRSTypeReport.TransferReport & "&counter=" & mCounter & "&pagename=" & mPageName & "&menuname=" & mMenuName
                    Response.Redirect(frameUrl)
                    Response.End()
                Case EnumTypeReport.CleaningReport
                    frameUrl = "SSRSReports.aspx"
                    frameUrl &= "?typereport=" & EnumSSRSTypeReport.CleaningReport & "&counter=" & mCounter & "&pagename=" & mPageName & "&menuname=" & mMenuName
                    Response.Redirect(frameUrl)
                    Response.End()
                Case EnumTypeReport.MixingReport
                    frameUrl = "SSRSReports.aspx"
                    frameUrl &= "?typereport=" & EnumSSRSTypeReport.MixingReport & "&counter=" & mCounter & "&pagename=" & mPageName & "&menuname=" & mMenuName
                    Response.Redirect(frameUrl)
                    Response.End()
                Case EnumTypeReport.MillingReport
                    frameUrl = "SSRSReports.aspx"
                    frameUrl &= "?typereport=" & EnumSSRSTypeReport.MillingReport & "&counter=" & mCounter & "&pagename=" & mPageName & "&menuname=" & mMenuName
                    Response.Redirect(frameUrl)
                    Response.End()
            End Select
        End If

    End Sub

    Private Function getRecipeLogId(ByVal counter As Integer) As Integer
        Dim retVal As Integer = m_InvalidInteger
        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(
            "SELECT RECIPE_LOG_ID FROM PRODUCTION_REPORTS WHERE COUNTER = " & counter,
            False
        )

        If dt.Rows.Count > 0 Then
            If Not IsDBNull(dt.Rows(0).Item("RECIPE_LOG_ID")) Then
                retVal = CInt(dt.Rows(0).Item("RECIPE_LOG_ID"))
            End If
        End If

        Return retVal
    End Function

    Private Function getProductionReportId(ByVal counter As Long) As Long
        Dim retVal As Long = m_InvalidInteger
        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(
            "SELECT ID FROM PRODUCTION_REPORTS WHERE COUNTER = " & counter,
            False
        )

        If dt.Rows.Count > 0 Then
            If Not IsDBNull(dt.Rows(0).Item("ID")) Then
                retVal = CInt(dt.Rows(0).Item("ID"))
            End If
        End If

        Return retVal
    End Function

    Private Sub DrawPrintButtonBase(ByVal calling_function As String)
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New LinkButton
        link.ID = "lb_Stampa"
        link.Text = m_config.GetEntryByKeyName("PRINT_BUTTON").GetValue()
        link.Attributes("href") = "#"
        link.OnClientClick = calling_function & ";"

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub DrawPrintReportButton()
        DrawPrintButtonBase("ButtonEvents.PrintReport()")
    End Sub

    Private Sub AddImageToOperation()
        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim img As New System.Web.UI.HtmlControls.HtmlImage

        img.Src = "~/image/RightArrow.png"
        tc.Attributes("class") = "buttonGreen"
        tc.Controls.Add(img)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

    Private Sub DrawBackButton()
        Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
        row.ID = "BackCont"
        tblOperation.Rows.Add(row)

        AddImageToOperation()

        Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
        Dim link As New HyperLink
        With link
            .ID = "lbBack"
            .Text = m_config.GetEntryByKeyName("Back").GetValue()
            .CssClass = "hoverHand"
            .Attributes.Add("onclick", "ButtonEvents.Back();")
        End With

        tc.Attributes("class") = "txtButtonGreen"
        tc.Controls.Add(link)

        tblOperation.Rows(tblOperation.Rows.Count - 1).Cells.Add(tc)
    End Sub

End Class