﻿Imports System.IO
Imports System.Reflection
Imports Microsoft.Reporting.WebForms
Imports ReportsTools
Imports StandardDefines.Defines
Imports UsersGUI
Imports WebTools.tools

Partial Class ssrs_SsrsReports
    Inherits System.Web.UI.Page

    Private mTypeReport As String = String.Empty
    Private mPageName As String = String.Empty
    Private mMenuName As String = String.Empty
    Public mConfig As UsersGUI.config

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        mConfig = CType(Application("Config"), config)

        ' Controlla se è necessario fare un update delle risorse del JS
        myScript.UpdateJSResources(mConfig)

        myScript.InvokeJS(Me.Page, "ShowUCCont();")

        If mConfig Is Nothing Then
            Exit Sub
        End If

        If Current.Request("pagename") Is Nothing Then
            Exit Sub
        End If
        mPageName = Current.Request("pagename").ToString

        If Current.Request("typereport") IsNot Nothing Then
            mTypeReport = Current.Request("typereport").ToString
        Else
            lblError.Text = "TypeReport not specified"
            ReportViewer1.Visible = False
            Me.dError.Visible = True

            Exit Sub
        End If

        If Current.Request("menuname") Is Nothing Then
            Exit Sub
        End If
        mMenuName = Current.Request("menuname").ToString

        CheckPageDB(mConfig, mMenuName, mPageName)
        If Not CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
            lblError.Text = mConfig.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
            Me.ReportViewer1.Visible = False
            Me.dError.Visible = True
            Me.divSelect.Visible = False
            myScript.InvokeJS(Me.Page, "myLoader.stop();")
            Exit Sub
        End If

        If (Not Page.IsPostBack) Then
            Me.lblYieldsPeriodSelect.Text = mConfig.GetEntryByKeyName("YIELDS_PERIOD_SELECT").GetValue
            Me.btnConferma.Text = mConfig.GetEntryByKeyName("SUBMIT_BUTTON").GetValue

            If Current.Request("typereport") IsNot Nothing AndAlso Current.Request("typereport").ToString() <> "" Then
                ddlPeriodicTime = PopolaPeriodiByReport(ddlPeriodicTime, mConfig, Current.Request("typereport").ToString())
            Else
                ddlPeriodicTime = WebTools.tools.PopolaPeriodi(ddlPeriodicTime, mConfig)
            End If

            ddlPeriodicTime.DataBind()

            Me.LoadReportChooser(Me.rbl_chooser)
            Me.rbl_chooser.DataBind()
            Me.rbl_chooser.SelectedIndex = EnumReportChooser.ByPeriod
            rbl_chooser_SelectedIndexChanged(sender, e)

            Dim _select As String = "SELECT CODE FROM SSRS_REPORTS_TYPE WHERE ID='" & mTypeReport & "'"
            Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(_select, False)

            If (dt IsNot Nothing AndAlso dt.Rows.Count > 0) Then
                Select Case CInt(dt.Rows(0).Item(0))
                    Case EnumTypeReport.MixingReport
                        ReportViewer1.Width = 1000

                        Me.divPeriodic.Visible = False
                        Me.divJob.Visible = False
                        Me.divBtnConferma.Visible = False
                        Me.divExtra.Visible = False
                        btnConferma_Click(sender, e)

                    Case EnumTypeReport.InstantReport, EnumTypeReport.InstantPowerReport, EnumTypeReport.ShipmentReport,
                         EnumTypeReport.ParcelReport, EnumTypeReport.TransferReport, EnumTypeReport.CleaningReport,
                        EnumTypeReport.MillingReport
                        Me.divPeriodic.Visible = False
                        Me.divJob.Visible = False
                        Me.divBtnConferma.Visible = False
                        Me.divExtra.Visible = False
                        btnConferma_Click(sender, e)

                    Case EnumTypeReport.PeriodicReport, EnumTypeReport.PeriodicPowerReport, EnumTypeReport.PeriodicShipmentReport, EnumTypeReport.ProductsSummationReport,
                         EnumTypeReport.GrainsSummationReport, EnumTypeReport.PeriodicCleaningReport, EnumTypeReport.PeriodicBaggingReport
                        Me.divPeriodic.Visible = True
                        Me.divJob.Visible = False
                        Me.divBtnConferma.Visible = True
                        Me.ddlPeriodicTime.SelectedIndex = 1
                        btnConferma_Click(sender, e)

                    Case EnumTypeReport.JobReport, EnumTypeReport.JobPowerReport
                        Me.divPeriodic.Visible = False
                        Me.divJob.Visible = True
                        Me.divBtnConferma.Visible = True

                        ' Select the desired job if JOB_ID is in the URL, otherwise the current active one
                        If Current.Request("JOB_ID") IsNot Nothing AndAlso Current.Request("JOB_ID") <> String.Empty AndAlso ddlJob.Items.FindByValue(Current.Request("JOB_ID").ToString()) IsNot Nothing Then
                            ddlJob.SelectedValue = Long.Parse(Current.Request("JOB_ID").ToString())
                        Else
                            Me.ddlJob.SelectedIndex = 1
                        End If

                        'btnConferma_Click(sender, e)
                    Case Else
                        lblError.Text = "TypeReport non configurato"
                        Me.dError.Visible = True
                        Exit Sub
                End Select
            End If
        End If

        Me.ReportViewer1.Visible = True
    End Sub

    Private Sub LoadReport(_TypeReport As Integer)

        'Recupero informazioni dalla tabella di configurazione SSRS_REPORTS_CONFIG
        ReportViewer1.ServerReport.Refresh()

        Dim sSelect As String = "SELECT * FROM SSRS_REPORTS_CONFIG WHERE TYPE='" & _TypeReport & "'"
        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If (dt IsNot Nothing AndAlso dt.Rows.Count = 1) Then
            'Set the processing mode for the ReportViewer to Remote
            ReportViewer1.ProcessingMode = ProcessingMode.Remote

            Dim serverReport As ServerReport
            serverReport = ReportViewer1.ServerReport

            'Set the report server URL and report path
            serverReport.ReportServerUrl = New Uri(dt.Rows(0).Item("SSRS_URL"))
            serverReport.ReportPath = dt.Rows(0).Item("REPORT_URL")

            'Create the sales order number report parameter
            Dim _ArrayParametri() As String = dt.Rows(0).Item("PARAMETERS").ToString().Split("§")

            Dim _ListOfStringParams As List(Of String) = New List(Of String)

            For i As Integer = 0 To _ArrayParametri.GetUpperBound(0)
                If (_ArrayParametri(i) IsNot Nothing AndAlso _ArrayParametri(i).ToString() <> String.Empty) Then
                    _ListOfStringParams.Add(_ArrayParametri(i).ToString())
                End If
            Next

            Dim _parameters(_ListOfStringParams.Count()) As String
            Dim i_param As Integer = 0

            For Each s In _ListOfStringParams

                _parameters(i_param) = s
                i_param = i_param + 1
            Next

            'Create the sales order number report parameter
            Dim _ListaParametri As List(Of ReportParameter) = New List(Of ReportParameter)
            For Each s_param In _parameters

                If (s_param Is Nothing) Then
                    Continue For
                End If

                Dim param As ReportParameter = New ReportParameter()
                param.Name = s_param.Split("|")(0)

                Dim s_stringa As String = String.Empty
                Dim p_value As String = s_param.Split("|")(1)
                If (p_value.Contains("{CUSTOMERLANGUAGE}")) Then
                    p_value = Integer.Parse(mConfig.GetCurrentLanguage())
                ElseIf (p_value.Contains("{SP}")) Then
                    s_stringa = p_value.Substring(4, p_value.Length - 4)

                    Select Case CInt([Enum].Parse(GetType(StandardDefines.Defines.eStoreProcedureReports), s_stringa.Split(":")(0)))
                        Case StandardDefines.Defines.eStoreProcedureReports.GetStartAndStopDateForPeriod
                            Dim s_peridod = _ListaParametri.Where(Function(w) w.Name = "Periodo").FirstOrDefault()
                            If (s_peridod IsNot Nothing) Then
                                Dim exec As String = "exec " & s_stringa.Split(":")(0) & " " & s_peridod.Values(0).ToString()
                                Dim dt_exec As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(exec, False)
                                If (dt_exec IsNot Nothing AndAlso dt_exec.Rows.Count > 0) Then
                                    p_value = dt_exec.Rows(0).Item(s_stringa.Split(":")(1)).ToString()
                                End If
                            End If
                        Case Else
                            'Store Procedure non gestita
                    End Select
                ElseIf (p_value.Contains("{QueryStringValue}")) Then
                    If Current.Request(p_value.Split(":")(1)) IsNot Nothing AndAlso Current.Request(p_value.Split(":")(1)).ToString() <> "" Then
                        p_value = Current.Request(p_value.Split(":")(1)).ToString()
                    End If
                ElseIf (p_value.Contains("{START}")) Then
                    p_value = Me.start_date.Text
                ElseIf (p_value.Contains("{STOP}")) Then
                    p_value = Me.stop_date.Text
                End If

                param.Values.Add(p_value)
                _ListaParametri.Add(param)
            Next

            Dim parameters(_ListOfStringParams.Count() - 1) As ReportParameter
            Dim index As Integer = 0
            For Each param As ReportParameter In _ListaParametri

                If (param Is Nothing) Then
                    Continue For
                End If

                parameters.SetValue(param, index)
                index = index + 1
            Next

            'Set the report parameters for the report
            serverReport.SetParameters(parameters)

        End If

    End Sub

    Public Shared Function PopolaPeriodiByReport(ByVal cmb As DropDownList, ByVal m_config As UsersGUI.config, ByVal m_typereport As String) As DropDownList
        Dim i As Integer = 0
        Dim dt As New Data.DataTable
        dt.Columns.Add("VALUE")
        dt.Columns.Add("NAME")

        Dim dr As Data.DataRow

        dr = dt.NewRow
        dr("VALUE") = "useCalendars"
        dr("NAME") = String.Empty
        dt.Rows.Add(dr)

        Select Case m_typereport.ToLower()

            Case Else
                cmb = WebTools.tools.PopolaPeriodiSSRS(cmb, m_config)
        End Select

        Return cmb
    End Function

    Private Function LoadReportChooser(ByVal rbl As RadioButtonList) As RadioButtonList

        Dim dt As New Data.DataTable
        dt.Columns.Add("ID")
        dt.Columns.Add("STEP")

        Dim dr As Data.DataRow
        dr = dt.NewRow
        dr("ID") = EnumReportChooser.ByDate
        dr("STEP") = mConfig.GetEntryByKeyName("BY_DATE").GetValue
        dt.Rows.Add(dr)

        dr = dt.NewRow
        dr("ID") = EnumReportChooser.ByPeriod
        dr("STEP") = mConfig.GetEntryByKeyName("BY_PERIOD").GetValue
        dt.Rows.Add(dr)

        rbl.DataSource = dt
        rbl.DataTextField = "STEP"
        rbl.DataValueField = "ID"

        Return rbl
    End Function

    Protected Sub rbl_chooser_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles rbl_chooser.SelectedIndexChanged
        Select Case rbl_chooser.SelectedIndex

            Case EnumReportChooser.ByDate
                'clear and hide Period section
                Me.ddlPeriodicTime.SelectedIndex = 0
                Me.tblByPeriod.Visible = False

                Me.tblByDate.Visible = True

            Case EnumReportChooser.ByPeriod
                'clear and hide Date section
                Me.start_date.Text = String.Empty
                Me.stop_date.Text = String.Empty
                Me.tblByDate.Visible = False

                Me.tblByPeriod.Visible = True

        End Select
    End Sub

    Protected Sub btnConferma_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnConferma.Click

        If (Me.ddlPeriodicTime.SelectedValue <> String.Empty AndAlso Me.ddlPeriodicTime.SelectedValue <> -1) Then

            Dim _Type As Integer = Me.ddlPeriodicTime.SelectedValue

            If (mPageName = "PERIODIC_POWER_REPORT") Then
                _Type = _Type + 200
            ElseIf (mPageName = "PERIODIC_CLEANING_REPORT") Then
                _Type = _Type + 300
            ElseIf (mPageName = "PERIODIC_BAGGING_REPORT") Then
                _Type = _Type + 400
            End If

            LoadReport(_Type)

            ' in caso di selezione manuale delle date, usare i report configurati in SSRS_REPORTS_CONFIG con relativo SSRS_REPORTS_TYPE
        ElseIf (Me.start_date.Text <> String.Empty AndAlso Me.stop_date.Text <> String.Empty) Then
            If (mPageName = "PERIODIC_YIELDS") Then
                LoadReport(EnumTypeReport.YieldsReportByDate)
            ElseIf (mPageName = "PERIODIC_CLEANING_REPORT") Then
                LoadReport(EnumTypeReport.CleaningReportByDate)
            ElseIf (mPageName = "PERIODIC_POWER_REPORT") Then
                LoadReport(EnumTypeReport.PowerReportByDate)
            ElseIf (mPageName = "PERIODIC_BAGGING_REPORT") Then
                LoadReport(EnumTypeReport.BaggingReportByDate)
            End If
        Else
            LoadReport(mTypeReport)
        End If

    End Sub

End Class