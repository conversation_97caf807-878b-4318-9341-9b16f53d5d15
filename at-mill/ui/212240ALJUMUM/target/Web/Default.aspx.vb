﻿Imports UsersGUI
Imports WebTools
Imports WebTools.tools

Partial Class _Default
    Inherits myWebPage

    Private mId As Long = 0
    Private mPageName As String = String.Empty
    Private mControl As String = String.Empty
    Private mMenuName As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Private mScreen As Screen = Nothing
    Private m_config As config
    Private msgGeneric As New myAlert
    Private msgDelete As New myConfirm
    Private msgDeleteAlert As New myAlert
    Private msgAbortCycle As New myConfirm
    Private msgAbortCycleAlert As New myAlert
    Private msgShowControls As New myAlert
    Private msgShowRecipeExecutable As New myAlert
    Private msgCloneCycle As New myConfirm
    Private msgCloneRecipe As New myPrompt
    Private msgForceCompleteOrder As New myConfirm

    Public Enum EnumStatusBatchNumbers
        InUse = 1
        Locked = 2
    End Enum

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName)

        ' msg init
        msgGeneric.Init(Me.Page, myMessageBoxParam.NoParameter)
        msgDelete.Init(Me.Page, Me.HiddenDelete, myMessageBoxParam.Delete)
        msgDeleteAlert.Init(Me.Page, myMessageBoxParam.Delete)
        msgAbortCycle.Init(Me.Page, Me.HiddenAbortCycle, myMessageBoxParam.AbortCycle)
        msgAbortCycleAlert.Init(Me.Page, myMessageBoxParam.AbortCycle)
        msgShowControls.Init(Me.Page, myMessageBoxParam.ShowControls)
        msgShowRecipeExecutable.Init(Me.Page, myMessageBoxParam.ShowRecipeExecutable)
        msgCloneCycle.Init(Me.Page, Me.HiddenCloneCycle, myMessageBoxParam.CloneCycle)
        msgCloneRecipe.Init(Me.Page, Me.HiddenCloneRecipe, Me.HiddenCloneRecipeText, myMessageBoxParam.CloneRecipe)
        msgForceCompleteOrder.Init(Me.Page, Me.HiddenForceCompleteOrder, myMessageBoxParam.ForceCompleteOrder)

        If Current.Request.QueryString("Id") IsNot Nothing Then
            Long.TryParse(Current.Request.QueryString("Id").ToString, Me.mId)
        End If

        If Current.Request.QueryString("sessionExpired") IsNot Nothing Then
            msgGeneric.Show(m_config.GetEntryByKeyName("SESSION_EXPIRED_MESSAGE").GetValue, "() => { removeSessionExpiredFromUrl() }")
        End If

        HandleDeleteRequestWithConfirm()

        HandleAbortCycleRequestWithConfirm()

        HandleShowControls()

        HandleShowRecipeExecutable()

        HandleCloneCycleAndRecipe()

        HandleForceCompleteOrder()

        If Current.Request.QueryString("operation") IsNot Nothing Then
            Select Case Current.Request.QueryString("operation")
                Case "ConfirmParcel"
                    ConfirmParcel()
                Case "ConfirmShipment"
                    ConfirmShipment()
            End Select
        End If

        If Not Page.IsPostBack Then
            Select Case mControl.ToUpper
                Case "STATUS"
                    Me.UCStatus.Visible = True
                Case "PLANTCONF"
                    Me.UCPlant.Visible = True
                Case "VIEW"
                    Me.UCView.Visible = True
                Case "ALARMCONF"
                    Me.UCAlarmConf.Visible = True
                Case "PROCEXECUTION"
                    Me.UCProcexecution.Visible = True
                Case "PDF"
                    Me.UCPdf.Visible = True
                Case "SYSTEMGROUPSRIGHTS"
                    Me.UCSystemGroupsRights.Visible = True
                Case Else
                    Me.UCMain.Visible = True
            End Select
        End If

        ' Updates JS resources if the flag in the session is "YES"
        myScript.UpdateJSResources(m_config)

        myScript.InvokeJS(Me.Page, "ShowUCCont();")

        If Current.Request.QueryString("embedded") IsNot Nothing Then
            If (Current.Request.QueryString("embedded").ToString.ToUpper = m_StringYes) Then
                Current.Session("embedded") = m_StringYes
            End If

            If (Current.Request.QueryString("embedded").ToString.ToUpper = m_StringNo) Then
                Current.Session.Remove("embedded")
            End If
        End If

        If Current.Request.QueryString("scadalogin") IsNot Nothing Then
            If (Current.Request.QueryString("scadalogin").ToString.ToUpper = m_StringYes) Then
                Current.Session("scadalogin") = m_StringYes
            End If

            If (Current.Request.QueryString("scadalogin").ToString.ToUpper = m_StringNo) Then
                Current.Session.Remove("scadalogin")
            End If
        Else
            Current.Session.Remove("scadalogin")
        End If

        If Current.Session("embedded") = m_StringYes Then
            Me.topMenuContainer.Visible = False
        End If

        If Current.Session("scadalogin") = m_StringYes Then
            If Not Page.IsPostBack Then
                myScript.InvokeJS(Me.Page, "LoginScadaUser();")
            End If
        End If
    End Sub

    ' gestione della delete con conferma dell'operatore
    Private Sub HandleDeleteRequestWithConfirm()
        If Current.Request.QueryString("deleted") IsNot Nothing Then

            If msgDelete.GetAnswer() Then

                ' 1. do operation
                Dim s_error As String = String.Empty

                If Not Delete(s_error) Then
                    msgDeleteAlert.Show(s_error)
                    Exit Sub
                End If

                ' 2. redirect
                myScript.InvokeJS(Me.Page, "DoneEvents.ActionURL();")
            ElseIf (Current.Request.QueryString("deleted").ToString.ToUpper = m_StringYes) Then
                msgDelete.Show(m_config.GetEntryByKeyName("CONFIRM_DELETE_RECORD").GetValue)
            End If
        End If
    End Sub

    ' gestione dell'abort cycle con conferma dell'operatore
    Private Sub HandleAbortCycleRequestWithConfirm()
        If Current.Request.QueryString("AbortCycle") IsNot Nothing Then

            If msgAbortCycle.GetAnswer() Then
                AbortCycle()
            ElseIf (Current.Request.QueryString("AbortCycle").ToString.ToUpper = m_StringYes) Then
                CheckCycleStatus()
            End If
        End If
    End Sub

    Private Sub HandleShowControls()
        If Current.Request.QueryString("ShowControls") IsNot Nothing Then
            If (Current.Request.QueryString("ShowControls").ToString.ToUpper = m_StringYes) Then
                ShowControls()
            End If
        End If
    End Sub

    Private Sub HandleShowRecipeExecutable()
        If Current.Request.QueryString("ShowRecipeExecutable") IsNot Nothing Then
            If (Current.Request.QueryString("ShowRecipeExecutable").ToString.ToUpper = m_StringYes) Then
                ShowRecipeExecutable()
            End If
        End If
    End Sub

    Private Sub HandleCloneCycleAndRecipe()
        Dim s_error As String = String.Empty
        Dim new_ppl_id As Long = m_InvalidId

        If Current.Request.QueryString("CloneCycle") IsNot Nothing Then
            If msgCloneCycle.GetAnswer() Then

                new_ppl_id = WebTools.tools.CloneCycle(Me.mId)

                myFunction.ForceOrderParamValuesOnClonedCycle(new_ppl_id)

                ' posto una richiesta per forzare il ricalcolo dei controlli di ciclo da parte del c++
                Dim req1 As New CommandInterface(EnumRequest.ForceCheckAllCyclesControls)
                req1.PostUniqueRequest()

                ' posto una richiesta per forzare la costruzione delle process recipe da parte del c++
                Dim req2 As New CommandInterface(EnumRequest.ForceBuildAllProcessRecipes)
                req2.PostUniqueRequest()

                myScript.InvokeJS(Me.Page, "DoneEvents.ActionURL();")
            ElseIf (Current.Request.QueryString("CloneCycle").ToString.ToUpper = m_StringYes) Then
                msgCloneCycle.Show(m_config.GetEntryByKeyName("JOB_CLONE_1").GetValue & "\n" &
                                   m_config.GetEntryByKeyName("JOB_CLONE_2").GetValue)
            End If

        ElseIf Current.Request.QueryString("CloneRecipe") IsNot Nothing Then

            If msgCloneRecipe.GetAnswer() Then

                If UsersGUI.tools.IsValueAlreadyPresentInTable("RECIPES", "DESCRIPTION", msgCloneRecipe.GetInputText(), m_InvalidId, "MTR_ID = " & WebTools.tools.GetMetaRecipeFromRecId(Me.mId)) Then
                    myErrorMessage.AppendValueAlreadyPresent(s_error, m_config, "DESCRIPTION", msgCloneRecipe.GetInputText())
                    msgGeneric.Show(s_error)
                    Exit Sub
                End If

                WebTools.tools.CloneRecipe(Me.mId, msgCloneRecipe.GetInputText())

                myScript.InvokeJS(Me.Page, "DoneEvents.ActionURL();")
            ElseIf (Current.Request.QueryString("CloneRecipe").ToString.ToUpper = m_StringYes) Then
                msgCloneRecipe.Show(m_config.GetEntryByKeyName("RECIPE_CLONE_1").GetValue & "\n" &
                                      m_config.GetEntryByKeyName("RECIPE_CLONE_2").GetValue & "\n" &
                                      m_config.GetEntryByKeyName("RECIPE_CLONE_3").GetValue)
            End If
        End If
    End Sub

    Private Sub HandleForceCompleteOrder()
        If Current.Request.QueryString("ForceCompleteOrder") IsNot Nothing Then

            If msgForceCompleteOrder.GetAnswer() Then
                If Current.Request.QueryString("Id") IsNot Nothing Then
                    Dim req As New CommandInterface(EnumRequest.ForceCompleteOrder, Me.mId)
                    req.PostUniqueRequest()
                End If

                myScript.InvokeJS(Me.Page, "DoneEvents.ActionURL();")
            ElseIf (Current.Request.QueryString("ForceCompleteOrder").ToString.ToUpper = m_StringYes) Then
                msgForceCompleteOrder.Show(m_config.GetEntryByKeyName("FORCE_COMPLETE_ORDER").GetValue)
            End If
        End If
    End Sub

    Private Function Delete(ByRef m_Error As String) As Boolean
        Dim mTableName As String = WebTools.tools.GetTableName(mPageName)
        Dim str_sql As String = String.Empty
        Dim str_delete As String = String.Empty

        If mId > 0 Then

            ' tabelle con ID non eliminabili / tabelle speciali
            Select Case mTableName.ToUpper
                Case "PO_NUMBER_LIST"

                    str_sql = "SELECT ID_STATUS,PO_NUMBER_ERP FROM VIEW_PO_NUMBER_LIST WHERE ID = " & mId
                    Dim dt_po As System.Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)
                    If (dt_po IsNot Nothing AndAlso dt_po.Rows(0)("ID_STATUS") = EnumStatusPONumbers.Used) Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, String.Format(m_config.GetEntryByKeyName("NO_DELETE_PO_NUMBER_INUSE").GetValue(), dt_po.Rows(0)("PO_NUMBER_ERP").ToString()))
                    ElseIf (dt_po IsNot Nothing AndAlso dt_po.Rows(0)("ID_STATUS") = EnumStatusPONumbers.Closed) Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, String.Format(m_config.GetEntryByKeyName("NO_DELETE_PO_NUMBER_CLOSED").GetValue(), dt_po.Rows(0)("PO_NUMBER_ERP").ToString()))
                    ElseIf WebTools.tools.PONumberAlreadyUsedInFlows(dt_po.Rows(0)("PO_NUMBER_ERP").ToString()) OrElse WebTools.tools.PONumberAlreadyUsedInOrderParams(dt_po.Rows(0)("PO_NUMBER_ERP").ToString()) Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, String.Format(m_config.GetEntryByKeyName("NO_DELETE_PO_NUMBER_ALREADYUSED").GetValue(), dt_po.Rows(0)("PO_NUMBER_ERP").ToString()))
                    End If

                Case "BATCH_NUMBER_LIST"

                    str_sql = "SELECT ID_STATUS,BATCH_NUMBER FROM VIEW_BATCH_NUMBER_LIST WHERE ID = " & mId
                    Dim dt_bn As System.Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)
                    If (dt_bn IsNot Nothing AndAlso dt_bn.Rows(0)("ID_STATUS") = EnumStatusBatchNumbers.Locked) Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, String.Format(m_config.GetEntryByKeyName("NO_DELETE_BATCH_NUMBER_LOCKED").GetValue(), dt_bn.Rows(0)("BATCH_NUMBER").ToString()))
                    Else
                        If (dt_bn IsNot Nothing AndAlso dt_bn.Rows.Count > 0) Then
                            str_sql = "SELECT top 1 * FROM FLOW_LOGS WHERE BATCH_NUMBER_ERP = '" & dt_bn.Rows(0)("BATCH_NUMBER").ToString() & "'"
                            Dim dt_fl As System.Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)
                            If (dt_fl IsNot Nothing AndAlso dt_fl.Rows.Count > 0) Then
                                myErrorMessage.AppendCustomErrorMsg(m_Error, String.Format(m_config.GetEntryByKeyName("NO_DELETE_BATCH_NUMBER_INUSE").GetValue(), dt_bn.Rows(0)("BATCH_NUMBER").ToString()))
                            Else
                                str_sql = "Select top 1 * From VIEW_ORDER_PARAMETERS Where ASP_NAME Like 'BATCH_NUMBER_ERP' and PARAMETER_VALUE ='" & dt_bn.Rows(0)("BATCH_NUMBER").ToString() & "'"
                                Dim dt_op As System.Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)
                                If (dt_op IsNot Nothing AndAlso dt_op.Rows.Count > 0) Then
                                    myErrorMessage.AppendCustomErrorMsg(m_Error, String.Format(m_config.GetEntryByKeyName("NO_DELETE_BATCH_NUMBER_INUSE").GetValue(), dt_bn.Rows(0)("BATCH_NUMBER").ToString()))
                                End If
                            End If
                        End If
                    End If

                Case "CUSTOMERS"
                    If mId = CommonDefines.Defines.ID_CUSTOMER_UNKNOWN Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("NO_DELETE_CUSTOMERS").GetValue)
                    End If

                Case "SUPPLIERS"
                    If mId = CommonDefines.Defines.ID_SUPPLIER_UNKNOWN Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("NO_DELETE_SUPPLIERS").GetValue)
                    End If

                Case "CARRIERS"
                    If mId = CommonDefines.Defines.ID_CARRIER_UNKNOWN Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("NO_DELETE_CARRIERS").GetValue)
                    End If

                Case "PRODUCTS"
                    Dim dt As Data.DataTable

                    If mId = CommonDefines.Defines.SSProducts.Water OrElse mId = CommonDefines.Defines.SSProducts.Screenings Then
                        myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("NO_DELETE_PRODUCTS").GetValue)
                    End If

                    str_sql = "SELECT PT_ID FROM PRODUCTS WHERE ID = " & mId
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    For Each dr As Data.DataRow In dt.Rows
                        If dr("PT_ID").ToString = CommonDefines.Defines.SSProductTypes.System Then
                            myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("NO_DELETE_PRODUCTS").GetValue)
                            Exit For
                        End If
                    Next

                    If m_Error.Length = 0 Then
                        If WebDataBaseLayer.DataBase.TableExist("VIEW_RECIPE_PARAMETERS") Then

                            str_sql = "SELECT FIELD_VALUE FROM VIEW_RECIPE_PARAMETERS WHERE ASP_NAME LIKE 'PRO_ID%' OR ASP_NAME LIKE '%INGR_ID%'"
                            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                            For Each dr As Data.DataRow In dt.Rows
                                If dr("FIELD_VALUE").ToString <> String.Empty AndAlso dr("FIELD_VALUE").ToString = mId Then
                                    myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("NO_DELETE_PRODUCTS").GetValue)
                                    Exit For
                                End If
                            Next
                        End If
                    End If

                Case "PRODUCTION_PLAN"
                    str_sql = "SELECT ORDER_STATUS FROM PRODUCTION_PLAN WHERE ID=" & mId
                    Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    For Each dr As Data.DataRow In dt.Rows
                        Select Case dr("ORDER_STATUS").ToString
                            Case EnumLineStatus.Starting, EnumLineStatus.Active
                                myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("CANNOT_DELETE_ACTIVE_CYCLE").GetValue)
                        End Select
                    Next
            End Select

            myFunction.CtrlDeleteCustom(mScreen, m_Error, mId)

            If m_Error.Length = 0 Then
                ' 1. gestione esportazione ad ERP
                myFunction.ExportDataToERP(mScreen, mPageName, SSBorderDatabaseAction.Delete, mId)

                ' 2. gestione casi particolari (e tipici) di query di calcellazione
                Select Case mPageName.ToUpper
                    Case Else
                        ' caso tipico
                        str_delete = "DELETE FROM " & mTableName & " WHERE ID = '" & mId & "'"
                End Select
            End If
        Else
            If Current.Request.QueryString("Id") IsNot Nothing AndAlso Current.Request.QueryString("Id").ToString = "All" Then
                If Current.Request.QueryString("sDelete") IsNot Nothing AndAlso Current.Request.QueryString("sDelete").ToString <> String.Empty Then
                    str_delete = Current.Request.QueryString("sDelete")
                End If

            ElseIf mId = m_InvalidId Then
                str_delete = "DELETE FROM " & mTableName & " WHERE ID = '" & mId & "'"
            End If
        End If

        If str_delete.Length > 0 Then
            ' eseguo la query di delete
            Try
                WebDataBaseLayer.DataBase.ExecuteSQL(str_delete)
            Catch ex As Exception
                myErrorMessage.AppendCustomErrorMsg(m_Error, m_config.GetEntryByKeyName("UNABLE_TO_CANCEL_RECORD").GetValue)
            End Try
        End If

        If m_Error.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If
    End Function

    Private Sub CheckCycleStatus()
        Dim sSelect As String
        Dim dt As Data.DataTable

        sSelect = "SELECT ORDER_STATUS FROM PRODUCTION_PLAN WHERE ID = " & mId

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As Data.DataRow In dt.Rows
            Select Case dr("ORDER_STATUS").ToString
                Case EnumLineStatus.Starting, EnumLineStatus.Lock, EnumLineStatus.Idle, EnumLineStatus.Complete
                    msgAbortCycleAlert.Show(m_config.GetEntryByKeyName("CANNOT_ABORT_NON_ACTIVE_CYCLE").GetValue)
                Case EnumLineStatus.Active, EnumLineStatus.Waiting
                    msgAbortCycle.Show(m_config.GetEntryByKeyName("CONFIRM_ABORT_CYCLE").GetValue)
            End Select
        Next

    End Sub

    Private Sub AbortCycle()
        Dim req As New CommandInterface(EnumRequest.AbortActiveOrder, Me.mId.ToString)
        req.PostUniqueRequest()

        myScript.InvokeJS(Me.Page, "DoneEvents.ActionURL();")
    End Sub

    Private Sub ConfirmParcel()

        Dim pa_id, sWhere, sSelect As String
        Dim dt As Data.DataTable

        ' confermo la parcel se e solo se l'operatore ha confermato tutti i flussi associati
        If Current.Request.QueryString("sql_where") IsNot Nothing AndAlso Current.Request.QueryString("sql_where").ToString <> String.Empty Then
            sWhere = Current.Request.QueryString("sql_where").ToString
            sSelect = "SELECT * FROM VIEW_FLOW_LOGS_TO_PARCEL_666 WHERE " & sWhere

            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count = 0 Then
                pa_id = WebTools.tools.GetQueryParamFromWhereStatement(sWhere, "PA_ID")

                Dim myreq As New CommandInterface(EnumRequest.ConfirmParcel, pa_id)
                myreq.PostUniqueRequest()

                Dim bOk As Boolean = False
                While Not bOk
                    bOk = myFunction.WaitCompleted(mScreen)
                End While
            End If
        End If

        myScript.InvokeJS(Me.Page, "DoneEvents.ActionGeneric();")
    End Sub

    Private Sub ConfirmShipment()

        Dim sh_id, sWhere, sSelect As String
        Dim dt As Data.DataTable

        ' confermo lo chipment se e solo se l'operatore ha confermato tutti i flussi associati
        If Current.Request.QueryString("sql_where") IsNot Nothing AndAlso Current.Request.QueryString("sql_where").ToString <> String.Empty Then
            sWhere = Current.Request.QueryString("sql_where").ToString
            sSelect = "SELECT * FROM VIEW_FLOW_LOGS_TO_SHIPMENT_666 WHERE " & sWhere

            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count = 0 Then
                sh_id = WebTools.tools.GetQueryParamFromWhereStatement(sWhere, "SH_ID")

                Dim myreq As New CommandInterface(EnumRequest.ConfirmShipment, sh_id)
                myreq.PostUniqueRequest()

                Dim bOk As Boolean = False
                While Not bOk
                    bOk = myFunction.WaitCompleted(mScreen)
                End While
            End If
        End If

        myScript.InvokeJS(Me.Page, "DoneEvents.ActionGeneric();")
    End Sub

    Private Sub ShowControls()
        Dim ppl_id, sWhere, sSelect As String
        Dim dt As Data.DataTable
        Dim pro_id As Integer = m_InvalidId
        Dim source_bin_prod_list As New List(Of myCompositeObjects.BinProd)
        Dim dest_bin_prod_list As New List(Of myCompositeObjects.BinProd)
        Dim lot_list As New List(Of Integer)
        Dim bin_id As Integer = m_InvalidId
        Dim m_StrError As String = String.Empty

        If Current.Request.QueryString("sql_where") IsNot Nothing AndAlso Current.Request.QueryString("sql_where").ToString <> String.Empty Then
            sWhere = Current.Request.QueryString("sql_where").ToString
            ppl_id = WebTools.tools.GetQueryParamFromWhereStatement(sWhere, "PPL_ID")

            sSelect = "SELECT * FROM PRODUCTION_PLAN_TO_CONTROLS WHERE PPL_ID = " & ppl_id &
                        " AND CTRL_ID = " & EnumProductionPlanControlTypes.SourceBinRestTime
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows
                    myErrorMessage.AppendCheckBinRestTimeMsg(m_StrError, m_config, CInt(dr("PARAM_1").ToString), "", False, True)
                Next
            End If

            sSelect = "SELECT * FROM PRODUCTION_PLAN_TO_CONTROLS WHERE PPL_ID = " & ppl_id &
                        " AND CTRL_ID = " & EnumProductionPlanControlTypes.SourceBinExcluded
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows
                    myErrorMessage.AppendDownloadNotEnabledMsg(m_StrError, m_config, CInt(dr("PARAM_1").ToString))
                Next
            End If

            sSelect = "SELECT * FROM PRODUCTION_PLAN_TO_CONTROLS WHERE PPL_ID = " & ppl_id &
                        " AND CTRL_ID = " & EnumProductionPlanControlTypes.SourceBinProd &
                        " ORDER BY PARAM_1, PARAM_2"

            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            pro_id = m_InvalidId

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows

                    ' gestisco il caso di ciclo con più prodotti in ingresso (e.g. ciclo con coda di origini)
                    If pro_id = m_InvalidId Then
                        pro_id = CInt(dr("PARAM_1").ToString)

                    ElseIf pro_id <> CInt(dr("PARAM_1").ToString) Then
                        ' stampo il messaggio "precedente"
                        myErrorMessage.AppendCheckBinProductMsg(m_StrError, m_config, pro_id, source_bin_prod_list, True)

                        ' salvo il prodotto "nuovo" e vuoto la lista
                        pro_id = CInt(dr("PARAM_1").ToString)
                        source_bin_prod_list.Clear()
                    End If

                    Dim bin_prod As New myCompositeObjects.BinProd(CInt(dr("PARAM_2").ToString), CInt(dr("PARAM_3").ToString))
                    source_bin_prod_list.Add(bin_prod)
                Next

                myErrorMessage.AppendCheckBinProductMsg(m_StrError, m_config, pro_id, source_bin_prod_list, True)
            End If

            sSelect = "SELECT * FROM PRODUCTION_PLAN_TO_CONTROLS WHERE PPL_ID = " & ppl_id &
                        " AND CTRL_ID = " & EnumProductionPlanControlTypes.DestBinProd &
                        " ORDER BY PARAM_1, PARAM_2"

            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            pro_id = m_InvalidId

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows

                    ' gestisco il caso di ciclo con più prodotti in uscita (e.g. molino)
                    If pro_id = m_InvalidId Then
                        pro_id = CInt(dr("PARAM_1").ToString)

                    ElseIf pro_id <> CInt(dr("PARAM_1").ToString) Then
                        ' stampo il messaggio "precedente"
                        myErrorMessage.AppendCheckBinProductMsg(m_StrError, m_config, pro_id, dest_bin_prod_list, False)

                        ' salvo il prodotto "nuovo" e vuoto la lista
                        pro_id = CInt(dr("PARAM_1").ToString)
                        dest_bin_prod_list.Clear()
                    End If

                    Dim bin_prod As New myCompositeObjects.BinProd(CInt(dr("PARAM_2").ToString), CInt(dr("PARAM_3").ToString))
                    dest_bin_prod_list.Add(bin_prod)
                Next

                ' stampo l'ultimo messaggio
                myErrorMessage.AppendCheckBinProductMsg(m_StrError, m_config, pro_id, dest_bin_prod_list, False)
            End If

            sSelect = "SELECT * FROM PRODUCTION_PLAN_TO_CONTROLS WHERE PPL_ID = " & ppl_id &
                        " AND CTRL_ID = " & EnumProductionPlanControlTypes.DestBinLots &
                        " ORDER BY PARAM_2"
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows

                    If bin_id <> CInt(dr("PARAM_2").ToString) AndAlso bin_id <> m_InvalidId Then

                        myErrorMessage.AppendCheckBinLotsMsg(m_StrError, m_config, bin_id, lot_list)

                        lot_list.Clear()
                    End If

                    bin_id = CInt(dr("PARAM_2").ToString)
                    lot_list.Add(dr("PARAM_1").ToString)
                Next

                myErrorMessage.AppendCheckBinLotsMsg(m_StrError, m_config, bin_id, lot_list)
            End If

            sSelect = "SELECT * FROM PRODUCTION_PLAN_TO_CONTROLS WHERE PPL_ID = " & ppl_id &
                         " AND CTRL_ID = " & EnumProductionPlanControlTypes.DestBinExcluded
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows
                    myErrorMessage.AppendLoadNotEnabledMsg(m_StrError, m_config, CInt(dr("PARAM_1").ToString))
                Next
            End If

            If m_StrError.Length > 0 Then
                msgShowControls.Show(m_StrError)
            Else
                If UsersGUI.tools.GetOrderParameter("IGNORE_CONTROLS", ppl_id) = String.Empty OrElse UsersGUI.tools.GetOrderParameter("IGNORE_CONTROLS", ppl_id) = 0 Then
                    msgShowControls.Show(m_config.GetEntryByKeyName("CHECK_CONTROLS_OK").GetValue)
                Else
                    msgShowControls.Show(m_config.GetEntryByKeyName("CHECK_CONTROLS_IGNORED").GetValue)
                End If
            End If
        Else
            ' should never happen
        End If
    End Sub

    Private Sub ShowRecipeExecutable()
        Dim ppl_id, str_where, str_select As String
        Dim dt As Data.DataTable
        Dim str_error As String = String.Empty

        If Current.Request.QueryString("sql_where") IsNot Nothing AndAlso Current.Request.QueryString("sql_where").ToString <> String.Empty Then
            str_where = Current.Request.QueryString("sql_where").ToString
            ppl_id = WebTools.tools.GetQueryParamFromWhereStatement(str_where, "PPL_ID")

            str_select = "SELECT * FROM PROCESS_RECIPES WHERE PPL_ID = " & ppl_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_select, False)

            If dt.Rows.Count > 0 Then
                For Each dr As Data.DataRow In dt.Rows

                    If Not IsDBNull(dr("CEL_ID")) Then
                        myErrorMessage.AppendIngredientFoundInBinMsg(str_error, m_config, CInt(dr("PRO_ID").ToString), CInt(dr("CEL_ID").ToString))
                    Else
                        myErrorMessage.AppendIngredientNotFoundInBinMsg(str_error, m_config, CInt(dr("PRO_ID").ToString))
                    End If
                Next
            End If

            msgShowControls.Show(str_error)
        Else
            ' should never happen
        End If
    End Sub

End Class