﻿// My history "back" navigation
const BACK_STEP = {
    START: 0,
    APPLIED: 1,
}

// Session
const MAX_SESSION_IDENTIFIER_VALUE = 999999999999999;

// JS styles
const CUSTOM_STYLES = {
    BORDER: 0,
    "BORDER-TOP": 1,
    "BORDER-RIGHT": 2,
    "BORDER-BOTTOM": 3,
    "BORDER-LEFT": 4,
    "BORDER-Y": 5,
    "BORDER-X": 6,
    BOLD: 7,
    "BOLD-VALUE": 8,
    "BOLD-LABEL": 9,
    "BG-GRAY": 10,
    "MARGIN-AUTO": 11,
    "MARGIN-TOP-AUTO": 12,
    "MARGIN-RIGHT-AUTO": 13,
    "MARGIN-BOTTOM-AUTO": 14,
    "MARGIN-LEFT-AUTO": 15,
    "MARGIN-Y-AUTO": 16,
    "MARGIN-X-AUTO": 17,
    "TEXT-CENTER": 18,
    "TEXT-LEFT": 19,
    "TEXT-RIGHT": 20,
    "TEXT-TOP": 21,
    "TEXT-MIDDLE": 22,
    "TEXT-BOTTOM": 23,
    "BG-POSITION-Y-TOP": 24,
    "BG-POSITION-Y-CENTER": 25,
    "BG-POSITION-Y-BOTTOM": 26,
    "BG-POSITION-X-LEFT": 27,
    "BG-POSITION-X-CENTER": 28,
    "BG-POSITION-X-RIGHT": 29,
    "LABEL-TEXT-CENTER": 30,
    "LABEL-TEXT-LEFT": 31,
    "LABEL-TEXT-RIGHT": 32,
    "VALUE-TEXT-CENTER": 33,
    "VALUE-TEXT-LEFT": 34,
    "VALUE-TEXT-RIGHT": 35,
    "LABEL-VALUE-WIDTH-ADAPT": 36,
    "LABEL-VALUE-25-75": 37,
    "LABEL-VALUE-75-25": 38,
    "PADDING-TOP-SM": 39,
    "PADDING-TOP-MD": 40,
    "PADDING-TOP-LG": 41,
    "PADDING-RIGHT-SM": 42,
    "PADDING-RIGHT-MD": 43,
    "PADDING-RIGHT-LG": 44,
    "PADDING-BOTTOM-SM": 45,
    "PADDING-BOTTOM-MD": 46,
    "PADDING-BOTTOM-LG": 47,
    "PADDING-LEFT-SM": 48,
    "PADDING-LEFT-MD": 49,
    "PADDING-LEFT-LG": 50,
    "PADDING-X-SM": 51,
    "PADDING-X-MD": 52,
    "PADDING-X-LG": 53,
    "PADDING-Y-SM": 54,
    "PADDING-Y-MD": 55,
    "PADDING-Y-LG": 56,
    "LABEL-VALUE-VERTICAL": 57,
    0: "BORDER",
    1: "BORDER-TOP",
    2: "BORDER-RIGHT",
    3: "BORDER-BOTTOM",
    4: "BORDER-LEFT",
    5: "BORDER-Y",
    6: "BORDER-X",
    7: "BOLD",
    8: "BOLD-VALUE",
    9: "BOLD-LABEL",
    10: "BG-GRAY",
    11: "MARGIN-AUTO",
    12: "MARGIN-TOP-AUTO",
    13: "MARGIN-RIGHT-AUTO",
    14: "MARGIN-BOTTOM-AUTO",
    15: "MARGIN-LEFT-AUTO",
    16: "MARGIN-Y-AUTO",
    17: "MARGIN-X-AUTO",
    18: "TEXT-CENTER",
    19: "TEXT-LEFT",
    20: "TEXT-RIGHT",
    21: "TEXT-TOP",
    22: "TEXT-MIDDLE",
    23: "TEXT-BOTTOM",
    24: "BG-POSITION-Y-TOP",
    25: "BG-POSITION-Y-CENTER",
    26: "BG-POSITION-Y-BOTTOM",
    27: "BG-POSITION-X-LEFT",
    28: "BG-POSITION-X-CENTER",
    29: "BG-POSITION-X-RIGHT",
    30: "LABEL-TEXT-CENTER",
    31: "LABEL-TEXT-LEFT",
    32: "LABEL-TEXT-RIGHT",
    33: "VALUE-TEXT-CENTER",
    34: "VALUE-TEXT-LEFT",
    35: "VALUE-TEXT-RIGHT",
    36: "LABEL-VALUE-WIDTH-ADAPT",
    37: "LABEL-VALUE-25-75",
    38: "LABEL-VALUE-75-25",
    39: "PADDING-TOP-SM",
    40: "PADDING-TOP-MD",
    41: "PADDING-TOP-LG",
    42: "PADDING-RIGHT-SM",
    43: "PADDING-RIGHT-MD",
    44: "PADDING-RIGHT-LG",
    45: "PADDING-BOTTOM-SM",
    46: "PADDING-BOTTOM-MD",
    47: "PADDING-BOTTOM-LG",
    48: "PADDING-LEFT-SM",
    49: "PADDING-LEFT-MD",
    50: "PADDING-LEFT-LG",
    51: "PADDING-X-SM",
    52: "PADDING-X-MD",
    53: "PADDING-X-LG",
    54: "PADDING-Y-SM",
    55: "PADDING-Y-MD",
    56: "PADDING-Y-LG",
    57: "LABEL-VALUE-VERTICAL",
};