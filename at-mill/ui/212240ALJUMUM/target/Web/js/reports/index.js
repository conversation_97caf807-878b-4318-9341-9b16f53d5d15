﻿//#region Public

var configuration = null;
var mediaType = null;
var reportId = null;
var reportTypeId = null;
var nameJSON = null;
var valueJSON = null;
var alarmJSON = null;
var languageJSON = null;

// Customizable in specific reports
var SpecificElementDrawing = {}; // Checked if the element type is not TABLE, GRID or OPERATION

var Operations = {}; // Customizable object responsible for drawing and handling operations
var PrecalculatedValues = {}; // Customizable object responsible for precalculating notable values

/**
 * Function called on page load, initializes and draws the page.
 * @param {number} reportIdx Report index
 * @param {number} type Type ID
 */
function initializeReport(reportIdx, type) {
    //let timestamp = Number(new Date());

    reportId = reportIdx
    reportTypeId = type;

    // Hides the container while drawing the content, then makes it visible again
    $("#print_area").hide(0);

    const genericConfiguration = CONFIGURATION[REPORT_TYPES[type]];
    const specificConfiguration = SPECIFIC_CONFIGURATION[REPORT_ID[reportIdx]] ? SPECIFIC_CONFIGURATION[REPORT_ID[reportIdx]][REPORT_TYPES[type]] : {};

    // Merge configurations
    configuration = mergeConfigurationObject(genericConfiguration, specificConfiguration, []);

    // Setup grid layout
    $("#reportsContainer").css("grid-template-columns", `repeat(${configuration.grid.x}, 1fr)`);
    $("#reportsContainer").css("grid-template-rows", `repeat(${configuration.grid.y}, auto)`);

    // Retrieve and removes nameJSON, valueJSON, alarmJSON and languageJSON from sessionStorage
    nameJSON = JSON.parse(sessionStorage.getItem("nameJSON"));
    valueJSON = JSON.parse(sessionStorage.getItem("valueJSON"));
    alarmJSON = JSON.parse(sessionStorage.getItem("alarmJSON"));
    languageJSON = JSON.parse(localStorage.getItem("reportLanguages")).reduce((languages, translation) => {
        for (var key in translation) {
            if (translation.hasOwnProperty(key)) {
                languages[key] = translation[key];
            }
        }
        return languages;
    }, {});

    sessionStorage.removeItem("nameJSON");
    sessionStorage.removeItem("valueJSON");
    sessionStorage.removeItem("alarmJSON");

    // Fill report name
    $("#reportHeader").html(getTranslatedTextReport(configuration.title));

    //console.debug(`Initialization of report type ${type} (index ${reportIdx}) took ${Number(new Date()) - timestamp}ms`);
}

/**
 * Updates the interface with the new data received
 */
function updateReport() {
    let timestamp = Number(new Date());

    // Empty the container in cases when the previous elements somehow persist a refresh (has to do with ASP.NET)
    $("#reportsContainer").html('');

    // Retrieve valueJSON from sessionStorage
    valueJSON = JSON.parse(sessionStorage.getItem("valueJSON"));
    sessionStorage.removeItem("valueJSON");

    // Check consistency of data from server (check for duplicates and missing data)
    if (!dataHasDuplicates()) {
        // Calculate remarkable values (appends to key 'values' in valueJSON)
        precalculateValues();

        // Draw elements
        for (let key in configuration.content) {
            drawElement(configuration.content[key]);
        }

        // Finish calculations for tables
        calculateTablePercentages();
        calculateTableAverages();
    }

    // Check alarmJSON and apply classes accordingly
    checkAlarms();

    // Collapse grid layout into rows when possible
    collapseGridForPrinting();

    //console.debug(`Drawing of report took ${Number(new Date()) - timestamp}ms`);

    addResizeObserver($("#print_area")[0], (entries) => {
        resizeIFrame();
    });

    // Execute specific report-related code if necessary
    if (!isNullOrUndefined(specificReportCode[REPORT_TYPES[reportTypeId]])) {
        specificReportCode[REPORT_TYPES[reportTypeId]](REPORT_ID[reportId]);
    }

    // Shows the container after it has been filled
    // This is the only way I achieved to fix a bug in which between the row of the last table and the row right below that
    // remains a misterious space. This bug has been detected only in Firefox.
    $("#print_area").show(0);
}

//#endregion Public

//#region Private

//  #region Support functions

/**
 * If the page is loaded in the iframe sets its height to match the #print_area element
 */
function resizeIFrame() {
    if (window.parent !== window) {
        // Set height of IFrame
        var printArea = document.getElementById("print_area");

        window.parent.document.getElementById(window.frameElement.id).style.height = (printArea.offsetHeight + printArea.offsetTop + 10) + "px";
    }
}

/**
 * Recursively checks and merges the specific configuration with the generic one.
 * @param {any} target Object to update
 * @param {any} source Object to check
 * @param {string[]} keys Array of previous keys in recursive calls
 */
function mergeConfigurationObject(target, source, keys) {
    for (let key in source) {
        if (typeof source[key] === "object") {
            if (target[key] === undefined) {
                target[key] = source[key];
            } else if (source[key] === null) {
                delete target[key];
            } else {
                mergeConfigurationObject(target[key], source[key], keys.concat(key));
            }
        } else {
            let configurationAttribute = target;

            configurationAttribute[key] = source[key];
        }
    }

    return target;
}

/**
 * Searches for print keys in the configuration, and manages them according to the media type
 * @param {any} obj Object to check
 */
function processConfigurationForPrint(obj = configuration) {
    for (let key in obj) {
        if (key === "print") {
            if (mediaType === "print") {
                Object.assign(obj[printKey], obj);
            } else {
                delete obj[key];
            }
        } else {
            if (obj[key] === "object") {
                processConfigurationForPrint(obj[key]);
            }
        }
    }
}

/**
 * Manages the drawing of the elements as described by the merged configurations.
 * The element type is defined as so: {type: number, position: {x: number, y: number}, size: {x: number, y: number}, parameters: any}.
 * @param {any} elem Element to draw
 * @param {jQuery<HTMLElement>} parent Parent jQuery element (null if #reportsContainer)
 */
function drawElement(elem, parent = $("#reportsContainer")) {
    if (checkMandatoryParameters(elem.type, elem.parameters)) {
        if (elem.visible !== false) {
            let $elem;

            switch (elem.type) {
                case ELEMENT_TYPES.GRID:
                    $elem = drawGrid(elem, parent);
                    break;

                case ELEMENT_TYPES.TABLE:
                    $elem = drawTable(elem, parent);
                    break;

                case ELEMENT_TYPES.OPERATION:
                    $elem = drawOperation(elem, parent);
                    break;

                default:
                    // In case the element type is managed by some specific report type
                    if (SpecificElementDrawing[ELEMENT_TYPES[elem.type]] && typeof SpecificElementDrawing[ELEMENT_TYPES[elem.type]] === "function") {
                        $elem = SpecificElementDrawing[ELEMENT_TYPES[elem.type]](elem, parent);
                    } else {
                        console.error(`[drawElement] Unknown element type: "${elem.type}".`);
                        console.error("[drawElement] Element: ", elem);
                        console.error("[drawElement] Parent: ", parent);
                    }
                    break;
            }

            if ($elem && $elem.length > 0) {
                if (elem.id) {
                    $elem.attr("id", elem.id);
                }

                // Manage grid size and position
                $elem.css("grid-column", `${elem.position.x} / span ${elem.size.x}`);
                $elem.css("grid-row", `${elem.position.y} / span ${elem.size.y}`);

                // Manage grid print size and position
                if (elem.print) {
                    if (elem.id) {
                        let $style = $("<style>");
                        let printSize = elem.print.size || elem.size;
                        let printPosition = elem.print.position || elem.position;

                        $style[0].innerHTML =
                            `@media print {` +
                            `#${elem.id} {` +
                            `grid - column: ${printPosition.x} / span ${printSize.x} !important; grid-row: ${printPosition.y} / span ${printSize.y} !important;` +
                            `}` +
                            `}` +
                            `;`

                        $elem.before($style);

                        // Format: "row,column,rowSpan,columnSpan"
                        $elem.attr("print-grid", `${printPosition.y},${printPosition.x},${printSize.y},${printSize.x}`);
                    } else {
                        console.error("[drawElement] A print layout was defined, but the element doesn't have an ID.", elem);
                    }
                }

                // Manage custom styles
                setCustomStyles($elem, elem.parameters.styles);
            }
        }
    } else {
        console.error(`[drawElement] Missing parameters for type ${ELEMENT_TYPES[elem.type]} (expected "${MANDATORY_ELEMENT_PARAMETERS[ELEMENT_TYPES[elem.type]].join('","')}").`);
        console.error("[drawElement] Element: ", elem);
        console.error("[drawElement] Parent: ", parent);
    }
}

/**
 * Checks whether the mandatory parameters for the current type are present on the object.
 * @param {number} type Element type number
 * @param {any} obj Parameters object
 */
function checkMandatoryParameters(type, obj) {
    return MANDATORY_ELEMENT_PARAMETERS[ELEMENT_TYPES[type]].every((k) => k in obj);
}

/**
 * Draws a grid element and starts the drawing of children.
 * @param {any} elem Element object
 * @param {jQuery<HTMLElement>} parent Parent jQuery element
 */
function drawGrid(elem, parent) {
    let $elem = $('<div class="reports--grid"></div>');
    $elem.css("grid-template-columns", `repeat(${elem.parameters.grid.x}, 1fr)`).css("grid-template-rows", `repeat(${elem.parameters.grid.y}, auto)`);

    for (let childKey in elem.parameters.content) {
        drawElement(elem.parameters.content[childKey], $elem);
    }

    parent.append($elem);

    return $elem;
}

/**
 * Draws the table element containing the header (if needed) and an empty body.
 * @param {any} table Element object
 * @param {jQuery<HTMLElement>} parent Parent jQuery element
 */
function drawTable(table, parent) {
    if (table.id) {
        let $elem = $(`<table id="${table.id}" class="reports--table"></table>`);
        let $tbody = $(`<tbody data-table="${table.id}"></tbody>`);

        let orderedColumnKeys = Object.keys(table.parameters.columns).sort((a, b) => table.parameters.columns[a].index - table.parameters.columns[b].index);

        // Retrieve all info about the table's contents
        let tableElements = [];
        for (let key of table.parameters.contents) {
            let keyArr = key.split(".");
            let currObj = nameJSON[keyArr[0]].find((v) => v.name === keyArr[1]);
            let currValueObj = valueJSON[keyArr[0]].find((v) => v.name === keyArr[1]);

            if (currObj != undefined) {
	            currObj.path = key;
	            currObj.valueObj = currValueObj;
	
	            tableElements.push(currObj);
	        }
        }

        if (!tableElements.some((el) => el.valueObj ? el.valueObj.elem.some((v) => !isNullOrUndefined(v)) : false) && table.parameters.hideIfEmpty) {
            $elem.hide();
            parent.append($elem);
        } else {
            let nonSumRowsAbove = 0;
            let nonTotalRowsAbove = 0;
            let element;

            // Draw table preparing rows
            for (let i = 0; i < tableElements.length; i++) {
                element = tableElements[i];
                if (element.elem) {
                    // 1 - This is a list of rows for which I will receive values
                    for (let row of element.elem) {
                        let $row = $(`<tr id="${element.path}.${row.id}" class="reports--table--row"></tr>`);

                        for (let column of orderedColumnKeys) {
                            let $td = $(`<td id="${element.path}.${row.id}.${column}"></td>`);
                            if (table.parameters.columns[column].main) {
                                // The row description column
                                $td.html(row.val);
                            } else if (!isNullOrUndefined(table.parameters.columns[column].valueIndex)) {
                                // The column is a simple values one
                                let val = element.valueObj.elem.find((v) => v.id === row.id).val;

                                if (typeof val === "object") {
                                    val = val[table.parameters.columns[column].valueIndex];
                                }

                                // Handles the value
                                if (isNullOrUndefined(val)) {
                                    // 1 - If null
                                    if (table.parameters.columns[column].hideIfNullValue) {
                                        // Hides the TD if the "hideIfNullValue" option is set
                                        $td.html("");
                                        $td.attr("isnullvalue", "");
                                    } else {
                                        $td.html(PLACEHOLDER_STRING);
                                    }
                                } else if (typeof val === "number") {
                                    // 2 - If number

                                    // Handle dynamic units
                                    switch (table.parameters.columns[column].unit) {
                                        case UNITS.DYNAMIC_WEIGHT_UNIT:
                                            $td.html(formatDynamicUnitWeight(val, table.parameters.columns[column].decimals, table.parameters.columns[column].decimalMode))
                                            $td.attr("data-unit", getDynamicUnitWeight(val));
                                            break;

                                        case UNITS.DYNAMIC_FLOWRATE_UNIT:
                                            $td.html(formatDynamicUnitFlowrate(val, table.parameters.columns[column].decimals, table.parameters.columns[column].decimalMode))
                                            $td.attr("data-unit", getDynamicUnitFlowrate(val));
                                            break;

                                        default:
                                            $td.html(formatValueWithDecimals(val, table.parameters.columns[column].decimals, table.parameters.columns[column].decimalMode));
                                    }
                                } else {
                                    // 3 - Others
                                    $td.html(val);
                                }

                                $td.attr("data-value", isNullOrUndefined(val) ? 0 : val);
                            } else {
                                // Checks if the column is of a specific special type, and if so manages it
                                if (!manageSpecialColumn($td, table.parameters.columns[column])) {
                                    // Otherwise the column has not been configured correctly
                                    console.error(`[drawTable] Column "${column}" in table "${table.id}" is incorrectly configured.`);
                                }
                            }

                            // Gets the translation for the unit and sets it as an attribute, so that the css can use it as the after's content
                            if (table.parameters.columns[column].unit !== undefined && table.parameters.columns[column].unit !== UNITS.DYNAMIC_WEIGHT_UNIT && table.parameters.columns[column].unit !== UNITS.DYNAMIC_FLOWRATE_UNIT) {
                                $td.attr("data-unit", getUnitTranslation(table.parameters.columns[column].unit));
                            }
                            $td.attr("data-unitenum", table.parameters.columns[column].unit);

                            $row.append($td);
                        }

                        $tbody.append($row);

                        nonSumRowsAbove += 1;
                        nonTotalRowsAbove += 1;
                    }
                } else if (element.sum) {
                    // 2- This is a total row, I will have to make calculations to fill it

                    let $row = $(`<tr id="${element.path}" class="reports--table--row" data-total-type="${element.style}"></tr>`);

                    for (let column of orderedColumnKeys) {
                        let $td = $(`<td id="${element.path}.${column}"></td>`);
                        if (table.parameters.columns[column].main) {
                            // The row description column
                            $td.html(element.trad);
                        } else {
                            // Checks if the column is of a specific special type, and if so manages it
                            if (!manageSpecialColumn($td, table.parameters.columns[column]) && !table.parameters.columns[column].noTotal) {
                                // Otherwise the column is a total of simple values, so get the data needed in order to calculate the total of the column
                                let calculatedTotal = 0;

                                if (table.parameters.columns[column].hideIfNullValue && element.sum.every(k => $tbody.find(`[id^="${element.path.replace(/\.\w+$/g, `.${k}`)}"][id$=".${column}"][isnullvalue]`).length > 0)) {
                                    $td.html(null);

                                    $td.attr("data-value", 0);
                                } else {
                                    for (let key of element.sum) {
                                        $tbody.find(`[id^="${element.path.replace(/\.\w+$/g, `.${key}`)}"][id$=".${column}"][data-value]`).each((idx, valueElement) => {
                                            calculatedTotal += Number(valueElement.dataset.value);
                                        });
                                    }

                                    // Handle dynamic units
                                    switch (table.parameters.columns[column].unit) {
                                        case UNITS.DYNAMIC_WEIGHT_UNIT:
                                            $td.html(formatDynamicUnitWeight(calculatedTotal, table.parameters.columns[column].decimals, table.parameters.columns[column].decimalMode));
                                            $td.attr("data-unit", getDynamicUnitWeight(calculatedTotal));
                                            break;

                                        case UNITS.DYNAMIC_FLOWRATE_UNIT:
                                            $td.html(formatDynamicUnitFlowrate(calculatedTotal, table.parameters.columns[column].decimals, table.parameters.columns[column].decimalMode));
                                            $td.attr("data-unit", getDynamicUnitFlowrate(calculatedTotal));
                                            break;

                                        default:
                                            $td.html(formatValueWithDecimals(calculatedTotal, table.parameters.columns[column].decimals, table.parameters.columns[column].decimalMode));
                                    }

                                    $td.attr("data-value", calculatedTotal);
                                }
                            }
                        }

                        // Gets the translation for the unit and sets it as an attribute, so that the css can use it as the after's content
                        if (table.parameters.columns[column].unit !== undefined && table.parameters.columns[column].unit !== UNITS.DYNAMIC_WEIGHT_UNIT && table.parameters.columns[column].unit !== UNITS.DYNAMIC_FLOWRATE_UNIT) {
                            $td.attr("data-unit", getUnitTranslation(table.parameters.columns[column].unit));
                        }
                        $td.attr("data-unitenum", table.parameters.columns[column].unit);

                        $row.append($td);
                    }

                    if ((nonSumRowsAbove <= 1 && element.style === "sub_total") || (nonTotalRowsAbove <= 1 && element.style === "total")) {
                        $tbody.append($row);

                        $row.css("display", "none");
                        $row.attr("hiddenTotal", "");

                        if (element.style === "sub_total" && tableElements.slice(i).filter((v) => v.valueObj ? v.valueObj.elem.length > 0 : false).length > 0) {
                            // Adds a dividing line instead of the sub total row if the row after this with some data in it isn't a total
                            $tbody.append($(`<tr class="reports--table--row--hr">${orderedColumnKeys.reduce((p, v) => p + "<td></td>", "")}</tr>`));

                            nonSumRowsAbove = 0;
                        }
                    }
                    else {
                        $tbody.append($row);

                        if (element.style === "sub_total") {
                            nonSumRowsAbove = 0;
                        } else {
                            nonTotalRowsAbove = 0;
                        }
                    }
                } else {
                    console.error(`[drawTable] Element "${element.name}" for table "${table.id}" incorrectly configured (missing 'sum' or 'elem').`);
                }
            }

            // Create and prepend to the tbody a ruler row
            $tbody.prepend($(`<tr class="reports--table--row--ruler">${orderedColumnKeys.map((v) => '<td data-column="' + v + '"></td>').join("")}</tr>`));

            // Manage column width via the ruler row
            for (let column of orderedColumnKeys) {
                if (!isNullOrUndefined(table.parameters.columns[column].width)) {
                    $tbody.find(`.reports--table--row--ruler td[data-column="${column}"]`).attr("width", table.parameters.columns[column].width * 100 + "%");
                } else {
                    // Set a default minimum width for the column calculated as [(100 / (columnsNumber + 1)) %]. (for example, 3 columns => 25%, 7 columns => 12.5%)
                    $tbody.find(`.reports--table--row--ruler td[data-column="${column}"]`).css("min-width", 100 / (orderedColumnKeys.length + 1) + "%");
                }
            }

            // Place the element in the DOM
            $elem.append($tbody);
            parent.append($elem);

            // Fix the width of every column before appending the header (ignoring the custom ones)
            $tbody.find(".reports--table--row--ruler td").each((idx, v) => {
                // Ignoring the custom ones
                if (isNullOrUndefined($(v).attr("width"))) {
                    $(v).attr("width", Math.max(($(v).width() * 100) / $(v).parent().width(), Number($(v).css("min-width").replace("%", ""))) + "%");
                }
            });

            if (table.parameters.header) {
                // Draw the header
                if (Object.keys(table.parameters.columns).every((v) => !!table.parameters.columns[v].title)) {
                    let $header = $("<thead></thead>");
                    let $headerRow = $("<tr></tr>");

                    for (let columnName of orderedColumnKeys) {
                        $headerRow.append($(`<th data-column="${columnName}">${getTranslatedTextReport(table.parameters.columns[columnName].title)}</th>`));
                    }

                    $header.append($headerRow);
                    $elem.prepend($header);
                } else {
                    console.error(`[drawTable] Ignoring header for table "${table.id}" because of missing columns titles.`);
                }
            }
        }

        return $elem;
    } else {
        console.error(`[drawTable] Table element is missing an ID.`, table);
    }
}

/**
 * Manages special column types: percentages, averages.
 * @param {JQuery<HTMLElement>} $elem Target element
 * @param {*} columnConfig Configuration object for the column
 */
function manageSpecialColumn($elem, columnConfig) {
    let retVal = true;

    // Set info as attributes, the calculations will happen later, once that every row is filled with data from the server)

    if (columnConfig.percentage) {
        // 1. Percentage column
        $elem.attr("data-percentage-of", columnConfig.percentage.of);
        $elem.attr("data-percentage-over", columnConfig.percentage.over);
        $elem.attr("data-decimals", columnConfig.decimals);
        $elem.attr("data-decimalmode", columnConfig.decimalMode);
        $elem.attr("data-hideifnullvalue", columnConfig.hideIfNullValue);
    } else if (columnConfig.average) {
        // 2. Average column
        $elem.attr("data-average-on", columnConfig.average.on);
        $elem.attr("data-average-of", columnConfig.average.of);
        $elem.attr("data-average-over", columnConfig.average.over);
        $elem.attr("data-decimals", columnConfig.decimals);
        $elem.attr("data-decimalmode", columnConfig.decimalMode);
        $elem.attr("data-hideifnullvalue", columnConfig.hideIfNullValue);
    } else {
        retVal = false;
    }

    return retVal;
}

//#region Operations

Operations.TEST = ($elem, elem) => {
    $elem.append($("<label>TEST:</label>"), $("<span>TEST</span>"));
};

Operations.LABEL_VALUE = ($elem, elem) => {
    if (!isNullOrUndefined(elem.parameters.label)) {
        $elem.append($(`<label>${getTranslatedTextReport(elem.parameters.label)}</label>`));
    }
    if (!isNullOrUndefined(elem.parameters.value)) {
        $elem.append($(`<span>${getTranslatedTextReport(formatWithValues(elem.parameters.value))}</span>`));
    }

    // Handles "hideIfNullValue" option
    if (elem.parameters.hideIfNullValue && isNullOrUndefined(getValueByPath(elem.parameters.value.match(/\{([^}]+)\}/)[1])) && formatWithValues(elem.parameters.value).indexOf(PLACEHOLDER_STRING) !== -1) {
        $elem.css("visibility", "hidden");  // Does not mess up the layout of the grid
    }

    // Handles "hideIfNullName" option
    if (elem.parameters.hideIfNullName && getTranslatedTextReport(elem.parameters.label).indexOf("undefined") !== -1) {
        $elem.css("display", "none");
    }
};

Operations.LOSS_ON_B1 = ($elem, elem) => {
    if (elem.parameters.label) {
        $elem.append($(`<label>${getTranslatedTextReport(elem.parameters.label)}</label>`));
    }

    let value = Number($('[id="product.tot.onB1"]').data("value")) ? 100 - Number($('[id="product.tot.onB1"]').data("value")) : PLACEHOLDER_STRING;
    $elem.append($(`<span id="LOSS">${typeof value === "number" ? formatValueWithDecimals(value) : value}</span>`).attr("data-value", typeof value === "number" ? value : null));

    let obs;

    obs = addMutationObserver($('[id="product.tot.onB1"]')[0], (mut) => {
        // if some has type="childList" and addedNodes.length > 0, then
        if (mut.some((v) => v.type === "childList")) {
            obs.disconnect();

            let value = Number($('[id="product.tot.onB1"]').data("value")) ? 100 - Number($('[id="product.tot.onB1"]').data("value")) : PLACEHOLDER_STRING;

            checkAlarmOnElement(
                $elem
                    .find("span#LOSS")
                    .html(`${typeof value === "number" ? formatValueWithDecimals(value) : value} %`)
                    .attr("data-value", typeof value === "number" ? value : null)
            );
        }
    });
};

Operations.LOSS_ON_B0 = ($elem, elem) => {
    if (elem.parameters.label) {
        $elem.append($(`<label>${getTranslatedTextReport(elem.parameters.label)}</label>`));
    }

    let value = Number($('[id="product.tot.onB0"]').data("value")) ? 100 - Number($('[id="product.tot.onB0"]').data("value")) : PLACEHOLDER_STRING;
    $elem.append($(`<span id="LOSS">${typeof value === "number" ? formatValueWithDecimals(value) : value}</span>`).attr("data-value", typeof value === "number" ? value : null));

    let obs;

    obs = addMutationObserver($('[id="product.tot.onB0"]')[0], (mut) => {
        // if some has type="childList" and addedNodes.length > 0, then
        if (mut.some((v) => v.type === "childList")) {
            obs.disconnect();

            let value = Number($('[id="product.tot.onB0"]').data("value")) ? 100 - Number($('[id="product.tot.onB0"]').data("value")) : PLACEHOLDER_STRING;

            $elem
                .find("span#LOSS")
                .html(`${typeof value === "number" ? formatValueWithDecimals(value) : value} %`)
                .attr("data-value", typeof value === "number" ? value : null)
        }
    });
};

/**
 * Draws an operation element according to its operation key (from the OPERATIONS enumerable).
 * @param {any} elem Element object
 * @param {jQuery<HTMLElement>} parent Parent jQuery element
 */
function drawOperation(elem, parent) {
    let $elem = $('<div class="reports--operation"></div>');

    if (!isNullOrUndefined(Operations[OPERATIONS[elem.parameters.operation]]) && typeof Operations[OPERATIONS[elem.parameters.operation]] === "function") {
        Operations[OPERATIONS[elem.parameters.operation]]($elem, elem);
    } else {
        console.error(`[drawOperation] Unknown operation: ${elem.parameters.operation}.`);

        $elem.append($(`<span style="color: red;">Error: unknown operation index ${elem.parameters.operation}</span>`));
    }

    $elem.addClass(`reports--operation--${OPERATIONS[elem.parameters.operation].toLowerCase()}`);

    parent.append($elem);

    return $elem;
}

//#endregion Operations

/**
 * Calculates the values and fills the columns representing certain percentages between others
 */
function calculateTablePercentages() {
    $("[data-percentage-of][data-percentage-over]")
        .sort((a) => ($(a).parent().is("[data-total-type]") ? 1 : -1)) // Simple columns first, then totals
        .each((idx, elem) => {
            let onValue;
            let overValue;

            if (elem.dataset.percentageOf.indexOf("$i") === -1) {
                onValue = getValueByPath(elem.dataset.percentageOf);
            } else {
                // The string refers to the element in the current row via its column name
                onValue = Number($(`[id="${elem.dataset.percentageOf.replace("$i", elem.id.replace(/\.\w+$/g, ""))}"]`).data("value"));
            }

            if (elem.dataset.percentageOver.indexOf("$i") === -1) {
                overValue = getValueByPath(elem.dataset.percentageOver);
            } else {
                // The string refers to the element in the current row via its column name
                overValue = Number($(`[id="${elem.dataset.percentageOver.replace("$i", elem.id.replace(/\.\w+$/g, ""))}"]`).data("value"));
            }

            let percentage = overValue !== 0 ? (onValue * 100) / overValue : 0;

            elem.innerHTML = formatValueWithDecimals(percentage, elem.dataset.decimals, elem.dataset.decimalMode) || 0;

            elem.setAttribute("data-value", percentage);
        });
}

/**
 * Calculates the values and fills the columns representing average on/over others/some data
 */
function calculateTableAverages() {
    $("[data-average-of][data-average-on][data-average-over]")
        .sort((a) => ($(a).parent().is("[data-total-type]") ? 1 : -1)) // Simple columns first, then totals
        .each((idx, elem) => {
            let ofValue;
            let onValue;
            let overValue;

            if (!(ofValue = Number(elem.dataset.averageOf))) {
                if (elem.dataset.averageOf.indexOf("$i") === -1) {
                    ofValue = getValueByPath(elem.dataset.averageOf);
                } else {
                    // The string refers to the element in the current row via its column name
                    ofValue = Number($(`[id="${elem.dataset.averageOf.replace("$i", elem.id.replace(/\.\w+$/g, ""))}"]`).data("value"));
                }
            }

            if (!(onValue = Number(elem.dataset.averageOn))) {
                if (elem.dataset.averageOn.indexOf("$i") === -1) {
                    onValue = getValueByPath(elem.dataset.averageOn);
                } else {
                    // The string refers to the element in the current row via its column name
                    onValue = Number($(`[id="${elem.dataset.averageOn.replace("$i", elem.id.replace(/\.\w+$/g, ""))}"]`).data("value"));
                }
            }

            if (!(overValue = Number(elem.dataset.averageOver))) {
                if (elem.dataset.averageOver.indexOf("$i") === -1) {
                    overValue = getValueByPath(elem.dataset.averageOver);
                } else {
                    // The string refers to the element in the current row via its column name
                    overValue = Number($(`[id="${elem.dataset.averageOver.replace("$i", elem.id.replace(/\.\w+$/g, ""))}"]`).data("value"));
                }
            }

            let average = (ofValue / onValue) * overValue;

            // Handle dynamic units
            switch (Number(elem.dataset.unitenum)) {
                case UNITS.DYNAMIC_WEIGHT_UNIT:
                    elem.innerHTML = formatDynamicUnitWeight(average, elem.dataset.decimals, elem.dataset.decimalMode) || 0;
                    elem.dataset.unit = getDynamicUnitWeight(average) || "";
                    break;

                case UNITS.DYNAMIC_FLOWRATE_UNIT:
                    elem.innerHTML = formatDynamicUnitFlowrate(average, elem.dataset.decimals, elem.dataset.decimalMode) || 0;
                    elem.dataset.unit = getDynamicUnitFlowrate(average) || "";
                    break;

                default:
                    elem.innerHTML = formatValueWithDecimals(average, elem.dataset.decimals, elem.dataset.decimalMode) || 0;
            }

            elem.setAttribute("data-value", average);
        });
}

/**
 * Checks every value in alarmJSON against the values
 */
function checkAlarms() {
    // Alarms are calculated as percentage on B1, so if the B1 value is not set, there's no point in even checking the alarms
    if (!isNullOrUndefined(getDataFromValueJSONViaPath("top.B1"))) {
        // If the alarmJSON key in the sessionStorage is not set, then something's wrong with the configuration of the report on the server side
        if (!isNullOrUndefined(alarmJSON)) {
            for (let alarm of alarmJSON.alarm) {
                // Searches for the corresponding td in the product area, referring to the percentage over the EMT
                let $elem = $(`[id^="product."][id$="${alarm.id}.onB1"]`);

                // Check the validity of the bounds for the value
                if (!isNullOrUndefined(alarm.val_min) && !isNullOrUndefined(alarm.val_max) && alarm.val_max < alarm.val_min) {
                    console.error(`[checkAlarms] Incorrectly configured alarms for scale id "${alarm.id}" (max < min).`);
                }

                if ($elem.length > 0) {
                    checkAlarmOnElement($elem, alarm);
                }
            }
        } else {
            console.warn(`[checkAlarms] sessionStorage returned null for key 'alarmJSON'.`);
        }
    }
}

/**
 * Checks the value of the $elem against any alarms with corresponding ID and applies classes in case of value out of bounds.
 * @param {JQuery<HTMLElement>} $elem Element to check
 * @param {id: string, val_max: number, val_min: number} alarm Alarm object
 */
function checkAlarmOnElement($elem, alarm = null) {
    if ($elem.length > 0) {
        // If the alarm object wasn't passed to the function, gets it using as key the $elem's ID
        if (alarm === null) {
            alarm = getAlarmById($elem.attr("id"));
        }

        if (!isNullOrUndefined(alarm) && !isNaN(Number($elem.data("value")))) {
            // If the element is found, check it against the alarmJSON values
            if (!isNullOrUndefined(alarm.val_min) && Number($elem.data("value")) < alarm.val_min) {
                $elem.addClass("alarm-low");
            } else if (!isNullOrUndefined(alarm.val_max) && Number($elem.data("value")) > alarm.val_max) {
                $elem.addClass("alarm-high");
            }
        } else {
            // The alarm is not set or the value is undefined, therefore do nothing
        }
    } else {
        console.error(`[checkAlarmOnElement] Alarm for scale id "${alarm.id}" found no match.`);
    }
}

/**
 * Returns the alarm object corresponding to the id.
 * @param {string} id Key of the alarm
 */
function getAlarmById(id) {
    return alarmJSON ? alarmJSON.alarm.find((v) => v.id === id) : null;
}

/**
 * Divides the grid layout into multiple rows (when possible, that is when there are no elements spanning over multiple rows, even when printing)
 * in order to make page breaks work for printing
 */
function collapseGridForPrinting() {
    let $container = $("#reportsContainer");

    // Checks for the requirements
    if (
        !$container
            .children()
            .toArray()
            .some((v) => (v.style.gridRow.match(/.*span (\d+).*/) ? v.style.gridRow.match(/.*span (\d+).*/)[1] > 1 : false)) &&
        !$container
            .children()
            .toArray()
            .some((v) => $(v).attr("print-grid") && $(v).attr("print-grid").split(",")[2] === 1)
    ) {
        // The check in the following row allows to handle different browsers (some browsers do this "attribute: repeat(3 auto)" while others do this "attribute: auto auto auto")
        let nRows = $container.css("grid-template-rows").indexOf("repeat") !== -1 ? Number($container.css("grid-template-rows").match(/repeat\((\d), auto\)/)[1]) : ($container.css("grid-template-rows").match(/auto/g) || []).length;
        let $currRow;
        let $virtualContainer = $("<div></div>");

        // Extract the children of the container
        let children = $container.children().toArray();

        for (let currRowIdx = 1; currRowIdx <= nRows; currRowIdx++) {
            $currRow = $("<div class='reports--gridsubrow'></div>");

            // The new row has the same column format but only one row
            $currRow.css("display", "grid");
            $currRow.css("grid-template-columns", $container.css("grid-template-columns"));
            $currRow.css("grid-template-rows", "auto");

            // Add the items with grid-row set to i
            let $styleTag; // To append right before the element coming after
            $.each(children, (idx, elem) => {
                let $elem = $(elem);
                if (elem.style.gridRow.match(/(\d+).*/)) {
                    if (Number(elem.style.gridRow.match(/(\d+).*/)[1]) === currRowIdx) {
                        // Prepends the style tag if defined
                        if (!isNullOrUndefined($styleTag)) {
                            $styleTag.appendTo($currRow);
                            $styleTag = null;
                        }

                        // Updates the grid row and appends it to the new row
                        elem.style.gridRow = 1;
                        $elem.appendTo($currRow);
                    } else {
                        $styleTag = null;
                    }
                } else if (elem.tagName === "STYLE") {
                    // Adapt the row in the print style for the new row
                    if (elem.innerHTML.length > 0) {
                        let precRow = Number(elem.innerHTML.match(/.*grid-row: (\S+).*/)[1]);

                        if (precRow === currRowIdx) {
                            $elem.html(`${elem.innerHTML.replace("grid-row: " + precRow, "grid-row: " + (precRow - (currRowIdx - 1)))}`);
                        }
                    }

                    // Save it to append it before the corresponding element
                    $styleTag = $elem;
                } else {
                    // Element with no grid-row defined and not of type STYLE
                    console.error(`[collapseGridForPrinting] Unmanaged element.`, elem);
                }
            });

            // In case from the loops there's a remaining style tag
            if (!isNullOrUndefined($styleTag)) {
                console.error(`[collapseGridForPrinting] Unmatched style tag.`, $styleTag);
            }

            // If the row contains only 1 element taking up all of the space, then display grid => display block
            if ($currRow.children().filter((idx, v) => v.tagName !== "STYLE").length === 1) {
                $currRow.css("display", "block");

                // Calculate values
                let child = $currRow.children().filter((idx, v) => v.tagName !== "STYLE")[0];
                let childStart = Number(child.style.gridColumn.match(/(\d+) \/ span (\d+)/)[1]) - 1;
                let childSpan = Number(child.style.gridColumn.match(/(\d+) \/ span (\d+)/)[2]);

                // The check in the following row allows to handle different browsers (some browsers do this "attribute: repeat(3 auto)" while others do this "attribute: auto auto auto")
                let parentSpan = $currRow.css("grid-template-columns").indexOf("repeat") !== -1 ? Number($currRow.css("grid-template-columns").match(/repeat\((\d+), 1fr\)/)[1]) : ($currRow.css("grid-template-columns").match(/1fr/g) || []).length;

                child.style.width = (childSpan / parentSpan) * 100 + "%";

                if (childStart !== 0) {
                    child.style.marginLeft = (childStart / parentSpan) * 100 + "%";
                }

                // Update custom print style if present
                // FIXME: any row setting != 1 is ignored (if the main setting for the vertical size was 1)
                if ($currRow.children().length > 1) {
                    let [row, column, rowSpan, columnSpan] = $(child)
                        .attr("print-grid")
                        .split(",")
                        .map((v) => Number(v));

                    let startIdx = $currRow.children()[0].innerText.indexOf("}");

                    $currRow.children()[0].innerText =
                        $currRow.children()[0].innerText.substring(0, startIdx) +
                        `width: ${(columnSpan / parentSpan) * 100 + "%"} !important;` +
                        (column !== 1 ? ((column - 1) / parentSpan) * 100 + "% !important" : "") +
                        $currRow.children()[0].innerText.substring(startIdx);
                }
            }

            $currRow.appendTo($virtualContainer);
        }

        $virtualContainer.children().appendTo($container);

        // Sets the container to display block
        $container.css("display", "block");
    }
}

//  #endregion Support functions

//  #region Translations

/**
 * Returns the string with every "[translation_key]" replaced by its translation.
 * @param {string} text Text to elaborate
 */
function getTranslatedTextReport(text) {
    let retVal = text;
    let match;

    if (!isNullOrUndefined(retVal)) {
        while ((match = retVal.match(/\[([^\]]+)\]/))) {
            // If the match contains a '.', than it's a path to a string in nameJSON
            if (match[1].indexOf(".") !== -1) {
                retVal = retVal.replace(`[${match[1]}]`, getStringFromNameJSON(match[1]));
            } else {
                retVal = retVal.replace(`[${match[1]}]`, getTranslationReport(match[1]));
            }
        }
    }

    return retVal;
}

/**
 * Returns the translation for the keyname in the current user language
 * @param {string} key Keyname of the desired translation
 */
function getTranslationReport(key) {
    let retVal;

    // 1 - First search in UNITS_TRANSLATIONS
    if (isNullOrUndefined((retVal = UNITS_TRANSLATIONS[key]))) {
        // 2 - Then search in DEFAULT_STRINGS
        if (isNullOrUndefined((retVal = DEFAULT_STRINGS[key]))) {
            // 3 - Finally check in the dictionary
            if (isNullOrUndefined(languageJSON) || isNullOrUndefined((retVal = languageJSON[key]))) {
                retVal = `JS: ${key}`;

                console.error(`[getTranslationReport] Keyname "${key}" not found in dictionary.`);

                updateMissingKeys(key, true);
            } else {
                updateMissingKeys(key, false);
            }
        }
    }

    return retVal;
}

/**
 * Updates the missing keys array in the sessionStorage according to the outcome of the last translation
 * @param {string} key Key of the translation
 * @param {boolean} missing True is the key is missing from languageJSON, False otherwise
 */
function updateMissingKeys(key, missing) {
    let missesArray = JSON.parse(sessionStorage.getItem("missingJSON")) || [];

    if (missing) {
        if (missesArray.indexOf(key) === -1) {
            missesArray.push(key);
        }
    } else {
        missesArray = missesArray.filter((k) => k === key);
    }

    sessionStorage.setItem("missingJSON", JSON.stringify(missesArray));
}

/**
 * Returns the unit translation for the desired unit
 * @param {string | number} unit Static unit translation or id of the unit
 */
function getUnitTranslation(unit) {
    let retVal;

    switch (typeof unit) {
        // If string: return the argument
        case "string":
            retVal = unit;
            break;

        // If number: get the translation from UNITS_TRANSLATIONS
        case "number":
            if (isNullOrUndefined((retVal = UNITS_TRANSLATIONS[UNITS[unit]]))) {
                console.error(`[getUnitTranslation] Unrecognized unit "${unit}".`);
            }
            break;

        //Otherwise log an error
        default:
            console.error(`[getUnitTranslation] Unrecognized input of type ${typeof unit}: `, unit);
    }

    return retVal;
}

//  #endregion Translations

//  #region Data management

/**
 * Checks the data from the server (valueJSON), returning true if duplicates were found
 */
function dataHasDuplicates() {
    let retVal = false;

    if (valueJSON) {
        let uniqueKeys = [];
        let keys = Object.keys(valueJSON);

        for (let key of keys) {
            if (uniqueKeys.indexOf(key) !== -1) {
                console.error(`[dataHasDuplicates] Duplicate key "${key}" in values object.`);

                retVal = true;
            } else {
                uniqueKeys.push(key);
            }
        }
    }

    return retVal;
}

/**
 * Returns the value/object contained in valueJSON and referred to by a dot-divided path (recursive).
 * @param {string} path String path
 * @param {any} fromObj? Object to search into, defaults to valueJSON
 */
function getDataFromValueJSONViaPath(path, fromObj = valueJSON) {
    // 1 - Turns the path into an array, if necessary
    let pathArr = isNullOrUndefined(path) ? null : (path instanceof Array ? path : path.split("."));

    // 2 - Checks whether there's more to do
    if (!isNullOrUndefined(pathArr) && !isNullOrUndefined(pathArr[0])) {
        // 2.1 - If so, first check if the key is in the object
        if (fromObj[pathArr[0]] !== undefined) {
            return getDataFromValueJSONViaPath(pathArr.slice(1, pathArr.length + 1), fromObj[pathArr[0]]);
        } else {
            // 3 - If the key is not set on the object
            if (fromObj instanceof Array) {
                // 3.1 - For arrays search in "id" or "name" attributes for the key
                return searchByNameOrId(pathArr, fromObj);
            } else if (fromObj.elem !== undefined && fromObj.elem instanceof Array) {
                // 3.2 - Even if the array is contained in the "elem" attribute
                return searchByNameOrId(pathArr, fromObj.elem);
            } else {
                // 3.3 - Otherwise, not found
                return null;
            }
        }
    } else {
        // 2.2 - Otherwise returns the current object, ending the chain
        return fromObj;
    }

    /**
     * Looks for the key in "id" or "name" attributes
     * @param {string[]} pathArr
     * @param {any} fromObj
     */
    function searchByNameOrId(pathArr, fromObj) {
        let foundElem;

        if ((foundElem = fromObj.find((v) => v.name === pathArr[0])) || (foundElem = fromObj.find((v) => v.id === pathArr[0]))) {
            return getDataFromValueJSONViaPath(pathArr.slice(1, pathArr.length + 1), foundElem);
        } else {
            return null;
        }
    }
}

/**
 * Returns the string in nameJSON at specified path
 * @param {string} path nameJSON path to desired string
 */
function getStringFromNameJSON(path) {
    let pathArr = path.split(".");
    let currObj = nameJSON;

    if (currObj[pathArr[0]] !== undefined) {
        currObj = currObj[pathArr[0]];

        if (pathArr[1]) {
            if (currObj.some((v) => !!v.name)) {
                currObj = currObj.filter((v) => v.name === pathArr[1])[0];

                if (currObj && pathArr[2]) {
                    currObj = currObj.elem.find((v) => v.id === pathArr[2]);

                    if (currObj && pathArr[3]) {
                        currObj = currObj[pathArr[3]];
                    }
                }
            } else {
                currObj = currObj.filter((v) => v.id === pathArr[1])[0];

                if (currObj && pathArr[2]) {
                    currObj = currObj[pathArr[2]];
                }
            }
        }
    } else {
        currObj = null;
    }

    return currObj;
}

/**
 * Formats the given string by replacing {path:pipe} with the value found at path 'path' passed thorugh pipe 'pipe if defined'
 * @param {string} input Input string representing the wanted output
 */
function formatWithValues(input = "") {
    let retVal = input;
    let match;
    let value;

    while ((match = retVal.match(/\{([^}]+)\}/))) {
        let valuePath = match[1].split(":")[0];
        let pipe = match[1].split(":")[1];

        if (!isNullOrUndefined((value = getValueByPath(valuePath)))) {
            retVal = pipe ? retVal.replace(match[0], applyPipe(value, pipe)) : retVal.replace(match[0], typeof value === "number" ? formatValueWithDecimals(value) : value);
        } else {
            retVal = retVal.replace(match[0], PLACEHOLDER_STRING);

            console.warn(`[formatWithValues] Couldn't find value for path "${valuePath}"`);
            break;
        }
    }

    return retVal;
}

/**
 * Manipulates the value according to the desired pipe
 * @param {any} value Value to elaborate
 * @param {string} pipe Key of the pipe to apply
 */
function applyPipe(value, pipe) {
    let retVal;

    if (Object.values(PIPES).indexOf(pipe) !== -1) {
        switch (pipe) {
            case PIPES.SECONDS_TO_TIME:
                retVal = secondsToTime(value);
                break;

            case PIPES.PERCENTAGE:
                retVal = Number(value) ? formatValueWithDecimals(Number(value) * 100, 2) + " %" : "0 %";
                break;

            case PIPES.DYNAMIC_WEIGHT_UNIT:
                retVal = Number(value) ? formatDynamicUnitWeight(Number(value), 3) + " " + getDynamicUnitWeight(Number(value)) : "0 " + UNITS_TRANSLATIONS.WEIGHT;
                break;

            case PIPES.DYNAMIC_FLOWRATE_UNIT:
                retVal = Number(value) ? formatDynamicUnitFlowrate(Number(value), 3) + " " + getDynamicUnitFlowrate(Number(value)) : "0 " + UNITS_TRANSLATIONS.WEIGHT_PER_HOUR;
                break;

            case PIPES.LINK:
                // Expects as value "[text of the link]:[url of the link]"
                retVal = `<a href="${localStorage.getItem("rootUrl") + value.split(":")[1]}" target="_parent">${value.split(":")[0]}`;
                break;

            default:
                console.error(`[applyPipe] Unconfigured pipe "${pipe}".`);
        }
    } else {
        console.error(`[applyPipe] Unrecognized pipe "${pipe}".`);
    }

    return retVal;
}

/**
 * Returns the value from the given path, searching first elements by id, then valueJSON
 * @param {string} path Value path
 */
function getValueByPath(path) {
    let retVal;
    let $elem;

    if (($elem = $(`[id="${path}"]`)).length > 0) {
        retVal = Number($elem.data("value"));
    } else {
        retVal = getDataFromValueJSONViaPath(path);
    }

    return retVal;
}

//#region Precalculated values

PrecalculatedValues.PERIOD = () => {
    let fromMoment;
    let toMoment;

    //nel caso in cui si utilizzi il formato 12h è necessario correggere il DATE_FORMAT per consentie il corretto calcolo della durata
    if (DATE_FORMAT != "DD-MM-YYYY hh:mm tt") {
        fromMoment = moment(getDataFromValueJSONViaPath("top.dateFrom.val"), DATE_FORMAT);
        toMoment = moment(getDataFromValueJSONViaPath("top.dateTo.val"), DATE_FORMAT);
    }
    else {
        DATE_FORMAT_EN = "DD-MM-YYYY hh:mm a";
        fromMoment = moment(getDataFromValueJSONViaPath("top.dateFrom.val"), DATE_FORMAT_EN);
        toMoment = moment(getDataFromValueJSONViaPath("top.dateTo.val"), DATE_FORMAT_EN);
    }

    if (fromMoment.isValid() && toMoment.isValid()) {
        valueJSON.values.push({ id: "PERIOD", val: toMoment.diff(fromMoment, "seconds", true) });
    }
};

PrecalculatedValues.EMT_OVER_PERIOD = () => {
    let emt = getDataFromValueJSONViaPath("top.EMT.val");
    let period = getPrecalculatedValueByEnum(PRECALCULATED_VALUES.PERIOD);

    if (!isNullOrUndefined(period) && !isNullOrUndefined(emt)) {
        valueJSON.values.push({ id: "EMT_OVER_PERIOD", val: Number(emt) / Number(period) });
    }
};

PrecalculatedValues.CAPACITY_ON_PERIOD = () => {
    let period = getPrecalculatedValueByEnum(PRECALCULATED_VALUES.PERIOD);
    let dailyCapacity = getDataFromValueJSONViaPath("top.plant_capacity_day.val");

    if (!isNullOrUndefined(period) && !isNullOrUndefined(dailyCapacity)) {
        valueJSON.values.push({ id: "CAPACITY_ON_PERIOD", val: (Number(dailyCapacity) * Number(period)) / (60 * 60 * 24) });
    }
};

PrecalculatedValues.UTILIZATION_OVER_CAPACITY = () => {
    let capacityOnPeriod = getPrecalculatedValueByEnum(PRECALCULATED_VALUES.CAPACITY_ON_PERIOD);
    let utilization = Number(getDataFromValueJSONViaPath("top.quantity.val"));

    if (!isNullOrUndefined(capacityOnPeriod) && !isNullOrUndefined(utilization)) {
        valueJSON.values.push({ id: "UTILIZATION_OVER_CAPACITY", val: utilization / Number(capacityOnPeriod) });
    }
};

PrecalculatedValues.CONSUMPTION_PER_QUANTITY = () => {
    let quantity = getDataFromValueJSONViaPath(`top.quantity.val`);

    if (!isNullOrUndefined(quantity)) {
        let tb = getDataFromValueJSONViaPath("power.tb_a");
        let totalConsumption = tb
            ? tb.elem.reduce((prev, curr) => {
                return (!isNullOrUndefined(prev.val) ? prev.val : prev) + curr.val;
            })
            : 0;

        valueJSON.values.push({ id: "CONSUMPTION_PER_QUANTITY", val: Number(quantity) !== 0 ? totalConsumption / Number(quantity) : PLACEHOLDER_STRING });
    }
};

/**
 * By going through the PRECALCULATED_VALUES enum calls the calculation of notable values
 */
function precalculateValues() {
    valueJSON.values = [];

    for (let key of Object.keys(PRECALCULATED_VALUES).filter((k) => isNaN(k))) {
        if (!isNullOrUndefined(PrecalculatedValues[key])) {
            PrecalculatedValues[key]();
        } else {
            console.error(`[precalculateValues] Pre-calculated value "${PRECALCULATED_VALUES[Number(key)]}" not configured.`);
        }
    }
}

/**
 * Retrieves the desired precalculated value from valueJSON.values by its key
 * @param {number} key Key of the value
 */
function getPrecalculatedValueByEnum(key) {
    let retVal = valueJSON.values.find((v) => v.id === PRECALCULATED_VALUES[key]);
    return retVal ? retVal.val : null;
}

//#endregion Precalculated values

//  #endregion Data management

//  #region Utilities

/**
 * Returns the desired key value
 * @param {string} path Value path separated by separator
 * @param {any} obj Object
 * @param {string} separator Separator character/string
 */
function resolve(path, obj = self, separator = ".") {
    var properties = path.split(separator);
    return properties.reduce((prev, curr) => prev && prev[curr], obj);
}

/**
 * Returns the value passed in as a string representing a number with the desired decimal digits
 * @param {number} value Value to parse
 * @param {number} decimalPlaces Number of decimal places
 */
function formatValueWithDecimals(value, decimalPlaces = DEFAULT_DECIMAL_PLACES, decimalMode = DECIMAL_MODES.AUTO) {
    let retVal = "0";

    if (isFinite(Number(value))) {
        switch (decimalMode) {
            // Shows decimal places (up to a max of [decimalPlaces]) if needed
            case DECIMAL_MODES.AUTO:
                retVal = `${Number(Number(value).toFixed(decimalPlaces))}`;
                break;

            // Shows [decimalPlaces] places regardless of the value
            case DECIMAL_MODES.FIXED:
                retVal = Number(value).toFixed(decimalPlaces);
                break;
        }
    }

    return retVal;
}

/**
 * Manages the value in case of dynamic weight unit.
 * @param {number} weight in tons
 * @param {number?} decimalPlaces
 * @param {number?} decimalMode
 */
function formatDynamicUnitWeight(weight, decimalPlaces = undefined, decimalMode = undefined) {
    let retVal = null;
    let value = Number(weight);

    if (!isNaN(value)) {
        if (Math.abs(value) < DYNAMIC_WEIGHT_UNITS_CONFIG.GRAMS_UNTIL) {
            retVal = formatValueWithDecimals(value * 1000000, 0);
        }
        else if (Math.abs(value) < DYNAMIC_WEIGHT_UNITS_CONFIG.KGS_UNTIL) {
            retVal = formatValueWithDecimals(value * 1000, decimalPlaces, decimalMode);
        }
        else {
            retVal = formatValueWithDecimals(value, decimalPlaces, decimalMode);
        }
    } else {
        retVal = 0;
    }

    return retVal;
}

/**
 * Manages the unit in case of dynamic weight unit.
 * @param {number} weight in tons
 */
function getDynamicUnitWeight(weight) {
    let retVal = null;
    let value = Number(weight);

    if (!isNaN(value) && value !== 0) {
        if (Math.abs(value) < DYNAMIC_WEIGHT_UNITS_CONFIG.GRAMS_UNTIL) {
            retVal = UNITS_TRANSLATIONS.WEIGHT_E_06;
        }
        else if (Math.abs(value) < DYNAMIC_WEIGHT_UNITS_CONFIG.KGS_UNTIL) {
            retVal = UNITS_TRANSLATIONS.WEIGHT_E_03;
        }
        else {
            retVal = UNITS_TRANSLATIONS.WEIGHT;
        }
    } else {
        retVal = UNITS_TRANSLATIONS.WEIGHT;
    }

    return retVal;
}

/**
 * Manages the value in case of dynamic flowrate unit.
 * @param {number} flowrate in ton/h
 * @param {number?} decimalPlaces
 * @param {number?} decimalMode
 */
function formatDynamicUnitFlowrate(flowrate, decimalPlaces = undefined, decimalMode = undefined) {
    let retVal = null;
    let value = Number(flowrate);

    if (!isNaN(value)) {
        if (Math.abs(value) < DYNAMIC_FLOWRATE_UNITS_CONFIG.GRAMS_UNTIL) {
            retVal = formatValueWithDecimals(value * 1000000, 0);
        }
        else if (Math.abs(value) < DYNAMIC_FLOWRATE_UNITS_CONFIG.KGS_UNTIL) {
            retVal = formatValueWithDecimals(value * 1000, decimalPlaces, decimalMode);
        }
        else {
            retVal = formatValueWithDecimals(value, decimalPlaces, decimalMode);
        }
    } else {
        retVal = 0;
    }

    return retVal;
}

/**
 * Manages the unit in case of dynamic flowrate unit.
 * @param {number} flowrate in ton/h
 */
function getDynamicUnitFlowrate(flowrate) {
    let retVal = null;
    let value = Number(flowrate);

    if (!isNaN(value) && value !== 0) {
        if (Math.abs(value) < DYNAMIC_FLOWRATE_UNITS_CONFIG.GRAMS_UNTIL) {
            retVal = UNITS_TRANSLATIONS.WEIGHT_E_06_PER_HOUR;
        }
        else if (Math.abs(value) < DYNAMIC_FLOWRATE_UNITS_CONFIG.KGS_UNTIL) {
            retVal = UNITS_TRANSLATIONS.WEIGHT_E_03_PER_HOUR;
        }
        else {
            retVal = UNITS_TRANSLATIONS.WEIGHT_PER_HOUR;
        }
    } else {
        retVal = UNITS_TRANSLATIONS.WEIGHT_PER_HOUR;
    }

    return retVal;
}

/**
 * Handles the mutations of an element
 * @callback mutationHandler
 * @param {any} Mutation object
 */
/**
 * Creates and return a mutation observer on the desired element
 * @param {HTMLElement} elem Target element
 * @param {mutationHandler} handler
 */
function addMutationObserver(elem, handler) {
    let observer = new MutationObserver(handler);
    let config = { characterData: true, attributes: true, childList: true, subtree: true };

    if (elem != undefined) {
    	observer.observe(elem, config);
    }
    

    return observer;
}

/**
 * Handles the resize event of an element
 * @callback resizeHandler
 * @param {any} Resize object
 */
/**
 * Creates and return a resize observer on the desired element
 * @param {HTMLElement} elem Target element
 * @param {resizeHandler} handler
 */
function addResizeObserver(elem, handler) {
    let observer = new ResizeObserver(handler);

    observer.observe(elem);

    return observer;
}

/**
 * Returns whether the object passed in is not defined
 * @param {any} v Object to check
 */
function isNullOrUndefined(v) {
    return v === null || v === undefined;
}

/**
 * Returns the number of seconds in the format "X hr Y mn Z s"
 * @param {number} seconds Seconds
 */
function secondsToTime(seconds) {
    let retVal = " ";

    retVal += seconds >= 3600 * 24 * 365 ? Math.floor(seconds / (3600 * 24 * 365)).toString() + "y " : "";

    retVal += seconds >= 3600 * 24 ? Math.floor((seconds / (3600 * 24)) % 365).toString() + "d " : "";

    retVal += Math.floor((seconds % (3600 * 24)) / 3600).toString() + "h ";

    retVal += Math.floor(((seconds % (3600 * 24)) % 3600) / 60).toString() + "mn ";

    retVal += Math.floor(((seconds % (3600 * 24)) % 3600) % 60).toString() + "s";

    return retVal;
}

//  #endregion Utilities
//#endregion Private