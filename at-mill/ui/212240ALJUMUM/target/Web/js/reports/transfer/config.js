﻿// The 'content' within each report type can be an object (structured like {0: {element}, where the 0 is the index of the array in the generic configuration})
// or an array (in this case the number and order of the elements has to correspond to the generic configuration)

const SPECIFIC_CONFIGURATION = {};

specificReportCode.TRANSFER = () => {
    // Check if the report has one row. If so hides the setpoint column completely together with the actual percentage column
    let $thead = $("#table1 thead");
    let $tbody = $("#table1 tbody");

    if ($tbody.children(".reports--table--row:not([data-total-type])").length === 1) {
        $tbody.children().find("td[id$='.requested'], td[data-column='requested']").hide();
        $thead.children().find("th[data-column='requested']").hide();

        $tbody.children().find("td[id$='.actual'], td[data-column='actual']").hide();
        $thead.children().find("th[data-column='actual']").hide();
    }
};