﻿//#region Enumerables

ELEMENT_TYPES.MIXING_PANEL = 3;
ELEMENT_TYPES[3] = "MIXING_PANEL";

REPORT_TYPES.MIXING = 11;
REPORT_TYPES[11] = "MIXING";

OPERATIONS.MIXING_TOTAL_PANEL = 5;
OPERATIONS[5] = "MIXING_TOTAL_PANEL";

OPERATIONS.MIXING_TOGGLE_DETAIL = 6;
OPERATIONS[6] = "MIXING_TOGGLE_DETAIL";

OPERATIONS.MIXING_TOGGLE_DETAIL_EXPANSION = 7;
OPERATIONS[7] = "MIXING_TOGGLE_DETAIL_EXPANSION";

//#endregion Enumerables

//#region Constants

ELEMENT_PARAMETERS.MIXING_PANEL = ["header", "styles", "contents"];

MANDATORY_ELEMENT_PARAMETERS.MIXING_PANEL = ["contents"];

//#endregion Constants

//#region Configurations

CONFIGURATION.MIXING = {
    title: "[MAIN_MIXING_REPORT_TITLE]",
    grid: { x: 12, y: 2 },
    content: [
        {
            id: "topgrid",
            type: ELEMENT_TYPES.GRID,
            position: { x: 1, y: 1 },
            size: { x: 12, y: 1 },
            parameters: {
                grid: { x: 4, y: 6 },
                content: [
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 1, y: 1 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[JOB]:",
                            value: "{top.counter.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 2, y: 1 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.startDate.val]:",
                            value: "{top.startDate.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 3, y: 1 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.stopDate.val]:",
                            value: "{top.stopDate.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 1, y: 2 },
                        size: { x: 2, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.productDescription.val]:",
                            value: "{top.productDescription.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 1, y: 3 },
                        size: { x: 2, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.recipeDescription.val]:",
                            value: "{top.recipeDescription.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 1, y: 4 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.requestedTotal.val]:",
                            value: "{top.requestedTotal.val} [WEIGHT]",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 2, y: 4 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.requestedBatches.val]:",
                            value: "{top.requestedBatches.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 3, y: 4 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.executedBatches.val]:",
                            value: "{top.executedBatches.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 1, y: 5 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.destinationBin.val]:",
                            value: "{top.destinationBin.val}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 2, y: 5 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.LABEL_VALUE,
                            label: "[top.lot.val]:",
                            value: "{top.lot.val:link}",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"], CUSTOM_STYLES["LABEL-VALUE-WIDTH-ADAPT"], CUSTOM_STYLES["TEXT-LEFT"]],
                            hideIfNullValue: true
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 4, y: 5 },
                        size: { x: 1, y: 1 },
                        parameters: {
                            operation: OPERATIONS.MIXING_TOGGLE_DETAIL,
                            label: "[SHOW_DETAILS]:",
                            target: "table1",
                            styles: [CUSTOM_STYLES["BOLD-LABEL"]],
                        },
                    },
                    {
                        type: ELEMENT_TYPES.OPERATION,
                        position: { x: 1, y: 6 },
                        size: { x: 4, y: 1 },
                        parameters: {
                            operation: OPERATIONS.MIXING_TOTAL_PANEL,
                            recipe: "top.recipeIngredients.val",
                            contents: "tb_a",
                            columns: {
                                productName: {
                                    label: "[Product]",
                                },
                                expected: {
                                    label: "[Recipe]",
                                    decimals: 2,
                                },
                                expectedKgs: {
                                    label: "[EXPECTED_TOTAL_WEIGHT]",
                                },
                                actual: {
                                    label: "[ACTUAL_TOTAL_WEIGHT]",
                                },
                                difference: {
                                    label: "[Difference]",
                                    decimals: 2,
                                },
                            },
                            styles: [CUSTOM_STYLES["PADDING-BOTTOM-LG"], CUSTOM_STYLES["PADDING-TOP-LG"]],
                        },
                    },
                ],
                styles: [CUSTOM_STYLES["PADDING-BOTTOM-LG"]],
            },
        },
        {
            id: "table1",
            type: ELEMENT_TYPES.MIXING_PANEL,
            position: { x: 1, y: 2 },
            size: { x: 12, y: 1 },
            parameters: {
                columns: {
                    originDescription: {
                        label: "[Source]",
                    },
                    expected: {
                        label: "[EXPECTED_WEIGHT]",
                    },
                    actual: {
                        label: "[ACTUAL_WEIGHT]",
                    },
                    difference: {
                        label: "[Difference]",
                        decimals: 2,
                    },
                },
                header: true,
                contents: "tb_a",
                styles: [CUSTOM_STYLES["PADDING-TOP-LG"], CUSTOM_STYLES["BORDER-TOP"]],
            },
        },
    ],
};

//#endregion Configurations