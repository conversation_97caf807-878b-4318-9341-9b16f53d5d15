﻿// The 'content' within each report type can be an object (structured like {0: {element}, where the 0 is the index of the array in the generic configuration})
// or an array (in this case the number and order of the elements has to correspond to the generic configuration)

const SPECIFIC_CONFIGURATION = {
};

const MIXING_INGREDIENT_TYPES = {
    MACRO: 0,
    MICR<PERSON>: 1,
    HANDTIPPING: 2,
    0: "MACRO",
    1: "<PERSON><PERSON><PERSON>",
    2: "HANDTIPPING",
}

const MIXING_INGREDIENT_TYPES_ORDER = {
    MACRO: 0,
    MICRO: 1,
    HANDTIPPING: 2,
}

const MIXING_INGREDIENT_TYPES_SHOW_DIFF = {
    MACRO: true,
    MICRO: true,
    HANDTIPPING: false,
}

const MIXING_INGREDIENT_TYPES_TRANSLATIONS = {
    "MACRO": "MACRO_INGR_SECTION",
    "MICRO": "MICRO_INGR_SECTION",
    "HANDTIPPING": "HANDTIPPING_INGR_SECTION",
}

// In grams
const DYNAMIC_WEIGHT_UNITS_MIXING_CONFIG = {
    GRAMS_UNTIL: 2000,
    KGS_UNTIL: 10000000
};