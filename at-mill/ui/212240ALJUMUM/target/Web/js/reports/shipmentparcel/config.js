﻿// The 'content' within each report type can be an object (structured like {0: {element}, where the 0 is the index of the array in the generic configuration})
// or an array (in this case the number and order of the elements has to correspond to the generic configuration)

const SPECIFIC_CONFIGURATION = {};

specificReportCode.PARCEL = () => {
    // Check if the report has lots information. If not hides the corresponding column
    let $thead = $("#table1 thead");
    let $tbody = $("#table1 tbody");

    if ($tbody.find(".reports--table--row:not([data-total-type]) td[id$='.lot']").toArray().every((v) => v.innerHTML.trim() === PLACEHOLDER_STRING)) {
        $tbody.find("td[id$='.lot'], td[data-column='lot']").hide();
        $thead.find("th[data-column='lot']").hide();
    }

    // "parcelShipmentTopGrid" visible only if some data is available
    let $parcelShipmentTopGrid = $("#parcelShipmentTopGrid");

    if ($parcelShipmentTopGrid.children().children("span").toArray().every((v) => v.innerHTML.trim() === PLACEHOLDER_STRING)) {
        $parcelShipmentTopGrid.hide();
    }
};

specificReportCode.SHIPMENT = specificReportCode.PARCEL