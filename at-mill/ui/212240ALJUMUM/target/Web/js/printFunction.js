﻿SetWidthTable = function (table_id, table_num) {
    var font_size_standard_px = 13;
    var table_obj = null;
    var table_width_px = null;
    var standard_height = null;
    var row_header = null;
    var th_header_elements = [];
    var td_body_elements = [];
    var rows_body = [];
    var row_height = null;

    var columns_width_perc = [];
    var columns_visibile = [];
    var td_list = [];

    var need_resize = false;
    var stop_resize = false;
    var cell_to_resize = null;
    var overcnt = false;

    //oggetti
    td_elem = function (row, col) {
        this.row = row;
        this.col = col;
        this.width = null;
        this.height = null;
        this.font_size = null;
        this.td_width = null;
        this.td_height = null;
    }

    function Start() {
        //ricavo parametri base
        table_obj = document.getElementById(table_id);
        table_width_px = table_obj.offsetWidth;
        row_header = document.getElementById("headElem_Tb" + table_num).getElementsByTagName("tr")[0];  //riga singola
        rows_body = document.getElementById("listElem_Tb" + table_num).getElementsByTagName("tr");      //n righe

        GetVisibleColumns();
        BaseColumnsWidth();

        /** NOTA
         * a questo punto ho:
         *  -tabella
         *  -larghezza massima tabella
         *  -altezza standard delle righe
         *  -elenco elementi del header
         *  -elenco righe del body
         *  -elenco degli id delle colonne visibili
         *  -elenco delle dimensioni delle colonne (%) visibili, settate!
         *  -array di array contenente, per ogni td, l'oggetto td_elem associato
         */
        var cnt = 0;
        do {
            cnt++;
            CheckSizeTd();
            if (need_resize) {
                ResizeColumn();
            }
        } while (need_resize && !(stop_resize) && (cnt < 50));
        if (cnt >= 50) {
            console.log("over cnt limit");
            overcnt = true;
            BaseColumnsWidth();
        }
    }

    /*  definisce l'array "columns_visible":
     * array contenente gli indici delle colonne visibili della tabella
     */
    function GetVisibleColumns() {
        th_header_elements = row_header.getElementsByTagName("th");
        for (var th = 0; th < th_header_elements.length; th++) {
            if (th_header_elements[th].style.display != "none") {
                columns_visibile.push(th);
            }
        }
    }

    //definisce la larghezza base delle colonne (%) in base al numero di colonne visibili
    function BaseColumnsWidth() {
        var width_base_perc = parseFloat((100 / columns_visibile.length).toFixed(2));
        columns_width_perc = [];
        for (var c = 0; c < columns_visibile.length; c++) {
            columns_width_perc.push(width_base_perc);
        }
        SetWidthPerc(columns_width_perc);
    }

    //imposta la larghezza delle colonne come indicato dalla variabile in ingresso
    function SetWidthPerc(width_perc_array) {
        for (var c = 0; c < columns_visibile.length; c++) {
            th_header_elements[columns_visibile[c]].style.width = width_perc_array[c] + "%";
        }
        if (!overcnt) {
            GetSizeTd();
        }
    }

    /* recupera le dimensioni degli span di ogni cella del body
     * e le dimensioni assolute: "standard_height" -> altezza standard delle righe
     */
    function GetSizeTd() {
        td_list = [];
        var span_el = null;
        var el = null;
        var elem_row = [];
        var td = null;
        for (var r = 0; r < rows_body.length; r++) {
            if (rows_body[r].getAttribute("type") != "separatore") {
                elem_row = [];
                var row_elements = rows_body[r].getElementsByTagName("td");
                for (var td_vis = 0; td_vis < columns_visibile.length; td_vis++) {
                    td = columns_visibile[td_vis];
                    el = new td_elem(r, td);
                    span_el = row_elements[td].getElementsByTagName("span")[0];
                    el.width = span_el.offsetWidth;
                    el.height = span_el.offsetHeight;
                    el.font_size = font_size_standard_px;
                    el.td_width = row_elements[td].offsetWidth;
                    el.td_height = row_elements[td].offsetHeight;
                    elem_row.push(el);

                    if (((el.height < standard_height) && (el.height > 0)) || (standard_height == null)) {
                        standard_height = el.height;
                    }
                }
            }
            td_list.push(elem_row);
        }
    }

    /*  verifica se le altezze dei vari elementi sono maggiori della standard, in quel caso setta
     * "need_resize" a "true" per indicare la necessità di riscalare le dimensioni, altrimenti lo
     * imposta a "false"
     * nel caso sia "true", imposto "cell_to_resize" tale da indicare la cella da sistemare cosi
     * da riscalare una cella alla volta
     */
    function CheckSizeTd() {
        need_resize = false;
        cell_to_resize = null;
        for (var r = 0; r < td_list.length; r++) {
            for (var c = 0; c < td_list[r].length; c++) {
                if (td_list[r][c].height > standard_height) {
                    need_resize = true;
                    cell_to_resize = new td_elem(td_list[r][c].row, td_list[r][c].col);
                    cell_to_resize.width = td_list[r][c].width;
                    cell_to_resize.height = td_list[r][c].height;
                    cell_to_resize.td_width = rows_body[r].getElementsByTagName("td")[c].offsetWidth;
                }
            }
        }
    }

    /*  scalo la larghezza delle colonne della tabella tale da:
     * aggiungere "1%" alla colonna da allargare
     * ridurre le altre colonne di "1/(#col-1)%"
     *   sse la width della cella ridotta varia
     *  (potrebbe capitare che continuo a ridurre la % di una colonna ma la sua effettiva
     *  larghezza non subirebbe variazioni in quanto raggiunto il minimo che può avere)
     */
    function ResizeColumn() {
        var new_width_perc = EditWidthPerc(cell_to_resize.col, columns_width_perc);
        SetWidthPerc(new_width_perc);

        if (td_list[cell_to_resize.row][cell_to_resize.col].td_width == cell_to_resize.td_width) {
            SetWidthPerc(columns_width_perc);
            stop_resize = true;
        }
    }

    /*  esegue il calcolo specificato per ResizeColumn
     * @param {integer} col -   indice della colonna da incrementare
     * @param {array} width_perc_array  -   array delle dimensioni (%) base da cui partire per riscalare
     * @return {array} dimensione delle colonne (%) riscalato
     */
    function EditWidthPerc(col, width_perc_array) {
        var other_col = columns_visibile.length - 1;
        var to_minus = parseFloat((1 / other_col).toFixed(2));
        var temp_width_perc = width_perc_array;

        for (var s = 0; s < columns_visibile.length; s++) {
            if (columns_visibile[s] == col) {
                temp_width_perc[s] = temp_width_perc[s] + 1;
            } else {
                temp_width_perc[s] = temp_width_perc[s] - to_minus;
            }
        }

        return temp_width_perc;
    }

    Start();
};