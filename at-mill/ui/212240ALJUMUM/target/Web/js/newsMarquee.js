var numWarnings;
var $ul_Ticker;
var fullMsg = [];
var intervalloUpdate = 30000; // intervallo di update da div hidden (generato da WUC_Warnings.ascx) ad ul visibile all'utente
var intervalloRoll = 3000; // intervallo di rotolamento

function getHeightTicker() {
    if (numWarnings.length == 0) {
        $("#warnings").removeAttr("onmouseover");
        $("#warnings").removeAttr("onmouseout");
        return "50px";
    } else if ((numWarnings.length > 0) && (numWarnings.length < 4)) {
        $("#warnings").removeAttr("onmouseover");
        $("#warnings").removeAttr("onmouseout");
        var altezza = 85 * numWarnings.length;
        return altezza + "px";
    } else {
        $("#warnings").attr({
            "onmouseover": "stopTicker()",
            "onmouseout": "startTicker()"
        });
        return "255px";
    }
}

function updateTicker() {
    //visibile
    $ul_Ticker = $("#ticker");
    var txt = "";
    //invisibile
    numWarnings = $("#HiddenTicker li");
    var hegTick = getHeightTicker();

    numWarnings.each(function (index) {
        var tmp = $(this).html();
        var tmpSplit = tmp.split("_$_");
        var tmpDesc;

        if (tmpSplit[3].length > 60) {
            tmpDesc = tmpSplit[3].substring(0, 60) + '..';
            fullMsg[index] = tmpSplit[3];
        }
        else {
            tmpDesc = tmpSplit[3];
            fullMsg[index] = null;
        }

        txt += "<li>" +
			"<table>" +
			"<tr>" +
				"<td class='bgCol1'>" +
						tmpSplit[0] +
				"</td>" +
				"<td class='bgCol1 txtBold'>" +
						tmpSplit[1] + "&nbsp;" + tmpSplit[2] +
					"</td>" +
		"</tr>" +
				"<tr>" +
				"<td colspan='2' id= 'tdMsg_" + index + "' onmouseover='PopMessage(this.id)' onmouseleave='HideMessage()'>" +
						tmpDesc +
					"</td>" +
		"</tr>" +
				"<tr>" +
				"<td colspan='2'>" +
		"<hr />" +
		"</td>" +
		"</tr>" +
			"</table>" +
		"</li>";
    });

    $ul_Ticker.html(txt);
    $ul_Ticker.css("height", hegTick);
}

var rollTickerInterval = setInterval(function () { rollTicker(); }, intervalloRoll);
var updateTickerInterval = setInterval(function () { updateTicker(); }, intervalloUpdate);
var cmdStopTicker = false;

function stopTicker() {
    cmdStopTicker = true;
}
function startTicker() {
    cmdStopTicker = false;
}

function rollTicker() {
    if (!cmdStopTicker) {
        var listaWarning = $("#ticker li");
        var lungWarning = listaWarning.length;

        if (lungWarning >= 4) {
            $('#ticker li:first').slideUp(
                function () { $(this).appendTo($('#ticker')).slideDown(); });
        }
    }
}

function PopMessage(id) {
    var id_msg = id.split('_');
    if (fullMsg[id_msg[1]] != null) {
        $("#DivPopMessage").html(fullMsg[id_msg[1]]);
        $("#DivPopMessage").show();
    }
}

function HideMessage() {
    $("#DivPopMessage").html("");
    $("#DivPopMessage").hide();
}