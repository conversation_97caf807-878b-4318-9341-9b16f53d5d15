﻿Imports UsersGUI

Partial Class _New
    Inherits myWebPage

    Private m_config As config
    Private sControl As String = String.Empty

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        m_config = CType(Current.Application("Config"), config)

        ' Controlla se è necessario fare un update delle risorse del JS
        myScript.UpdateJSResources(m_config)

        myScript.InvokeJS(Me.Page, "ShowUCCont();")

        If Not Page.IsPostBack Then
            If Current.Request("control") Is Nothing Then
                Exit Sub
            End If

            sControl = Current.Request("control").ToString

            Select Case sControl.ToUpper
                Case "NEW"
                    Me.WebNew.Visible = True
                Case Else

            End Select
        End If
    End Sub

    Protected Function GetLinkItem(ByVal m As MenuAtmill, ByVal count As Integer) As String
        Dim ret_val As String = String.Empty
        Dim m_Item As menuItemsLink

        m_Item = m.menuItemsLinks(count)
        ret_val = m_Item.GetValue
        Return ret_val
    End Function

End Class