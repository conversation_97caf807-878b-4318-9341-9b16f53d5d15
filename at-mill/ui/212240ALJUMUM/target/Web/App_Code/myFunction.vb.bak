﻿Imports UsersGUI
Imports WebTools
Imports WebTools.tools
Imports CommonDefines.Defines
Imports System.Activities.Expressions
Imports System.Web.UI.WebControls.Expressions
Imports System.Security.Authentication.ExtendedProtection

Public Class myFunction

    Public Shared Function GetRoot() As String
        Return System.Configuration.ConfigurationManager.AppSettings("root")
    End Function

    Public Shared Function GetServerName() As String
        Return System.Configuration.ConfigurationManager.AppSettings("ServerName")
    End Function

    Private Shared Function GetIdCycleFromScreen(scr As Screen, ByRef error_msg As String) As Integer
        Dim ret_val As Integer = m_InvalidId

        Try
            ' tutte le pagine web al momento di un salvataggio chiamano tutte le funzioni
            ' in questo caso mi assicuro che il chiamante sia di tipo CYCLE (EnumPageName.ProductionPlan)
            If (scr.EnumPageNameCode = EnumPageName.ProductionPlan) Then
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("CYC_ID") Then
                        'Recupero il codice del cycle dallo screen di riferimento
                        ret_val = CInt(f.FixedRuntimeValue)
                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            myErrorMessage.AppendCustomErrorMsg(error_msg, "[GetIdCycleFromScreen]: Nome ciclo errato -> " & scr.Name.ToUpper())
        End Try

        Return ret_val
    End Function

    Private Shared Function GetIdRecipeFromScreen(scr As Screen, ByRef error_msg As String) As Integer
        Dim ret_val As Integer = m_InvalidId

        Try
            ' tutte le pagine web al momento di un salvataggio chiamano tutte le funzioni
            ' in questo caso mi assicuro che il chiamante sia di tipo CYCLE (EnumPageName.ProductionPlan)
            If (scr.EnumPageNameCode = EnumPageName.RecipeParameters) Then
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("MTR_ID") Then
                        'Recupero il codice del cycle dallo screen di riferimento
                        ret_val = CInt(f.FixedRuntimeValue)
                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            myErrorMessage.AppendCustomErrorMsg(error_msg, "[GetIdRecipeFromScreen]: Nome meta recipe errato -> " & scr.Name.ToUpper())
        End Try

        Return ret_val
    End Function

    Public Shared Function CtrlDataCycle(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim rec_id As Long = m_InvalidId
        Dim pro_id As Long = m_InvalidId
        Dim ingr_pro_id As Integer = 0

        Dim source_set As Integer = 0
        Dim source_bin As Long = 0
        Dim list_source As New Generic.List(Of Long)

        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim dest_set As Integer = 0
        Dim dest_bin As Long = 0
        Dim list_dest As New Generic.List(Of Long) ' lista delle destinazioni in coda
        Dim list_alt_dest As New Generic.List(Of Long) ' lista delle destinazioni in coda
        Dim list_dest_slot_used As New Generic.List(Of Integer) ' lista degli slot della coda usati
        Dim list_alt_dest_slot_used As New Generic.List(Of Integer) ' lista degli slot della coda usati
        Dim list_index As Integer = 1 ' rappresenta l'indice di una lista da 1 a n (es.: 5)

        Dim custom_error_msg As String = String.Empty
        Dim value As Double = 0.0
        Dim valueUI As Double = 0.0
        Dim valueUF As Double = 0.0
        Dim cardinality As Integer = 0
        Dim source_slot As Integer = 0
        Dim dic_source_slot_used As New Dictionary(Of Integer, List(Of Integer)) ' dizionario degli slot usati per ingrediente
        Dim field_split As String() = Nothing
        Dim recipe_param_val As String = String.Empty
        Dim load_mode As Integer = 0
        Dim alt_load_mode As Integer = 0

        Dim b_load_mode_continuous_valid_time = False
        Dim cycle_id As Integer = 0
        Dim opz_stop As Integer = 0

        Dim IdCyleFromName As Integer = GetIdCycleFromScreen(scr, error_msg)

        ' se il chiamante non è un CYCLE_N esco subito
        If (IdCyleFromName = m_InvalidId) Then
            Return True
        End If

        Select Case CType(IdCyleFromName, SSCycles)

            Case SSCycles.C01_Intake

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)

                                list_dest_slot_used.Add(list_index) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                        End If

                        list_index += 1 ' aggiorno list_index a ogni iterazione dei dest_bin_x
                    End If

                Next

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                If list_dest_slot_used.Count > 0 AndAlso list_dest_slot_used(0) <> 1 Then 'se ci sono elementi nella lista di selected
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_DEST_NOT_SELECTED").GetValue)

                End If

                For i As Integer = 1 To list_dest_slot_used.Count - 1
                    If list_dest_slot_used(i) <> (list_dest_slot_used(i - 1) + 1) Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next
                list_dest_slot_used.Clear() ' clear the list_dest to be used by next cycle
                list_index = 1 ' assign index to 1 to be used by next cycle

            Case SSCycles.C02_FirstCleaning
                Dim source_selected_per_ingredient(SSMaxNumOfSourcesPerIngredient.C02_FirstCleaning) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C02_FirstCleaning) As Integer
                Dim source_list_bins As New Dictionary(Of Integer, List(Of Long))
                Dim b_opz_dry_bottom As Boolean = False
                Dim perc_ui_DM201 As Double = 0.0
                Dim perc_uf_DM201 As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("CYC_ID") Then
                        cycle_id = f.FixedRuntimeValue
                    ElseIf f.FieldDb.Equals("REC_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            rec_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            Try
                                pro_id = Long.Parse(GetRecipeParameter("PRO_ID", rec_id))
                                If IsProductObsolete(pro_id) Then
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("PRODUCT_NAME").GetValue() & ", " & scr.Parent.GetEntryByKeyName("RCP_CONTAINS_OBSOLETE_PRODUCT").GetValue)
                                End If
                            Catch ex As Exception
                                ' should never happen
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("PRO_NAME_NOT_SELECTED_IN_RECIPE").GetValue())
                            End Try
                        End If
                    ElseIf f.FieldDb.StartsWith("RECIPE_INGR_ID_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            ingr_pro_id = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myDropDownList).SelectedValue, f.nDecimal)
                            If IsProductObsolete(ingr_pro_id) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ", " & scr.Parent.GetEntryByKeyName("RCP_CONTAINS_OBSOLETE_PRODUCT").GetValue)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient(cardinality) = True

                            If (source_list_bins.ContainsKey(cardinality)) Then
                                source_list_bins(cardinality).Add(source_bin)
                            Else
                                source_list_bins.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = 1
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_DRY_BOTTOM") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                b_opz_dry_bottom = True
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("PERC_UI_DM201") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            valueUI = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If valueUI < SSPercUIDM.MinDM201 OrElse valueUI > SSPercUIDM.MaxDM201 Then
                                myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, valueUI, SSPercUIDM.MinDM201, SSPercUIDM.MaxDM201)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("PERC_UF_DM201") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            valueUF = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If valueUF < SSPercUFDM.MinDM201 OrElse valueUF > SSPercUFDM.MaxDM201 Then
                                myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, valueUF, SSPercUFDM.MinDM201, SSPercUFDM.MaxDM201)
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("CAL_SET_DM201") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.Equals("MIN_REST_TIME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If value < 0 Then
                                myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If load_mode <> MODE_LINEAR AndAlso load_mode <> MODE_CIRCULAR AndAlso load_mode <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If

                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)

                                list_dest_slot_used.Add(list_index) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                        End If

                        list_index += 1 ' aggiorno list_index a ogni iterazione dei dest_bin_x
                    End If

                Next

                'Controlli post ciclo for dei singoli Field
                For Each f As Field In scr.EditFields
                    If b_opz_dry_bottom AndAlso f.FieldDb.Equals("T_DRY_BOTTOM_MIN") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If value <= 0 Then
                                myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    End If
                Next

                For i As Integer = 1 To SSMaxNumOfIngredients.C02_FirstCleaning
                    If (source_mode(i) = MODE_CONTINUOUS) Then
                        For Each f As Field In scr.EditFields

                            If f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If b_load_mode_continuous_valid_time = False Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_1").GetValue() & " " & scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If
                Next

                For i As Integer = 1 To SSMaxNumOfIngredients.C02_FirstCleaning
                    If source_list_bins.ContainsKey(i) AndAlso source_mode(i) <> MODE_LINEAR AndAlso source_list_bins(i) IsNot Nothing AndAlso source_list_bins(i).Count < 2 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                    End If
                Next

                If load_mode = MODE_CONTINUOUS Then
                    For Each f As Field In scr.EditFields

                        If f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_MIN") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 Then
                                        myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                    Else
                                        If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                            b_load_mode_continuous_valid_time = True
                                        End If
                                    End If
                                End If
                            End If
                        ElseIf f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_SEC") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 OrElse value > 59 Then
                                        myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                    End If
                                End If
                            End If
                        End If
                    Next

                    If b_load_mode_continuous_valid_time = False Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                            " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                            scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

                ' controllo coerenza ricetta e code origine
                For i As Integer = 1 To source_selected_per_ingredient.Length - 1
                    recipe_param_val = GetRecipeParameter("INGR_ID_" & i, rec_id)

                    If recipe_param_val <> String.Empty AndAlso source_selected_per_ingredient(i) = False Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg,
                                    scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                    scr.Parent.GetEntryByKeyName("SOURCE_QUEUE_INGREDIENT_RECIPE_SELECTED_BUT_CYCLE_NOT_SELECTED").GetValue())
                    End If

                    If recipe_param_val = String.Empty AndAlso source_selected_per_ingredient(i) = True Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg,
                                    scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                    scr.Parent.GetEntryByKeyName("SOURCE_QUEUE_INGREDIENT_RECIPE_NOT_SELECTED_BUT_CYCLE_SELECTED").GetValue())
                    End If

                Next

                If load_mode <> MODE_LINEAR And list_dest.Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

                If source_selected_per_ingredient(cardinality) = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SOURCE_NOT_OK").GetValue)
                End If

                If Not source_list_bins.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_EXTRACTION_NOT_SELECTED").GetValue)
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                If list_dest_slot_used.Count > 0 AndAlso list_dest_slot_used(0) <> 1 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_DEST_NOT_SELECTED").GetValue)

                End If

                For i As Integer = 1 To list_dest_slot_used.Count - 1
                    If list_dest_slot_used(i) <> (list_dest_slot_used(i - 1) + 1) Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next

                If valueUI >= valueUF Then
                    custom_error_msg = scr.Parent.GetEntryByKeyName("OPTIONS_SECTION_DM201").GetValue & ": " & scr.Parent.GetEntryByKeyName("PERC_UI_BIGGER_THAN_PERC_UF").GetValue
                    myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                End If

            Case SSCycles.C03_SecondTempering
                Dim source_selected_per_ingredient(SSMaxNumOfSourcesPerIngredient.C03_SecondTempering) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C03_SecondTempering) As Integer
                Dim source_list_bins As New Dictionary(Of Integer, List(Of Long))
                Dim b_opz_dry_bottom As Boolean = False
                Dim perc_ui_DM202 As Double = 0.0
                Dim perc_uf_DM202 As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("CYC_ID") Then
                        cycle_id = f.FixedRuntimeValue
                    ElseIf f.FieldDb.Equals("REC_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            rec_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            Try
                                pro_id = Long.Parse(GetRecipeParameter("PRO_ID", rec_id))
                                If IsProductObsolete(pro_id) Then
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("PRODUCT_NAME").GetValue() & ", " & scr.Parent.GetEntryByKeyName("RCP_CONTAINS_OBSOLETE_PRODUCT").GetValue)
                                End If
                            Catch ex As Exception
                                ' should never happen
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("PRO_NAME_NOT_SELECTED_IN_RECIPE").GetValue())
                            End Try
                        End If
                    ElseIf f.FieldDb.StartsWith("RECIPE_INGR_ID_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            ingr_pro_id = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myDropDownList).SelectedValue, f.nDecimal)
                            If IsProductObsolete(ingr_pro_id) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ", " & scr.Parent.GetEntryByKeyName("RCP_CONTAINS_OBSOLETE_PRODUCT").GetValue)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient(cardinality) = True

                            If (source_list_bins.ContainsKey(cardinality)) Then
                                source_list_bins(cardinality).Add(source_bin)
                            Else
                                source_list_bins.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = 1
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_DRY_BOTTOM") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                b_opz_dry_bottom = True
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("PERC_UI_DM202") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            valueUI = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If valueUI < SSPercUIDM.MinDM202 OrElse valueUI > SSPercUIDM.MaxDM202 Then
                                myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, valueUI, SSPercUIDM.MinDM202, SSPercUIDM.MaxDM202)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("PERC_UF_DM202") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            valueUF = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If valueUF < SSPercUFDM.MinDM202 OrElse valueUF > SSPercUFDM.MaxDM202 Then
                                myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, valueUF, SSPercUFDM.MinDM202, SSPercUFDM.MaxDM202)
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("CAL_SET_DM202") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.Equals("MIN_REST_TIME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If value < 0 Then
                                myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If load_mode <> MODE_LINEAR AndAlso load_mode <> MODE_CIRCULAR AndAlso load_mode <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)

                                list_dest_slot_used.Add(list_index) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                        End If

                        list_index += 1 ' aggiorno list_index a ogni iterazione dei dest_bin_x
                    End If

                Next

                'Controlli post ciclo for dei singoli Field
                For Each f As Field In scr.EditFields
                    If b_opz_dry_bottom AndAlso f.FieldDb.Equals("T_DRY_BOTTOM_MIN") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If value <= 0 Then
                                myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    End If
                Next

                For i As Integer = 1 To SSMaxNumOfIngredients.C03_SecondTempering
                    If (source_mode(i) = MODE_CONTINUOUS) Then
                        For Each f As Field In scr.EditFields

                            If f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If b_load_mode_continuous_valid_time = False Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_1").GetValue() & " " & scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If
                Next

                For i As Integer = 1 To SSMaxNumOfIngredients.C03_SecondTempering
                    If source_list_bins.ContainsKey(i) AndAlso source_mode(i) <> MODE_LINEAR AndAlso source_list_bins(i) IsNot Nothing AndAlso source_list_bins(i).Count < 2 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                    End If
                Next

                If load_mode = MODE_CONTINUOUS Then
                    For Each f As Field In scr.EditFields

                        If f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_MIN") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 Then
                                        myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                    Else
                                        If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                            b_load_mode_continuous_valid_time = True
                                        End If
                                    End If
                                End If
                            End If
                        ElseIf f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_SEC") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 OrElse value > 59 Then
                                        myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                    End If
                                End If
                            End If
                        End If
                    Next

                    If b_load_mode_continuous_valid_time = False Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                            " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                            scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

                ' controllo coerenza ricetta e code origine
                For i As Integer = 1 To source_selected_per_ingredient.Length - 1
                    recipe_param_val = GetRecipeParameter("INGR_ID_" & i, rec_id)

                    If recipe_param_val <> String.Empty AndAlso source_selected_per_ingredient(i) = False Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg,
                                    scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                    scr.Parent.GetEntryByKeyName("SOURCE_QUEUE_INGREDIENT_RECIPE_SELECTED_BUT_CYCLE_NOT_SELECTED").GetValue())
                    End If

                    If recipe_param_val = String.Empty AndAlso source_selected_per_ingredient(i) = True Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg,
                                    scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                    scr.Parent.GetEntryByKeyName("SOURCE_QUEUE_INGREDIENT_RECIPE_NOT_SELECTED_BUT_CYCLE_SELECTED").GetValue())
                    End If

                Next

                If load_mode <> MODE_LINEAR And list_dest.Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

                If source_selected_per_ingredient(cardinality) = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SOURCE_NOT_OK").GetValue)
                End If

                If Not source_list_bins.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_EXTRACTION_NOT_SELECTED").GetValue)
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                If list_dest_slot_used.Count > 0 AndAlso list_dest_slot_used(0) <> 1 Then 'se ci sono elementi nella lista di selected
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_DEST_NOT_SELECTED").GetValue)

                End If

                For i As Integer = 1 To list_dest_slot_used.Count - 1
                    If list_dest_slot_used(i) <> (list_dest_slot_used(i - 1) + 1) Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next

                If valueUI >= valueUF Then
                    custom_error_msg = scr.Parent.GetEntryByKeyName("OPTIONS_SECTION_DM202").GetValue & ": " & scr.Parent.GetEntryByKeyName("PERC_UI_BIGGER_THAN_PERC_UF").GetValue
                    myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                End If

            Case SSCycles.C04_SecondCleaningAndMilling
                Dim source_mode(SSMaxNumOfIngredients.C04_SecondCleaningAndMilling) As Integer
                Dim source_selected_per_ingredient(SSMaxNumOfIngredients.C04_SecondCleaningAndMilling) As Boolean
                Dim source_list_bins As New Dictionary(Of Integer, List(Of Long))

                Dim opz_line As Integer = 0
                Dim opz_qty As Boolean = False
                Dim opz_stop_line As Boolean = False

                Dim rb_1 As myRadioButton = UsersGUI.tools.FindControlRecursive(root, "SS_C04_QTY_LINE_1")
                Dim rb_2 As myRadioButton = UsersGUI.tools.FindControlRecursive(root, "SS_C04_QTY_LINE_2")
                Dim rb_3 As myRadioButton = UsersGUI.tools.FindControlRecursive(root, "SS_C04_QTY_LINE_3")
                Dim rb_4 As myRadioButton = UsersGUI.tools.FindControlRecursive(root, "SS_C04_QTY_LINE_4")

                Dim opz_enable_f1 As Boolean = False
                Dim opz_enable_f2 As Boolean = False
                Dim opz_enable_f3 As Boolean = False
                Dim opz_enable_bran As Boolean = False

                Dim prod_id_f1 As Long = 0
                Dim prod_id_f2 As Long = 0
                Dim prod_id_f3 As Long = 0
                Dim prod_id_bran As Long = 0

                Dim list_dest_f1 As New List(Of Long)
                Dim list_dest_f2 As New List(Of Long)
                Dim list_dest_f3 As New List(Of Long)
                Dim list_dest_bran As New List(Of Long)

                Dim list_dest_slot_used_f1 As New Generic.List(Of Integer)
                Dim list_dest_slot_used_f2 As New Generic.List(Of Integer)
                Dim list_dest_slot_used_f3 As New Generic.List(Of Integer)
                Dim list_dest_slot_used_bran As New Generic.List(Of Integer)

                Dim load_mode_f1 As Integer = 0
                Dim load_mode_f2 As Integer = 0
                Dim load_mode_f3 As Integer = 0
                Dim load_mode_bran As Integer = 0

                ' 1. controlli sui singoli campi
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("REC_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            rec_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            Try
                                pro_id = Long.Parse(GetRecipeParameter("PRO_ID", rec_id))
                                If IsProductObsolete(pro_id) Then
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("PRODUCT_NAME").GetValue() & ", " & scr.Parent.GetEntryByKeyName("RCP_CONTAINS_OBSOLETE_PRODUCT").GetValue)
                                End If
                            Catch ex As Exception
                                ' should never happen
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("PRO_NAME_NOT_SELECTED_IN_RECIPE").GetValue())
                            End Try
                        End If
                    ElseIf f.FieldDb.StartsWith("RECIPE_INGR_ID_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            ingr_pro_id = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myDropDownList).SelectedValue, f.nDecimal)
                            If IsProductObsolete(ingr_pro_id) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ", " & scr.Parent.GetEntryByKeyName("RCP_CONTAINS_OBSOLETE_PRODUCT").GetValue)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                    opz_qty = True
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY_LINE") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            opz_stop_line = True
                                            Exit For
                                        End If
                                    End If
                                Next
                            End If

                        End If
                    ElseIf f.FieldDb.Equals("OPZ_ENABLE_GREEN_LINE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_line = opz_line + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_ENABLE_PINK_LINE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_line = opz_line + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_SCR_TRANS_LINE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_line = opz_line + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_SL401_TRANS_F1_LINE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_line = opz_line + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.Equals("PROD_ID_F1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty AndAlso IsNumeric(CType(ctrl, myDropDownList).SelectedValue) Then
                            prod_id_f1 = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            opz_enable_f1 = True
                        End If
                    ElseIf f.FieldDb.StartsWith("F1_DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            If Not list_dest_f1.Contains(dest_bin) Then
                                list_dest_f1.Add(dest_bin)

                                list_dest_slot_used_f1.Add(cardinality) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                            'controllo che i dest bin siano stati inseriti in ordine, cioè, che non ci siano bucchi
                            If cardinality > 1 AndAlso Not list_dest_slot_used_f1.Contains(cardinality - 1) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F1").GetValue & ": " & scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                            End If

                        End If

                    ElseIf f.FieldDb.Equals("PROD_ID_F2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty AndAlso IsNumeric(CType(ctrl, myDropDownList).SelectedValue) Then
                            prod_id_f2 = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            opz_enable_f2 = True
                        End If
                    ElseIf f.FieldDb.StartsWith("F2_DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            If Not list_dest_f2.Contains(dest_bin) Then
                                list_dest_f2.Add(dest_bin)

                                list_dest_slot_used_f2.Add(cardinality) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                            'controllo che i dest bin siano stati inseriti in ordine, cioè, che non ci siano bucchi
                            If cardinality > 1 AndAlso Not list_dest_slot_used_f2.Contains(cardinality - 1) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F2").GetValue & ": " & scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                            End If

                        End If

                    ElseIf f.FieldDb.Equals("PROD_ID_F3") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty AndAlso IsNumeric(CType(ctrl, myDropDownList).SelectedValue) Then
                            prod_id_f3 = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            opz_enable_f3 = True
                        End If
                    ElseIf f.FieldDb.StartsWith("F3_DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            If Not list_dest_f3.Contains(dest_bin) Then
                                list_dest_f3.Add(dest_bin)

                                list_dest_slot_used_f3.Add(cardinality) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                            'controllo che i dest bin siano stati inseriti in ordine, cioè, che non ci siano bucchi
                            If cardinality > 1 AndAlso Not list_dest_slot_used_f3.Contains(cardinality - 1) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F3").GetValue & ": " & scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                            End If

                        End If

                    ElseIf f.FieldDb.Equals("PROD_ID_BRAN") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty AndAlso IsNumeric(CType(ctrl, myDropDownList).SelectedValue) Then
                            prod_id_bran = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            opz_enable_bran = True
                        End If
                    ElseIf f.FieldDb.StartsWith("BRAN_DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            If Not list_dest_bran.Contains(dest_bin) Then
                                list_dest_bran.Add(dest_bin)

                                list_dest_slot_used_bran.Add(cardinality) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                            'controllo che i dest bin siano stati inseriti in ordine, cioè, che non ci siano bucchi
                            If cardinality > 1 AndAlso Not list_dest_slot_used_bran.Contains(cardinality - 1) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DESTINATION_BIN_BRAN").GetValue & ": " & scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("LOAD_MODE_1") Then
                        load_mode_f1 = -1
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode_f1 = i
                                            Exit For
                                        End If
                                    End If
                                Next
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE_2") Then
                        load_mode_f2 = -1
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode_f2 = i
                                            Exit For
                                        End If
                                    End If
                                Next
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE_3") Then
                        load_mode_f3 = -1
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode_f3 = i
                                            Exit For
                                        End If
                                    End If
                                Next
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE_4") Then
                        load_mode_bran = -1
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode_bran = i
                                            Exit For
                                        End If
                                    End If
                                Next
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)
                            source_slot = field_split(4)

                            'se lo slot che sto analizzando non è il primo, lo aggiungo al dizionario e faccio il controllo che il precedente slot non sia vuoto
                            If source_slot > 1 Then
                                If Not dic_source_slot_used.ContainsKey(cardinality) Then
                                    dic_source_slot_used.Add(cardinality, New List(Of Integer))
                                End If

                                dic_source_slot_used(cardinality).Add(source_slot)

                                'se lo slot precedente a quello che sto analizzando è vuoto, faccio display dell'error message
                                If Not dic_source_slot_used(cardinality).Contains(source_slot - 1) Then
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & scr.Parent.GetEntryByKeyName("SRC_NOT_PROPERLY_FILLED").GetValue)
                                End If
                            Else
                                'se lo slot che sto analizzando è il primo, lo aggiungo al dizionario senza fare controlli
                                dic_source_slot_used.Add(cardinality, New List(Of Integer))
                                dic_source_slot_used(cardinality).Add(source_slot)
                            End If

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " - " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient(cardinality) = True

                            If (source_list_bins.ContainsKey(cardinality)) Then
                                source_list_bins(cardinality).Add(source_bin)
                            Else
                                source_list_bins.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        Else
                            If (Not source_list_bins.ContainsKey(cardinality)) Then
                                source_list_bins.Add(cardinality, New List(Of Long) From {0})
                            Else
                                source_list_bins(cardinality).Add(0)
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entrare nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = 1
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    End If
                Next

                'Controlli post ciclo for dei singoli Field

                ' 2. controlli sugli ingredienti
                ' controllo coerenza ricetta e code origine
                For i As Integer = 1 To source_selected_per_ingredient.Length - 1
                    recipe_param_val = GetRecipeParameter("INGR_ID_" & i, rec_id)

                    If recipe_param_val <> String.Empty AndAlso source_selected_per_ingredient(i) = False Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg,
                                    scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                    scr.Parent.GetEntryByKeyName("SOURCE_QUEUE_INGREDIENT_RECIPE_SELECTED_BUT_CYCLE_NOT_SELECTED").GetValue())
                    End If

                    If recipe_param_val = String.Empty AndAlso source_selected_per_ingredient(i) = True Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg,
                                    scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                    scr.Parent.GetEntryByKeyName("SOURCE_QUEUE_INGREDIENT_RECIPE_NOT_SELECTED_BUT_CYCLE_SELECTED").GetValue())
                    End If

                Next

                'Verifico che le code di origine siano compilate in ordine 1,2,3,4
                Dim bTrovoAlmenoUnaOriginePerLaCoda1 As Boolean = False
                For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    If (source_list_bins(1).Item(i - 1) > 0) Then
                        bTrovoAlmenoUnaOriginePerLaCoda1 = True
                    End If
                Next

                Dim bTrovoAlmenoUnaOriginePerLaCoda2 As Boolean = False
                For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    If (source_list_bins(2).Item(i - 1) > 0) Then
                        bTrovoAlmenoUnaOriginePerLaCoda2 = True
                    End If
                Next

                Dim bTrovoAlmenoUnaOriginePerLaCoda3 As Boolean = False
                For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    If (source_list_bins(3).Item(i - 1) > 0) Then
                        bTrovoAlmenoUnaOriginePerLaCoda3 = True
                    End If
                Next

                Dim bTrovoAlmenoUnaOriginePerLaCoda4 As Boolean = False
                For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    If (source_list_bins(4).Item(i - 1) > 0) Then
                        bTrovoAlmenoUnaOriginePerLaCoda4 = True
                    End If
                Next

                If (bTrovoAlmenoUnaOriginePerLaCoda2 And Not bTrovoAlmenoUnaOriginePerLaCoda1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "Ingredient 2 is selected, but ingredient 1 is NOT selected")
                ElseIf (bTrovoAlmenoUnaOriginePerLaCoda3 And Not bTrovoAlmenoUnaOriginePerLaCoda2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "Ingredient 3 is selected, but ingredient 2 is NOT selected")
                ElseIf (bTrovoAlmenoUnaOriginePerLaCoda4 And Not bTrovoAlmenoUnaOriginePerLaCoda3) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "Ingredient 4 is selected, but ingredient 3 is NOT selected")
                End If

                ' controllo che la coda origini non sia vuota
                If list_source.Count = 0 Then
                    myErrorMessage.AppendAllSourcesPercEmptyMsg(error_msg, scr.Parent)
                End If

                ' per ogni ingrediente, è un errore se il secondo elemento della coda non è compilato, quindi c'è solo una origine in coda, ma si è selezionato una modalità di estrazione diverse dalla LINEAR
                For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    If source_mode(i) <> MODE_LINEAR Then
                        If source_list_bins(i).Item(1) <= 0 Then
                            myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_CIRC_CONT_AT_LEAST_TWO_SOURCES").GetValue())
                        End If
                    End If
                Next

                ' 3. controllo code destinazioni linea per linea
                ' linea F1
                If (opz_enable_f1) Then
                    If list_dest_f1.Count = 0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_DESTINATION_F1_NOT_OK").GetValue())
                    End If

                    If load_mode_f1 <> MODE_LINEAR And list_dest_f1.Count < 2 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_1_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                    End If

                    If load_mode_f1 = MODE_CONTINUOUS Then
                        For Each f As Field In scr.EditFields
                            If f.FieldDb.Equals("LOAD_MODE_1_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("LOAD_MODE_1_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If Not b_load_mode_continuous_valid_time Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If

                End If

                ' linea F2
                If (opz_enable_f2) Then
                    If list_dest_f2.Count = 0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_DESTINATION_F2_NOT_OK").GetValue())
                    End If

                    If load_mode_f2 <> MODE_LINEAR And list_dest_f2.Count < 2 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_2_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                    End If

                    If load_mode_f2 = MODE_CONTINUOUS Then
                        For Each f As Field In scr.EditFields
                            If f.FieldDb.Equals("LOAD_MODE_2_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("LOAD_MODE_2_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If Not b_load_mode_continuous_valid_time Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If

                End If

                ' linea F3
                If (opz_enable_f3) Then
                    If list_dest_f3.Count = 0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_DESTINATION_F3_NOT_OK").GetValue())
                    End If

                    If load_mode_f3 <> MODE_LINEAR And list_dest_f3.Count < 2 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_3_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                    End If

                    If load_mode_f3 = MODE_CONTINUOUS Then
                        For Each f As Field In scr.EditFields
                            If f.FieldDb.Equals("LOAD_MODE_3_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("LOAD_MODE_3_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If Not b_load_mode_continuous_valid_time Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If

                End If

                ' linea bran
                If (opz_enable_bran) Then
                    If list_dest_bran.Count = 0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_DESTINATION_BRAN_NOT_OK").GetValue())
                    End If

                    If load_mode_bran <> MODE_LINEAR And list_dest_bran.Count < 2 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_4_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                    End If

                    If load_mode_bran = MODE_CONTINUOUS Then
                        For Each f As Field In scr.EditFields
                            If f.FieldDb.Equals("LOAD_MODE_4_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("LOAD_MODE_4_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If Not b_load_mode_continuous_valid_time Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If

                End If

                ' Controllo che almeno una stop option sia selezionata
                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

                ' Controllo che se la stop options quantity è selezionata, sia selezionata anche la stop line
                If opz_qty = True AndAlso opz_stop_line = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("STOP_OPTIONS_SECTION").GetValue() & ": " & scr.Parent.GetEntryByKeyName("STOP_LINE_SELECTION_NEEDED").GetValue())
                ElseIf opz_qty = True Then
                    If Not opz_enable_f1 AndAlso rb_1.Checked Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("STOP_OPTIONS_SECTION").GetValue() & ": " &
                                                                       scr.Parent.GetEntryByKeyName("STOP_LINE_SELECTED").GetValue() & " " & scr.Parent.GetEntryByKeyName("SS_C04_QTY_LINE_4_1").GetValue() & ", " &
                                                                       scr.Parent.GetEntryByKeyName("LINE_NOT_ENABLED_FOR_RECIPE").GetValue())
                    ElseIf Not opz_enable_f2 AndAlso rb_2.Checked Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("STOP_OPTIONS_SECTION").GetValue() & ": " &
                                                                       scr.Parent.GetEntryByKeyName("STOP_LINE_SELECTED").GetValue() & " " & scr.Parent.GetEntryByKeyName("SS_C04_QTY_LINE_4_2").GetValue() & ", " &
                                                                       scr.Parent.GetEntryByKeyName("LINE_NOT_ENABLED_FOR_RECIPE").GetValue())
                    ElseIf Not opz_enable_f3 AndAlso rb_3.Checked Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("STOP_OPTIONS_SECTION").GetValue() & ": " &
                                                                       scr.Parent.GetEntryByKeyName("STOP_LINE_SELECTED").GetValue() & " " & scr.Parent.GetEntryByKeyName("SS_C04_QTY_LINE_4_3").GetValue() & ", " &
                                                                       scr.Parent.GetEntryByKeyName("LINE_NOT_ENABLED_FOR_RECIPE").GetValue())
                    ElseIf Not opz_enable_bran AndAlso rb_4.Checked Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("STOP_OPTIONS_SECTION").GetValue() & ": " &
                                                                       scr.Parent.GetEntryByKeyName("STOP_LINE_SELECTED").GetValue() & " " & scr.Parent.GetEntryByKeyName("SS_C04_QTY_LINE_4_4").GetValue() & ", " &
                                                                       scr.Parent.GetEntryByKeyName("LINE_NOT_ENABLED_FOR_RECIPE").GetValue())
                    End If
                End If

                ' Controllo che almeno una linea sia stata selezionata
                If (opz_line <= 0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("OPZ_ENABLE_LINE_NOT_SELECTED").GetValue())
                End If

                ''Controllo che non ci siano destinazioni ripetute nelle linee
                ''F1 vs F2
                'For i = 0 To list_dest_f1.Count - 1
                '    For j = 0 To list_dest_f2.Count - 1
                '        If list_dest_f1(i) = list_dest_f2(j) Then
                '            myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F1").GetValue() & ", " &
                '                                                scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F2").GetValue() & ": " &
                '                                                scr.Parent.GetEntryByKeyName("REPEATED_DESTINATIONS").GetValue())
                '            Exit For
                '        End If
                '    Next
                'Next
                ''F1 vs F3
                'For i = 0 To list_dest_f1.Count - 1
                '    For j = 0 To list_dest_f3.Count - 1
                '        If list_dest_f1(i) = list_dest_f3(j) Then
                '            myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F1").GetValue() & ", " &
                '                                                scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F3").GetValue() & ": " &
                '                                                scr.Parent.GetEntryByKeyName("REPEATED_DESTINATIONS").GetValue())
                '            Exit For
                '        End If
                '    Next
                'Next
                ''F2 vs F3
                'For i = 0 To list_dest_f2.Count - 1
                '    For j = 0 To list_dest_f3.Count - 1
                '        If list_dest_f2(i) = list_dest_f3(j) Then
                '            myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F2").GetValue() & ", " &
                '                                                scr.Parent.GetEntryByKeyName("DESTINATION_BIN_F3").GetValue() & ": " &
                '                                                scr.Parent.GetEntryByKeyName("REPEATED_DESTINATIONS").GetValue())
                '            Exit For
                '        End If
                '    Next
                'Next

                ' Automatic notes
                If error_msg.Length = 0 Then
                    AutomaticNotesFill(root, list_source, list_dest)
                End If

            Case SSCycles.C06_FlourTransportRV501
                Dim source_mode(SSMaxNumOfIngredients.C06_FlourTransportRV501) As Integer

                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C06_FlourTransportRV501) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C06_FlourTransportRV501) As Boolean
                Dim source_selected_per_ingredient_3(SSMaxNumOfSourcesPerIngredient.C06_FlourTransportRV501) As Boolean
                Dim source_selected_per_ingredient_4(SSMaxNumOfSourcesPerIngredient.C06_FlourTransportRV501) As Boolean
                Dim source_selected_per_ingredient_5(SSMaxNumOfSourcesPerIngredient.C06_FlourTransportRV501) As Boolean

                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_2 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_3 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_4 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_5 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0
                Dim perc_bin_2 As Double = 0
                Dim perc_bin_3 As Double = 0
                Dim perc_bin_4 As Double = 0
                Dim perc_bin_5 As Double = 0
                Dim perc_tot As Double = 0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_2 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_2
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_3") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_3 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_3
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_4") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_4 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_4
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_5") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_5 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_5
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_2_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_2(cardinality) = True

                            If (source_list_bins_2.ContainsKey(cardinality)) Then
                                source_list_bins_2(cardinality).Add(source_bin)
                            Else
                                source_list_bins_2.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_3_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_3(cardinality) = True

                            If (source_list_bins_3.ContainsKey(cardinality)) Then
                                source_list_bins_3(cardinality).Add(source_bin)
                            Else
                                source_list_bins_3.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_4_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_4(cardinality) = True

                            If (source_list_bins_4.ContainsKey(cardinality)) Then
                                source_list_bins_4(cardinality).Add(source_bin)
                            Else
                                source_list_bins_4.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_5_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_5(cardinality) = True

                            If (source_list_bins_5.ContainsKey(cardinality)) Then
                                source_list_bins_5(cardinality).Add(source_bin)
                            Else
                                source_list_bins_5.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = MODE_LINEAR
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If load_mode <> MODE_LINEAR AndAlso load_mode <> MODE_CIRCULAR AndAlso load_mode <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)

                                list_dest_slot_used.Add(list_index) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                        End If

                        list_index += 1 ' aggiorno list_index a ogni iterazione dei dest_bin_x
                    End If
                Next

                ' INGREDIENT 1
                ' coerenza prodotto - percentuale
                If perc_bin_1 = 0 AndAlso source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_1 <> 0 AndAlso Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_1.ContainsKey(1) AndAlso source_mode(1) <> MODE_LINEAR AndAlso source_list_bins_1(1) IsNot Nothing AndAlso source_list_bins_1(1).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_EXTRACTION_NOT_SELECTED").GetValue)
                End If

                ' INGREDIENT 2
                ' coerenza prodotto - percentuale
                If perc_bin_2 = 0 AndAlso source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_2 <> 0 AndAlso Not source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_2.ContainsKey(2) AndAlso source_mode(2) <> MODE_LINEAR AndAlso source_list_bins_2(2) IsNot Nothing AndAlso source_list_bins_2(2).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' INGREDIENT 3
                ' coerenza prodotto - percentuale
                If perc_bin_3 = 0 AndAlso source_list_bins_3.ContainsKey(3) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_3").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_3 <> 0 AndAlso Not source_list_bins_3.ContainsKey(3) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_3").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_3.ContainsKey(3) AndAlso source_mode(3) <> MODE_LINEAR AndAlso source_list_bins_3(3) IsNot Nothing AndAlso source_list_bins_3(3).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_3").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' INGREDIENT 4
                ' coerenza prodotto - percentuale
                If perc_bin_4 = 0 AndAlso source_list_bins_4.ContainsKey(4) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_4").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_4 <> 0 AndAlso Not source_list_bins_4.ContainsKey(4) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_4").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_4.ContainsKey(4) AndAlso source_mode(4) <> MODE_LINEAR AndAlso source_list_bins_4(4) IsNot Nothing AndAlso source_list_bins_4(4).Count < 4 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_4").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' INGREDIENT 5
                ' coerenza prodotto - percentuale
                If perc_bin_5 = 0 AndAlso source_list_bins_5.ContainsKey(5) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_5").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_5 <> 0 AndAlso Not source_list_bins_5.ContainsKey(5) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_5").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_5.ContainsKey(5) AndAlso source_mode(5) <> MODE_LINEAR AndAlso source_list_bins_5(5) IsNot Nothing AndAlso source_list_bins_5(5).Count < 5 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_5").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                For i As Integer = 1 To SSMaxNumOfIngredients.C06_FlourTransportRV501
                    If (source_mode(i) = MODE_CONTINUOUS) Then
                        For Each f As Field In scr.EditFields

                            If f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If b_load_mode_continuous_valid_time = False Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_1").GetValue() & " " & scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If
                Next

                If load_mode = MODE_CONTINUOUS Then
                    For Each f As Field In scr.EditFields

                        If f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_MIN") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 Then
                                        myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                    Else
                                        If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                            b_load_mode_continuous_valid_time = True
                                        End If
                                    End If
                                End If
                            End If
                        ElseIf f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_SEC") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 OrElse value > 59 Then
                                        myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                    End If
                                End If
                            End If
                        End If
                    Next

                    If b_load_mode_continuous_valid_time = False Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                            " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                            scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

                If load_mode <> MODE_LINEAR And list_dest.Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                If list_dest_slot_used.Count > 0 AndAlso list_dest_slot_used(0) <> 1 Then 'se ci sono elementi nella lista di selected
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_DEST_NOT_SELECTED").GetValue)

                End If

                For i As Integer = 1 To list_dest_slot_used.Count - 1
                    If list_dest_slot_used(i) <> (list_dest_slot_used(i - 1) + 1) Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next
                list_dest_slot_used.Clear() ' clear the list_dest to be used by next cycle
                list_index = 1 ' assign index to 1 to be used by next cycle

            Case SSCycles.C07_FlourTransportRV502
                Dim source_mode(SSMaxNumOfIngredients.C07_FlourTransportRV502) As Integer

                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C07_FlourTransportRV502) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C07_FlourTransportRV502) As Boolean
                Dim source_selected_per_ingredient_3(SSMaxNumOfSourcesPerIngredient.C07_FlourTransportRV502) As Boolean
                Dim source_selected_per_ingredient_4(SSMaxNumOfSourcesPerIngredient.C07_FlourTransportRV502) As Boolean
                Dim source_selected_per_ingredient_5(SSMaxNumOfSourcesPerIngredient.C07_FlourTransportRV502) As Boolean

                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_2 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_3 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_4 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_5 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0
                Dim perc_bin_2 As Double = 0
                Dim perc_bin_3 As Double = 0
                Dim perc_bin_4 As Double = 0
                Dim perc_bin_5 As Double = 0
                Dim perc_tot As Double = 0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_2 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_2
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_3") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_3 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_3
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_4") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_4 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_4
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_5") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_5 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_5
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_2_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_2(cardinality) = True

                            If (source_list_bins_2.ContainsKey(cardinality)) Then
                                source_list_bins_2(cardinality).Add(source_bin)
                            Else
                                source_list_bins_2.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_3_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_3(cardinality) = True

                            If (source_list_bins_3.ContainsKey(cardinality)) Then
                                source_list_bins_3(cardinality).Add(source_bin)
                            Else
                                source_list_bins_3.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_4_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_4(cardinality) = True

                            If (source_list_bins_4.ContainsKey(cardinality)) Then
                                source_list_bins_4(cardinality).Add(source_bin)
                            Else
                                source_list_bins_4.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_5_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_5(cardinality) = True

                            If (source_list_bins_5.ContainsKey(cardinality)) Then
                                source_list_bins_5(cardinality).Add(source_bin)
                            Else
                                source_list_bins_5.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = MODE_LINEAR
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If load_mode <> MODE_LINEAR AndAlso load_mode <> MODE_CIRCULAR AndAlso load_mode <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)

                                list_dest_slot_used.Add(list_index) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                        End If

                        list_index += 1 ' aggiorno list_index a ogni iterazione dei dest_bin_x
                    End If
                Next

                ' INGREDIENT 1
                ' coerenza prodotto - percentuale
                If perc_bin_1 = 0 AndAlso source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_1 <> 0 AndAlso Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_1.ContainsKey(1) AndAlso source_mode(1) <> MODE_LINEAR AndAlso source_list_bins_1(1) IsNot Nothing AndAlso source_list_bins_1(1).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_EXTRACTION_NOT_SELECTED").GetValue)
                End If

                ' INGREDIENT 2
                ' coerenza prodotto - percentuale
                If perc_bin_2 = 0 AndAlso source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_2 <> 0 AndAlso Not source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_2.ContainsKey(2) AndAlso source_mode(2) <> MODE_LINEAR AndAlso source_list_bins_2(2) IsNot Nothing AndAlso source_list_bins_2(2).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' INGREDIENT 3
                ' coerenza prodotto - percentuale
                If perc_bin_3 = 0 AndAlso source_list_bins_3.ContainsKey(3) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_3").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_3 <> 0 AndAlso Not source_list_bins_3.ContainsKey(3) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_3").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_3.ContainsKey(3) AndAlso source_mode(3) <> MODE_LINEAR AndAlso source_list_bins_3(3) IsNot Nothing AndAlso source_list_bins_3(3).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_3").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' INGREDIENT 4
                ' coerenza prodotto - percentuale
                If perc_bin_4 = 0 AndAlso source_list_bins_4.ContainsKey(4) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_4").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_4 <> 0 AndAlso Not source_list_bins_4.ContainsKey(4) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_4").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_4.ContainsKey(4) AndAlso source_mode(4) <> MODE_LINEAR AndAlso source_list_bins_4(4) IsNot Nothing AndAlso source_list_bins_4(4).Count < 4 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_4").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' INGREDIENT 5
                ' coerenza prodotto - percentuale
                If perc_bin_5 = 0 AndAlso source_list_bins_5.ContainsKey(5) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_5").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_5 <> 0 AndAlso Not source_list_bins_5.ContainsKey(5) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_5").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_5.ContainsKey(5) AndAlso source_mode(5) <> MODE_LINEAR AndAlso source_list_bins_5(5) IsNot Nothing AndAlso source_list_bins_5(5).Count < 5 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_5").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                For i As Integer = 1 To SSMaxNumOfIngredients.C07_FlourTransportRV502
                    If (source_mode(i) = MODE_CONTINUOUS) Then
                        For Each f As Field In scr.EditFields

                            If f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If b_load_mode_continuous_valid_time = False Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_1").GetValue() & " " & scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If
                Next

                If load_mode = MODE_CONTINUOUS Then
                    For Each f As Field In scr.EditFields

                        If f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_MIN") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 Then
                                        myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                    Else
                                        If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                            b_load_mode_continuous_valid_time = True
                                        End If
                                    End If
                                End If
                            End If
                        ElseIf f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_SEC") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 OrElse value > 59 Then
                                        myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                    End If
                                End If
                            End If
                        End If
                    Next

                    If b_load_mode_continuous_valid_time = False Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                            " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                            scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

                If load_mode <> MODE_LINEAR And list_dest.Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                If list_dest_slot_used.Count > 0 AndAlso list_dest_slot_used(0) <> 1 Then 'se ci sono elementi nella lista di selected
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_DEST_NOT_SELECTED").GetValue)

                End If

                For i As Integer = 1 To list_dest_slot_used.Count - 1
                    If list_dest_slot_used(i) <> (list_dest_slot_used(i - 1) + 1) Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next
                list_dest_slot_used.Clear() ' clear the list_dest to be used by next cycle
                list_index = 1 ' assign index to 1 to be used by next cycle

            Case SSCycles.C08_FlourBaggingBS501
                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C08_FlourBaggingBS501) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C08_FlourBaggingBS501) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C08_FlourBaggingBS501) As Integer
                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_2 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0.0
                Dim perc_bin_2 As Double = 0.0
                Dim perc_tot As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_2 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_2
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_2_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_2(cardinality) = True

                            If (source_list_bins_2.ContainsKey(cardinality)) Then
                                source_list_bins_2(cardinality).Add(source_bin)
                            Else
                                source_list_bins_2.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = 1
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If
                        End If
                    End If
                Next

                ' INGREDIENT 1
                ' coerenza prodotto - percentuale
                If perc_bin_1 = 0 AndAlso source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_1 <> 0 AndAlso Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_1.ContainsKey(1) AndAlso source_mode(1) <> MODE_LINEAR AndAlso source_list_bins_1(1) IsNot Nothing AndAlso source_list_bins_1(1).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_EXTRACTION_NOT_SELECTED").GetValue)
                End If

                ' INGREDIENT 2
                ' coerenza prodotto - percentuale
                If perc_bin_2 = 0 AndAlso source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_2 <> 0 AndAlso Not source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_2.ContainsKey(2) AndAlso source_mode(2) <> MODE_LINEAR AndAlso source_list_bins_2(2) IsNot Nothing AndAlso source_list_bins_2(2).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' controlli generali
                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

            Case SSCycles.C09_FlourBaggingBS502
                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C09_FlourBaggingBS502) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C09_FlourBaggingBS502) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C09_FlourBaggingBS502) As Integer
                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_2 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0.0
                Dim perc_bin_2 As Double = 0.0
                Dim perc_tot As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_2 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_2
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_2_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_2(cardinality) = True

                            If (source_list_bins_2.ContainsKey(cardinality)) Then
                                source_list_bins_2(cardinality).Add(source_bin)
                            Else
                                source_list_bins_2.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = 1
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If
                        End If
                    End If
                Next

                ' INGREDIENT 1
                ' coerenza prodotto - percentuale
                If perc_bin_1 = 0 AndAlso source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_1 <> 0 AndAlso Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_1.ContainsKey(1) AndAlso source_mode(1) <> MODE_LINEAR AndAlso source_list_bins_1(1) IsNot Nothing AndAlso source_list_bins_1(1).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_EXTRACTION_NOT_SELECTED").GetValue)
                End If

                ' INGREDIENT 2
                ' coerenza prodotto - percentuale
                If perc_bin_2 = 0 AndAlso source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_2 <> 0 AndAlso Not source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_2.ContainsKey(2) AndAlso source_mode(2) <> MODE_LINEAR AndAlso source_list_bins_2(2) IsNot Nothing AndAlso source_list_bins_2(2).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' controlli generali
                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

            Case SSCycles.C10_BranRecycle
                Dim source_mode(SSMaxNumOfIngredients.C10_BranRecycle) As Integer
                Dim source_list_bins As New Dictionary(Of Integer, List(Of Long))
                Dim source_selected_per_ingredient(SSMaxNumOfSourcesPerIngredient.C10_BranRecycle) As Boolean
                Dim perc_bin(SSMaxNumOfSourcesPerIngredient.C10_BranRecycle) As Double
                Dim perc_tot As Double = 0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If

                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(3)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            perc_bin(cardinality) = 0.0
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            perc_bin(cardinality) = value

                            perc_tot += value

                            If value < 0.0 OrElse value > 100.0 Then
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If

                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)
                            source_slot = field_split(4)

                            'se lo slot che sto analizzando non è il primo, lo aggiungo al dizionario e faccio il controllo che il precedente slot non sia vuoto
                            If source_slot > 1 Then
                                If Not dic_source_slot_used.ContainsKey(cardinality) Then
                                    dic_source_slot_used.Add(cardinality, New List(Of Integer))
                                End If

                                dic_source_slot_used(cardinality).Add(source_slot)

                                'se lo slot precedente a quello che sto analizzando è vuoto, faccio display dell'error message
                                If Not dic_source_slot_used(cardinality).Contains(source_slot - 1) Then
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & scr.Parent.GetEntryByKeyName("SRC_NOT_PROPERLY_FILLED").GetValue)
                                End If
                            Else
                                'se lo slot che sto analizzando è il primo, lo aggiungo al dizionario senza fare controlli
                                dic_source_slot_used.Add(cardinality, New List(Of Integer))
                                dic_source_slot_used(cardinality).Add(source_slot)
                            End If

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " - " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient(cardinality) = True

                            If (source_list_bins.ContainsKey(cardinality)) Then
                                source_list_bins(cardinality).Add(source_bin)
                            Else
                                source_list_bins.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If

                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = MODE_LINEAR
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LOAD_MODE") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))
                                Dim i As Integer = 0

                                For i = 1 To n_ripetizioni

                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            load_mode = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If load_mode <> MODE_LINEAR AndAlso load_mode <> MODE_CIRCULAR AndAlso load_mode <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)

                                list_dest_slot_used.Add(cardinality) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                            'controllo che i dest bin siano stati inseriti in ordine, cioè, che non ci siano bucchi
                            If cardinality > 1 AndAlso Not list_dest_slot_used.Contains(cardinality - 1) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                            End If

                        End If
                    ElseIf f.FieldDb.StartsWith("ALT_DEST_BIN_") Then 'eseguito tante volte quanto sono i alternative dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(3)

                            If Not list_alt_dest.Contains(dest_bin) Then
                                list_alt_dest.Add(dest_bin)

                                list_alt_dest_slot_used.Add(cardinality) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                            'controllo che i dest bin siano stati inseriti in ordine, cioè, che non ci siano bucchi
                            If cardinality > 1 AndAlso Not list_alt_dest_slot_used.Contains(cardinality - 1) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                            End If

                        End If
                    End If
                Next

                'coerenza prodotto - percentuale
                For i As Integer = 1 To SSMaxNumOfIngredients.C10_BranRecycle
                    If Not source_list_bins.ContainsKey(i) AndAlso perc_bin(i) > 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                    ElseIf source_list_bins.ContainsKey(i) AndAlso source_list_bins(i).Count <> 0 AndAlso perc_bin(i) = 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                    End If

                Next

                'Controllo percentuale totale = 100%
                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                For i As Integer = 1 To SSMaxNumOfIngredients.C10_BranRecycle
                    If (source_mode(i) = MODE_CONTINUOUS) Then
                        For Each f As Field In scr.EditFields

                            If f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_MIN") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 Then
                                            myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                        Else
                                            If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                                b_load_mode_continuous_valid_time = True
                                            End If
                                        End If
                                    End If
                                End If
                            ElseIf f.FieldDb.Equals("SRC_" & i & "_CONTINUOUS_SEC") Then
                                ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                                If ctrl.Visible Then
                                    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                        value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                        If value < 0 OrElse value > 59 Then
                                            myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                        End If
                                    End If
                                End If
                            End If
                        Next

                        If b_load_mode_continuous_valid_time = False Then
                            custom_error_msg = scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_1").GetValue() & " " & scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                                scr.Parent.GetEntryByKeyName("SOURCE_MODE_CONT_INVALID_TIME_2").GetValue()
                            myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                        End If
                    End If
                Next

                For i As Integer = 1 To SSMaxNumOfIngredients.C10_BranRecycle
                    If source_list_bins.ContainsKey(i) AndAlso source_mode(i) <> MODE_LINEAR AndAlso source_list_bins(i) IsNot Nothing AndAlso source_list_bins(i).Count < 2 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() &
                                ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                    End If
                Next

                'Destinations: load mode
                If load_mode = MODE_CONTINUOUS Then
                    For Each f As Field In scr.EditFields

                        If f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_MIN") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 Then
                                        myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                    Else
                                        If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                            b_load_mode_continuous_valid_time = True
                                        End If
                                    End If
                                End If
                            End If
                        ElseIf f.FieldDb.Equals("LOAD_MODE_CONTINUOUS_SEC") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 OrElse value > 59 Then
                                        myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                    End If
                                End If
                            End If
                        End If
                    Next

                    If b_load_mode_continuous_valid_time = False Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                            " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                            scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

                If load_mode <> MODE_LINEAR And list_dest.Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                'Alternative destinations: load mode
                If alt_load_mode = MODE_CONTINUOUS Then
                    For Each f As Field In scr.EditFields

                        If f.FieldDb.Equals("ALT_LOAD_MODE_CONTINUOUS_MIN") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 Then
                                        myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                                    Else
                                        If value >= MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN Then
                                            b_load_mode_continuous_valid_time = True
                                        End If
                                    End If
                                End If
                            End If
                        ElseIf f.FieldDb.Equals("ALT_LOAD_MODE_CONTINUOUS_SEC") Then
                            ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                            If ctrl.Visible Then
                                If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text <> String.Empty Then
                                    value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                                    If value < 0 OrElse value > 59 Then
                                        myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, value, 0, 59)
                                    End If
                                End If
                            End If
                        End If
                    Next

                    If b_load_mode_continuous_valid_time = False Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_1").GetValue() &
                            " " & MODE_CONTINUOUS_MINIMUM_TIME_IN_MIN & " " &
                            scr.Parent.GetEntryByKeyName("LOAD_MODE_CONT_INVALID_TIME_2").GetValue()
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

                If alt_load_mode <> MODE_LINEAR And list_alt_dest.Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("LOAD_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                'Empty dest
                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                'Empty alternative dest -> non è obbligatorio selezionare una dest alternativa
                'If list_alt_dest.Count = 0 Then
                '    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                'End If

                'Empty source
                If b_at_least_one_source = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SOURCE_NOT_OK").GetValue)
                End If

                'At least one stop option selected
                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

            Case SSCycles.C11_FlourBaggingBS601
                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C11_FlourBaggingBS601) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C11_FlourBaggingBS601) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C11_FlourBaggingBS601) As Integer
                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0.0
                Dim perc_tot As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If
                        End If
                    End If
                Next

                ' controlli generali
                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

            Case SSCycles.C12_ScreeningsGrinding
                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C12_ScreeningsGrinding) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C12_ScreeningsGrinding) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C12_ScreeningsGrinding) As Integer
                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0.0
                Dim perc_tot As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If

                        'ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        '    ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        '    If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                        '        myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        '    End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If
                        End If
                    End If
                Next

                ' controlli generali
                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

            Case SSCycles.C13_ScreeningsTransport
                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C13_ScreeningsTransport) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C13_ScreeningsTransport) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C13_ScreeningsTransport) As Integer
                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0.0
                Dim perc_tot As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("OPZ_FULL_DEST") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myCheckBox).Checked = True Then
                                opz_stop = opz_stop + 1
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_QTY") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If CType(ctrl, myTextBox).Text <> String.Empty Then
                                If CDbl(CType(ctrl, myTextBox).Text) > 0.0 Then
                                    opz_stop = opz_stop + 1
                                Else
                                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("QUANTITY_TO_PRODUCE").GetValue() + " " + scr.Parent.GetEntryByKeyName("VALUE_MORE_THAN_XXX").GetValue() + " 0")
                                End If
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then 'eseguito tante volte quanto sono i dest bin
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)

                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)

                                list_dest_slot_used.Add(list_index) ' se aggiungo un dest bin alla lista di destinazioni aggiungo anche il suo indice alla lista di slot usati
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If

                        End If

                        list_index += 1 ' aggiorno list_index a ogni iterazione dei dest_bin_x
                    End If
                Next

                ' controlli generali
                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

                If list_dest_slot_used.Count > 0 AndAlso list_dest_slot_used(0) <> 1 Then 'se ci sono elementi nella lista di selected
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_DEST_NOT_SELECTED").GetValue)

                End If

                For i As Integer = 1 To list_dest_slot_used.Count - 1
                    If list_dest_slot_used(i) <> (list_dest_slot_used(i - 1) + 1) Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("DEST_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next

                If opz_stop.Equals(0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_AT_LEAST_ONE_STOP_OPTION").GetValue())
                End If

            Case SSCycles.C14_FlourBaggingBS503
                Dim source_selected_per_ingredient_1(SSMaxNumOfSourcesPerIngredient.C14_FlourBaggingBS503) As Boolean
                Dim source_selected_per_ingredient_2(SSMaxNumOfSourcesPerIngredient.C14_FlourBaggingBS503) As Boolean
                Dim source_mode(SSMaxNumOfIngredients.C14_FlourBaggingBS503) As Integer
                Dim source_list_bins_1 As New Dictionary(Of Integer, List(Of Long))
                Dim source_list_bins_2 As New Dictionary(Of Integer, List(Of Long))

                Dim perc_bin_1 As Double = 0.0
                Dim perc_bin_2 As Double = 0.0
                Dim perc_tot As Double = 0.0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_1 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_1
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SRC_INGR_PERC_2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            If (IsNumeric(CType(ctrl, myTextBox).Text)) Then
                                perc_bin_2 = CDbl(CType(ctrl, myTextBox).Text)
                                perc_tot = perc_tot + perc_bin_2
                            Else
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_1_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_1(cardinality) = True

                            If (source_list_bins_1.ContainsKey(cardinality)) Then
                                source_list_bins_1(cardinality).Add(source_bin)
                            Else
                                source_list_bins_1.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_2_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then

                            field_split = f.FieldDb.Split("_")
                            cardinality = field_split(2)

                            source_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_source.Contains(source_bin) Then
                                list_source.Add(source_bin)
                                b_at_least_one_source = True
                            Else
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & ": " & GetBinNameFromId(source_bin) & " " & scr.Parent.GetEntryByKeyName("SOURCE_PRESENT").GetValue())
                            End If

                            ' mi segno se almeno una cella è selezionata per questo ingrediente
                            source_selected_per_ingredient_2(cardinality) = True

                            If (source_list_bins_2.ContainsKey(cardinality)) Then
                                source_list_bins_2(cardinality).Add(source_bin)
                            Else
                                source_list_bins_2.Add(cardinality, New List(Of Long) From {source_bin})
                            End If
                        End If
                    ElseIf f.FieldDb.Contains("SOURCE_MODE_") Then
                        Dim s_filter As String() = Split(f.RadioButtonAspFilter, ":")
                        If s_filter.Length > 1 Then
                            If s_filter(0) = "CONFIG" Then
                                Dim n_ripetizioni As Integer = Integer.Parse(s_filter(2))

                                Dim array_livello = f.FieldDb.Split("_")

                                Dim n_livello As Integer = Integer.Parse(array_livello(array_livello.GetUpperBound(0)))

                                Dim i As Integer = 0

                                'Prima di entarre nel For lo setto di default a LINEAR
                                source_mode(n_livello) = MODE_LINEAR
                                For i = 1 To n_ripetizioni

                                    source_mode(n_livello) = 1
                                    ctrl = CType(UsersGUI.tools.FindControlRecursive(root, f.FieldName & "_" & i), myRadioButton)

                                    If ctrl IsNot Nothing Then
                                        If CType(ctrl, myRadioButton).Checked = True Then
                                            source_mode(n_livello) = i
                                            Exit For
                                        End If
                                    End If
                                Next
                                If source_mode(n_livello) <> MODE_LINEAR AndAlso source_mode(n_livello) <> MODE_CIRCULAR AndAlso source_mode(n_livello) <> MODE_CONTINUOUS Then
                                    myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                                End If
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("OPZ_EMPTY_SOURCE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                        End If
                    ElseIf f.FieldDb.Equals("LINE_FLOWRATE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            Else
                                myErrorMessage.AppendDuplicateDestInQueueMsg(error_msg, scr.Parent, dest_bin)
                            End If
                        End If
                    End If
                Next

                ' INGREDIENT 1
                ' coerenza prodotto - percentuale
                If perc_bin_1 = 0 AndAlso source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_1 <> 0 AndAlso Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_1.ContainsKey(1) AndAlso source_mode(1) <> MODE_LINEAR AndAlso source_list_bins_1(1) IsNot Nothing AndAlso source_list_bins_1(1).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_1").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                If Not source_list_bins_1.ContainsKey(1) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_EXTRACTION_NOT_SELECTED").GetValue)
                End If

                ' INGREDIENT 2
                ' coerenza prodotto - percentuale
                If perc_bin_2 = 0 AndAlso source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                ElseIf perc_bin_2 <> 0 AndAlso Not source_list_bins_2.ContainsKey(2) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                End If

                'se ho selezionato modalità circolare, devo selezionare almeno due celle
                If source_list_bins_2.ContainsKey(2) AndAlso source_mode(2) <> MODE_LINEAR AndAlso source_list_bins_2(2) IsNot Nothing AndAlso source_list_bins_2(2).Count < 2 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_2").GetValue() &
                            ": " & scr.Parent.GetEntryByKeyName("SOURCE_MODE_CIRC_CONT_AT_LEAST_TWO_DEST").GetValue())
                End If

                ' controlli generali
                If (perc_tot <> 100.0) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("WRONG_TOTAL_PERC").GetValue())
                End If

                If list_dest.Count = 0 Then
                    myErrorMessage.AppendDestQueueEmptyMsg(error_msg, scr.Parent)
                End If

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("CYCLE_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "[CtrlDataCycle] Unhandled CYCLE")
                End If

        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If
    End Function

    Public Shared Function AskUserUnfilledDataCycle(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim rec_id As Long = m_InvalidId
        Dim pro_id As Long = m_InvalidId
        Dim source_set As Integer = 0
        Dim source_bin As Long = 0
        Dim list_source As New Generic.List(Of Long)
        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim dest_set As Integer = 0
        Dim dest_bin As Long = 0
        Dim list_dest As New Generic.List(Of Long)
        Dim value As Double = 0.0
        Dim recipe_param_val As String = String.Empty

        Dim IdCyleFromName As Integer = GetIdCycleFromScreen(scr, error_msg)

        ' se il chiamante non è un CYCLE_N esco subito
        If (IdCyleFromName = m_InvalidId) Then
            Return True
        End If

        Select Case CType(IdCyleFromName, SSCycles)
            Case SSCycles.C01_Intake
            Case SSCycles.C02_FirstCleaning
            Case SSCycles.C03_SecondTempering
            Case SSCycles.C04_SecondCleaningAndMilling
            Case SSCycles.C06_FlourTransportRV501
            Case SSCycles.C07_FlourTransportRV502
            Case SSCycles.C08_FlourBaggingBS501
            Case SSCycles.C09_FlourBaggingBS502
            Case SSCycles.C10_BranRecycle
            Case SSCycles.C11_FlourBaggingBS601
            Case SSCycles.C12_ScreeningsGrinding
            Case SSCycles.C13_ScreeningsTransport
            Case SSCycles.C14_FlourBaggingBS503

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("CYCLE_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "[AskUserUnfilledDataCycle] Unhandled CYCLE")
                End If
        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If

    End Function

    Public Shared Function AskUserConfirmDataCycle(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim rec_id As Long = m_InvalidId
        Dim pro_id As Long = m_InvalidId
        Dim source_set As Integer = 0
        Dim source_bin As Long = 0
        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim dest_set As Integer = 0
        Dim dest_bin As Long = 0
        Dim list_dest As New Generic.List(Of Long)
        Dim value As Double = 0.0
        Dim cardinality As Integer = 0
        Dim field_split As String() = Nothing
        Dim recipe_param_val As String = String.Empty
        Dim ingr_cardinality As Integer
        Dim ingr_pro_id As Long = m_InvalidId

        Dim IdCyleFromName As Integer = GetIdCycleFromScreen(scr, error_msg)

        ' se il chiamante non è un CYCLE_N esco subito
        If (IdCyleFromName = m_InvalidId) Then
            Return True
        End If

        Select Case CType(IdCyleFromName, SSCycles)

            Case SSCycles.C01_Intake

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            End If
                        End If
                    End If
                Next

                ' 1. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C02_FirstCleaning
                Dim bin_id(SSMaxNumOfIngredients.C02_FirstCleaning, SSSourceNumber.C02_FirstCleaning) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.Equals("REC_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            rec_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2)
                        cardinality = field_split(4)

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C02_FirstCleaning
                    For j As Integer = 1 To SSSourceNumber.C02_FirstCleaning
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C03_SecondTempering
                Dim bin_id(SSMaxNumOfIngredients.C03_SecondTempering, SSSourceNumber.C03_SecondTempering) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.Equals("REC_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            rec_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2)
                        cardinality = field_split(4)

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C03_SecondTempering
                    For j As Integer = 1 To SSSourceNumber.C03_SecondTempering
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C04_SecondCleaningAndMilling
                Dim bin_id(SSMaxNumOfIngredients.C04_SecondCleaningAndMilling, SSSourceNumber.C04_SecondCleaningAndMilling) As Integer
                Dim list_dest_f1 As Generic.List(Of Integer) = New List(Of Integer)
                Dim list_dest_f2 As Generic.List(Of Integer) = New List(Of Integer)
                Dim list_dest_f3 As Generic.List(Of Integer) = New List(Of Integer)
                Dim list_dest_bran As Generic.List(Of Integer) = New List(Of Integer)
                Dim pro_id_f1 As Integer = 0
                Dim pro_id_f2 As Integer = 0
                Dim pro_id_f3 As Integer = 0
                Dim pro_id_bran As Integer = 0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.Equals("REC_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            rec_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2)
                        cardinality = field_split(4)

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("PROD_ID_F1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            pro_id_f1 = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        Else
                            pro_id_f1 = 0
                        End If
                    ElseIf f.FieldDb.StartsWith("PROD_ID_F2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            pro_id_f2 = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        Else
                            pro_id_f2 = 0
                        End If
                    ElseIf f.FieldDb.StartsWith("PROD_ID_F3") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            pro_id_f3 = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        Else
                            pro_id_f3 = 0
                        End If
                    ElseIf f.FieldDb.StartsWith("PROD_ID_BRAN") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            pro_id_bran = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        Else
                            pro_id_bran = 0
                        End If
                    ElseIf f.FieldDb.StartsWith("F1_DEST_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest_f1.Contains(dest_bin) Then
                                list_dest_f1.Add(dest_bin)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("F2_DEST_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest_f2.Contains(dest_bin) Then
                                list_dest_f2.Add(dest_bin)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("F3_DEST_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest_f3.Contains(dest_bin) Then
                                list_dest_f3.Add(dest_bin)
                            End If
                        End If
                    ElseIf f.FieldDb.StartsWith("BRAN_DEST_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest_bran.Contains(dest_bin) Then
                                list_dest_bran.Add(dest_bin)
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    For j As Integer = 1 To SSSourceNumber.C04_SecondCleaningAndMilling
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest_f1
                    DestinationCheck(scr, dest, pro_id_f1, Nothing, error_msg)
                Next
                For Each dest In list_dest_f2
                    DestinationCheck(scr, dest, pro_id_f2, Nothing, error_msg)
                Next
                For Each dest In list_dest_f3
                    DestinationCheck(scr, dest, pro_id_f3, Nothing, error_msg)
                Next
                For Each dest In list_dest_bran
                    DestinationCheck(scr, dest, pro_id_bran, Nothing, error_msg)
                Next

            Case SSCycles.C06_FlourTransportRV501
                Dim bin_id(SSMaxNumOfIngredients.C06_FlourTransportRV501, SSSourceNumber.C06_FlourTransportRV501) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C06_FlourTransportRV501
                    For j As Integer = 1 To SSSourceNumber.C06_FlourTransportRV501
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C07_FlourTransportRV502
                Dim bin_id(SSMaxNumOfIngredients.C07_FlourTransportRV502, SSSourceNumber.C07_FlourTransportRV502) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If

                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C07_FlourTransportRV502
                    For j As Integer = 1 To SSSourceNumber.C07_FlourTransportRV502
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C08_FlourBaggingBS501
                Dim bin_id(SSMaxNumOfIngredients.C08_FlourBaggingBS501, SSSourceNumber.C08_FlourBaggingBS501) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If

                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If (TypeOf ctrl Is myDropDownList) AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                                dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                If Not list_dest.Contains(dest_bin) Then
                                    list_dest.Add(dest_bin)
                                End If
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C08_FlourBaggingBS501
                    For j As Integer = 1 To SSSourceNumber.C08_FlourBaggingBS501
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C09_FlourBaggingBS502
                Dim bin_id(SSMaxNumOfIngredients.C09_FlourBaggingBS502, SSSourceNumber.C09_FlourBaggingBS502) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If (TypeOf ctrl Is myDropDownList) AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                                dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                If Not list_dest.Contains(dest_bin) Then
                                    list_dest.Add(dest_bin)
                                End If
                            End If
                        End If
                    End If
                Next
                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C09_FlourBaggingBS502
                    For j As Integer = 1 To SSSourceNumber.C09_FlourBaggingBS502
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C10_BranRecycle
                Dim bin_id(SSMaxNumOfIngredients.C10_BranRecycle, SSSourceNumber.C10_BranRecycle) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If

                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                            If Not list_dest.Contains(dest_bin) Then
                                list_dest.Add(dest_bin)
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C10_BranRecycle
                    For j As Integer = 1 To SSSourceNumber.C10_BranRecycle
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C11_FlourBaggingBS601
                Dim bin_id(SSMaxNumOfIngredients.C11_FlourBaggingBS601, SSSourceNumber.C11_FlourBaggingBS601) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If (TypeOf ctrl Is myDropDownList) AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                                dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                If Not list_dest.Contains(dest_bin) Then
                                    list_dest.Add(dest_bin)
                                End If
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C11_FlourBaggingBS601
                    For j As Integer = 1 To SSSourceNumber.C11_FlourBaggingBS601
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C12_ScreeningsGrinding
                Dim bin_id(SSMaxNumOfIngredients.C12_ScreeningsGrinding, SSSourceNumber.C12_ScreeningsGrinding) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If (TypeOf ctrl Is myDropDownList) AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                                dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                If Not list_dest.Contains(dest_bin) Then
                                    list_dest.Add(dest_bin)
                                End If
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C12_ScreeningsGrinding
                    For j As Integer = 1 To SSSourceNumber.C12_ScreeningsGrinding
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                '' 2. Destinations -> controllo non necessario per questo ciclo
                'For Each dest In list_dest
                '    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                'Next

            Case SSCycles.C13_ScreeningsTransport
                Dim bin_id(SSMaxNumOfIngredients.C13_ScreeningsTransport, SSSourceNumber.C13_ScreeningsTransport) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If (TypeOf ctrl Is myDropDownList) AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                                dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                If Not list_dest.Contains(dest_bin) Then
                                    list_dest.Add(dest_bin)
                                End If
                            End If
                        End If
                    End If
                Next

                '' 1. Sources -> controllo non necessario per questo ciclo
                'For i As Integer = 1 To SSMaxNumOfIngredients.C13_ScreeningsTransport
                '    For j As Integer = 1 To SSSourceNumber.C13_ScreeningsTransport
                '        If bin_id(i, j) <> 0 Then

                '            ' 1.1 Prodotto
                '            Try
                '                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                '            Catch ex As Exception
                '                ingr_pro_id = m_InvalidId
                '            End Try

                '            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                '        End If
                '    Next
                'Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case SSCycles.C14_FlourBaggingBS503
                Dim bin_id(SSMaxNumOfIngredients.C14_FlourBaggingBS503, SSSourceNumber.C14_FlourBaggingBS503) As Integer

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ' should never happen
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Return False
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_PERC_") Then
                        'nothing to do
                        'Serve per non creare eccezione nel controllo sotto
                    ElseIf f.FieldDb.StartsWith("SRC_INGR_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        ingr_cardinality = field_split(2) 'ingr number
                        cardinality = field_split(4) 'bin number

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            bin_id(ingr_cardinality, cardinality) = 0
                        Else
                            bin_id(ingr_cardinality, cardinality) = CType(ctrl, myDropDownList).SelectedValue
                        End If
                    ElseIf f.FieldDb.StartsWith("DEST_BIN_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            If (TypeOf ctrl Is myDropDownList) AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                                dest_bin = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                                If Not list_dest.Contains(dest_bin) Then
                                    list_dest.Add(dest_bin)
                                End If
                            End If
                        End If
                    End If
                Next

                ' 1. Sources
                For i As Integer = 1 To SSMaxNumOfIngredients.C14_FlourBaggingBS503
                    For j As Integer = 1 To SSSourceNumber.C14_FlourBaggingBS503
                        If bin_id(i, j) <> 0 Then

                            ' 1.1 Prodotto
                            Try
                                ingr_pro_id = Long.Parse(GetRecipeParameter("INGR_ID_" & i, rec_id))
                            Catch ex As Exception
                                ingr_pro_id = m_InvalidId
                            End Try

                            SourceCheck(scr, bin_id(i, j), ingr_pro_id, error_msg)

                        End If
                    Next
                Next

                ' 2. Destinations
                For Each dest In list_dest
                    DestinationCheck(scr, dest, pro_id, Nothing, error_msg)
                Next

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("CYCLE_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "[AskUserConfirmDataCycle] Unhandled CYCLE")
                End If
        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If

    End Function

    Public Shared Function CtrlDataRecipe(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control = Nothing
        Dim ddl As DropDownList = Nothing
        Dim custom_error_msg As String = String.Empty
        Dim value As Double = 0.0
        Dim source_perc_sum As Double = 0.0
        Dim b_at_least_one_source As Boolean = False
        Dim field_split As String() = Nothing
        Dim cardinality As Integer = 0

        Dim IdMetaRecipeFromName As Integer = GetIdRecipeFromScreen(scr, error_msg)

        Select Case CType(IdMetaRecipeFromName, SSMetaRecipe)

            Case SSMetaRecipe.R2_FirstCleaning
                Dim ingr_id(SSMaxNumOfIngredients.C02_FirstCleaning) As Integer
                Dim ingr_perc(SSMaxNumOfIngredients.C02_FirstCleaning) As Double

                ' controllo prodotti e ingredienti
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If

                    ElseIf f.FieldDb.StartsWith("INGR_ID_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(2)

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ingr_id(cardinality) = 0
                        Else
                            If ingr_id.Contains(CType(ctrl, myDropDownList).SelectedValue) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & " " &
                                                                    scr.Parent.GetEntryByKeyName("USED_MORE_THAN_ONCE").GetValue())
                            End If

                            ingr_id(cardinality) = CType(ctrl, myDropDownList).SelectedValue

                            b_at_least_one_source = True
                        End If

                    ElseIf f.FieldDb.StartsWith("INGR_PERC_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(2)

                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            ingr_perc(cardinality) = 0.0
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            ingr_perc(cardinality) = value

                            source_perc_sum += value

                            If value < 0.0 OrElse value > 100.0 Then
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If

                    End If

                Next

                ' coerenza prodotto - percentuale
                For i As Integer = 1 To SSMaxNumOfIngredients.C02_FirstCleaning
                    If ingr_id(i) = 0 AndAlso ingr_perc(i) > 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                    ElseIf ingr_id(i) <> 0 AndAlso ingr_perc(i) = 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                    End If
                Next

                ' almeno un'origine selezionata
                If Not b_at_least_one_source Then
                    myErrorMessage.AppendAllSourcesPercEmptyMsg(error_msg, scr.Parent)
                End If

                ' il primo ingrediente deve essere compilato
                If ingr_id(1) = 0 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_INGR_NOT_SELECTED").GetValue)
                End If

                ' gli ingredienti devono essere compilati in ordine
                For i As Integer = 2 To SSMaxNumOfIngredients.C02_FirstCleaning
                    If ingr_id(i) <> 0 AndAlso ingr_id(i - 1) = 0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGR_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next

                If source_perc_sum <> 100.0 Then
                    myErrorMessage.AppendTotalSourcePercMsg(error_msg, scr.Parent, source_perc_sum)
                End If

            Case SSMetaRecipe.R3_SecondTempering
                Dim ingr_id(SSMaxNumOfIngredients.C03_SecondTempering) As Integer
                Dim ingr_perc(SSMaxNumOfIngredients.C03_SecondTempering) As Double

                ' controllo prodotti e ingredienti
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If

                    ElseIf f.FieldDb.StartsWith("INGR_ID_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(2)

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ingr_id(cardinality) = 0
                        Else
                            If ingr_id.Contains(CType(ctrl, myDropDownList).SelectedValue) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & " " &
                                                                    scr.Parent.GetEntryByKeyName("USED_MORE_THAN_ONCE").GetValue())
                            End If

                            ingr_id(cardinality) = CType(ctrl, myDropDownList).SelectedValue

                            b_at_least_one_source = True
                        End If

                    ElseIf f.FieldDb.StartsWith("INGR_PERC_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(2)

                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            ingr_perc(cardinality) = 0.0
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            ingr_perc(cardinality) = value

                            source_perc_sum += value

                            If value < 0.0 OrElse value > 100.0 Then
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If

                    End If

                Next

                ' coerenza prodotto - percentuale
                For i As Integer = 1 To SSMaxNumOfIngredients.C03_SecondTempering
                    If ingr_id(i) = 0 AndAlso ingr_perc(i) > 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                    ElseIf ingr_id(i) <> 0 AndAlso ingr_perc(i) = 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                    End If
                Next

                ' almeno un'origine selezionata
                If Not b_at_least_one_source Then
                    myErrorMessage.AppendAllSourcesPercEmptyMsg(error_msg, scr.Parent)
                End If

                ' il primo ingrediente deve essere compilato
                If ingr_id(1) = 0 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_INGR_NOT_SELECTED").GetValue)
                End If

                ' gli ingredienti devono essere compilati in ordine
                For i As Integer = 2 To SSMaxNumOfIngredients.C03_SecondTempering
                    If ingr_id(i) <> 0 AndAlso ingr_id(i - 1) = 0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGR_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next

                If source_perc_sum <> 100.0 Then
                    myErrorMessage.AppendTotalSourcePercMsg(error_msg, scr.Parent, source_perc_sum)
                End If

            Case SSMetaRecipe.R4_SecondCleaningAndMilling
                Dim ingr_id(SSMaxNumOfIngredients.C04_SecondCleaningAndMilling) As Integer
                Dim ingr_perc(SSMaxNumOfIngredients.C04_SecondCleaningAndMilling) As Double

                Dim sel_pro_id_f1 As Boolean = False
                Dim sel_pro_id_f2 As Boolean = False
                Dim sel_pro_id_f3 As Boolean = False
                Dim sel_pro_id_bran As Boolean = False

                Dim setpoint_dic As New Dictionary(Of Integer, Double)
                Dim setpoint_value As Double = 0.0

                ' controllo prodotti e ingredienti
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If

                    ElseIf f.FieldDb.StartsWith("INGR_ID_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(2)

                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            ingr_id(cardinality) = 0
                        Else
                            If ingr_id.Contains(CType(ctrl, myDropDownList).SelectedValue) Then
                                myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & cardinality).GetValue() & " " &
                                                                    scr.Parent.GetEntryByKeyName("USED_MORE_THAN_ONCE").GetValue())
                            End If

                            ingr_id(cardinality) = CType(ctrl, myDropDownList).SelectedValue

                            b_at_least_one_source = True
                        End If

                    ElseIf f.FieldDb.StartsWith("INGR_PERC_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)

                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(2)

                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            ingr_perc(cardinality) = 0.0
                        Else
                            value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            ingr_perc(cardinality) = value

                            source_perc_sum += value

                            If value < 0.0 OrElse value > 100.0 Then
                                myErrorMessage.AppendPercentageOutLimitsMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If

                    ElseIf f.FieldDb.Equals("PROD_ID_F1") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            sel_pro_id_f1 = True
                        End If

                    ElseIf f.FieldDb.Equals("PROD_ID_F2") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            sel_pro_id_f2 = True
                        End If

                    ElseIf f.FieldDb.Equals("PROD_ID_F3") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            sel_pro_id_f3 = True
                        End If

                    ElseIf f.FieldDb.Equals("PROD_ID_BRAN") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myDropDownList).SelectedValue <> String.Empty Then
                            sel_pro_id_bran = True
                        End If

                    ElseIf f.FieldDb.StartsWith("FC_") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        field_split = f.FieldDb.Split("_")
                        cardinality = field_split(1)
                        If ctrl IsNot Nothing Then
                            setpoint_value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)
                            If setpoint_value <> 0 AndAlso (setpoint_value < SSRecipeParamRange.R4_min_set_point OrElse setpoint_value > SSRecipeParamRange.R4_max_set_point) Then
                                myErrorMessage.AppendValueOutOfBounds(error_msg, scr.Parent, f.FieldName, setpoint_value, SSRecipeParamRange.R4_min_set_point, SSRecipeParamRange.R4_max_set_point)
                            End If
                            setpoint_dic.Add(cardinality, setpoint_value)

                        End If
                    End If

                Next

                ' coerenza prodotto - percentuale
                For i As Integer = 1 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    If ingr_id(i) = 0 AndAlso ingr_perc(i) > 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("NOT_SELECTED_BUT_PERC_GREATER_0").GetValue())
                    ElseIf ingr_id(i) <> 0 AndAlso ingr_perc(i) = 0.0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGREDIENT_" & i).GetValue() & " " &
                                                            scr.Parent.GetEntryByKeyName("PERC_MISSING_VALUE").GetValue())
                    End If
                Next

                ' coerenza setpoint - final product
                If (setpoint_dic.Item(401) <> 0 OrElse setpoint_dic.Item(402) <> 0) AndAlso sel_pro_id_f1 = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_PRODUCT_F1").GetValue() & " " & scr.Parent.GetEntryByKeyName("SETPOINT_LINE_INCOHERENCE").GetValue())
                End If

                If (setpoint_dic.Item(403) <> 0 OrElse setpoint_dic.Item(404) <> 0) AndAlso sel_pro_id_f2 = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_PRODUCT_F2").GetValue() & " " & scr.Parent.GetEntryByKeyName("SETPOINT_LINE_INCOHERENCE").GetValue())
                End If

                If (setpoint_dic.Item(405) <> 0 OrElse setpoint_dic.Item(406) <> 0) AndAlso sel_pro_id_f3 = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SS_PRODUCT_F3").GetValue() & " " & scr.Parent.GetEntryByKeyName("SETPOINT_LINE_INCOHERENCE").GetValue())
                End If

                ' almeno un'origine selezionata
                If Not b_at_least_one_source Then
                    myErrorMessage.AppendAllSourcesPercEmptyMsg(error_msg, scr.Parent)
                End If

                ' il primo ingrediente deve essere compilato
                If ingr_id(1) = 0 Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("FIRST_INGR_NOT_SELECTED").GetValue)
                End If

                ' gli ingredienti devono essere compilati in ordine
                For i As Integer = 2 To SSMaxNumOfIngredients.C04_SecondCleaningAndMilling
                    If ingr_id(i) <> 0 AndAlso ingr_id(i - 1) = 0 Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("INGR_NOT_PROPERLY_FILLED").GetValue)
                    End If
                Next

                ' percentuale deve fare 100
                If source_perc_sum <> 100.0 Then
                    myErrorMessage.AppendTotalSourcePercMsg(error_msg, scr.Parent, source_perc_sum)
                End If

                ' Product line bran deve essere selezionata
                If sel_pro_id_bran = False Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("BYPRODUCT_SELECTION_NEEDED").GetValue)
                End If

            Case Else
                ' nothing to do

                If scr.Name.ToUpper.StartsWith("RECIPE_PARAMS_") Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, "Unhandled RECIPE")
                End If
        End Select

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If

    End Function

    Public Shared Function CtrlDataScreen(ByVal scr As Screen, ByRef error_msg As String, ByVal root As Control) As Boolean
        Dim ctrl As Control
        Dim custom_error_msg As String = String.Empty
        Dim dbl_value As Double = 0.0
        Dim id As Long = m_InvalidId

        Select Case scr.EnumPageNameCode

            Case EnumPageName.SystemUsers

                Dim password As String = String.Empty
                Dim password_confirm As String = String.Empty

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.Equals("USER_NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("PASSWORD") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            password = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("PASSWORD_CONFIRM") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            password_confirm = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                If password <> String.Empty AndAlso password_confirm <> String.Empty Then
                    If password <> password_confirm Then
                        custom_error_msg = scr.Parent.GetEntryByKeyName("PASSWORDS_DONT_MATCH").GetValue
                        myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                    End If
                End If

            Case EnumPageName.SystemGroups
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("GROUP_NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.SystemUsersGroups
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID_USER") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedIndex = 0 Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If

                    End If
                Next

            Case EnumPageName.EquipmentsModels
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("REFERENCE") OrElse f.FieldDb.Equals("DESCRIPTION") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next

            Case EnumPageName.ProcedureDoc, EnumPageName.EquipmentDoc
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("DESCRIPTION") OrElse f.FieldDb.Equals("LINK") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next

            Case EnumPageName.EquipmentsMainData
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") OrElse f.FieldDb.Equals("EQU_ID_MAINT_DATA") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    ElseIf f.FieldDb.Equals("RATIO") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            Double.TryParse(CType(ctrl, myTextBox).Text, dbl_value)

                            If dbl_value < 0 OrElse dbl_value > 1 Then
                                custom_error_msg = scr.Parent.GetEntryByKeyName(f.FieldName).GetValue() & ": " & scr.Parent.GetEntryByKeyName("ERRORRANGE_1_0").GetValue
                                myErrorMessage.AppendCustomErrorMsg(error_msg, custom_error_msg)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.ProductTypes
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("PT_TYPE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next

            Case EnumPageName.Products
                Dim pt_id As Long = m_InvalidId

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("PT_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pt_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id, "PT_ID = " & pt_id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("SHORT_NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If CType(ctrl, myTextBox).Text.Length > PROD_NAME_BIN_SIZE Then
                                myErrorMessage.AppendTextIsTooLong(error_msg, scr.Parent, f.FieldName, PROD_NAME_BIN_SIZE)
                            End If
                        End If
                    ElseIf f.FieldDb.Equals("HECTOLITRIC_WEIGHT") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            dbl_value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)

                            If dbl_value <= 0.0 Then
                                myErrorMessage.AppendValueEqualOrLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.Customers, EnumPageName.Suppliers, EnumPageName.Carriers
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("NAME") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.StockManualCorrReq
                Dim cel_id As Long = m_InvalidId
                Dim pro_id As Long = m_InvalidId

                Dim str_sql As String = String.Empty
                Dim dt As Data.DataTable

                Dim b_bin_not_idle = False

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("CEL_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            cel_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If

                    ElseIf f.FieldDb.Equals("PRO_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            pro_id = Long.Parse(CType(ctrl, myDropDownList).SelectedValue.ToString)
                        End If
                    ElseIf f.FieldDb.Equals("AMOUNT") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            dbl_value = UsersGUI.tools.WebInputDoubleParse(CType(ctrl, myTextBox).Text, f.nDecimal)

                            If dbl_value < 0.0 Then
                                myErrorMessage.AppendValueLessThanZeroMsg(error_msg, scr.Parent, f.FieldName)
                            End If
                        End If
                    End If
                Next

                If cel_id <> m_InvalidId Then

                    ' 1. controllo se la cella è idle
                    ' (equivalente della CDatabaseTools::IsIdle di c++)
                    ' 1.1 controllo su tabella FLOWS
                    str_sql = "SELECT * FROM FLOWS WHERE DISABLED = '0' AND STATUS <> 'OFF' AND (SOURCE_CELL = " & cel_id & " OR DEST_CELL = " & cel_id & ")"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    If dt.Rows.Count > 0 Then
                        b_bin_not_idle = True
                    End If

                    ' 1.2 controllo su tabella FLOW_LOGS
                    str_sql = "SELECT * FROM FLOW_LOGS WHERE MANAGED_FLAG = 0 AND (SOURCE_CELL = " & cel_id & " OR DEST_CELL = " & cel_id & ")"
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                    If dt.Rows.Count > 0 Then
                        b_bin_not_idle = True
                    End If

                    If b_bin_not_idle Then
                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SMC_NOT_POSSIBLE_BIN_NOT_IDLE").GetValue())
                    End If

                    ' 2. non è possibile cambiare prodotto senza prima resettare la cella
                    If pro_id <> m_InvalidId Then
                        str_sql = "SELECT PRO_ID FROM CELLS WHERE ID = " & cel_id
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

                        If dt.Rows.Count > 0 Then

                            For Each dr As Data.DataRow In dt.Rows
                                If Not IsDBNull(dr.Item(0)) Then
                                    If pro_id <> Integer.Parse(dr.Item(0).ToString) Then
                                        myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("SMC_NOT_POSSIBLE_BIN_WITH_DIFFERENT_PRODUCT").GetValue())
                                    End If
                                End If
                            Next

                        End If
                    End If
                End If

            Case EnumPageName.Recipes
                Dim mtr_id As Long = m_InvalidId

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("MTR_ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl.Visible Then
                            If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                                myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                            Else
                                mtr_id = Long.Parse(CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("DESCRIPTION") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            If UsersGUI.tools.IsValueAlreadyPresentInTable(scr.DBName, f.FieldDb, CType(ctrl, myTextBox).Text, id, "MTR_ID = " & mtr_id) Then
                                myErrorMessage.AppendValueAlreadyPresent(error_msg, scr.Parent, f.FieldName, CType(ctrl, myTextBox).Text)
                            End If
                        End If
                    End If
                Next

            Case EnumPageName.PoNumbers

                'Dim po_number_erp As String = String.Empty
                Dim status_po_number As Integer = m_InvalidId
                Dim id_po_number As String = String.Empty

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") AndAlso f.IsHidden = True Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing Then
                            id_po_number = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("NUMBER") AndAlso f.IsHidden = False Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            'po_number_erp = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("ID_STATUS") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myDropDownList).SelectedValue = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            status_po_number = Integer.Parse(CType(ctrl, myDropDownList).SelectedValue)
                        End If
                    End If
                Next

                Dim max_digit As Integer = Integer.Parse(GetParameter("MAX_PO_NUMBER_DIGIT"))
                If (id_po_number.Length > max_digit) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, String.Format(scr.Parent.GetEntryByKeyName("PO_NUMBER_MAX_LENGTH").GetValue(), max_digit))
                End If

                If (id_po_number <> String.Empty) Then
                    Dim s_actual_status As Integer = WebTools.tools.GetPoNumberStatus(id_po_number)
                    If (s_actual_status > EnumStatusPONumbers.NeverUsed) Then
                        'Se sono qui vuol dire che il sistema ha già cambiato lo stato INSERT del PO_NUMBER
                        'può essere stato fatto in automatico dal sistema perchè presente almeno un flusso con questo PO_NUMBER
                        'oppure può essere stato messo manualmente dall'operatore
                        If (WebTools.tools.PONumberAlreadyUsedInFlows(id_po_number) AndAlso status_po_number = EnumStatusPONumbers.NeverUsed) Then
                            'Ho già almeno un flusso con il PO_NUMBER, non posso metterlo allo stato di INSERT (status_po_number)
                            myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("NO_SET_NEVERUSED_PO_NUMBER").GetValue())
                        End If
                    End If
                End If
            Case EnumPageName.ScalesBatchSize
                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            id = m_InvalidId
                        Else
                            id = CType(ctrl, myTextBox).Text
                        End If
                    ElseIf f.FieldDb.Equals("BATCH_SIZE") Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        End If
                    End If
                Next
            Case EnumPageName.BatchNumber
                Dim batch_number As String = String.Empty
                Dim id_batch_number As Long = 0

                For Each f As Field In scr.EditFields
                    If f.FieldDb.Equals("ID") AndAlso f.IsHidden = True Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl IsNot Nothing AndAlso CType(ctrl, myTextBox).Text <> String.Empty Then
                            id_batch_number = Long.Parse(CType(ctrl, myTextBox).Text)
                        End If
                    ElseIf f.FieldDb.Equals("BATCH_NUMBER") AndAlso f.IsHidden = False Then
                        ctrl = UsersGUI.tools.FindControlRecursive(root, f.FieldDb)
                        If ctrl Is Nothing OrElse CType(ctrl, myTextBox).Text = String.Empty Then
                            myErrorMessage.AppendRequiredFieldMsg(error_msg, scr.Parent, f.FieldName)
                        Else
                            batch_number = CType(ctrl, myTextBox).Text
                        End If
                    End If
                Next

                If (batch_number.Length > MAX_DIGIT_BATCH_NUMBER) Then
                    myErrorMessage.AppendCustomErrorMsg(error_msg, scr.Parent.GetEntryByKeyName("BATCH_NUMBER_MAX_LENGTH").GetValue())
                End If
        End Select

        ' chiamo la funzione per eventuali controlli custom sugli screen non di ciclo e non di ricetta
        CtrlDataScreenCustom(scr, error_msg, root)

        If error_msg.Length > 0 Then
            ' ho almeno un errore
            Return False
        Else
            Return True
        End If
    End Function

    Private Shared Sub CtrlDataScreenCustom(ByVal scr As Screen, ByRef m_Error As String, ByVal root As Control)
        Dim ctrl As Control = Nothing
        Dim custom_error_msg As String = String.Empty
        Dim id As Long = m_InvalidId
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        Select Case scr.EnumPageNameCode

        End Select

    End Sub

    Public Shared Sub CtrlDeleteCustom(ByVal scr As Screen, ByRef m_Error As String, ByVal id As Integer)
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        Select Case scr.EnumPageNameCode
            ' nothing to do
        End Select

    End Sub

    Public Shared Sub SetDefaultDataCycle(ByVal scr As Screen, ByVal root As Control)
        Dim ctrl As Control = Nothing

        Select Case scr.Name.ToUpper

        End Select
    End Sub

    Public Shared Sub SetDefaultDataScreen(ByVal scr As Screen, ByVal root As Control)
        Dim ctrl As Control = Nothing

        Select Case scr.EnumPageNameCode

        End Select
    End Sub

    Public Shared Function WaitCompleted(ByVal scr As Screen, Optional ByVal wait_operation As EnumWaitOperation = EnumWaitOperation.Submit) As Boolean
        Dim bOk As Boolean = False
        Dim temp_string As String

        Select Case wait_operation
            Case EnumWaitOperation.Submit
                Select Case scr.EnumPageNameCode
                    Case EnumPageName.StockManualCorrReq
                        WebTools.tools.SetParameter("STOCK_MANUAL_CORR_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("STOCK_MANUAL_CORR_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ParcelsToBeConfirmed
                        WebTools.tools.SetParameter("PARCEL_TO_BE_CONFIRMED_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("PARCEL_TO_BE_CONFIRMED_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ShipmentsToBeConfirmed
                        WebTools.tools.SetParameter("SHIPMENT_TO_BE_CONFIRMED_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("SHIPMENT_TO_BE_CONFIRMED_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ScalesBatchSize
                        WebTools.tools.SetParameter("SCALES_BATCH_SIZE_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("SCALES_BATCH_SIZE_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case Else
                        bOk = True
                End Select

            Case EnumWaitOperation.Upload
                Select Case scr.EnumPageNameCode
                    Case EnumPageName.RecipeParameters
                        WebTools.tools.SetParameter("RECIPE_UPLOAD_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("RECIPE_UPLOAD_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While

                    Case EnumPageName.ScalesBatchSize
                        WebTools.tools.SetParameter("REFRESH_SCALES_BATCH_SIZE_PROCESSED", m_StringNo)

                        While Not bOk
                            temp_string = WebTools.tools.GetParameter("REFRESH_SCALES_BATCH_SIZE_PROCESSED")

                            If temp_string = m_StringYes Then
                                bOk = True
                            End If

                        End While
                    Case Else
                        bOk = True
                End Select

            Case Else
                bOk = True
        End Select

        Return bOk
    End Function

    Private Shared Sub AutomaticNotesFill(ByVal root As Control, ByVal dest_list As List(Of Long))
        Dim m_control As System.Web.UI.Control = Nothing
        Dim m_control_source_bin As System.Web.UI.Control = Nothing
        Dim txt As myTextBox
        Dim cyc_id As Integer
        Dim perc As Double
        Dim dr_index As Integer
        Dim sSelect As String
        Dim dt As Data.DataTable
        Dim b_source_found = False
        Dim number_of_sources As Integer = 0

        Dim source_bin As String = String.Empty
        Dim source_str As String = String.Empty
        Dim dest_str As String = String.Empty

        txt = UsersGUI.tools.FindControlRecursive(root, "AUTO_NOTES")

        If txt IsNot Nothing Then

            m_control = UsersGUI.tools.FindControlRecursive(root, "CYC_ID")
            If m_control IsNot Nothing AndAlso CType(m_control, myTextBox).Text <> String.Empty Then
                cyc_id = CType(m_control, myTextBox).Text
            Else
                Exit Sub
            End If

            ' recupero il numero di origini
            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each dr As Data.DataRow In dt.Rows
                number_of_sources += 1
            Next

            Dim i As Integer = 1

            While i <= number_of_sources

                m_control = UsersGUI.tools.FindControlRecursive(root, "SOURCE_PERC_" & i)

                If m_control IsNot Nothing AndAlso CType(m_control, myTextBox).Text <> String.Empty Then
                    perc = UsersGUI.tools.WebInputDoubleParse(CType(m_control, myTextBox).Text, 1)
                Else
                    m_control = UsersGUI.tools.FindControlRecursive(root, "SOURCE_ENABLE_" & i)

                    If m_control IsNot Nothing AndAlso CType(m_control, myCheckBox).Checked = True Then
                        perc = 100.0
                    Else
                        perc = 0.0
                    End If
                End If

                If perc > 0.0 Then

                    b_source_found = True

                    ' cerco il source bin associato (se esiste)
                    m_control_source_bin = UsersGUI.tools.FindControlRecursive(root, "SOURCE_BIN_" & i)

                    If m_control_source_bin IsNot Nothing Then
                        ' esiste il source bin associato
                        source_bin = CType(m_control_source_bin, myDropDownList).SelectedValue

                        sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id & " AND CEL_ID = " & source_bin
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                        For Each dr As Data.DataRow In dt.Rows
                            If source_str <> String.Empty Then
                                source_str &= " "
                            End If
                            source_str &= dr("DESCRIPTION").ToString
                        Next
                    Else
                        ' non esiste il source bin associato, cerco l'iesima cella tra le disponibili nella VIEW_CYCLES_TO_BINS
                        sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id
                        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                        dr_index = 1

                        For Each dr As Data.DataRow In dt.Rows
                            If dr_index = i Then
                                If source_str <> String.Empty Then
                                    source_str &= " "
                                End If
                                source_str &= dr("DESCRIPTION").ToString
                                Exit For
                            Else
                                dr_index += 1
                            End If
                        Next
                    End If

                End If

                i += 1
            End While

            If Not b_source_found Then

                ' cerco il source bin associato (se esiste)
                m_control_source_bin = UsersGUI.tools.FindControlRecursive(root, "SOURCE_BIN")

                If m_control_source_bin IsNot Nothing Then
                    ' esiste il source bin associato
                    source_bin = CType(m_control_source_bin, myDropDownList).SelectedValue

                    sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id & " AND CEL_ID = " & source_bin
                    dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                    For Each dr As Data.DataRow In dt.Rows
                        If source_str <> String.Empty Then
                            source_str &= " "
                        End If
                        source_str &= dr("DESCRIPTION").ToString
                    Next

                End If

            End If

            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_DESTINATION='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each dest As Long In dest_list

                For Each dr As Data.DataRow In dt.Rows
                    If dr("CEL_ID") = dest Then
                        If dest_str <> String.Empty Then
                            dest_str &= " "
                        End If
                        dest_str &= dr("DESCRIPTION").ToString
                        Exit For

                    End If
                Next
            Next

            ' compongo la stringa
            txt.Text = source_str

            If dest_str.Length > 0 Then
                txt.Text &= " -> " & dest_str
            End If
        End If

    End Sub

    Private Shared Sub AutomaticNotesFill(ByVal root As Control, ByVal source_list As List(Of Long), ByVal dest_list As List(Of Long))
        Dim m_control As System.Web.UI.Control = Nothing
        Dim txt As myTextBox
        Dim cyc_id As Integer
        Dim sSelect As String
        Dim dt As Data.DataTable
        Dim number_of_ingredients As Integer = 0

        Dim source_str As String = String.Empty
        Dim dest_str As String = String.Empty

        txt = UsersGUI.tools.FindControlRecursive(root, "AUTO_NOTES")

        If txt IsNot Nothing Then

            m_control = UsersGUI.tools.FindControlRecursive(root, "CYC_ID")
            If m_control IsNot Nothing AndAlso CType(m_control, myTextBox).Text <> String.Empty Then
                cyc_id = CType(m_control, myTextBox).Text
            Else
                Exit Sub
            End If

            ' origini
            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_SOURCE='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each source As Long In source_list

                For Each dr As Data.DataRow In dt.Rows
                    If dr("CEL_ID") = source Then
                        If source_str <> String.Empty Then
                            source_str &= " "
                        End If
                        source_str &= dr("DESCRIPTION").ToString
                        Exit For

                    End If
                Next
            Next

            ' destinazioni
            sSelect = "SELECT CEL_ID, DESCRIPTION FROM VIEW_CYCLES_TO_BINS WHERE IS_DESTINATION='YES' AND CYC_ID = " & cyc_id
            dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

            For Each dest As Long In dest_list

                For Each dr As Data.DataRow In dt.Rows
                    If dr("CEL_ID") = dest Then
                        If dest_str <> String.Empty Then
                            dest_str &= " "
                        End If
                        dest_str &= dr("DESCRIPTION").ToString
                        Exit For

                    End If
                Next
            Next

            ' compongo la stringa
            txt.Text = source_str

            If dest_str.Length > 0 Then
                txt.Text &= " -> " & dest_str
            End If
        End If

    End Sub

    Public Shared Sub ExportDataToERP(ByVal scr As Screen, ByVal pagename As String, ByVal action As SSBorderDatabaseAction, ByVal id As Long)
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        Select Case scr.EnumPageNameCode
            Case Else
                ' nothing to do

        End Select

    End Sub

    ' permette di forzare valori nel job appena clonato
    Public Shared Sub ForceOrderParamValuesOnClonedCycle(ByVal new_ppl_id As Long)
        Dim str_sql As String = String.Empty
        Dim dt As Data.DataTable = Nothing
        Dim cycle_id As Integer = m_InvalidId

        str_sql = "SELECT CYC_ID FROM PRODUCTION_PLAN WHERE ID = " & new_ppl_id

        dt = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(str_sql, False)

        If dt.Rows.Count > 0 Then
            For Each dr As Data.DataRow In dt.Rows
                cycle_id = dr(0).ToString
            Next
        End If

        Select Case cycle_id

            Case Else
                ' nothing to do
        End Select

    End Sub

    Public Shared Sub DestinationCheck(scr As Screen, dest_bin As Long, pro_id As Long, list_source As Generic.List(Of Long), ByRef error_msg As String)
        Dim s_error As String = String.Empty

        ' 1.2.a Controllo di inquinamento prodotto
        CheckDestBinProduct(scr, dest_bin, pro_id, s_error)

        ' 1.2.b Controllo di inquinamento lotti
        If ((list_source Is Nothing) OrElse (Not list_source.Contains(dest_bin))) Then
            CheckBinLots(scr, dest_bin, s_error)
        End If

        ' 1.2.c Load exclusion
        CheckBinLoadStatus(scr, dest_bin, s_error)

        If (s_error <> String.Empty) Then
            If (error_msg <> String.Empty) Then
                error_msg &= "<br><hr>"
            End If

            error_msg &= scr.Parent.GetEntryByKeyName("Destination bin").GetValue() & " " & WebTools.tools.GetBinNameFromId(dest_bin) & ":<br>"

            error_msg &= s_error
        End If
    End Sub

    Public Shared Sub SourceCheck(scr As Screen, source_bin As Long, pro_id As Long, ByRef error_msg As String)
        Dim s_error As String = String.Empty

        ' Controlli di inquinamento
        ' 1.a Prodotto
        If pro_id <> m_InvalidId Then
            CheckSourceBinProduct(scr, source_bin, pro_id, s_error)
        End If

        ' 1.b Download exclusion
        CheckBinDownloadStatus(scr, source_bin, s_error)

        If (s_error <> String.Empty) Then
            If (error_msg <> String.Empty) Then
                error_msg &= "<br><hr>"
            End If

            error_msg &= scr.Parent.GetEntryByKeyName("Source bin").GetValue() & " " & WebTools.tools.GetBinNameFromId(source_bin) & ":<br>"

            error_msg &= s_error
        End If
    End Sub

    Public Shared Function IsSpecificControlled(cycle_id As Integer) As Boolean
        Dim ret_val As Boolean = False

        Select Case cycle_id
            Case SSCycles.C01_Intake
                ret_val = True
            Case SSCycles.C02_FirstCleaning
                ret_val = False
            Case SSCycles.C03_SecondTempering
                ret_val = False
            Case SSCycles.C04_SecondCleaningAndMilling
                ret_val = False
            Case SSCycles.C06_FlourTransportRV501
                ret_val = False
            Case SSCycles.C07_FlourTransportRV502
                ret_val = False
            Case SSCycles.C08_FlourBaggingBS501
                ret_val = True
            Case SSCycles.C09_FlourBaggingBS502
                ret_val = True
            Case SSCycles.C10_BranRecycle
                ret_val = False
            Case SSCycles.C11_FlourBaggingBS601
                ret_val = True
            Case SSCycles.C12_ScreeningsGrinding
                ret_val = True
            Case SSCycles.C13_ScreeningsTransport
                ret_val = True
            Case SSCycles.C14_FlourBaggingBS503
                ret_val = True

        End Select

        Return ret_val
    End Function

    Public Shared Function IsSpecificPlanned(cycle_id As Integer) As Boolean
        Dim ret_val As Boolean = False

        Select Case cycle_id

            Case SSCycles.C01_Intake
                ret_val = False
            Case SSCycles.C02_FirstCleaning
                ret_val = True
            Case SSCycles.C03_SecondTempering
                ret_val = True
            Case SSCycles.C04_SecondCleaningAndMilling
                ret_val = True
            Case SSCycles.C06_FlourTransportRV501
                ret_val = True
            Case SSCycles.C07_FlourTransportRV502
                ret_val = True
            Case SSCycles.C08_FlourBaggingBS501
                ret_val = False
            Case SSCycles.C09_FlourBaggingBS502
                ret_val = False
            Case SSCycles.C10_BranRecycle
                ret_val = True
            Case SSCycles.C11_FlourBaggingBS601
                ret_val = False
            Case SSCycles.C12_ScreeningsGrinding
                ret_val = False
            Case SSCycles.C13_ScreeningsTransport
                ret_val = False
            Case SSCycles.C14_FlourBaggingBS503
                ret_val = False

        End Select

        Return ret_val
    End Function

    Public Class AutoSourceRecipeData

        Public Enum TypeAutoSource
            Invalid = 0
            Macro = 1
            Micro = 2
        End Enum

        Public SourceBinId As Long = 0
        Public Ingredient As Integer = 0
        Public Repeat As Integer = 0
        Public Type As TypeAutoSource = TypeAutoSource.Invalid
    End Class

End Class