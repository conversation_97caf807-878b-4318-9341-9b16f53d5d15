﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="ProductionReports.aspx.vb" Inherits="_ProductionReports" %>

<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>
<%@ Register Src="~/UserControl/WUC_Edit.ascx" TagPrefix="WUCEdit" TagName="eEdit" %>
<%@ Register Src="~/UserControl/WUC_Operation.ascx" TagPrefix="WUCOperation" TagName="eOperation" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>
        <%= System.Configuration.ConfigurationManager.AppSettings("SiteName").ToString%>
    </title>
</head>
<body onload="InitPage();">
    <form id="form_ProductionReports" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true" EnableScriptLocalization="true">
        </asp:ScriptManager>
        <div class="navbarContainer">
            <WUCLoginTop:LoginTop ID="Login" runat="server" />
            <div id="topMenuContainer">
                <div id="dhtmlgoodies_menu">
                    <WUCTopMenu:TopMenu ID="mainTopMenu" runat="server" />
                </div>
            </div>
        </div>
        <div class="mainContainer">
            <div id="UCCont" class="mainSection">
                <div id="dError" runat="server" visible="false">
                    <table id="Table1" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
                <div id="dView" runat="server" visible="True">
                    <table>
                        <tr>
                            <td class="txtCenter">
                                <iframe id="IFrameReport" width="100%" height="100%" runat="server" marginwidth="0"
                                    marginheight="0" name="FrameReport" frameborder="0"></iframe>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div id="wucCont" class="rightSection">
                <!-- Operazioni.... inizio -->
                <div id="DivOperation" style="display: none;">
                    <asp:HiddenField ID="HiddenDeleteAll" runat="server" />
                    <div class="menuTOP">
                        <div class="txtCenter txtWhite txtBold">
                            <%= m_config.GetEntryByKeyName("Operations").GetValue%>
                        </div>
                    </div>
                    <div id="DivElements" class="menuMID midOperation">
                        <table id="tblOperation" runat="server">
                        </table>
                    </div>
                </div>
                <!-- Operazioni.... fine-->

                <!-- Avvisi di processo.... inizio -->
                <WUCWarning:Warning ID="Warning" runat="server" />
                <!-- Avvisi di processo.... fine -->
            </div>
        </div>
    </form>
</body>
</html>