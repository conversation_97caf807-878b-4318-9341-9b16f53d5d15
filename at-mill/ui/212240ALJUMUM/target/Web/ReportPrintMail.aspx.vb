﻿Imports UsersGUI

Partial Class ReportPrintMail
    Inherits myWebPage

    Private m_req As String
    Private m_config As config

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim base_url As String = String.Empty
        Dim new_url As String = String.Empty
        Dim rec_list As String = String.Empty
        Dim file_name As String = String.Empty

        m_config = CType(Current.Application("Config"), UsersGUI.config)

        If Current.Request("REQ") Is Nothing Then
            Return
        End If

        m_req = Current.Request("REQ").ToString

        ' ricostruisco l'url del report
        base_url = Current.Request().Url.ToString
        base_url = base_url.Replace("ReportPrintMail.aspx", "ReportYields.aspx")
        base_url = base_url.Substring(0, base_url.IndexOf("?"))

        ' build file name (nella forma "@mill #job report.pdf")
        file_name = "@mill "
        file_name &= m_config.GetCommessa.ToString() & " "
        file_name &= "report.pdf"

        Select Case m_req
            Case "print" ' obsoleto
                Dim objNewValueCollection As NameValueCollection = HttpUtility.ParseQueryString(Request.QueryString.ToString())
                objNewValueCollection.Remove("REQ")

                new_url = base_url + "?" + objNewValueCollection.ToString() + "&disabletimer=YES"

                PostPrintRequest(new_url, file_name)

            Case "mail"
                If Current.Request("REC_LIST") IsNot Nothing Then
                    rec_list = Current.Request("REC_LIST").ToString

                    Dim objNewValueCollection As NameValueCollection = HttpUtility.ParseQueryString(Request.QueryString.ToString())
                    objNewValueCollection.Remove("REQ")
                    objNewValueCollection.Remove("REC_LIST")

                    new_url = base_url + "?" + objNewValueCollection.ToString() + "&disabletimer=YES"

                    PostMailRequest(new_url, file_name, rec_list)
                End If
        End Select
    End Sub

    Private Sub PostPrintRequest(ByVal url As String, ByVal file_name As String)

        Dim req As New CommandInterface(EnumRequest.CreateReportForPrintDistribution, url, file_name)
        req.PostUniqueRequest()

    End Sub

    Private Sub PostMailRequest(ByVal url As String, ByVal file_name As String, ByVal rec_list As String)

        Dim req As New CommandInterface(EnumRequest.CreateReportForMailDistribution, url, file_name, rec_list)
        req.PostUniqueRequest()

    End Sub

End Class