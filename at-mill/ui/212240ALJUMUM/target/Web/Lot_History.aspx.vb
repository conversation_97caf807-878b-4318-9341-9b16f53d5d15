﻿Option Strict On

Imports UsersGUI

Partial Class lots_Lot_History
    Inherits myWebPage

    Public m_config As config
    Private mMenuName As String
    Public mPageName As String
    Private mScreen As Screen
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Private mSessionId As String

    Public lot_id As Long
    Private url As String

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        If Current.Request.QueryString("session") IsNot Nothing Then
            mSessionId = Current.Request.QueryString("session")

            m_config = CType(Application("Config"), config)

            If Not WebTools.tools.GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
                lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                myScript.InvokeJS(Me.Page, "myLoader.stop();")
                myScript.InvokeJS(Me.Page, "ShowUCCont();")
                Exit Sub
            End If

            If Current.Request("LO_ID") <> "" Then
                lot_id = Long.Parse(Current.Request("LO_ID"))
            ElseIf Current.Request("ID") <> "" Then
                lot_id = Long.Parse(Current.Request("ID"))
            ElseIf Current.Request("DESCRIPTOR") <> "" Then
                lot_id = myScript.GetLotIdFromDescriptor(Current.Request("DESCRIPTOR"))
            End If

            If Not Page.IsPostBack Then
                Me.WUClotsMenu.Visible = True

                Current.Session.Remove(mSessionId & "_reloadIFrameDetail_lotId")
                Current.Session.Remove(mSessionId & "_reloadIFrameDetail_composizione")
                Current.Session.Remove(mSessionId & "_reloadIFrameDetail_lotMode")

                IFrameLotDetail.Attributes.Add("src", myScript.AppendSessionIdToUrl("lotHistoryDetail.aspx?Id=" & lot_id & "&Comp=false"))
            Else
                If Current.Session(mSessionId & "_reloadWucLotsMenu") IsNot Nothing Then
                    Me.WUClotsMenu.Ricerca(m_InvalidId, True)
                    Current.Session.Remove(mSessionId & "_reloadWucLotsMenu")
                End If
            End If
        End If

        myScript.InvokeJS(Me.Page, "ShowUCCont();")
    End Sub

    Public Sub ReloadIFrame(ByVal lotId As Long, ByVal composizione As Boolean, ByVal lotMode As LotMode)
        IFrameLotDetail.Attributes("src") = myScript.AppendSessionIdToUrl(myFunction.GetRoot() & "lotHistoryDetail.aspx?Id=" & lotId & "&Comp=" & composizione.ToString.ToLower & "&lotmode=" & lotMode)

        UpdatePanelIFrame.Update()
    End Sub

    Protected Sub WUClotsMenu_UpdateIFrame() Handles WUClotsMenu.UpdateIFrame
        ReloadIFrame(
            Long.Parse(Current.Session(mSessionId & "_reloadIFrameDetail_lotId").ToString),
            Boolean.Parse(Current.Session(mSessionId & "_reloadIFrameDetail_composizione").ToString),
            CType(Current.Session(mSessionId & "_reloadIFrameDetail_lotMode"), LotMode)
        )
    End Sub

    Protected Sub Timer1_Tick(sender As Object, e As EventArgs)
        If Current.Session(mSessionId & "_reloadWucLotsMenu") IsNot Nothing Then
            Me.WUClotsMenu.Ricerca(lot_id, True)
            Current.Session.Remove(mSessionId & "_reloadWucLotsMenu")
        End If
    End Sub

End Class