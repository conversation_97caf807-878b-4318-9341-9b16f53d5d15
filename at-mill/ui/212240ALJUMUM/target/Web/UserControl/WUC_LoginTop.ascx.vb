﻿Imports UsersGUI
Imports WebDataBaseLayer

Partial Class WUC_LoginTop
    Inherits UserControl

    Private msgGeneric As New myAlert

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' message init
        msgGeneric.Init(Me.Page, myMessageBoxParam.NoParameter)

        If Current.Request.QueryString("logout") IsNot Nothing Then
            If Current.Request.QueryString("logout").ToUpper = m_StringYes Then
                ' Clears the values in the server session
                Current.Session.Clear()

                Current.Session("UserName") = Nothing
                Current.Session("UserID") = Nothing

                Dim page_name_s As String = costanti.a_home

                page_name_s = myScript.AppendSessionIdToUrl(page_name_s)

                If Current.Request.QueryString("pagename") IsNot Nothing Then
                    page_name_s &= "&pagename=" & Current.Request.QueryString("pagename").ToString
                End If

                Current.Response.Redirect(page_name_s)
            End If
        End If

        If Current.Session("UserName") IsNot Nothing Then
            If Current.Session("UserName").ToString <> String.Empty Then
                Me.Div_NoLoginTop.Visible = False
                Me.Div_YesLoginTop.Visible = True
                Dim sSelect As String = "SELECT * FROM SYSTEM_USERS WHERE ID='" & Current.Session("UserID").ToString & "'"
                Dim dt As Data.DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)
                For Each dr As Data.DataRow In dt.Rows
                    Me.lblLoginUser.Text = "(" & dr.Item("USER_NAME").ToString & ")"
                Next
            End If
        End If
    End Sub

    Protected Sub btnLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnLogin.Click
        Dim sSelect As String = "SELECT * FROM SYSTEM_USERS WHERE USER_NAME='" &
            Me.txtUserName.Text & "' AND PASSWORD='" & Me.txtPassword.Text & "'"

        Dim dt As Data.DataTable = DataBase.ExecuteSQL_DataTable(sSelect, False)

        For Each dr As Data.DataRow In dt.Rows
            Current.Session("UserName") = dr.Item("USER_NAME").ToString
            Current.Session("UserID") = dr.Item("ID").ToString

            ' redirect sulla pagina dalla quale ho fato login
            myScript.InvokeJS(Me.Page, "DoneEvents.Login();")
            Exit Sub
        Next

        ' Se passo qui ho errore di login
        msgGeneric.Show("Incorrect user name or password")
    End Sub

End Class