﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_LoginTop.ascx.vb"
    Inherits="WUC_LoginTop" %>

<link rel="stylesheet" type="text/css" href="<%= "./css/default.css" & myScript.GetQueryClientUpdate() %>" />
<link rel="stylesheet" type="text/css" href="<%= "./css/print.css" & myScript.GetQueryClientUpdate() %>" media="print" />
<link rel="stylesheet" type="text/css" href="<%= "./css/TopMenu.css" & myScript.GetQueryClientUpdate() %>" />
<link rel="stylesheet" type="text/css" href="./css/jquery.datetimepicker.css" />

<script type="text/javascript" src="./js/jquery-1.9.1.js"></script>

<script type="text/javascript" src="<%= "./js/common/constants.js" & myScript.GetQueryClientUpdate() %>"></script>

<script type="text/javascript" src="<%= "./js/header/constants.js" & myScript.GetQueryClientUpdate() %>"></script>
<script type="text/javascript" src="<%= "./js/header/config.js" & myScript.GetQueryClientUpdate() %>"></script>
<script type="text/javascript" src="<%= "./js/header/index.js" & myScript.GetQueryClientUpdate() %>"></script>

<script type="text/javascript" src="<%= "./js/myScript.js" & myScript.GetQueryClientUpdate() %>"></script>
<script type="text/javascript" src="<%= "./js/myHistory.js" & myScript.GetQueryClientUpdate() %>"></script>
<script type="text/javascript" src="<%= "./js/loader.js" & myScript.GetQueryClientUpdate() %>"></script>
<script type="text/javascript" src="<%= "./js/EventInterface.js" & myScript.GetQueryClientUpdate() %>"></script>
<script type="text/javascript" src="./js/jquery.datetimepicker.full.js"></script>

<div id="loginTopCont" class="ui-widget-content">
    <table class="menuWucTOP">
        <tr>
            <td>
                <a href="./default.aspx" class="ocrimLink">
                    <img src="./image/OcrimLogo.png" />
                </a>
            </td>
            <td class="tdTime">
                <iframe class="iframeTime" src="Time.aspx" frameborder="0" scrolling="no" visible="true"></iframe>
            </td>
            <td class="tdLogin">
                <div id="Div_NoLoginTop" class="Div_LoginTop" runat="server" visible="true">
                    <label class="textLogin" for="Login_txtUserName">Username:</label>
                    <asp:TextBox ID="txtUserName" runat="server" CssClass="textbox-text valueLogin"></asp:TextBox>
                    <label class="textLogin" for="Login_txtPassword">Password:</label>
                    <asp:TextBox ID="txtPassword" runat="server" CssClass="textbox-text valueLogin" TextMode="Password"> </asp:TextBox>
                    <asp:Button ID="btnLogin" runat="server" Text="Login" CssClass="btnLogin valueLogin" />
                </div>
                <div id="Div_YesLoginTop" class="Div_LoginTop" runat="server" visible="false">
                    <div class="loggedUser">
                        <label class="textLogin">Username:&nbsp;</label>
                        <asp:Label ID="lblLoginUser" CssClass="valueLogin" runat="server" />
                    </div>
                    <a id="IdLogoutTop" href="./Default.aspx?logout=yes" class="lblError valueLogin">logout</a>
                </div>
            </td>
        </tr>
    </table>
</div>