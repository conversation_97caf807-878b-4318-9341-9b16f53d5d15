﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_StatusFull.ascx.vb"
    Inherits="UserControl_WUC_StatusFull" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>

<div>
    <table class="tabMrg3">
        <tr>
            <td class="bgImg2">&nbsp;
            </td>
            <td class="tabBground1">&nbsp;
            </td>
        </tr>
        <tr>
            <td class="txtBold" colspan="2">
                <%= m_config.GetEntryByKeyName("MAIN_SYSTEM_INFORMATIONS_TITLE").GetValue()%>
            </td>
        </tr>
    </table>
    <table class="tabMrg3 tabMrg3Top">
        <tr>
            <td>
                <div id="dView" runat="server" visible="true">
                    <div id="divLanguage" runat="server">
                        <table id="tblLanguageTitle" class="txtBold bgCol1 txtCenter" runat="server">
                        </table>
                        <table id="tblLanguage" class="tblLanguage" runat="server">
                        </table>
                    </div>
                    <br />
                    <div>
                        <table id="tblTimeTitle" class="txtBold bgCol1 txtCenter" runat="server">
                        </table>
                        <table id="tblTime" class="tblTime" runat="server">
                        </table>
                        <br />
                        <table class="txtCenter">
                            <tr>
                                <td>
                                    <asp:Button ID="btnEnableCPU" runat="server" CssClass="btnGeneral" />&nbsp;
                                </td>
                            </tr>
                        </table>
                    </div>

                    <br />
                    <div id="dForceSyncBrdhnd" runat="server" visible="false">
                        <table id="tblForceSyncBrdhnd" class="txtBold bgCol1 txtCenter" runat="server">
                        </table>
                        <br />
                        <table class="txtCenter">
                            <tr>
                                <td>
                                    <asp:Button ID="btnForceSyncBrdhnd" runat="server" CssClass="btnGeneral" />&nbsp;
                                </td>
                            </tr>
                        </table>
                    </div>
                    <br />
                    <div id="dSystemUtilities" runat="server" visible="false">
                        <table id="tblSystemUtilitiesTitle" class="txtBold bgCol1 txtCenter" runat="server">
                        </table>
                        <br />
                        <table id="tblReloadXmlTitle" class="txtBold bgCol1 txtCenter" runat="server">
                        </table>
                        <br />
                        <table class="txtCenter">
                            <tr>
                                <td>
                                    <asp:Button ID="btnReloadXml" runat="server" CssClass="btnGeneral" />&nbsp;
                                </td>
                            </tr>
                        </table>
                        <br />
                        <table id="tblForceClientUpdate" class="txtBold bgCol1 txtCenter" runat="server">
                        </table>
                        <br />
                        <table class="txtCenter">
                            <tr>
                                <td>
                                    <asp:Label runat="server" ID="lblLastClientUpdate" CssClass="textCenter"></asp:Label>
                                </td>
                            </tr>
                        </table>
                        <br />
                        <table class="txtCenter">
                            <tr>
                                <td>
                                    <asp:Button ID="btnReloadClientResources" runat="server" CssClass="btnGeneral" />&nbsp;
                                </td>
                            </tr>
                        </table>
                        <br />
                        <table id="tblVersionInfo" class="txtBold bgCol1 txtCenter" runat="server">
                        </table>
                        <table style="width:100%;">
                             <tr>
                                <td class="tbVersionInfo txtRight"><strong>Web SVN Revision:</strong></td>
                                <td class="tbVersionInfo txtLeft"><%=WebTools.tools.GetParameter("WEB_SVN_REVISION") %></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div id="dError" runat="server" visible="false">
                    <table id="Table2" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</div>