﻿Imports UsersGUI
Imports WebDataBaseLayer

Partial Class WUC_Lots_menu
    Inherits UserControl

    Private m_LotIdPrincipale As Long
    Private mLotti As Generic.List(Of Lot)
    Private nDepth As Integer = 0
    Public m_config As config = Nothing

    Private sessionId As String
    Private showUserNotesIndicatorDesc As Boolean = False

    Public Event UpdateIFrame()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Current.Request.QueryString("session") IsNot Nothing Then
            sessionId = Current.Request.QueryString("session")

            m_config = CType(Current.Application("Config"), config)

            If m_config Is Nothing Then
                Exit Sub
            End If

            Me.rbRinTracciabilita.Text = m_config.GetEntryByKeyName("Rintracciabilità").GetValue
            Me.rbTracciabilita.Text = m_config.GetEntryByKeyName("Tracciabilità").GetValue
            Me.cbComposizione.Text = m_config.GetEntryByKeyName("SoloComposizione").GetValue & ":"
            Me.cbPrintTree.Text = m_config.GetEntryByKeyName("ALSO_PRINT_TREE").GetValue & ":"
            Me.cbCompTree.Text = m_config.GetEntryByKeyName("SHOW_COMPLETE_TREE").GetValue & ":"
            Me.btnEsegui.Text = m_config.GetEntryByKeyName("Esegui").GetValue

            If Not Page.IsPostBack Then
                Current.Session.Remove(sessionId & "_reloadWucLotsMenu")

                If Current.Request("ID") <> "" Then
                    Current.Session(sessionId & "_mLotId_tosource") = Current.Request.QueryString("ID")
                ElseIf Current.Request("DESCRIPTOR") <> "" Then
                    Current.Session(sessionId & "_mLotId_tosource") = myScript.GetLotIdFromDescriptor(Current.Request("DESCRIPTOR"))
                End If

                Me.Ricerca(Current.Session(sessionId & "_mLotId_tosource"))
            End If
        End If
    End Sub

    Protected Sub btnEsegui_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnEsegui.Click
        Current.Session(sessionId & "_reloadIFrameDetail_lotId") = Current.Session(sessionId & "_mLotId_tosource")
        Current.Session(sessionId & "_reloadIFrameDetail_composizione") = cbComposizione.Checked
        Current.Session(sessionId & "_reloadIFrameDetail_lotMode") = If(rbTracciabilita.Checked, LotMode.Tracciabilità, LotMode.Rintracciabilità)

        If cbComposizione.Checked Then
            compTreeSection.Visible = True
        Else
            compTreeSection.Visible = False
            cbCompTree.Checked = True
        End If

        If cbPrintTree.Checked Then
            UpdatePanelTreeView.Attributes("class") = ""
        Else
            UpdatePanelTreeView.Attributes("class") = "noPrint"
        End If

        Me.Ricerca(Current.Session(sessionId & "_mLotId_tosource"))

        RaiseEvent UpdateIFrame()
    End Sub

    ''' <summary>
    ''' La funzione costruisce l'albero dei lotti
    ''' </summary>
    ''' <remarks></remarks>
    Public Sub Ricerca(mainLotId As Long, Optional keepSelectedNode As Boolean = False)
        Dim b_Composizione As Boolean = Not Me.cbCompTree.Checked
        Dim node As TreeNodeLot
        Dim selectedNodeValuePath As String = Nothing

        If keepSelectedNode AndAlso treeLots.SelectedNode IsNot Nothing Then
            selectedNodeValuePath = treeLots.SelectedNode.ValuePath
        End If

        showUserNotesIndicatorDesc = False

        If m_config Is Nothing Then
            m_config = CType(Current.Application("Config"), config)
        End If

        If sessionId Is Nothing Then
            sessionId = Current.Request.QueryString("session")
        End If

        If mainLotId <> m_InvalidId Then
            m_LotIdPrincipale = mainLotId
        Else
            m_LotIdPrincipale = GetLotIdByDescriptor(treeLots.Nodes(0).Value)
        End If

        Me.treeLots.Nodes.Clear()
        If mLotti Is Nothing Then
            mLotti = New Generic.List(Of Lot)
        Else
            mLotti.Clear()
        End If

        If rbTracciabilita.Checked Then
            'Gestione della tracciabilità.
            'Da un lotto di consegna ad un lotto di ricevimento
            If Not b_Composizione Then
                node = BuildLotHistory(m_LotIdPrincipale, New ArrayList, b_Composizione)
                If node.Lot Is Nothing Then
                    Dim lot As New Lot
                    lot = GetLot(m_LotIdPrincipale)
                    node.Lot = lot
                End If
                node = SetNodeLotId(node.Lot.Id, node)
                Me.treeLots.Nodes.Add(node)
            Else
                mLotti = UsersGUI.FunctionLots.GetListaComponenti(m_LotIdPrincipale)
                BuildTreeViewComposizione(m_LotIdPrincipale)
            End If
        Else
            'Gestione della RIN-tracciabilità.
            'Da un lotto di ricevimento ad un lotto di consegna
            If Not b_Composizione Then
                node = BuildLotHistory(m_LotIdPrincipale, New ArrayList, b_Composizione)
                If node.Lot Is Nothing Then
                    Dim lot As New Lot
                    lot = GetLot(m_LotIdPrincipale)
                    node.Lot = lot
                End If
                node = SetNodeLotId(node.Lot.Id, node)
                Me.treeLots.Nodes.Add(node)
            Else
                mLotti = UsersGUI.FunctionLots.GetListaComponentiRin(m_LotIdPrincipale)
                BuildTreeViewComposizione(m_LotIdPrincipale)
            End If
        End If

        Me.treeLots.ExpandAll()

        If keepSelectedNode AndAlso selectedNodeValuePath IsNot Nothing Then
            Dim nodeToSelect As TreeNode = treeLots.FindNode(selectedNodeValuePath)

            If nodeToSelect IsNot Nothing Then
                nodeToSelect.Select()
            End If
        End If

        If treeLots.SelectedValue = Nothing Then
            treeLots.Nodes(0).Select()
        End If

        ' Visibility of the user note indicator description, only if some lot has a user note
        userNotes_indicatorDescription.Visible = showUserNotesIndicatorDesc
        userNotes_indicatorDescription.InnerText = m_config.GetEntryByKeyName("USER_NOTES_INDICATOR").GetValue

        UpdatePanelTreeView.Update()
    End Sub

    Protected Sub BuildTreeViewComposizione(ByVal LotId As Integer)
        Dim node As TreeNode
        node = SetNodeLotId(LotId, Nothing)
        Me.treeLots.Nodes.Add(node)
        BuildComposizione(LotId, node, 0)

        Me.treeLots.ExpandAll()
    End Sub

    Protected Function ExistLot(ByVal txt As String) As Boolean
        Dim ret_val As Boolean = False
        For Each x As TreeNode In Me.treeLots.Nodes(0).ChildNodes
            If txt = x.Text Then
                ret_val = True
                Exit For
            End If
        Next
        Return ret_val
    End Function

    Protected Function BuildComposizione(ByVal LotId As Integer, ByVal node As TreeNode, ByRef nDepth As Integer) As Boolean
        Dim nodx As TreeNode
        Dim lot_s As UsersGUI.Lot = Nothing

        For Each lot_s In mLotti
            If lot_s.IsFiglio = LotId And Not lot_s.IsFiglio = lot_s.Id And Not ExistLot(lot_s.Descrizione) Then
                If node IsNot Nothing Then
                    nodx = New TreeNode
                    nodx.Text = lot_s.Descrizione
                    nodx.Value = lot_s.Descrizione
                    nodx.NavigateUrl = myFunction.GetRoot() & "lotHistoryDetail.aspx?Id=" & lot_s.Id & "&Comp=" & Me.cbComposizione.Checked & "&lotmode=" & If(rbTracciabilita.Checked, 1, 2)    ' 1: Tracciabilità  2: Rintracciabilità
                    nodx.Target = "LotDetail"
                    nodx.ImageUrl = "../image/" & lot_s.ImageUrl

                    If lot_s.UserNotes IsNot Nothing AndAlso lot_s.UserNotes <> String.Empty Then
                        nodx.ToolTip = lot_s.UserNotes

                        showUserNotesIndicatorDesc = True
                    End If

                    node.ChildNodes.Add(nodx)
                    If BuildComposizione(lot_s.Id, nodx, nDepth + 1) Then
                        If node.ImageUrl = String.Empty Then
                            node.ImageUrl = "../image/" & lot_s.ImageUrl
                        End If
                    End If
                End If
            End If
        Next
        If (node.ImageUrl = String.Empty Or mLotti.Count = 1) And lot_s IsNot Nothing Then
            node.ImageUrl = "../image/" & lot_s.ImageUrl
        End If
        Return True
    End Function

    ''' <summary>
    ''' Construisce l'albero della Tracciabilità
    ''' </summary>
    ''' <param name="lotId"></param>
    ''' <param name="lotList"></param>
    ''' <param name="bComposizione"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function BuildLotHistory(ByVal lotId As Integer, ByVal lotList As ArrayList, ByVal bComposizione As Boolean) As TreeNodeLot
        Dim strSQL As String = String.Empty
        Dim dt As Data.DataTable

        Dim lot As UsersGUI.Lot = Nothing

        Dim nodx_sup As New TreeNodeLot

        If lotList Is Nothing Then
            lotList = New ArrayList
        End If

        If Not bComposizione Then
            If rbTracciabilita.Checked Then
                strSQL = "SELECT H.SOURCE_LOT, H.AMOUNT, L.DESCRIPTOR, L.NAME, L.IMAGE_TYPE, L.PID FROM VIEW_LOTS L INNER JOIN " &
                         "LOTS_HISTORIES H ON L.ID = H.FINAL_LOT WHERE FINAL_LOT = '" & lotId & "' ORDER BY L.ID"
            Else
                strSQL = "SELECT H.FINAL_LOT, H.AMOUNT, L.DESCRIPTOR, L.NAME, L.IMAGE_TYPE, L.PID FROM VIEW_LOTS L INNER JOIN " &
                     "LOTS_HISTORIES H ON L.ID = H.FINAL_LOT WHERE SOURCE_LOT = '" & lotId & "' ORDER BY L.ID"
            End If
        End If

        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        If dt.Rows.Count > 0 Then

            'Creo il primo nodo relativo al lotto in esame

            lot = GetLot(lotId)

            nodx_sup.Lot = lot
            nodx_sup.Text = lot.Descrizione
            nodx_sup.Value = lot.Descrizione
            nodx_sup.NavigateUrl = myFunction.GetRoot() & "lotHistoryDetail.aspx?Id=" & lot.Id & "&Comp=" & Me.cbComposizione.Checked & "&lotmode=" & If(rbTracciabilita.Checked, 1, 2)    ' 1: Tracciabilità  2: Rintracciabilità
            nodx_sup.Target = "LotDetail"
            nodx_sup.ImageUrl = "../image/" & lot.ImageUrl

            If lot.UserNotes IsNot Nothing AndAlso lot.UserNotes <> String.Empty Then
                nodx_sup.ToolTip = lot.UserNotes

                showUserNotesIndicatorDesc = True
            End If

            BuildTreeView(Nothing, nodx_sup)

            GetSourceLotsRecursively(nodx_sup, Nothing, nodx_sup.Lot.Id)
        End If

        Return nodx_sup
    End Function

    Protected Sub GetSourceLotsRecursively(ByVal nodx_sup As TreeNodeLot, ByVal dt As Data.DataTable, ByVal lotId As Long)
        Dim strSql As String
        Dim lot As Lot

        If dt Is Nothing Then
            If rbTracciabilita.Checked Then
                strSql = "SELECT H.SOURCE_LOT, H.AMOUNT, L.DESCRIPTOR, L.NAME, L.IMAGE_TYPE, L.PID FROM VIEW_LOTS L INNER JOIN " &
                         "LOTS_HISTORIES H ON L.ID = H.FINAL_LOT WHERE FINAL_LOT = '" & lotId & "' ORDER BY L.ID"
            Else
                strSql = "SELECT H.FINAL_LOT, H.AMOUNT, L.DESCRIPTOR, L.NAME, L.IMAGE_TYPE, L.PID FROM VIEW_LOTS L INNER JOIN " &
                    "LOTS_HISTORIES H ON L.ID = H.FINAL_LOT WHERE SOURCE_LOT = '" & lotId & "' ORDER BY L.ID"
            End If

            dt = DataBase.ExecuteSQL_DataTable(strSql, False)
        End If

        'For each dei risultati prendendo il source_lot e creo i nodi successivi (primo livello)
        For Each dr As Data.DataRow In dt.Rows

            'Creo il primo nodo relativo al lotto in esame
            lot = GetLot(Long.Parse(dr.Item(0).ToString))

            Dim nodx As New TreeNodeLot
            nodx.Lot = lot
            nodx.Text = lot.Descrizione
            nodx.Value = lot.Descrizione
            nodx.NavigateUrl = myFunction.GetRoot() & "lotHistoryDetail.aspx?Id=" & lot.Id & "&Comp=" & Me.cbComposizione.Checked & "&lotmode=" & If(rbTracciabilita.Checked, 1, 2)    ' 1: Tracciabilità  2: Rintracciabilità
            nodx.Target = "LotDetail"
            nodx.ImageUrl = "../image/" & lot.ImageUrl

            If lot.UserNotes IsNot Nothing AndAlso lot.UserNotes <> String.Empty Then
                nodx.ToolTip = lot.UserNotes

                showUserNotesIndicatorDesc = True
            End If

            lot.Node = nodx
            BuildTreeView(nodx_sup, nodx)

            GetSourceLotsRecursively(nodx, Nothing, nodx.Lot.Id)
        Next

    End Sub

    Protected Function GetLot(ByVal lotId As Long) As Lot
        Dim strSql As String
        Dim dt_lot As Data.DataTable
        Dim Lot As Lot

        strSql = "SELECT * FROM VIEW_LOTS WHERE ID = '" & lotId & "'"
        dt_lot = DataBase.ExecuteSQL_DataTable(strSql, False)

        If dt_lot.Rows.Count > 0 Then
            Lot = New UsersGUI.Lot
            Lot.Id = lotId
            Lot.Descrizione = dt_lot.Rows(0).Item("DESCRIPTOR").ToString

            Lot.IsFiglio = lotId
            Lot.ImageUrl = "./image/" & dt_lot.Rows(0).Item("IMAGE_TYPE").ToString
            Lot.UnitAsp = dt_lot.Rows(0).Item("UNIT_ASP").ToString.Trim
            Lot.CreationDate = Date.Parse(dt_lot.Rows(0).Item("CREATION_DATE").ToString)
            Lot.Quantity = Double.Parse(dt_lot.Rows(0).Item("AMOUNT").ToString)
            If Not dt_lot.Rows(0).Item("PID") Is DBNull.Value Then
                Lot.ProductId = Integer.Parse(dt_lot.Rows(0).Item("PID").ToString)
            Else
                Lot.ProductId = m_InvalidId
            End If
            Lot.UserNotes = dt_lot.Rows(0).Item("USER_NOTES").ToString
        Else
            Throw New myException.myException(
                System.Reflection.Assembly.GetExecutingAssembly.GetName.Name,
                System.Reflection.MethodBase.GetCurrentMethod.DeclaringType.Name,
                System.Reflection.MethodBase.GetCurrentMethod().Name,
                "Lot not found for ID """ & lotId & """"
            )
        End If

        Return Lot
    End Function

    Protected Sub BuildTreeView(ByVal node_sup As TreeNodeLot, ByVal node As TreeNodeLot)
        Dim Lot As Lot
        Lot = node.Lot

        node = SetNodeLotId(Lot.Id, node)

        If node_sup IsNot Nothing Then
            If Not node_sup.ChildNodes.Contains(node) Then
                node_sup.ChildNodes.Add(node)
            End If

        End If

    End Sub

    Protected Function SetNodeLotId(ByVal LotId As Integer, ByVal node As TreeNodeLot) As TreeNodeLot
        Dim ds As New Data.DataSet
        Dim strSQL As String = String.Empty
        Dim sDescrizione As String = String.Empty

        strSQL = "SELECT * FROM VIEW_LOTS WHERE ID = " & LotId

        ds = WebDataBaseLayer.DataBase.ExecuteSQL_DataSet(strSQL, False)

        If ds.Tables("Results").Rows.Count <= 0 Then
            Return Nothing
        End If

        sDescrizione = ds.Tables("Results").Rows(0).Item("DESCRIPTOR").ToString

        If node Is Nothing Then
            node = New TreeNodeLot
        End If

        node.Text = sDescrizione
        node.Value = sDescrizione
        node.ImageUrl = "../image/" & ds.Tables("Results").Rows(0).Item("IMAGE_TYPE").ToString
        node.NavigateUrl = myFunction.GetRoot() & "lotHistoryDetail.aspx?Id=" & LotId & "&Comp=" & Me.cbComposizione.Checked & "&lotmode=" & If(rbTracciabilita.Checked, 1, 2)    ' 1: Tracciabilità  2: Rintracciabilità
        node.Target = "LotDetail"

        If ds.Tables("Results").Rows(0).Item("USER_NOTES").ToString IsNot Nothing AndAlso ds.Tables("Results").Rows(0).Item("USER_NOTES").ToString <> String.Empty Then
            node.ToolTip = ds.Tables("Results").Rows(0).Item("USER_NOTES").ToString

            showUserNotesIndicatorDesc = True
        End If

        Return node
    End Function

    Private Function GetLotIdByDescriptor(descriptor As String) As Long
        Dim retVal As Long = Nothing
        Dim sql As String = "SELECT ID FROM VIEW_LOTS WHERE DESCRIPTOR = '" & descriptor & "'"

        Dim dt As Data.DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sql, False)

        If dt.Rows.Count > 0 Then
            retVal = Long.Parse(dt.Rows(0).Item("ID").ToString)
        End If

        Return retVal
    End Function

    Private Sub cbPrintTree_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles cbPrintTree.CheckedChanged
        If cbPrintTree.Checked Then
            UpdatePanelTreeView.Attributes("class") = ""
        Else
            UpdatePanelTreeView.Attributes("class") = "noPrint"
        End If
    End Sub

    Private Sub cbCompTree_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles cbCompTree.CheckedChanged
        Me.Ricerca(Current.Session(sessionId & "_mLotId_tosource"))
    End Sub

End Class