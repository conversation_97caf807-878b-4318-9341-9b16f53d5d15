﻿Imports System.Data
Imports System.IO
Imports UsersGUI
Imports WebTools.tools
Imports System.Web.HttpContext

Partial Class WUC_WUC_View
    Inherits UserControl

    Public mPageName As String
    Public mScreen As Screen
    Public mConfig As config

    Private mTopMenuName As String = String.Empty
    Private mMenuName As String
    Private mDataTable As Data.DataTable
    Private mSqlWhere As String
    Private mControl As String = String.Empty
    Private mSessionId As String = Nothing

    Private bPrint As Boolean = False
    Private bExtraColumn As Boolean = False

    ' When set to True tells the btnStartQuery click handler to apply a pagination to the results
    Private bPaginationChanged As Boolean = False

    Private adim As AddMenuItemName = Nothing

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded and the session is set in the URL
        If Me.Visible AndAlso Current.Request.QueryString("session") <> Nothing Then
            mSessionId = Current.Request.QueryString("session")

            If Current.Request.QueryString("print") IsNot Nothing AndAlso (Current.Request.QueryString("print").ToString.ToUpper = m_StringYes) Then
                bPrint = True
            Else
                bPrint = False
            End If

            mConfig = CType(Application("Config"), config)

            If Not GetWebParams(mConfig, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            ' cambio pagina
            If Not IsPostBack() AndAlso Not bPrint Then

                ' resetto i filtri se è la prima volta che entro nella pagina
                Current.Session(mSessionId + "_sql_where_base") = String.Empty
                Current.Session(mSessionId + "_sql_where") = String.Empty

                Current.Session(mSessionId + "_sql_where_print") = String.Empty
            End If

            If Not Current.Request("excol") Is Nothing Then
                bExtraColumn = CBool(Current.Request("excol").ToString)
            End If

            If mScreen IsNot Nothing AndAlso mScreen.HasSearchButton AndAlso mScreen.HasQueryFields AndAlso CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Searching) Then
                Me.btnStartQuery.Text = mConfig.GetEntryByKeyName("SearchSelection").GetValue()

                WebTools.drawings.DrawQueryFields(tblQuery, mScreen, mConfig)
                Me.div_query.Visible = True
            Else
                Me.div_query.Visible = False
            End If

            If mPageName.StartsWith("CYCLE_") AndAlso Current.Request.QueryString("CYC_ID") IsNot Nothing AndAlso Current.Request.QueryString("CYC_ID") <> String.Empty Then
                Dim IdCyle As Integer = Integer.Parse(Current.Request.QueryString("CYC_ID"))
                    If Not Page.IsPostBack Then
                        Me.TimerReloadPage.Enabled = True

                        If Current.Request.QueryString("direction") IsNot Nothing AndAlso Current.Request.QueryString("direction") <> String.Empty Then
                            MoveUpDownRecord(mScreen, Current.Request.QueryString("ID").ToString, " CYC_ID = '" & Current.Request.QueryString("CYC_ID").ToString & "' ", Current.Request.QueryString("LINE_POSITION").ToString, Current.Request.QueryString("direction").ToString)
                        End If

                    Me.Buttons.Visible = IsPlanned(IdCyle) Or IsControlled(IdCyle)

                        Me.btnStartPlanning.Visible = True
                    Me.btnPlanningMode.Visible = myFunction.IsSpecificPlanned(IdCyle)

                        Me.btnStopPlanning.Text = mConfig.GetEntryByKeyName("STOP_SCHEDULING_BUTTON").GetValue
                    End If

                    ' devono essere sempre valutati (potrebbe manipolarli anche il c++)
                    If GetParameter(mPageName & "_AUTOMATIC_MODE").ToUpper = costanti.m_StringOn Then
                    Me.btnStopPlanning.Visible = myFunction.IsSpecificPlanned(IdCyle)
                        Me.btnStartPlanning.Text = mConfig.GetEntryByKeyName("START_SCHEDULING_BUTTON").GetValue
                        Me.btnPlanningMode.Text = mConfig.GetEntryByKeyName("SWITCH_TO_SEMIAUTOMATIC_BUTTON").GetValue
                    Else
                        Me.btnStopPlanning.Visible = False
                        Me.btnStartPlanning.Text = mConfig.GetEntryByKeyName("START_NEXT_BUTTON").GetValue
                        Me.btnPlanningMode.Text = mConfig.GetEntryByKeyName("SWITCH_TO_AUTOMATIC_BUTTON").GetValue
                    End If

                    If GetParameter(mPageName & "_START_BUTTON_ENABLED").ToUpper = costanti.m_StringOn Then
                        Me.btnStartPlanning.Enabled = True
                        Me.btnStopPlanning.Enabled = False
                    Else
                        Me.btnStartPlanning.Enabled = False
                    Me.btnStopPlanning.Enabled = myFunction.IsSpecificPlanned(IdCyle)
                End If
            ElseIf mPageName.StartsWith("RECIPE_PARAMS_") AndAlso Current.Request.QueryString("MTR_ID") IsNot Nothing AndAlso Current.Request.QueryString("MTR_ID") <> String.Empty Then
                Dim IdMetaRecipe As Integer = Integer.Parse(Current.Request.QueryString("MTR_ID"))

                If Not Page.IsPostBack Then
                    Dim from_page As String = String.Empty
                    Dim upload As String = String.Empty

                    If Current.Request.QueryString("from") IsNot Nothing AndAlso Current.Request.QueryString("from").ToString <> String.Empty Then
                        from_page = Current.Request.QueryString("from")
                    End If
                    If Current.Request.QueryString("upload") IsNot Nothing AndAlso Current.Request.QueryString("upload").ToString <> String.Empty Then
                        upload = Current.Request.QueryString("upload")
                    End If

                    Response.Redirect(mScreen.GetPathStringForEdit(mPageName,
                                                               "edit.aspx",
                                                               mMenuName & "&MTR_ID=" & IdMetaRecipe & "&from=" & from_page & "&upload=" & upload,
                                                               mScreen.AddButtonParams,
                                                               Integer.Parse(Current.Request.QueryString("ID")),
                                                               mTopMenuName))
                End If

            ElseIf mPageName.StartsWith("ANALYSIS_PARAMS_") AndAlso Current.Request.QueryString("MTA_ID") IsNot Nothing AndAlso Current.Request.QueryString("MTA_ID") <> String.Empty Then
                Dim IdMetaAnalysis As Integer = Integer.Parse(Current.Request.QueryString("MTA_ID"))

                If Not Page.IsPostBack Then
                    Dim from_page As String = String.Empty

                    If Current.Request.QueryString("from") IsNot Nothing AndAlso Current.Request.QueryString("from").ToString <> String.Empty Then
                        from_page = Current.Request.QueryString("from")
                    End If

                    Response.Redirect(mScreen.GetPathStringForEdit(mPageName,
                                                               "edit.aspx",
                                                               mMenuName & "&MTA_ID=" & IdMetaAnalysis & "&from=" & from_page,
                                                               mScreen.AddButtonParams,
                                                               Integer.Parse(Current.Request.QueryString("ID")),
                                                               mTopMenuName))
                End If

            ElseIf mPageName.StartsWith("OPERATION_PARAMS_") AndAlso Current.Request.QueryString("MTO_ID") IsNot Nothing AndAlso Current.Request.QueryString("MTO_ID") <> String.Empty Then
                Dim IdMetaOperation As Integer = Integer.Parse(Current.Request.QueryString("MTO_ID"))

                If Not Page.IsPostBack Then
                    Dim from_page As String = String.Empty

                    If Current.Request.QueryString("from") IsNot Nothing AndAlso Current.Request.QueryString("from").ToString <> String.Empty Then
                        from_page = Current.Request.QueryString("from")
                    End If

                    Response.Redirect(mScreen.GetPathStringForEdit(mPageName,
                                                               "edit.aspx",
                                                               mMenuName & "&MTO_ID=" & IdMetaOperation & "&from=" & from_page,
                                                               mScreen.AddButtonParams,
                                                               Integer.Parse(Current.Request.QueryString("ID")),
                                                               mTopMenuName))
                End If

            ElseIf mPageName.StartsWith("CONTRACT_PARAMS_") AndAlso Current.Request.QueryString("MTC_ID") IsNot Nothing AndAlso Current.Request.QueryString("MTC_ID") <> String.Empty Then
                Dim IdMetaContract As Integer = Integer.Parse(Current.Request.QueryString("MTC_ID"))

                If Not Page.IsPostBack Then
                    Dim from_page As String = String.Empty

                    If Current.Request.QueryString("from") IsNot Nothing AndAlso Current.Request.QueryString("from").ToString <> String.Empty Then
                        from_page = Current.Request.QueryString("from")
                    End If

                    Response.Redirect(mScreen.GetPathStringForEdit(mPageName,
                                                               "edit.aspx",
                                                               mMenuName & "&MTC_ID=" & IdMetaContract & "&from=" & from_page,
                                                               mScreen.AddButtonParams,
                                                               Integer.Parse(Current.Request.QueryString("ID")),
                                                               mTopMenuName))
                End If

            ElseIf mPageName.StartsWith("SCALES_BATCH_SIZE") Then

                If Not Page.IsPostBack Then

                    Me.Buttons.Visible = True
                    Me.btnStartPlanning.Visible = False
                    Me.btnPlanningMode.Visible = False
                    Me.btnStopPlanning.Visible = False

                    Me.btnRefreshBatchSize.Visible = True
                    Me.btnRefreshBatchSize.Text = mConfig.GetEntryByKeyName("REFRESH_DATA").GetValue

                End If

            End If

            Dim sTitle As String = mConfig.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue()

            'Da qui gestisco l'interfaccia della pagina,
            'precedentemente ho recuperato i dati che mi servono per la corretta
            'configurazione.
            CheckPageDB(mConfig, mMenuName, mPageName)
            If Not CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
                lblError.Text = mConfig.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If

            Me.dgRisultati.HeaderStyle.BackColor = mConfig.GetMenuByMenuName(mMenuName).Color
            Me.dgRisultati.HeaderStyle.ForeColor = Drawing.Color.White
            Me.dgRisultati.FooterStyle.BackColor = mConfig.GetMenuByMenuName(mMenuName).Color
            Me.dgRisultati.FooterStyle.ForeColor = Drawing.Color.White
            Try
                Me.dgRisultati.PageSize = mScreen.LinesPerPage
            Catch ex As Exception
                'Da errore se non è una pagina view, poichè il control=view lo esegue indistintamente è necessario ignorare l'errore in queste condizioni
            End Try

            Me.dgRisultati.HeaderStyle.HorizontalAlign = HorizontalAlign.Center
            Me.dgRisultati.PagerSettings.LastPageText = mConfig.GetEntryByKeyName("LAST").GetValue
            Me.dgRisultati.PagerSettings.FirstPageText = mConfig.GetEntryByKeyName("FIRST").GetValue

            If Not Page.IsPostBack Then
                Me.tblQuery.Rows.Clear()
                WebTools.drawings.DrawQueryFields(Me.tblQuery, mScreen, mConfig)

                If Not bPrint Then
                    ' inizializzo il sort expression e direction (da config.xml)
                    Dim str_expr As String = String.Empty
                    Dim str_direction As String = String.Empty

                    Dim str_temp As String = mScreen.OrderBy
                    Dim str_temp_1 As String = String.Empty
                    Dim str_temp_2 As String = String.Empty

                    If Not str_temp Is Nothing Then
                        If str_temp.Contains(",") Then
                            str_temp_1 = str_temp.Substring(0, str_temp.LastIndexOf(",") + 1)
                            str_temp_2 = str_temp.Substring(str_temp.LastIndexOf(",") + 1, str_temp.Length - str_temp.LastIndexOf(",") - 1)

                            str_temp_1 = str_temp_1.Trim
                            str_temp_2 = str_temp_2.Trim

                            If str_temp_2.Contains(" ") Then
                                Dim str_split() As String = str_temp_2.Split(" ")
                                str_expr = str_temp_1 + str_split(0).Trim
                                str_direction = str_split(1).Trim
                            Else
                                str_expr = str_temp
                                str_direction = "ASC"
                            End If
                        Else
                            If str_temp.Contains(" ") Then
                                Dim str_split() As String = str_temp.Split(" ")
                                str_expr = str_split(0).Trim
                                str_direction = str_split(1).Trim
                            Else
                                str_expr = str_temp
                                str_direction = "ASC"
                            End If
                        End If

                        GridViewSortExpression = str_expr
                        If str_direction.ToUpper = "DESC" Then
                            GridViewSortDirection = SortDirection.Descending
                        Else
                            GridViewSortDirection = SortDirection.Ascending
                        End If
                    End If
                Else
                    ' ordinamento da sessione
                    GridViewSortExpression = Current.Session(mSessionId + "_sortExpression")
                    GridViewSortDirection = Current.Session(mSessionId + "_sortDirection")
                End If

                If bExtraColumn And Not mPageName.Contains("CYCLE") Then
                    mSqlWhere = Current.Request("sql_where").ToString
                    Current.Session(mSessionId & "_sql_where_base") = mSqlWhere
                    SelectData()
                Else
                    If mScreen.FilterColumnsName IsNot Nothing AndAlso mScreen.FilterColumnsName.Count > 0 Then
                        For Each f As FilterColumnName In mScreen.FilterColumnsName
                            If Current.Request.QueryString(f.FieldDB) IsNot Nothing Then
                                mSqlWhere &= " " & f.FieldDB & "='" & Current.Request.QueryString(f.FieldDB) & "' "
                            End If
                        Next
                        Current.Session(mSessionId & "_sql_where_base") = mSqlWhere
                        SelectData()
                    Else
                        If Current.Request.QueryString("sql_where") IsNot Nothing AndAlso Current.Request.QueryString("sql_where").ToString <> String.Empty Then
                            mSqlWhere = Current.Request.QueryString("sql_where").ToString
                            Current.Session(mSessionId & "_sql_where_base") = mSqlWhere
                            SelectData()
                        ElseIf bPrint Then
                            If Current.Session(mSessionId & "_sql_where_print") <> String.Empty Then
                                ' caso della stampa (con query fields)
                                mSqlWhere = Current.Session(mSessionId & "_sql_where_print")
                                SelectData()
                            Else
                                mSqlWhere = String.Empty
                                SelectData()
                            End If
                        Else
                            ViewState.Remove("sql_where")
                            Current.Session.Remove(mSessionId & "_sql_where_print")
                            mSqlWhere = String.Empty
                            SelectData()
                        End If
                    End If
                End If

                Me.btnViewSearch.Text = mConfig.GetEntryByKeyName("Apri").GetValue
                Me.btnViewSearch.Attributes.Add("myValue", "0")

                Me.DrawSumColumns()

                ' pagine con reload automatico
                Select Case mScreen.EnumPageNameCode
                    Case EnumPageName.FlowsActive, EnumPageName.CyclesActive
                        Me.TimerReloadPage.Enabled = True
                End Select
            End If

            ' Draw the header fields above the results
            WebTools.drawings.DrawHeaderFields(tblHeader, mScreen.Header, mConfig, True)

            If tblHeader.Rows.Count <= 0 Then
                tblHeader.Visible = False
            End If

            ' gestione blanket (solo sul 1° load, gestito da js)
            div_blanket.Attributes.Add("IsFirstLoad", (Not Page.IsPostBack).ToString)

            myScript.InvokeJS(Me.Page, "HideRows('UCView_tblHeader');")
            myScript.InvokeJS(Me.Page, "SetTdColspan('UCView_tblHeader');")
            myScript.InvokeJS(Me.Page, "ShowUCCont();")
            myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('UCView_tblHeader');")
        End If
    End Sub

    Private Sub SelectData(Optional ByVal nPageIndex As Integer? = Nothing)

        ' Handles the default case (nPageIndex = 0)
        If nPageIndex Is Nothing Then
            nPageIndex = 0
        End If

        Me.dgRisultati.Columns.Clear()
        If mScreen IsNot Nothing Then
            Dim sExtraColums(0) As String
            Dim sUnitColumns(0) As String
            Dim sDeleteColumn(0) As String
            Dim sLedColumns(0) As String
            Dim sSelect As String
            Dim column_offset As Integer = 0
            Dim column_count As Integer = 0

            Dim row_num_start As Integer
            Dim row_num_stop As Integer

            ' calcolo il m_sql_where
            If Current.Session(mSessionId + "_sql_where_base") <> String.Empty Then
                mSqlWhere = Current.Session(mSessionId + "_sql_where_base")
            End If

            If bPrint Then
                ' Printing
                If mSqlWhere <> String.Empty AndAlso Current.Session(mSessionId + "_sql_where_print") <> String.Empty Then
                    mSqlWhere &= " AND " & Current.Session(mSessionId + "_sql_where_print")
                End If

                sSelect = mScreen.GetSqlSelectFullTable(mSqlWhere, GridViewSortExpression, GridViewSortDirection)
            ElseIf nPageIndex.Equals(m_InvalidInteger) Then
                If mSqlWhere <> String.Empty AndAlso Current.Session(mSessionId + "_sql_where") <> String.Empty Then
                    mSqlWhere &= " AND " & Current.Session(mSessionId + "_sql_where")
                End If

                sSelect = mScreen.GetSqlSelectFullTable(mSqlWhere, GridViewSortExpression, GridViewSortDirection)
            Else
                ' Default
                row_num_start = (nPageIndex * mScreen.LinesPerPage) + 1
                row_num_stop = row_num_start - 1 + mScreen.LinesPerPage

                If mSqlWhere <> String.Empty AndAlso Current.Session(mSessionId + "_sql_where") <> String.Empty Then
                    mSqlWhere &= " AND " & Current.Session(mSessionId + "_sql_where")
                End If

                sSelect = mScreen.GetSqlSelect(mSqlWhere, row_num_start, row_num_stop, GridViewSortExpression, GridViewSortDirection)
            End If

            If sSelect = String.Empty Then
                Exit Sub
            End If

            mDataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)
            If mDataTable Is Nothing OrElse mDataTable.Rows.Count <= 0 Then
                Me.dgRisultati.DataSource = mDataTable
                Me.dgRisultati.DataBind()
                DrawPager(nPageIndex)
                myScript.InvokeJS(Me.Page, "ShowUCCont();")
                myScript.InvokeJS(Me.Page, "SetDataTable();")
                Exit Sub
            End If

            sExtraColums = mScreen.GetExtraColumns(mDataTable)
            sUnitColumns = mScreen.GetUnitColumns(mDataTable)
            sLedColumns = mScreen.GetLedColumns(mDataTable)

            If mScreen.HasDelButton And CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Deleting) Then
                Me.dgRisultati.Columns.Add(mScreen.GetDeleteColumn(mDataTable, String.Empty))
                column_offset += 1
            End If

            If mScreen.HasEditButton And CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Editing) Then
                Me.dgRisultati.Columns.Add(mScreen.GetEditColumn(mDataTable))
                column_offset += 1
            End If

            If mScreen.HasSimplePrintButton And CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Printing) Then
                Me.dgRisultati.Columns.Add(mScreen.GetPrintColumn(mDataTable))
                column_offset += 1
            End If

            For Each dr As Data.DataRow In mDataTable.Rows

                'Creo il contenuto della colonna di delete
                If mScreen.HasDelButton And CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Deleting) Then
                    mScreen.SetPathForDeleteColumns(dr, mPageName, "default.aspx", mMenuName, mScreen.AddButtonParams, mControl, mSqlWhere, mTopMenuName, String.Empty)
                End If

                'Creo il contenuto della colonna di edit
                If mScreen.HasEditButton And CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Editing) Then
                    mScreen.SetPathForEditColumns(dr, mPageName, "edit.aspx", mMenuName, mScreen.AddButtonParams, mTopMenuName)
                End If

                'Creo il contenuto della colonna di print
                If mScreen.HasSimplePrintButton And CheckAccess(mConfig, mMenuName, mPageName, EnumAccessLevel.Printing) Then
                    ' not handled
                End If

                'Creo il contenuto necessario al corretto funzionamento delle extra columns
                If mScreen.extraColumns IsNot Nothing Then
                    For Each exCol As extraColumn In mScreen.extraColumns
                        If exCol.Visible AndAlso CheckAccess(mConfig, mMenuName, mPageName, exCol.RequiredAccessLevel) Then
                            mScreen.SetPathDataForExtraColumns(exCol, mPageName, dr)
                        End If
                    Next
                End If

                'Regolo il valore delle colonne contenenti i tag di UnitAsp e UnitDb
                If mScreen.ViewFields IsNot Nothing Then
                    For Each s As Field In mScreen.ViewFields
                        If s.VisibleField Then
                            If s.UnitAsp <> String.Empty And s.UnitDb <> String.Empty And s.DynamicUnitAsp = String.Empty And dr(s.FieldDb).ToString <> String.Empty Then
                                Dim m_value As Double

                                m_value = Double.Parse(dr(s.FieldDb).ToString) * UsersGUI.tools.GetConversionFactorToASP(s.UnitDb, s.UnitAsp)

                                dr(s.FieldDb) = Math.Round(m_value, s.nDecimal)
                                dr(s.UnitAsp) = s.UnitAsp
                            ElseIf s.UnitAsp <> String.Empty And s.UnitDb <> String.Empty And s.DynamicUnitAsp <> String.Empty And dr(s.FieldDb).ToString <> String.Empty Then
                                Dim m_value As Double

                                m_value = Double.Parse(dr(s.FieldDb).ToString) * UsersGUI.tools.GetConversionFactorToASP(s.UnitDb, dr(s.DynamicUnitAsp).ToString)

                                dr(s.FieldDb) = Math.Round(m_value, s.nDecimal)
                                dr(s.DynamicUnitAsp) = dr(s.DynamicUnitAsp).ToString
                            ElseIf s.GetFieldType = EnumFieldType.FieldNumber Then
                                If IsNumeric(dr(s.FieldDb).ToString) Then
                                    dr(s.FieldDb) = Math.Round(Double.Parse(dr(s.FieldDb).ToString), s.nDecimal)
                                End If
                            End If
                        End If
                    Next
                End If
            Next

            'Extra columns
            If mScreen.extraColumns IsNot Nothing Then
                For Each exCol As extraColumn In mScreen.extraColumns
                    If exCol.Visible AndAlso CheckAccess(mConfig, mMenuName, mPageName, exCol.RequiredAccessLevel) Then
                        Dim n As New myHyperLinkField

                        Dim sColName(0) As String
                        sColName(0) = exCol.Name

                        n.ImageUrl = "../" & exCol.ImageSrc.Replace("\", "/")

                        n.Text = mConfig.GetEntryByKeyName(exCol.ImageToolTip).GetValue

                        n.ControlStyle.CssClass = "label-text"
                        n.ItemStyle.BorderStyle = BorderStyle.None
                        n.HeaderStyle.BorderStyle = BorderStyle.None
                        n.ItemStyle.CssClass = "hideOnPrint"
                        n.HeaderStyle.CssClass = "hideOnPrint icon-col"
                        n.DataNavigateUrlFields = sColName
                        n.DataGridParent = mDataTable

                        Me.dgRisultati.Columns.Add(n)
                        column_offset += 1
                    End If
                Next
            End If

            Dim ListColumns As New Generic.List(Of String)

            'VisibileField Columns
            Dim ListLedStringColumns As New Dictionary(Of UsersGUI.Field, UsersGUI.Field)

            If mScreen.ViewFields IsNot Nothing Then
                For Each vf As Field In mScreen.ViewFields
                    If vf.VisibleField Then
                        If vf.EnableKeyEntry Then
                            ListColumns.Add(vf.FieldDb)
                        End If

                        Select Case vf.GetFieldType
                            Case EnumFieldType.FieldLed
                                Dim n As New ImageField

                                n.DataImageUrlField = vf.FieldDb
                                n.SortExpression = vf.FieldDb
                                n.HeaderStyle.BorderStyle = BorderStyle.None
                                n.HeaderText = mConfig.GetEntryByKeyName(vf.FieldName).GetValue
                                n.ItemStyle.HorizontalAlign = vf.HAlign
                                n.ItemStyle.BorderStyle = BorderStyle.None

                                Me.dgRisultati.Columns.Add(n)

                            Case EnumFieldType.FieldLinkString, EnumFieldType.FieldLink
                                Dim n As New myHyperLinkField
                                Dim sColName(0) As String

                                sColName(0) = vf.FieldDb

                                ' Se la colonna ha FieldDb del tipo [COLUMN]_LINK, usa come testo del link [COLUMN]
                                n.DataTextField = vf.FieldDb.Replace("_LINK", "")
                                n.SortExpression = vf.FieldDb.Replace("_LINK", "")
                                n.DataNavigateUrlFields = sColName
                                n.DataNavigateUrlFormatString = Path.Combine(myFunction.GetRoot(), "{0}")
                                n.HeaderStyle.BorderStyle = BorderStyle.None
                                n.HeaderText = mConfig.GetEntryByKeyName(vf.FieldName).GetValue
                                n.ItemStyle.HorizontalAlign = vf.HAlign
                                n.ItemStyle.BorderStyle = BorderStyle.None
                                n.ItemStyle.Font.Underline = True

                                Me.dgRisultati.Columns.Add(n)

                            Case EnumFieldType.FieldCalendar
                                Dim dateFormat As String = String.Empty
                                Dim n As New BoundField()

                                n.DataField = vf.FieldDb
                                n.SortExpression = vf.FieldDb
                                n.HeaderStyle.BorderStyle = BorderStyle.None
                                n.HeaderText = mConfig.GetEntryByKeyName(vf.FieldName).GetValue
                                n.ItemStyle.HorizontalAlign = vf.HAlign
                                n.ItemStyle.BorderStyle = BorderStyle.None

                                Select Case vf.DateFormat
                                    Case EnumDateFormats.DateTime
                                        dateFormat = mConfig.GetLanguage().FormatDateTime
                                    Case EnumDateFormats.DateOnly
                                        dateFormat = mConfig.GetLanguage().FormatDate
                                    Case EnumDateFormats.TimeOnly
                                        dateFormat = mConfig.GetLanguage().FormatTime
                                End Select

                                n.DataFormatString = "{0:" & dateFormat & "}"

                                ' Handles string column types that are to be read as TDates
                                For Each dr As DataRow In mDataTable.Rows
                                    If Not TypeOf (dr.Item(vf.FieldDb)) Is Date And Not IsDBNull(dr.Item(vf.FieldDb)) Then
                                        dr.Item(vf.FieldDb) = UsersGUI.tools.ConvertDateTimeFromDB(dr.Item(vf.FieldDb).ToString(), dateFormat)
                                    End If
                                Next

                                Me.dgRisultati.Columns.Add(n)
                            Case EnumFieldType.FieldLedString
                                Dim vf_precedente As UsersGUI.Field = UsersGUI.tools.GetFieldByDbName(mScreen.ViewFields, vf.FieldWithLed)
                                ListLedStringColumns.Add(vf_precedente, vf)

                                Dim n1 As New ImageField
                                n1.DataImageUrlField = vf_precedente.FieldDb
                                n1.SortExpression = vf_precedente.FieldDb
                                n1.HeaderStyle.BorderStyle = BorderStyle.None
                                n1.HeaderText = ""
                                n1.ItemStyle.HorizontalAlign = vf.HAlign
                                n1.ItemStyle.BorderStyle = BorderStyle.None
                                n1.ControlStyle.Width = 24
                                n1.ControlStyle.Height = 24

                                Me.dgRisultati.Columns.Add(n1)

                                Dim n As New BoundField()

                                n.DataField = vf.FieldDb
                                n.SortExpression = vf.FieldDb
                                n.HeaderStyle.BorderStyle = BorderStyle.None
                                n.HeaderText = mConfig.GetEntryByKeyName(vf.FieldName).GetValue
                                n.ItemStyle.HorizontalAlign = vf.HAlign
                                n.ItemStyle.BorderStyle = BorderStyle.None

                                Me.dgRisultati.Columns.Add(n)

                            Case Else
                                Dim n As New BoundField()

                                n.DataField = vf.FieldDb
                                n.SortExpression = vf.FieldDb
                                n.HeaderStyle.BorderStyle = BorderStyle.None
                                n.HeaderText = mConfig.GetEntryByKeyName(vf.FieldName).GetValue
                                n.ItemStyle.HorizontalAlign = vf.HAlign
                                n.ItemStyle.BorderStyle = BorderStyle.None

                                Me.dgRisultati.Columns.Add(n)
                        End Select
                    End If
                Next
            End If

            For Each s As KeyValuePair(Of UsersGUI.Field, UsersGUI.Field) In ListLedStringColumns
                For Each dr As DataRow In mDataTable.Rows
                    Dim vector_show_image_if_ledstring_is As String() = s.Key.ShowImageIfLedStringIs.Split(";")
                    Dim vector_image_led As String() = s.Key.ImageLed.Split(";")

                    For i As Integer = 0 To vector_show_image_if_ledstring_is.GetUpperBound(0)
                        If vector_show_image_if_ledstring_is(i).ToUpper() = dr.Item(s.Value.FieldDb).ToString().ToUpper() Then
                            dr.Item(s.Key.FieldDb) = (vector_image_led(i))
                        Else
                            dr.Item(s.Key.FieldDb) = ""
                        End If
                    Next

                Next
            Next

            For Each s As String In ListColumns
                For Each dr As DataRow In mDataTable.Rows
                    If dr.Item(s).ToString <> String.Empty Then
                        dr.Item(s) = mConfig.GetEntryByKeyName(dr.Item(s).ToString).GetValue
                    End If
                Next
            Next

            ' associo il dt al datagrid
            Me.dgRisultati.DataSource = mDataTable

            If nPageIndex.Equals(m_InvalidInteger) Then
                ' In case all rows are shown, select the first page (index 0)
                Me.dgRisultati.PageIndex = 0
            Else
                Me.dgRisultati.PageIndex = nPageIndex
            End If

            Me.dgRisultati.DataBind()
            DrawPager(nPageIndex)

            ' per finire, settaggio di parametri custom sull'header
            column_count = column_offset

            Dim dt_forunit As DataTable = Me.dgRisultati.DataSource
            Dim _lista_uom As Dictionary(Of Integer, String) = New Dictionary(Of Integer, String)
            Dim r As Integer = 0
            If mScreen.ViewFields IsNot Nothing Then
                For Each s As Field In mScreen.ViewFields
                    If s.VisibleField Then

                        ' setto l'attributo name = nome della colonna del DB
                        Me.dgRisultati.HeaderRow.Cells(column_count).Attributes.Add("name", s.FieldDb)

                        ' setto l'attributo con l'unità di misura per le colonne con UnitAsp
                        If s.UnitAsp <> String.Empty AndAlso s.UnitDb <> String.Empty AndAlso s.DynamicUnitAsp = String.Empty Then
                            Me.dgRisultati.HeaderRow.Cells(column_count).Attributes.Add("MeasurementUnit", s.UnitAsp)
                        Else
                            If s.UnitAsp <> String.Empty AndAlso s.UnitDb <> String.Empty AndAlso s.DynamicUnitAsp <> String.Empty Then
                                For Each dr As System.Data.DataRow In dt_forunit.Rows
                                    _lista_uom.Add(r, dr(s.DynamicUnitAsp).ToString())
                                    r = r + 1
                                Next
                            End If
                        End If

                        ' setto gli attributi per la conversione dei timespan
                        If s.TimeSpanFormatAsp <> String.Empty Then
                            Me.dgRisultati.HeaderRow.Cells(column_count).Attributes.Add("TimeSpanFormatAsp", s.TimeSpanFormatAsp)
                        End If
                        If s.TimeSpanFormatDb <> String.Empty Then
                            Me.dgRisultati.HeaderRow.Cells(column_count).Attributes.Add("TimeSpanFormatDb", s.TimeSpanFormatDb)
                        End If

                        ' setto l'attributo per nascondere le colonne se vuote
                        If s.HideIfEmpty <> False Then
                            Me.dgRisultati.HeaderRow.Cells(column_count).Attributes.Add("HideIfEmpty", s.HideIfEmpty.ToString)
                        End If

                        If s.GetFieldType = EnumFieldType.FieldNumber Then
                            Me.dgRisultati.HeaderRow.Cells(column_count).Attributes.Add("FieldType", s.FieldType)
                        End If

                        If s.GetFieldType = EnumFieldType.FieldLedString Then
                            For Each dr_view As GridViewRow In dgRisultati.Rows
                                If s.FieldWithLed <> String.Empty Then
                                    Dim vf As Field = UsersGUI.tools.GetFieldByDbName(mScreen.ViewFields, s.FieldWithLed)
                                    If (vf IsNot Nothing) Then
                                        Dim vector_show_image_if_ledstring_is As String() = vf.ShowImageIfLedStringIs.Split(";")
                                        For i As Integer = 0 To vector_show_image_if_ledstring_is.GetUpperBound(0)
                                            Dim sValueToControl As String = String.Empty
                                            If s.EnableKeyEntry Then
                                                sValueToControl = mConfig.GetEntryByKeyName(vector_show_image_if_ledstring_is(i)).GetValue().ToUpper()
                                            Else
                                                sValueToControl = vector_show_image_if_ledstring_is(i).ToUpper()
                                            End If
                                            'Devo fare column_count + 1 perchè per questo tipo di FieldType Creo 2 colonne
                                            If (sValueToControl = dr_view.Cells(column_count + 1).Text.ToString().ToUpper()) Then
                                                dr_view.Cells(column_count).ToolTip = mConfig.GetEntryByKeyName("WAITING_TO_START").GetValue()
                                            End If
                                        Next
                                    End If
                                End If
                            Next
                        End If

                        column_count += 1
                    End If
                Next
            End If

            If mScreen.ViewFields IsNot Nothing Then
                For Each s As Field In mScreen.ViewFields
                    If s.VisibleField Then
                        If (Me.dgRisultati.Rows.Count > 0) Then
                            If s.UnitAsp <> String.Empty AndAlso s.UnitDb <> String.Empty AndAlso s.DynamicUnitAsp <> String.Empty Then
                                column_count = 0
                                For Each dr As GridViewRow In Me.dgRisultati.Rows
                                    Dim index_col As Integer = GetColumnIndexByName(dr, s.FieldDb)
                                    If (index_col >= 0) Then
                                        dr.Cells(index_col).Attributes.Add("MeasurementUnit", _lista_uom(column_count))
                                    End If
                                    column_count = column_count + 1
                                Next
                            End If
                        End If
                    End If
                Next
        End If

        End If
        ' mostro il datagrid solo dopo averlo popolato
        Me.dgRisultati.Visible = True

        ' chiamata allo script per settare la larghezza delle colonne
        myScript.InvokeJS(Me.Page, "ShowUCCont();")
        myScript.InvokeJS(Me.Page, "SetDataTable();")
    End Sub

    Private Function GetColumnIndexByName(row As GridViewRow, columnName As String) As Integer
        Dim columnIndex As Integer = 0
        Dim bTrovato As Boolean = False
        For Each cell As DataControlFieldCell In row.Cells

            If (CType(cell.ContainingField, BoundField)).DataField.Equals(columnName) Then
                bTrovato = True
                Exit For
            Else
                columnIndex = columnIndex + 1
            End If

        Next
        If (Not bTrovato) Then
            columnIndex = -1
        End If
        Return columnIndex
    End Function

    Private Sub DrawPager(ByVal nPageIndex As Integer)
        Dim record_count As Integer = 0
        Dim page_count_dbl As Double
        Dim page_count_int As Integer
        Dim pages As New List(Of ListItem)

        Dim sSelect As String = mScreen.GetSqlCount(mSqlWhere, GridViewSortExpression)

        mDataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

        If mDataTable IsNot Nothing AndAlso mDataTable.Rows.Count > 0 Then
            Int32.TryParse(mDataTable.Rows(0).Item("RowNum").ToString, record_count)

            page_count_dbl = CType((CType(record_count, Decimal) / Decimal.Parse(mScreen.LinesPerPage)), Double)
            page_count_int = CType(Math.Ceiling(page_count_dbl), Integer)

            div_pager_js.Attributes("data-pages-number") = page_count_int
            div_pager_js.Attributes("data-selected-page") = nPageIndex

            div_pager_js.Visible = True
        Else
            div_pager_js.Visible = False
        End If

    End Sub

    ''' <summary>
    ''' Handles the pager by checking the value of the corresponding controller field, which has as value the page to load
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Protected Sub pager_ControllerField_ValueChanged(ByVal sender As HiddenField, ByVal e As System.EventArgs) Handles pager_ControllerField.ValueChanged
        If Current.Session(mSessionId + "_sql_where") <> Nothing Then
            mSqlWhere = Current.Session(mSessionId + "_sql_where")
        End If

        Dim pageIdx As Integer
        If Integer.TryParse(sender.Value, pageIdx) Then
            SelectData(pageIdx)
        Else
            ' Nothing
        End If

        Me.DrawSumColumns()

        bPaginationChanged = True
    End Sub

    Protected Sub dgRisultati_Sorting(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewSortEventArgs) Handles dgRisultati.Sorting
        If Current.Session(mSessionId + "_sql_where") <> Nothing Then
            mSqlWhere = Current.Session(mSessionId + "_sql_where")
        End If

        GridViewSortExpression = e.SortExpression

        If GridViewSortDirection = System.Web.UI.WebControls.SortDirection.Ascending Then
            GridViewSortDirection = System.Web.UI.WebControls.SortDirection.Descending
        Else
            GridViewSortDirection = System.Web.UI.WebControls.SortDirection.Ascending
        End If

        Me.SortGridView(GridViewSortExpression, GridViewSortDirection)

        Me.DrawSumColumns()
    End Sub

    Private Property GridViewSortDirection() As System.Web.UI.WebControls.SortDirection
        Get
            If ViewState("sortDirection") Is Nothing Then

                If Not Current.Session(mSessionId + "_sortDirection") Is Nothing Then
                    ViewState("sortDirection") = Current.Session(mSessionId + "_sortDirection")
                Else
                    ViewState("sortDirection") = System.Web.UI.WebControls.SortDirection.Ascending
                End If

            End If
            Return DirectCast(ViewState("sortDirection"), System.Web.UI.WebControls.SortDirection)
        End Get
        Set(ByVal value As System.Web.UI.WebControls.SortDirection)
            ViewState("sortDirection") = value
            Current.Session(mSessionId + "_sortDirection") = value
        End Set
    End Property

    Private Property GridViewSortExpression() As String
        Get
            If ViewState("sortExpression") Is Nothing Then
                If Not Current.Session(mSessionId + "_sortExpression") Is Nothing Then
                    ViewState("sortExpression") = Current.Session(mSessionId + "_sortExpression")
                End If
            End If
            Return ViewState("sortExpression")
        End Get

        Set(ByVal value As String)
            ViewState("sortExpression") = value
            Current.Session(mSessionId + "_sortExpression") = ViewState("sortExpression")
        End Set
    End Property

    Private Sub SortGridView(ByVal sortExpression As String, ByVal direction As System.Web.UI.WebControls.SortDirection)
        GridViewSortExpression = sortExpression
        GridViewSortDirection = direction

        mSqlWhere = Current.Session(mSessionId + "_sql_where")

        SelectData()
    End Sub

    Protected Sub btnStartPlanning_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnStartPlanning.Click
        If GetParameter(mPageName & "_READY_TO_START").ToUpper = costanti.m_StringOn Then
            If GetParameter(mPageName & "_AUTOMATIC_MODE").ToUpper = costanti.m_StringOn Then
                SetParameter(mPageName & "_PLANNING_STATUS", costanti.m_StringOn)
            Else
                SetParameter(mPageName & "_START_REQUEST", costanti.m_StringOn)
            End If
            SetParameter(mPageName & "_START_BUTTON_ENABLED", costanti.m_StringOff)
            Me.btnStartPlanning.Enabled = False
            Me.btnStopPlanning.Enabled = True
            lblStatusPlanning.Text = mConfig.GetEntryByKeyName("START_PLANNING").GetValue()
        Else
            lblStatusPlanning.Text = mConfig.GetEntryByKeyName("AUTOMATION_NOT_READY_TO_START").GetValue
        End If

        myScript.InvokeJS(Me.Page, "ButtonEvents.PlannedCycle.StartPlanning('" & mPageName & "');")
    End Sub

    Protected Sub btnStopPlanning_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnStopPlanning.Click
        ' Cancel a pending start request
        SetParameter(mPageName & "_START_REQUEST", costanti.m_StringOff)
        ' Set the plan scrolling mode to off (only used when in automatic)
        SetParameter(mPageName & "_PLANNING_STATUS", costanti.m_StringOff)
        Me.btnStartPlanning.Enabled = True
        Me.btnStopPlanning.Enabled = False
        lblStatusPlanning.Text = String.Empty

        myScript.InvokeJS(Me.Page, "ButtonEvents.PlannedCycle.StopPlanning('" & mPageName & "');")
    End Sub

    Protected Sub btnPlanningMode_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnPlanningMode.Click
        If GetParameter(mPageName & "_AUTOMATIC_MODE").ToUpper = costanti.m_StringOn Then
            ' Sicurezza : disabilito scorrimento automatico
            SetParameter(mPageName & "_PLANNING_STATUS", costanti.m_StringOff)
            ' set system parameter AUTOMATIC_MODE al valore OFF
            SetParameter(mPageName & "_AUTOMATIC_MODE", costanti.m_StringOff)
            Me.btnStartPlanning.Text = mConfig.GetEntryByKeyName("START_NEXT_BUTTON").GetValue

            If GetParameter(mPageName & "_START_BUTTON_ENABLED").ToUpper = costanti.m_StringOn Then
                Me.btnStartPlanning.Enabled = True
                Me.btnStopPlanning.Enabled = False
            Else
                Me.btnStartPlanning.Enabled = False
                Me.btnStopPlanning.Enabled = True
            End If
            Me.btnStopPlanning.Visible = False
            Me.btnPlanningMode.Text = mConfig.GetEntryByKeyName("SWITCH_TO_AUTOMATIC_BUTTON").GetValue
        Else
            ' Sicurezza : disabilito scorrimento automatico
            SetParameter(mPageName & "_PLANNING_STATUS", costanti.m_StringOff)
            ' set system parameter AUTOMATIC_MODE al valore ON
            SetParameter(mPageName & "_AUTOMATIC_MODE", costanti.m_StringOn)
            Me.btnStartPlanning.Text = mConfig.GetEntryByKeyName("START_SCHEDULING_BUTTON").GetValue

            If GetParameter(mPageName & "_START_BUTTON_ENABLED").ToUpper = costanti.m_StringOn Then
                Me.btnStartPlanning.Enabled = True
                Me.btnStopPlanning.Enabled = False
            Else
                Me.btnStartPlanning.Enabled = False
                Me.btnStopPlanning.Enabled = True
            End If
            Me.btnStopPlanning.Visible = True
            Me.btnPlanningMode.Text = mConfig.GetEntryByKeyName("SWITCH_TO_SEMIAUTOMATIC_BUTTON").GetValue

        End If

        myScript.InvokeJS(Me.Page, "ButtonEvents.PlannedCycle.PlanningMode('" & mPageName & "');")
    End Sub

    Protected Sub TimerReloadPage_Tick(ByVal sender As Object, ByVal e As System.EventArgs) Handles TimerReloadPage.Tick
        If mScreen.FilterColumnsName IsNot Nothing AndAlso mScreen.FilterColumnsName.Count > 0 Then
            For Each f As FilterColumnName In mScreen.FilterColumnsName
                If Current.Request.QueryString(f.FieldDB) IsNot Nothing Then
                    mSqlWhere &= " " & f.FieldDB & "='" & Current.Request.QueryString(f.FieldDB) & "' "
                End If
            Next

            Current.Session(mSessionId & "_sql_where_base") = mSqlWhere

            SelectData()
        Else
            Current.Session(mSessionId + "_sql_where_base") = String.Empty

            If Current.Session(mSessionId + "_sql_where") = Nothing Then
                mSqlWhere = String.Empty
                SelectData()
            Else
                mSqlWhere = Current.Session(mSessionId + "_sql_where")
                SelectData()
            End If
        End If

        Me.DrawSumColumns()
    End Sub

    Protected Sub btnStartQuery_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnStartQuery.Click
        Dim s_name As String = String.Empty
        Dim n_value As String = String.Empty
        Dim bPrimaVolta As Boolean = True
        Dim temp_date As DateTime = Nothing

        For Each s As System.Web.UI.HtmlControls.HtmlTableRow In Me.tblQuery.Rows
            For Each c As System.Web.UI.HtmlControls.HtmlTableCell In s.Cells
                c.Attributes.Remove("applied")

                For Each d As Control In c.Controls
                    s_name = d.ID
                    If TypeOf d Is TextBox Then
                        If CType(d, TextBox).ID.EndsWith("_DATE_DA") Then
                            n_value = CType(d, TextBox).Text.Trim()
                            If s_name <> String.Empty AndAlso n_value <> String.Empty Then
                                If Not bPrimaVolta Then
                                    mSqlWhere &= " AND "
                                End If

                                Dim segno As String = CType(WebTools.tools.FindControlRecursive(Me, s_name & "_SEGNO"), DropDownList).SelectedValue.ToString

                                If (n_value.Contains("AM") Or n_value.Contains("PM")) Then
                                    temp_date = Date.ParseExact(n_value,
                                                mConfig.GetLanguage().FormatDateTime,
                                                System.Threading.Thread.CurrentThread.CurrentCulture)
                                Else
                                    temp_date = Date.ParseExact(n_value & ":00",
                                               mConfig.GetLanguage().FormatDateTime,
                                               System.Threading.Thread.CurrentThread.CurrentCulture)
                                End If

                                s_name = s_name.Substring(0, s_name.LastIndexOf("_DATE_DA"))

                                mSqlWhere &= " " & s_name & " " & segno & UsersGUI.tools.PrepareDateForSQL(temp_date)
                                bPrimaVolta = False

                                c.Attributes.Add("applied", "applied")
                            End If
                        ElseIf CType(d, TextBox).ID.EndsWith("_DATE_A") Then
                            n_value = CType(d, TextBox).Text.Trim()
                            If s_name <> String.Empty AndAlso n_value <> String.Empty Then
                                If Not bPrimaVolta Then
                                    mSqlWhere &= " AND "
                                End If

                                Dim segno As String = CType(WebTools.tools.FindControlRecursive(Me, s_name & "_SEGNO"), DropDownList).SelectedValue.ToString

                                If (n_value.Contains("AM") Or n_value.Contains("PM")) Then
                                    temp_date = Date.ParseExact(n_value,
                                                mConfig.GetLanguage().FormatDateTime,
                                                System.Threading.Thread.CurrentThread.CurrentCulture)
                                Else
                                    temp_date = Date.ParseExact(n_value & ":00",
                                               mConfig.GetLanguage().FormatDateTime,
                                               System.Threading.Thread.CurrentThread.CurrentCulture)
                                End If

                                s_name = s_name.Substring(0, s_name.LastIndexOf("_DATE_A"))

                                mSqlWhere &= s_name & " " & segno & UsersGUI.tools.PrepareDateForSQL(temp_date)
                                bPrimaVolta = False

                                c.Attributes.Add("applied", "applied")
                            End If
                        Else
                            n_value = CType(d, TextBox).Text.Trim()
                            If s_name <> String.Empty And s_name <> " " Then
                                If (s_name.IndexOf("_MIN") > 0) AndAlso (n_value <> String.Empty) Then
                                    If Not bPrimaVolta Then
                                        mSqlWhere &= " AND "
                                    End If
                                    s_name = s_name.Substring(0, s_name.IndexOf("_MIN"))
                                    mSqlWhere &= " " & s_name & " >= " & n_value
                                    bPrimaVolta = False
                                    c.Attributes.Add("applied", "applied")
                                ElseIf (s_name.IndexOf("_MAX") > 0) AndAlso n_value <> String.Empty Then
                                    If Not bPrimaVolta Then
                                        mSqlWhere &= " AND "
                                    End If
                                    s_name = s_name.Substring(0, s_name.IndexOf("_MAX"))
                                    mSqlWhere &= s_name & " <= " & n_value
                                    bPrimaVolta = False
                                    c.Attributes.Add("applied", "applied")
                                Else
                                    If n_value <> String.Empty Then
                                        If Not bPrimaVolta Then
                                            mSqlWhere &= " AND "
                                        End If
                                        mSqlWhere &= s_name & " LIKE '%" & UsersGUI.tools.SqlStr(n_value) & "%'"
                                        bPrimaVolta = False
                                        c.Attributes.Add("applied", "applied")
                                    End If
                                End If
                            End If
                        End If
                    ElseIf TypeOf d Is myDropDownList Then
                        If CType(d, myDropDownList).SelectedValue IsNot Nothing Then
                            n_value = CType(d, myDropDownList).SelectedValue
                            If s_name <> String.Empty Then

                                If CType(d, myDropDownList).FieldObject.QueryFieldName <> String.Empty Then
                                    s_name = CType(d, myDropDownList).FieldObject.QueryFieldName
                                End If

                                If n_value <> String.Empty Then
                                    If Not bPrimaVolta Then
                                        mSqlWhere &= " AND "
                                    End If
                                    mSqlWhere &= s_name & " = '" & n_value & "'"
                                    bPrimaVolta = False
                                    c.Attributes.Add("applied", "applied")

                                End If
                            End If
                        End If
                    ElseIf TypeOf d Is myCheckBox Then
                        If CType(d, myCheckBox).Checked Then
                            Select Case CType(d, myCheckBox).FieldObject.GetFieldType
                                Case EnumFieldType.FieldCheckBoxOneZero
                                    n_value = m_StringUno

                                Case EnumFieldType.FieldCheckBoxOnOff
                                    n_value = m_StringOn

                                Case EnumFieldType.FieldCheckBoxYesNo
                                    n_value = m_StringYes
                            End Select

                            If Not bPrimaVolta Then
                                mSqlWhere &= " AND "
                            End If
                            mSqlWhere &= s_name & " = '" & n_value & "'"
                            bPrimaVolta = False
                            c.Attributes.Add("applied", "applied")
                        End If
                    End If
                Next
            Next
        Next

        Current.Session(mSessionId + "_sql_where") = mSqlWhere
        Current.Session(mSessionId + "_sql_where_print") = mSqlWhere

        If bPaginationChanged Then
            SelectData(pager_ControllerField.Value)
        Else
            pager_ControllerField.Value = 0
            SelectData()
        End If

        Me.DrawSumColumns()
    End Sub

    ''' <summary>
    ''' Handles the visibility of the div query by checking the corresponding controller field, which has as value "1" (show) or "0" (hide)
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Protected Sub divQueryVisibility_ControllerField_ValueChanged(sender As Object, e As System.EventArgs) Handles divQueryVisibility_ControllerField.ValueChanged
        If CType(sender, HiddenField).Value = "1" Then
            Me.btnViewSearch.Text = mConfig.GetEntryByKeyName("Chiudi").GetValue
            Me.btnViewSearch.Attributes.Add("myValue", "1")
        ElseIf CType(sender, HiddenField).Value = "0" Then
            Me.btnViewSearch.Text = mConfig.GetEntryByKeyName("Apri").GetValue
            Me.btnViewSearch.Attributes.Add("myValue", "0")
        End If
    End Sub

    Protected Sub btnViewSearch_Click(sender As Object, e As System.EventArgs) Handles btnViewSearch.Click
        If Me.btnViewSearch.Text = mConfig.GetEntryByKeyName("Apri").GetValue Then
            Me.btnViewSearch.Text = mConfig.GetEntryByKeyName("Chiudi").GetValue
            Me.btnViewSearch.Attributes.Add("myValue", "1")
        Else
            Me.btnViewSearch.Text = mConfig.GetEntryByKeyName("Apri").GetValue
            Me.btnViewSearch.Attributes.Add("myValue", "0")
        End If

        ' ridisegno le sum column
        If Current.Session(mSessionId + "_sql_where") = Nothing Then
            mSqlWhere = String.Empty
        Else
            mSqlWhere = Current.Session(mSessionId + "_sql_where")
        End If

        myScript.InvokeJS(Me.Page, "ShowUCCont();")
        myScript.InvokeJS(Me.Page, "SetDataTable();")
        Me.DrawSumColumns()
    End Sub

    Private Sub DrawSumColumns()
        Dim sSelect As String = String.Empty
        Dim tot As Double = 0.0

        If mScreen.SumColumns IsNot Nothing AndAlso mScreen.SumColumns.Count > 0 Then
            Me.tblSum.Rows.Clear()

            If mScreen.SumColumns.Count > 0 Then
                Dim row_th As New System.Web.UI.HtmlControls.HtmlTableRow
                Dim row_td As New System.Web.UI.HtmlControls.HtmlTableRow

                row_th.Attributes.Add("class", "rowHeader")

                For Each s As SumColumn In mScreen.SumColumns

                    sSelect = mScreen.GetSqlSelectSum(mSqlWhere, s.Field, s.DynamicUnitAsp)
                    mDataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, False)

                    If mDataTable Is Nothing OrElse mDataTable.Rows.Count <= 0 Then
                        Return
                    End If

                    For Each dr As System.Data.DataRow In mDataTable.Rows
                        If dr.Item(0).ToString <> String.Empty Then
                            tot = Double.Parse(dr.Item(0).ToString)

                        If s.UnitASP <> String.Empty AndAlso s.UnitDB <> String.Empty Then
                                If (s.DynamicUnitAsp = String.Empty) Then
                            tot *= UsersGUI.tools.GetConversionFactorToASP(s.UnitDB, s.UnitASP)
                                Else
                                    tot *= UsersGUI.tools.GetConversionFactorToASP(s.UnitDB, dr.Item(s.DynamicUnitAsp).ToString())
                                End If
                        End If

                        tot = Math.Round(tot, s.nDecimal)
                    Else
                        tot = 0
                    End If

                    ' 1. sigma
                    ' 1.1 header
                    Dim th1 As New System.Web.UI.HtmlControls.HtmlTableCell("th")
                    row_th.Cells.Add(th1)
                    ' 1.2 body
                    Dim tc1 As New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim l1 As New Label
                    l1.Text = mConfig.GetEntryByKeyName("SIGMA").GetValue
                    l1.CssClass = "span-sigma"
                    tc1.Controls.Add(l1)
                    row_td.Cells.Add(tc1)

                    ' 2. label
                    ' 2.1 header
                    Dim th2 As New System.Web.UI.HtmlControls.HtmlTableCell("th")
                    row_th.Cells.Add(th2)
                    ' 2.2 body
                    Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim l2 As New Label
                    l2.Text = mConfig.GetEntryByKeyName(s.FieldName).GetValue & " ="
                    tc2.Controls.Add(l2)
                    row_td.Cells.Add(tc2)

                    ' 3. value
                    ' 3.1 header
                    Dim th3 As New System.Web.UI.HtmlControls.HtmlTableCell("th")

                    ' setto l'attributo name = nome della colonna del DB
                    th3.Attributes.Add("name", s.Field)

                    ' setto l'attributo con l'unità di misura per le colonne con UnitAsp
                    If s.UnitASP <> String.Empty AndAlso s.UnitDB <> String.Empty Then
                            If (s.DynamicUnitAsp <> String.Empty) Then
                                th3.Attributes.Add("MeasurementUnit", dr.Item(s.DynamicUnitAsp).ToString())
                            Else
                        th3.Attributes.Add("MeasurementUnit", s.UnitASP)
                    End If
                        End If

                    ' setto gli attributi per la conversione dei timespan
                    If s.TimeSpanFormatDB <> String.Empty Then
                        th3.Attributes.Add("TimeSpanFormatDB", s.TimeSpanFormatDB)
                    End If
                    If s.TimeSpanFormatASP <> String.Empty Then
                        th3.Attributes.Add("TimeSpanFormatASP", s.TimeSpanFormatASP)
                    End If

                    row_th.Cells.Add(th3)

                    ' 3.2 body
                    Dim tc3 As New System.Web.UI.HtmlControls.HtmlTableCell
                    Dim l3 As New Label
                    l3.Text = tot.ToString()
                    tc3.Controls.Add(l3)
                    row_td.Cells.Add(tc3)

                    ' 4. separator
                    ' 4.1 header
                    Dim th4 As New System.Web.UI.HtmlControls.HtmlTableCell("th")
                    th4.Attributes.Add("class", "tblSumSeparator")
                    row_th.Cells.Add(th4)
                    ' 4.2 body
                    Dim tc4 As New System.Web.UI.HtmlControls.HtmlTableCell
                    row_td.Cells.Add(tc4)
                    Next

                Next

                Me.tblSum.Rows.Add(row_th)
                Me.tblSum.Rows.Add(row_td)
            End If

            Me.dSumColumns.Visible = True
        End If
    End Sub

    Protected Sub btnRefreshBatchSize_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnRefreshBatchSize.Click
        Dim req As New CommandInterface(EnumRequest.UploadScalesBatchSize, CommonDefines.Defines.ReservedInvalidId.InvalidId)
        req.PostUniqueRequest()

        Dim bOk As Boolean = False
        While Not bOk
            bOk = myFunction.WaitCompleted(mScreen, EnumWaitOperation.Upload)
        End While

    End Sub

    Protected Sub dgRisultati_Load(sender As Object, e As EventArgs)
        myScript.InvokeJS(Me.Page, "CreatePager();")
    End Sub

End Class