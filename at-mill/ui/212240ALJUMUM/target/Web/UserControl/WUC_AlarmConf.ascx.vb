﻿Imports UsersGUI
Imports WebDataBaseLayer

Partial Class WUC_WUC_AlarmConf
    Inherits UserControl

    Public m_config As UsersGUI.config
    Private mMenuName As String
    Public mPageName As String
    Private mScreen As Screen
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Private msgGeneric As New myAlert
    Private m_plant_id As Integer

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded
        If Me.Visible Then
            m_config = CType(Application("Config"), config)

            If Not WebTools.tools.GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            ' message init
            msgGeneric.Init(Me.Page, myMessageBoxParam.NoParameter)

            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
                lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If

            If Not Page.IsPostBack Then
                Me.btnConferma.Text = m_config.GetEntryByKeyName("SUBMIT_BUTTON").GetValue()
            End If

            If Current.Request("plant_id") Is Nothing Then
                Exit Sub
            End If
            m_plant_id = Current.Request("plant_id").ToString

            Me.GetValues()
        End If
    End Sub

    Private Sub GetValues()
        Dim strSQL As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        strSQL = "SELECT INDICATORS_PER_SCALE.PARAMETER_ENA, INDICATORS_PER_SCALE.PARAMETER_MIN, INDICATORS_PER_SCALE.PARAMETER_MAX, " &
                    "SCALES.DESCRIPTION, SCALES.PLANT_ID, INDICATORS_PER_SCALE.IND_ID " &
                    "FROM INDICATORS_PER_SCALE INNER JOIN SCALES ON INDICATORS_PER_SCALE.SCALE_ID = SCALES.ID " &
                    "WHERE SCALES.PLANT_ID = " & m_plant_id & " AND " &
                    "INDICATORS_PER_SCALE.PARAMETER_ENA IS NOT NULL AND " &
                    "INDICATORS_PER_SCALE.PARAMETER_MIN IS NOT NULL AND " &
                    "INDICATORS_PER_SCALE.PARAMETER_MAX IS NOT NULL " &
                    "ORDER BY INDICATORS_PER_SCALE.IND_ID, INDICATORS_PER_SCALE.SCALE_ID"

        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        Dim nCount_Row As Integer = 0
        If dt IsNot Nothing Then

            ' header
            Dim row_header As New System.Web.UI.HtmlControls.HtmlTableRow
            row_header.BgColor = "#4682B4"
            row_header.Attributes("class") = "myRowHeader"
            Me.tblAlarms.Rows.Add(row_header)

            Dim th As New System.Web.UI.HtmlControls.HtmlTableCell
            th.InnerText = m_config.GetEntryByKeyName("SCALES").GetValue
            tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(th)

            Dim th2 As New System.Web.UI.HtmlControls.HtmlTableCell
            th2.InnerText = m_config.GetEntryByKeyName("ENABLE").GetValue
            tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(th2)

            Dim th3 As New System.Web.UI.HtmlControls.HtmlTableCell
            th3.InnerText = m_config.GetEntryByKeyName("MIN").GetValue
            tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(th3)

            Dim th4 As New System.Web.UI.HtmlControls.HtmlTableCell
            th4.InnerText = m_config.GetEntryByKeyName("MAX").GetValue
            tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(th4)

            For Each dr As Data.DataRow In dt.Rows

                Dim row As New System.Web.UI.HtmlControls.HtmlTableRow
                If nCount_Row Mod 2 = 0 Then
                    row.Attributes("class") = "rowEven"
                Else
                    row.Attributes("class") = "rowOdd"
                End If
                Me.tblAlarms.Rows.Add(row)

                ' descr
                Dim tc As New System.Web.UI.HtmlControls.HtmlTableCell
                If dr("IND_ID") <> EnumGraphIndicators.Loss Then
                    tc.InnerText = m_config.GetEntryByKeyName(dr.Item("DESCRIPTION")).GetValue
                Else
                    tc.InnerText = m_config.GetEntryByKeyName("YIELDS_MILL_LOSS").GetValue
                End If
                tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(tc)

                ' enable
                Dim tc2 As New System.Web.UI.HtmlControls.HtmlTableCell
                Dim chk As New CheckBox
                chk.ID = dr.Item("PARAMETER_ENA").ToString
                If WebTools.tools.GetParameter(dr.Item("PARAMETER_ENA").ToString).Trim = costanti.m_StringOn Then
                    chk.Checked = True
                Else
                    chk.Checked = False
                End If
                tc2.Attributes("class") = "txtCenter"
                tc2.Controls.Add(chk)
                tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(tc2)

                ' min
                Dim tc3 As New System.Web.UI.HtmlControls.HtmlTableCell
                Dim t1 As New myTextBox
                t1.ID = dr.Item("PARAMETER_MIN").ToString
                t1.Text = WebTools.tools.GetParameter(dr.Item("PARAMETER_MIN").ToString)
                t1.Width = System.Web.UI.WebControls.Unit.Pixel(50)
                t1.CssClass = "textbox-text-right"
                t1.MaxLength = 5
                tc3.Controls.Add(t1)
                tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(tc3)

                Dim l1 As New Label
                l1.Text = "%"
                tc3.Controls.Add(l1)
                tc3.Attributes("class") = "txtCenter"
                tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(tc3)

                ' max
                Dim tc5 As New System.Web.UI.HtmlControls.HtmlTableCell
                Dim t2 As New myTextBox
                t2.ID = dr.Item("PARAMETER_MAX").ToString
                t2.Text = WebTools.tools.GetParameter(dr.Item("PARAMETER_MAX").ToString)
                t2.Width = System.Web.UI.WebControls.Unit.Pixel(50)
                t2.CssClass = "textbox-text-right"
                t2.MaxLength = 5
                tc5.Controls.Add(t2)
                tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(tc5)

                Dim l2 As New Label
                l2.Text = "%"
                tc5.Controls.Add(l2)
                tc5.Attributes("class") = "txtCenter"
                tblAlarms.Rows(tblAlarms.Rows.Count - 1).Cells.Add(tc5)

                nCount_Row += 1

            Next
        End If
    End Sub

    Protected Sub btnConferma_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnConferma.Click
        Dim strSQL As String = String.Empty
        Dim dt As Data.DataTable = Nothing

        Dim ctr As Control
        Dim min_val As Double = 0.0
        Dim max_val As Double = 0.0
        Dim error_msg As String = String.Empty

        ' valido i valori
        strSQL = "SELECT INDICATORS_PER_SCALE.PARAMETER_ENA, INDICATORS_PER_SCALE.PARAMETER_MIN, INDICATORS_PER_SCALE.PARAMETER_MAX " &
                    "FROM INDICATORS_PER_SCALE INNER JOIN SCALES ON INDICATORS_PER_SCALE.SCALE_ID = SCALES.ID " &
                    "WHERE SCALES.PLANT_ID = " & m_plant_id & " AND " &
                        "INDICATORS_PER_SCALE.PARAMETER_ENA IS NOT NULL AND " &
                        "INDICATORS_PER_SCALE.PARAMETER_MIN IS NOT NULL AND " &
                        "INDICATORS_PER_SCALE.PARAMETER_MAX IS NOT NULL " &
                    "ORDER BY INDICATORS_PER_SCALE.IND_ID, INDICATORS_PER_SCALE.SCALE_ID"
        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        For Each dr As Data.DataRow In dt.Rows

            ctr = UsersGUI.tools.FindControlRecursive(Me, dr.Item("PARAMETER_MIN").ToString)
            min_val = UsersGUI.tools.WebInputDoubleParse(CType(ctr, myTextBox).Text, 1)

            ctr = UsersGUI.tools.FindControlRecursive(Me, dr.Item("PARAMETER_MAX").ToString)
            max_val = UsersGUI.tools.WebInputDoubleParse(CType(ctr, myTextBox).Text, 1)

            If min_val >= max_val Then
                WebTools.myErrorMessage.AppendMinValueGraterEqualThanMaxValue(error_msg, m_config, min_val, max_val)
            End If
        Next

        If error_msg.Length > 0 Then
            msgGeneric.Show(error_msg)

            Return
        End If

        ' li salvo
        strSQL = "SELECT SYSTEM_PARAMETERS.PARAMETER_NAME, SYSTEM_PARAMETERS.ID " &
                 "FROM SCALES INNER JOIN INDICATORS_PER_SCALE " &
                 "ON INDICATORS_PER_SCALE.SCALE_ID = SCALES.ID INNER JOIN SYSTEM_PARAMETERS " &
                 "ON INDICATORS_PER_SCALE.PARAMETER_MIN = SYSTEM_PARAMETERS.PARAMETER_NAME OR  " &
                 "INDICATORS_PER_SCALE.PARAMETER_MAX = SYSTEM_PARAMETERS.PARAMETER_NAME OR  " &
                 "INDICATORS_PER_SCALE.PARAMETER_ENA = SYSTEM_PARAMETERS.PARAMETER_NAME  " &
                 "WHERE SCALES.PLANT_ID = " & m_plant_id &
                 "ORDER BY 2"
        dt = DataBase.ExecuteSQL_DataTable(strSQL, False)

        For Each dr As Data.DataRow In dt.Rows
            ctr = UsersGUI.tools.FindControlRecursive(Me, dr.Item("PARAMETER_NAME").ToString)
            If ctr IsNot Nothing Then
                Dim ac As New AlarmConf
                ac.Id = Long.Parse(dr.Item("ID").ToString)

                If TypeOf ctr Is TextBox Then
                    ac.Value = UsersGUI.tools.WebInputDoubleParse(CType(ctr, myTextBox).Text, 1)

                ElseIf TypeOf ctr Is CheckBox Then
                    ac.Value = costanti.m_StringOff
                    If CType(ctr, CheckBox).Checked Then
                        ac.Value = costanti.m_StringOn
                    End If
                End If

                ac.Save()
            End If
        Next
    End Sub

End Class