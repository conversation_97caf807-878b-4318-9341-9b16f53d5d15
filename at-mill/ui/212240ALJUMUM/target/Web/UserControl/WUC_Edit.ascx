﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_Edit.ascx.vb" Inherits="WUC_WUC_Edit" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>
<%@ Register Src="~/UserControl/WUC_TopMenu.ascx" TagPrefix="WUCTopMenu" TagName="TopMenu" %>
<%@ Register Src="~/UserControl/WUC_LoginTop.ascx" TagPrefix="WUCLoginTop" TagName="LoginTop" %>
<%@ Register Src="~/UserControl/WUC_Warnings.ascx" TagPrefix="WUCWarning" TagName="Warning" %>

<div class="tabMrg3 tabMrg3Top">
    <asp:HiddenField ID="HiddenAskUserUnfilledDataCycle" runat="server" />
    <asp:HiddenField ID="HiddenAskUserConfirmDataCycle" runat="server" />
    <asp:HiddenField ID="HiddenAbortCycle" runat="server" />
    <asp:Timer ID="TimerReloadPage" runat="server" Interval="30000" Enabled="false"></asp:Timer>
    <asp:Timer ID="TimerReloadButtons" runat="server" Interval="30000" Enabled="false"></asp:Timer>

    <div id="print_area">
        <div id="customerHeader"></div>
        <table id="titleTable" class="tabMrg3">
            <tr class="noPrint">
                <td class="bgImg2">&nbsp;
                </td>
                <td class="tabBground1">&nbsp;
                </td>
            </tr>
            <tr>
                <td id="pageTitle" colspan="2" class="txtLeft txtBold">
                    <%= If(mPageName IsNot Nothing, m_config.GetEntryByKeyName("MAIN_" & mPageName & "_TITLE").GetValue(), "")%>
                </td>
            </tr>
        </table>
        <table class="tabDimRid1">
            <tr>
                <td>
                    <div id="dView" runat="server" visible="true">
                        <table id="tblHeader" runat="server" visible="true"></table>
                        <hr style="display: none;" />
                        <table id="tblEdit" class="txtLeft" runat="server">
                        </table>
                        <br />
                        <table id="tblButtons" runat="server" class="noPrint">
                            <tr>
                                <td class="txtLeft">
                                    <asp:Button ID="btnSubmit" runat="server" OnClientClick="myLoader.start()" CssClass="btnGeneral" />&nbsp;
                                <asp:Button ID="btnCancel" runat="server" CssClass="btnGeneral" />
                                </td>
                            </tr>
                        </table>
                        <br />
                        <table id="tblCompare" runat="server" class="noPrint">
                        </table>
                    </div>
                    <div id="dError" runat="server" visible="false">
                        <table id="Table1" runat="server">
                            <tr>
                                <td>
                                    <asp:Label runat="server" ID="lblError" class="lblError"></asp:Label>
                                </td>
                            </tr>
                        </table>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>

<div id="div_blanket" runat="server" class="blanket" blanket></div>

<script type="text/javascript">
    blanket.show();
</script>