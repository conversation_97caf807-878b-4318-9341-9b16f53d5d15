﻿Imports UsersGUI
Imports WebTools.tools

Partial Class WUC_WUC_Pdf
    Inherits UserControl

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing
    Private mTopMenuName As String = String.Empty
    Private mMenuName As String = String.Empty
    Private mControl As String = String.Empty

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded
        If Me.Visible Then
            m_config = CType(Application("Config"), config)

            If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            'Access rights
            CheckPageDB(m_config, mMenuName, mPageName)
            If Not CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
                lblError.Text = m_config.GetEntryByKeyName("ACCESS_DENIED").GetValue
                Me.dError.Visible = True
                Me.dView.Visible = False
                Exit Sub
            End If

            myScript.InvokeJS(Me.Page, "SetPdfViewHeight();")
        End If
    End Sub

End Class