﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_PlantConf.ascx.vb"
    Inherits="WUC_PlantConf" EnableTheming="true" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>

<div>
    <table class="tabMrg3">
        <tr>
            <td class="bgImg2">&nbsp;
            </td>
            <td class="tabBground1">&nbsp;
            </td>
        </tr>
        <tr>
            <td class="txtBold" colspan="2">
                <%= m_config.GetEntryByKeyName("MAIN_SYSTEM_PARAMETERS_TITLE").GetValue()%>
            </td>
        </tr>
    </table>
    <table class="tabMrg3 tabMrg3Top">
        <tr>
            <td>
                <div id="dView" runat="server" visible="true">
                    <table>
                        <tr class="rowSection">
                            <td colspan="2">
                                <strong><%=m_config.GetEntryByKeyName("MAIN_SYSTEM_PARAMETERS_TITLE").GetValue()%></strong>
                            </td>
                        </tr>
                        <tr class="rowOdd">
                            <td>
                                <%=lblHourStartDay%>:
                            </td>
                            <td>
                                <asp:TextBox ID="parHourStartDay" runat="server" MaxLength="5" Width="50px" CssClass="text"></asp:TextBox>&nbsp;<%=lblInfoHourStartDay%>
                                <asp:Label ID="lblErrorHourStartDay" runat="server" Visible="false" Text="" CssClass="txtLeft txtSmall"></asp:Label>
                            </td>
                        </tr>
                        <tr class="rowEven">
                            <td>
                                <%=lblFirstShiftHour%>:
                            </td>
                            <td>
                                <asp:TextBox ID="parFirstShiftHour" runat="server" MaxLength="5" Width="50px" CssClass="text"></asp:TextBox>&nbsp;<%=lblInfoFirstShiftHour%>
                                <asp:Label ID="lblErrorFirstShiftHour" runat="server" Visible="false" Text="" CssClass="txtRed txtSmall"></asp:Label>
                            </td>
                        </tr>
                        <tr class="rowOdd">
                            <td>
                                <%=lblSecondShiftHour%>:
                            </td>
                            <td>
                                <asp:TextBox ID="parSecondShiftHour" runat="server" MaxLength="5" Width="50px" CssClass="text"></asp:TextBox>&nbsp;<%=lblInfoSecondShiftHour%>
                                <asp:Label ID="lblErrorSecondShiftHour" runat="server" Visible="false" Text="" CssClass="txtRed txtSmall"></asp:Label>
                            </td>
                        </tr>
                        <tr class="rowEven">
                            <td>
                                <%=lblThirdShiftHour%>:
                            </td>
                            <td>
                                <asp:TextBox ID="parThirdShiftHour" runat="server" MaxLength="5" Width="50px" CssClass="text"></asp:TextBox>&nbsp;<%=lblInfoThirdShiftHour%>
                                <asp:Label ID="lblErrorThirdShiftHour" runat="server" Visible="false" Text="" CssClass="txtRed txtSmall"></asp:Label>
                            </td>
                        </tr>
                        <tr class="rowOdd">
                            <td>
                                <%=lblWeekFirstDay%>:
                            </td>
                            <td>
                                <asp:TextBox ID="parWeekFirstDay" runat="server" MaxLength="2" Width="50px" CssClass="text"></asp:TextBox>&nbsp;<%=lblInfoWeekFirstDay%>
                                <asp:Label ID="lblErrorWeekFirstDay" runat="server" Visible="false" Text="" CssClass="txtRed txtSmall"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">&nbsp;
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <asp:Button ID="btnUpdatePlantConf" runat="server" CssClass="btnGeneral" />&nbsp;
                            &nbsp;<asp:Button ID="btnCancelPlantConf" runat="server" CssClass="btnGeneral" />
                            </td>
                        </tr>
                    </table>
                </div>
                <div id="dError" runat="server" visible="false">
                    <table id="Table2" runat="server">
                        <tr>
                            <td>
                                <asp:Label runat="server" ID="lblError" CssClass="lblError"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</div>