﻿Imports UsersGUI
Imports WebTools.tools
Imports CommonDefines.Defines

Partial Class WUC_WUC_New
    Inherits UserControl

    Public mPageName As String
    Private mMenuName As String
    Public mScreen As Screen
    Private dt As Data.DataTable
    Public m_config As config
    Private sql_where As String
    Private sControl As String = String.Empty
    Private bResult As String
    Private adim As AddMenuItemName = Nothing
    Private mTopMenuName As String = String.Empty
    Private msgGeneric As New myAlert
    Private msgAskUserUnfilledDataCycle As New myConfirm
    Private msgAskUserConfirmDataCycle As New myConfirm

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded and the session is set in the URL
        If Me.Visible Then
            m_config = CType(Application("Config"), config)
            If Not GetWebParams(m_config, mPageName, mMenuName, sControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            ' message init
            msgGeneric.Init(<PERSON><PERSON>Page, myMessageBoxParam.NoParameter)
            msgAskUserUnfilledDataCycle.Init(Me.Page, Me.HiddenAskUserUnfilledDataCycle, myMessageBoxParam.NoParameter)
            msgAskUserConfirmDataCycle.Init(Me.Page, Me.HiddenAskUserConfirmDataCycle, myMessageBoxParam.NoParameter)

            'Access rights
            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Adding) Then
                lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue
                Me.dError.Visible = True
                Me.dView.Visible = False
                Exit Sub
            End If

            Me.btnSubmit.Text = m_config.GetEntryByKeyName("SUBMIT_BUTTON").GetValue()
            Me.btnCancel.Text = m_config.GetEntryByKeyName("CANCEL_BUTTON").GetValue()

            If mScreen.ComparesWeb IsNot Nothing AndAlso mScreen.ComparesWeb(0) IsNot Nothing AndAlso mScreen.ComparesWeb IsNot Nothing Then
                Me.btnSubmit.ValidationGroup = mScreen.ComparesWeb(0).ValidationGroup
            End If

            WebTools.drawings.DrawInsertFields(tblNew, mScreen, m_config)
            WebTools.drawings.DrawCompares(tblCompare, mScreen, m_config, EnumCompareType.Insert)
            GenericsEvents.SetEvents.UpdateCallEventFields(Me, mScreen, m_config)

            If Not Page.IsPostBack Then
                For Each f As Field In mScreen.EditFields
                    If f.CallEvent <> String.Empty Then
                        ' ho un qualche tipo di call event specificato a livello di config.xml da gestire
                        GenericsEvents.EventDelegate.AppendCallEvent(Me, f)
                    End If
                Next
            End If

            ' chiamata alle funzioni per settare i valori di default degli screen e dei cicli
            myFunction.SetDefaultDataScreen(mScreen, Me)
            myFunction.SetDefaultDataCycle(mScreen, Me)

            ' chiamata allo script custom per nascondere le righe con elementi non visibili
            myScript.InvokeJS(Me.Page, "HideRows('WebNew_tblNew');")
            ' chiamata allo script custom per settare il colspan
            myScript.InvokeJS(Me.Page, "SetTdColspan('WebNew_tblNew');")
            ' chiamata allo script per settare la larghezza delle colonne
            myScript.InvokeJS(Me.Page, "ShowUCCont();")
            myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('WebNew_tblNew');")

            If Page.IsPostBack Then
                ' risposta positiva ad msgAskUserUnfilledDataCycle ed AskUserConfirmDataCycle (solo cicli planned)
                If msgAskUserUnfilledDataCycle.GetAnswer() Then
                    Dim s_warning As String = String.Empty

                    If Not myFunction.AskUserConfirmDataCycle(mScreen, s_warning, Me) Then
                        msgAskUserConfirmDataCycle.Show(s_warning)
                        HiddenAskUserConfirmDataCycle.Value = s_warning
                        Exit Sub
                    Else
                        SaveData()
                    End If
                End If

                If msgAskUserConfirmDataCycle.GetAnswer() Then
                    SaveData()
                End If
            End If

            ' gestione blanket (solo sul 1° load, gestito da js)
            div_blanket.Attributes.Add("IsFirstLoad", (Not Page.IsPostBack).ToString)
        End If
    End Sub

    Protected Sub btnSubmit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSubmit.Click
        Dim s_error As String = String.Empty
        Dim s_warning As String = String.Empty

        If Not myFunction.CtrlDataScreen(mScreen, s_error, Me) Then
            msgGeneric.Show(s_error)
            Exit Sub
        End If

        If Not myFunction.CtrlDataRecipe(mScreen, s_error, Me) Then
            msgGeneric.Show(s_error)
            Exit Sub
        End If

        If Not myFunction.CtrlDataCycle(mScreen, s_error, Me) Then
            msgGeneric.Show(s_error)
            Exit Sub
        End If

        If Not myFunction.AskUserUnfilledDataCycle(mScreen, s_warning, Me) Then
            msgAskUserUnfilledDataCycle.Show(s_warning)
            HiddenAskUserUnfilledDataCycle.Value = s_warning
            Exit Sub
        Else
            HiddenAskUserUnfilledDataCycle.Value = String.Empty
        End If

        If Not myFunction.AskUserConfirmDataCycle(mScreen, s_warning, Me) Then
            msgAskUserConfirmDataCycle.Show(s_warning)
            HiddenAskUserConfirmDataCycle.Value = s_warning
            Exit Sub
        Else
            HiddenAskUserConfirmDataCycle.Value = String.Empty
        End If

        SaveData()
    End Sub

    Private Sub SaveData()
        WebTools.tools.SetObject(mScreen, Me, costanti.m_InvalidId)

        myFunction.ExportDataToERP(mScreen, mPageName, SSBorderDatabaseAction.Add, costanti.m_InvalidId)

        Dim bOk As Boolean = False
        While Not bOk
            bOk = myFunction.WaitCompleted(mScreen)
        End While

        myScript.InvokeJS(Me.Page, "ButtonEvents.WUC_New.Submit('" & mPageName & "');")
    End Sub

    Protected Sub btnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        myScript.InvokeJS(Me.Page, "ButtonEvents.WUC_New.Cancel('" & mPageName & "');")
    End Sub

End Class