﻿<%@ Control Language="VB" AutoEventWireup="false" CodeFile="WUC_PLCStatus.ascx.vb" Inherits="UserControl_WUC_PLCStatus" %>
<%@ Register Assembly="UsersGUI" Namespace="UsersGUI" TagPrefix="iba" %>

<div id="DivStatus">
    <div class="menuTOP">
        <div class="txtCenter txtWhite txtBold">
            <%= m_config.GetEntryByKeyName("PLC_STATUS").GetValue%>
        </div>
    </div>
    <div id="DivStatusElements" class="menuMID">
        <table>
            <tr>
                <th style="background-color:#F0F0F0;width:50px;">Name</th>
                <th style="background-color:#F0F0F0;text-align:center;">Reachable</th>
                <th style="background-color:#F0F0F0;text-align:center;width:50px;">Status</th>
            </tr>
            
            <% For Each _plc As PLC In _ListPLC %>
                <tr>
                    <td style="vertical-align:top;padding-top:10px;font-size:xx-small;">
                        <a href="default.aspx?control=status&pagename=status&menuname=MENU_SYSTEM&session=1">
                            <%=_plc.DESCRIPTION %>
                        </a>
                    </td>
                    <% If _plc.REACHABLE = "YES" Then %>
                        <td style="text-align:center;vertical-align:top;padding-top:5px;font-size:xx-small;"><asp:Image ID="imgRRun" runat="server" ImageUrl="~/image/greenled.png" /><br />Yes</td>
                    <%elseIf _plc.REACHABLE = "NO" %>
                        <td style="text-align:center;vertical-align:top;padding-top:5px;font-size:xx-small;"><asp:Image ID="imgRStoped" runat="server" ImageUrl="~/image/redled.png" /><br />No</td>
                    <%else %>
                        <td style="text-align:center;vertical-align:top;padding-top:5px;font-size:xx-small;"><asp:Image ID="Image1" runat="server" ImageUrl="~/image/greyled.png" /></td>
                    <%end if %>
                    <%If _plc.IN_START = "YES" Then %>
                     <td style="text-align:center;vertical-align:top;padding-top:5px;font-size:xx-small;"><asp:Image ID="imgStarting" runat="server" ImageUrl="~/image/yellowled.png" /><br />Starting</td>
                    <%elseif _plc.STATUS = "ON" Then%>
                        <td style="text-align:center;vertical-align:top;padding-top:5px;font-size:xx-small;"><asp:Image ID="imgPRun" runat="server" ImageUrl="~/image/greenled.png" /><br />Run</td>
                    <%elseIf _plc.STATUS = "OFF" Then %>
                        <td style="text-align:center;vertical-align:top;padding-top:5px;font-size:xx-small;"><asp:Image ID="imgStoped" runat="server" ImageUrl="~/image/redled.png" /><br />Stopped</td>
                    <%end if %>
                </tr>
            <% Next %>
            
        </table>
    </div>
</div>