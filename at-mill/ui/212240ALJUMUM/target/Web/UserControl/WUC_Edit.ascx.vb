﻿Imports System.Data
Imports UsersGUI
Imports WebTools.tools
Imports CommonDefines.Defines
Imports System.Web.HttpContext

Partial Class WUC_WUC_Edit
    Inherits UserControl

    Public mPageName As String = String.Empty
    Public mScreen As Screen = Nothing
    Public m_config As config = Nothing
    Public m_ReadOnly As Boolean = False
    Public m_id As Long = m_InvalidId
    Private mTopMenuName As String = String.Empty
    Private mMenuName As String = String.Empty
    Private dt As Data.DataTable
    Private sql_where As String = String.Empty
    Private mControl As String = String.Empty
    Private bResult As String
    Private adim As AddMenuItemName = Nothing
    Private mCounter As Long = m_InvalidId
    Private msgGeneric As New myAlert
    Private msgAskUserUnfilledDataCycle As New myConfirm
    Private msgAskUserConfirmDataCycle As New myConfirm
    Private msgAbortCycle As New myConfirm
    Private sessionId As String
    Private po_number_status As Integer = m_InvalidId

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded and the session is set in the URL
        If Me.Visible AndAlso Current.Request.QueryString("session") IsNot Nothing Then
            sessionId = Current.Request.QueryString("session")

            m_config = CType(Application("Config"), config)

            If Not GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            If Current.Request("Id") Is Nothing Then
                Exit Sub
            End If

            If Current.Request("readonly") IsNot Nothing AndAlso Current.Request("readonly").ToString <> String.Empty Then
                m_ReadOnly = CBool(Current.Request("readonly").ToString)
            End If

            ' message init
            msgGeneric.Init(Me.Page, myMessageBoxParam.NoParameter)
            msgAskUserUnfilledDataCycle.Init(Me.Page, Me.HiddenAskUserUnfilledDataCycle, myMessageBoxParam.NoParameter)
            msgAskUserConfirmDataCycle.Init(Me.Page, Me.HiddenAskUserConfirmDataCycle, myMessageBoxParam.NoParameter)
            msgAbortCycle.Init(Me.Page, Me.HiddenAbortCycle, myMessageBoxParam.AbortCycle)

            Dim nCount As Integer = 1
            If mScreen.AddMenuItemNames IsNot Nothing Then
                For Each Me.adim In mScreen.AddMenuItemNames
                    If adim.MenuBound = EnumAddMenuBound.Edit Then
                        Dim hl As New HyperLink
                        If adim.NameLink <> String.Empty Then
                            hl.ID = adim.NameLink
                            hl.Text = m_config.GetEntryByKeyName(adim.NameLink).GetValue()
                        Else
                            hl.Text = m_config.GetEntryByKeyName(adim.Name).GetValue()
                            hl.ID = adim.Name
                        End If

                        hl.NavigateUrl = adim.GetNavigateUrl

                        For Each p As AddMenuItemNameParameter In adim.ListParameters
                            hl.NavigateUrl &= "&" & p.FieldDB & "=" & Current.Request.QueryString(p.FieldDB)
                        Next

                        nCount += 1
                    End If
                Next
            End If

            'Access rights
            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Editing) AndAlso m_ReadOnly = False Then
                lblError.Text = m_config.GetEntryByKeyName("EDIT_DENIED").GetValue
                Me.dError.Visible = True
                Me.dView.Visible = False
                Exit Sub
            ElseIf Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) AndAlso m_ReadOnly = True Then
                lblError.Text = m_config.GetEntryByKeyName("ACCESS_DENIED").GetValue
                Me.dError.Visible = True
                Me.dView.Visible = False
                Exit Sub
            End If

            Me.btnSubmit.Text = m_config.GetEntryByKeyName("SUBMIT_BUTTON").GetValue()
            Me.btnCancel.Text = m_config.GetEntryByKeyName("CANCEL_BUTTON").GetValue()

            Dim order_status As Integer = -1
            Dim id_cycle As Integer = m_InvalidId

            ' rimuovo ReadOnly sul primo load della pagina
            If Not Page.IsPostBack Then
                Select Case mScreen.EnumPageNameCode
                    Case EnumPageName.ProductionPlan
                        Session.Remove(sessionId & "_ReadOnly")
                End Select
            End If

            ' recupero l'id (vale per tutti gli screen)
            m_id = Long.Parse(Current.Request("Id").ToString)

            Select Case mScreen.EnumPageNameCode
                Case EnumPageName.ProductionPlan
                    order_status = GetOrderStatus(m_id)
                    If order_status = UsersGUI.costanti.m_StatusRelease Or order_status = UsersGUI.costanti.m_StatusLocked Then
                        m_ReadOnly = False
                    Else
                        m_ReadOnly = True
                    End If

                    ' uso il ReadOnly e la sessione per vedere il cambio di stato ReadOnly true->false e false->true.
                    ' in questi casi faccio un Response.Redirect per ricaricare tutti i dati da DB
                    ' (e.g. serve sul job che va starting/attivo mentre sto modificando cycle params, caso possibile
                    ' in architettura client/server. Serve anche sul ritorno IDLE per i RadioButton.)
                    If (Not Session(sessionId & "_ReadOnly") Is Nothing AndAlso Session(sessionId & "_ReadOnly").ToString <> String.Empty) Then
                        If Session(sessionId & "_ReadOnly").ToString <> m_ReadOnly.ToString Then
                            Response.Redirect(Request.RawUrl)
                        Else
                            Session(sessionId & "_ReadOnly") = m_ReadOnly.ToString
                        End If
                    Else
                        Session(sessionId & "_ReadOnly") = m_ReadOnly.ToString
                    End If
            End Select

            If Not Page.IsPostBack Then
                ' ReadOnly must be evaluated before to call GetData()
                GetData()

                For Each f As Field In mScreen.EditFields
                    If f.CallEvent <> String.Empty Then
                        ' ho un qualche tipo di call event specificato a livello di config.xml da gestire
                        GenericsEvents.EventDelegate.AppendCallEvent(Me, f)
                    End If
                Next

                Select Case mScreen.EnumPageNameCode
                    Case EnumPageName.ProductionPlan
                        If mPageName.Contains("CYCLE") Then

                            If Current.Request.QueryString("CYC_ID") IsNot Nothing AndAlso Current.Request.QueryString("CYC_ID") <> String.Empty Then
                                id_cycle = Integer.Parse(Current.Request.QueryString("CYC_ID"))
                            End If
                            If id_cycle = m_InvalidId AndAlso Current.Request.QueryString("ID") IsNot Nothing AndAlso Current.Request.QueryString("ID") <> String.Empty Then
                                id_cycle = GetCycleIdFromPplId(Integer.Parse(Current.Request.QueryString("ID")))
                            End If

                        End If
                End Select
            Else
                ' submit e cancel custom (query non su ID)
                Select Case mScreen.EnumPageNameCode
                    'Case EnumPageName.ConfirmFlowLogs
                    '    Dim sSelect As String = "SELECT * FROM VIEW_CONFIRM_FLOW_LOGS WHERE COUNTER = '" & Current.Request("counter").ToString & "' AND CYC_ID ='" & Current.Request("cyc_id").ToString & "'"
                    '    '                    Dim sSelect As String = "SELECT * FROM VIEW_CONFIRM_FLOW_LOGS WHERE ID = '" & Current.Request("id").ToString & "'"
                    '    Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

                    '    If mScreen.ComparesWeb IsNot Nothing AndAlso mScreen.ComparesWeb(0) IsNot Nothing AndAlso mScreen.ComparesWeb IsNot Nothing Then
                    '        Me.btnSubmit.ValidationGroup = mScreen.ComparesWeb(0).ValidationGroup
                    '    End If

                    '    WebTools.drawings.DrawUpdateFields(tblEdit, mScreen, m_config, dt, m_ReadOnly, ppl_id)
                    '    WebTools.drawings.DrawCompares(tblCompare, mScreen, m_config, EnumCompareType.Update)
                    Case Else
                        GetData()
                End Select

            End If

            ' per i controllati devo forzare la visibilità
            If m_ReadOnly Then
                Me.btnSubmit.Visible = False
                Me.btnCancel.Visible = False
            End If

            ' chiamata allo script custom per nascondere le righe con elementi non visibili
            myScript.InvokeJS(Me.Page, "HideRows('WebEdit_tblEdit');")
            myScript.InvokeJS(Me.Page, "HideRows('WebEdit_tblHeader');")
            ' chiamata allo script custom per settare il colspan
            myScript.InvokeJS(Me.Page, "SetTdColspan('WebEdit_tblEdit');")
            myScript.InvokeJS(Me.Page, "SetTdColspan('WebEdit_tblHeader');")
            ' chiamata allo script per settare la larghezza delle colonne
            myScript.InvokeJS(Me.Page, "ShowUCCont();")
            myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('WebEdit_tblEdit');")
            myScript.InvokeJS(Me.Page, "SetColumnWidth_ParamTable('WebEdit_tblHeader');")

            If Page.IsPostBack Then
                ' risposta positiva ad msgAskUserUnfilledDataCycle ed AskUserConfirmDataCycle (cicli planned e controllati)
                If msgAskUserUnfilledDataCycle.GetAnswer() Then
                    Dim s_warning As String = String.Empty

                    If Not myFunction.AskUserConfirmDataCycle(mScreen, s_warning, Me) Then
                        msgAskUserConfirmDataCycle.Show(s_warning)
                        HiddenAskUserConfirmDataCycle.Value = s_warning
                        Exit Sub
                    Else
                        m_id = Long.Parse(Current.Request("Id").ToString)
                            SaveData()
                        End If
                    End If

                If msgAskUserConfirmDataCycle.GetAnswer() Then
                    m_id = Long.Parse(Current.Request("Id").ToString)
                        SaveData()
                    End If

                ' risposta positiva alla pressione dell' END JOB
                If msgAbortCycle.GetAnswer Then
                    ' Cancel a pending start request
                    WebTools.tools.SetParameter(mPageName & "_START_REQUEST", costanti.m_StringOff)
                    WebTools.tools.SetParameter(mPageName & "_STOP_REQUEST", costanti.m_StringOn)

                    myScript.InvokeJS(Me.Page, "ButtonEvents.ControlledCycle.EndJob('" & mPageName & "');")
                End If
            End If

            ' gestione blanket (solo sul 1° load, gestito da js)
            div_blanket.Attributes.Add("IsFirstLoad", (Not Page.IsPostBack).ToString)
        End If
    End Sub

    Private Sub GetData()
        Dim sSelect As String = "SELECT * FROM " & mScreen.DBName & " WHERE ID = '" & Current.Request("Id").ToString & "'"
        Dim dt As DataTable = WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(sSelect, True)

        If mScreen.ComparesWeb IsNot Nothing AndAlso mScreen.ComparesWeb(0) IsNot Nothing AndAlso mScreen.ComparesWeb IsNot Nothing Then
            Me.btnSubmit.ValidationGroup = mScreen.ComparesWeb(0).ValidationGroup
        End If

        WebTools.drawings.DrawHeaderFields(tblHeader, mScreen.Header, m_config, True)

        If tblHeader.Rows.Count <= 0 Then
            tblHeader.Visible = False
        End If

        WebTools.drawings.DrawUpdateFields(tblEdit, mScreen, m_config, dt, m_ReadOnly, m_id)
        WebTools.drawings.DrawCompares(tblCompare, mScreen, m_config, EnumCompareType.Update)
        GenericsEvents.SetEvents.UpdateCallEventFields(Me, mScreen, m_config)

        If tblCompare.Rows.Count <= 0 Then
            tblCompare.Visible = False
        End If
    End Sub

    Protected Sub btnSubmit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSubmit.Click
        Dim s_error As String = String.Empty
        Dim s_warning As String = String.Empty

        If Not myFunction.CtrlDataScreen(mScreen, s_error, Me) Then
            msgGeneric.Show(s_error)
            Exit Sub
        End If

        If Not myFunction.CtrlDataRecipe(mScreen, s_error, Me) Then
            msgGeneric.Show(s_error)
            Exit Sub
        End If

        If Not myFunction.CtrlDataCycle(mScreen, s_error, Me) Then
            msgGeneric.Show(s_error)
            Exit Sub
        End If

        If Not myFunction.AskUserUnfilledDataCycle(mScreen, s_warning, Me) Then
            msgAskUserUnfilledDataCycle.Show(s_warning)
            HiddenAskUserUnfilledDataCycle.Value = s_warning
            Exit Sub
        Else
            HiddenAskUserUnfilledDataCycle.Value = String.Empty
        End If

        If Not myFunction.AskUserConfirmDataCycle(mScreen, s_warning, Me) Then
            msgAskUserConfirmDataCycle.Show(s_warning)
            HiddenAskUserConfirmDataCycle.Value = s_warning
            Exit Sub
        Else
            HiddenAskUserConfirmDataCycle.Value = String.Empty
        End If

        ' Executed only if CtrlDataCycle, AskUserUnfilledDataCycle and AskUserConfirmDataCycle return true
        SaveData()
    End Sub

    Private Sub SaveData()
        ' 1. salvo i parametri (di ciclo e non..)
        WebTools.tools.SetObject(mScreen, Me, Long.Parse(Current.Request("Id").ToString))

        myFunction.ExportDataToERP(mScreen, mPageName, SSBorderDatabaseAction.Edit, Long.Parse(Current.Request("Id").ToString))

        Dim bOk As Boolean = False
        While Not bOk
            bOk = myFunction.WaitCompleted(mScreen)
        End While

        ' 2. redirect
        myScript.InvokeJS(Me.Page, "ButtonEvents.WUC_Edit.Submit('" & mPageName & "');")
    End Sub

    Protected Sub btnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        myScript.InvokeJS(Me.Page, "ButtonEvents.WUC_Edit.Cancel('" & mPageName & "');")
    End Sub

    Protected Sub TimerReloadPage_Tick(ByVal sender As Object, ByVal e As System.EventArgs) Handles TimerReloadPage.Tick
        Response.Redirect(Request.RawUrl)
    End Sub

End Class