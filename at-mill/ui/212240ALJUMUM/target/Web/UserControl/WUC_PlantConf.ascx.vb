﻿Imports UsersGUI

Partial Class WUC_PlantConf
    Inherits UserControl

    Public m_config As UsersGUI.config
    Private mMenuName As String
    Public mPageName As String
    Private mScreen As Screen
    Private mControl As String = String.Empty
    Private mTopMenuName As String = String.Empty
    Public lblHourStartDay As String = String.Empty
    Public lblFirstShiftHour As String = String.Empty
    Public lblSecondShiftHour As String = String.Empty
    Public lblThirdShiftHour As String = String.Empty
    Public lblWeekFirstDay As String = String.Empty
    Public lblInfoHourStartDay As String = String.Empty
    Public lblInfoFirstShiftHour As String = String.Empty
    Public lblInfoSecondShiftHour As String = String.Empty
    Public lblInfoThirdShiftHour As String = String.Empty
    Public lblInfoWeekFirstDay As String = String.Empty

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Proceed only if the WUC is to be loaded
        If Me.Visible Then
            m_config = CType(Application("Config"), UsersGUI.config)

            If Not WebTools.tools.GetWebParams(m_config, mPageName, mMenuName, mControl, mScreen, mTopMenuName) Then
                Exit Sub
            End If

            WebTools.tools.CheckPageDB(m_config, mMenuName, mPageName)
            If Not WebTools.tools.CheckAccess(m_config, mMenuName, mPageName, EnumAccessLevel.Accessing) Then
                lblError.Text = m_config.GetEntryByKeyName("PERMISSION_DENIED").GetValue()
                Me.dView.Visible = False
                Me.dError.Visible = True
                Exit Sub
            End If

            lblHourStartDay = m_config.GetEntryByKeyName("CONF_HOUR_START_DAY").GetValue
            lblFirstShiftHour = m_config.GetEntryByKeyName("CONF_FIRST_SHIFT_START").GetValue
            lblSecondShiftHour = m_config.GetEntryByKeyName("CONF_SECOND_SHIFT_START").GetValue
            lblThirdShiftHour = m_config.GetEntryByKeyName("CONF_THIRD_SHIFT_START").GetValue
            lblWeekFirstDay = m_config.GetEntryByKeyName("CONF_WEEK_FIRST_DAY").GetValue

            lblInfoHourStartDay = m_config.GetEntryByKeyName("MOD_PARAM_HOUR_START_DAY").GetValue
            lblInfoFirstShiftHour = m_config.GetEntryByKeyName("MOD_PARAM_1ST_SHIFT").GetValue
            lblInfoSecondShiftHour = m_config.GetEntryByKeyName("MOD_PARAM_2ND_SHIFT").GetValue
            lblInfoThirdShiftHour = m_config.GetEntryByKeyName("MOD_PARAM_3RD_SHIFT").GetValue
            lblInfoWeekFirstDay = m_config.GetEntryByKeyName("MOD_PARAM_WEEK_FIRST_DAY").GetValue

            If Not Page.IsPostBack Then
                Me.parHourStartDay.Text = GetFormatData(WebTools.tools.GetParameter("HOUR_START_DAY"), WebTools.tools.GetParameter("MINUTE_START_DAY"))
                Me.parFirstShiftHour.Text = GetFormatData(WebTools.tools.GetParameter("1ST_SHIFT_HOUR"), WebTools.tools.GetParameter("1ST_SHIFT_MINUTE"))
                Me.parSecondShiftHour.Text = GetFormatData(WebTools.tools.GetParameter("2ND_SHIFT_HOUR"), WebTools.tools.GetParameter("2ND_SHIFT_MINUTE"))
                Me.parThirdShiftHour.Text = GetFormatData(WebTools.tools.GetParameter("3RD_SHIFT_HOUR"), WebTools.tools.GetParameter("3RD_SHIFT_MINUTE"))

                Me.parWeekFirstDay.Text = WebTools.tools.GetParameter("WEEK_FIRST_DAY")

                Me.btnUpdatePlantConf.Text = m_config.GetEntryByKeyName("SUBMIT_BUTTON").GetValue()
                Me.btnCancelPlantConf.Text = m_config.GetEntryByKeyName("CANCEL_BUTTON").GetValue()
            End If
        End If
    End Sub

    Private Function GetFormatData(_hh As String, _mm As String) As String
        Dim ret_val As String = String.Empty

        If (Integer.Parse(_hh) < 10) Then
            ret_val = "0"
        End If
        ret_val &= Integer.Parse(_hh).ToString()

        ret_val &= ":"

        If (Integer.Parse(_mm) < 10) Then
            ret_val &= "0"
        End If
        ret_val &= Integer.Parse(_mm).ToString()

        Return ret_val
    End Function

    Protected Sub btnUpdatePlantConf_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnUpdatePlantConf.Click
        If SavePlantConf() Then
            Current.Response.Redirect(myScript.AppendSessionIdToUrl(costanti.a_home))
        End If
    End Sub

    Private Function IsCorrectValue(hh_mm As String) As Boolean
        Dim ret_val As Boolean = True

        'Controllo che non sia nullo
        If (hh_mm = String.Empty) Then
            Return False
        End If

        'Controllo se le ho il formato HH:mm
        If (Not hh_mm.Contains(":")) Then
            Return False
        End If

        Try
            'Separo il formato HH:mm in HH e mm
            Dim _Array As String() = hh_mm.Split(Char.Parse(":"))

            'Controllo se le ore sono numeriche
            If (Not IsNumeric(_Array(0))) Then
                Return False
            End If

            'Controllo se i minuti sono numerici
            If (Not IsNumeric(_Array(1))) Then
                Return False
            End If
        Catch ex As Exception
            Return False
        End Try

        Return ret_val
    End Function

    Private Function SavePlantConf() As Boolean
        Dim err_count = 0
        Dim _TimeArray As String()

        Dim bError As Boolean = False

        bError = False
        If Not IsCorrectValue(Me.parHourStartDay.Text) Then
            bError = True
        Else
            ' Faccio una verifica sul formato del HH:mm
            _TimeArray = Me.parHourStartDay.Text.Split(Char.Parse(":"))

            If Integer.Parse(_TimeArray(0)) < 0 OrElse Integer.Parse(_TimeArray(0)) > 23 Then
                bError = True
            ElseIf Integer.Parse(_TimeArray(1)) < 0 OrElse Integer.Parse(_TimeArray(1)) > 59 Then
                bError = True
            Else
                WebTools.tools.SetParameter("HOUR_START_DAY", (_TimeArray(0)))
                WebTools.tools.SetParameter("MINUTE_START_DAY", (_TimeArray(1)))
                Me.lblErrorHourStartDay.Visible = False
                Me.lblErrorHourStartDay.Text = String.Empty
            End If
        End If

        If bError Then
            Me.lblErrorHourStartDay.Text = m_config.GetEntryByKeyName("INVALID_PARAM").GetValue & " " & lblHourStartDay
            Me.lblErrorHourStartDay.Font.Bold = True
            Me.lblErrorHourStartDay.Visible = True
            err_count += 1
        End If

        bError = False
        If Not IsCorrectValue(Me.parFirstShiftHour.Text) Then
            bError = True
        Else
            ' Faccio una verifica sul formato del HH:mm
            _TimeArray = Me.parFirstShiftHour.Text.Split(Char.Parse(":"))

            If Integer.Parse(_TimeArray(0)) < 0 OrElse Integer.Parse(_TimeArray(0)) > 23 Then
                bError = True
            ElseIf Integer.Parse(_TimeArray(1)) < 0 OrElse Integer.Parse(_TimeArray(1)) > 59 Then
                bError = True
            Else
                WebTools.tools.SetParameter("1ST_SHIFT_HOUR", (_TimeArray(0)))
                WebTools.tools.SetParameter("1ST_SHIFT_MINUTE", (_TimeArray(1)))
                Me.lblErrorFirstShiftHour.Visible = False
                Me.lblErrorFirstShiftHour.Text = String.Empty
            End If
        End If

        If bError Then
            Me.lblErrorFirstShiftHour.Text = m_config.GetEntryByKeyName("INVALID_PARAM").GetValue & " " & lblFirstShiftHour
            Me.lblErrorFirstShiftHour.Font.Bold = True
            Me.lblErrorFirstShiftHour.Visible = True
            err_count += 1
        End If

        bError = False
        If Not IsCorrectValue(Me.parSecondShiftHour.Text) Then
            bError = True
        Else
            ' Faccio una verifica sul formato del HH:mm
            _TimeArray = Me.parSecondShiftHour.Text.Split(Char.Parse(":"))

            If Integer.Parse(_TimeArray(0)) < 0 OrElse Integer.Parse(_TimeArray(0)) > 23 Then
                bError = True
            ElseIf Integer.Parse(_TimeArray(1)) < 0 OrElse Integer.Parse(_TimeArray(1)) > 59 Then
                bError = True
            Else
                WebTools.tools.SetParameter("2ND_SHIFT_HOUR", (_TimeArray(0)))
                WebTools.tools.SetParameter("2ND_SHIFT_MINUTE", (_TimeArray(1)))
                Me.lblErrorSecondShiftHour.Visible = False
                Me.lblErrorSecondShiftHour.Text = String.Empty
            End If
        End If

        If bError Then
            Me.lblErrorSecondShiftHour.Text = m_config.GetEntryByKeyName("INVALID_PARAM").GetValue & " " & lblSecondShiftHour
            Me.lblErrorSecondShiftHour.Font.Bold = True
            Me.lblErrorSecondShiftHour.Visible = True
            err_count += 1
        End If

        bError = False
        If Not IsCorrectValue(Me.parThirdShiftHour.Text) Then
            bError = True
        Else
            ' Faccio una verifica sul formato del HH:mm
            _TimeArray = Me.parThirdShiftHour.Text.Split(Char.Parse(":"))

            If Integer.Parse(_TimeArray(0)) < 0 OrElse Integer.Parse(_TimeArray(0)) > 23 Then
                bError = True
            ElseIf Integer.Parse(_TimeArray(1)) < 0 OrElse Integer.Parse(_TimeArray(1)) > 59 Then
                bError = True
        Else
                WebTools.tools.SetParameter("3RD_SHIFT_HOUR", (_TimeArray(0)))
                WebTools.tools.SetParameter("3RD_SHIFT_MINUTE", (_TimeArray(1)))
                Me.lblErrorThirdShiftHour.Visible = False
                Me.lblErrorThirdShiftHour.Text = String.Empty
            End If
        End If

        If bError Then
            Me.lblErrorThirdShiftHour.Text = m_config.GetEntryByKeyName("INVALID_PARAM").GetValue & " " & lblThirdShiftHour
            Me.lblErrorThirdShiftHour.Font.Bold = True
            Me.lblErrorThirdShiftHour.Visible = True
            err_count += 1
        End If

        If Me.parWeekFirstDay.Text = String.Empty OrElse Not IsNumeric(Me.parWeekFirstDay.Text) _
           OrElse Integer.Parse(Me.parWeekFirstDay.Text) < 1 OrElse Integer.Parse(Me.parWeekFirstDay.Text) > 7 Then
            Me.lblErrorWeekFirstDay.Text = m_config.GetEntryByKeyName("INVALID_PARAM").GetValue & " " & lblWeekFirstDay
            Me.lblErrorWeekFirstDay.Font.Bold = True
            Me.lblErrorWeekFirstDay.Visible = True
            err_count += 1
        Else
            WebTools.tools.SetParameter("WEEK_FIRST_DAY", Me.parWeekFirstDay.Text)
            Me.lblErrorWeekFirstDay.Visible = False
            Me.lblErrorWeekFirstDay.Text = String.Empty
        End If

        If err_count = 0 Then

            Dim req As New CommandInterface(EnumRequest.SynchronizePlantConfiguration)
            req.PostUniqueRequest()

            Return True
        Else
            Return False
        End If

    End Function

    Protected Sub btnCancelPlantConf_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnCancelPlantConf.Click
        myScript.InvokeJS(Me.Page, "ButtonEvents.WUC_PlantConf.Cancel('" & mPageName & "');")
    End Sub

End Class