﻿Partial Class _HttpError
    Inherits myWebPage
    Protected exceptions As List(Of Exception)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Current.Server.GetLastError IsNot Nothing Then
            exceptions = New List(Of Exception)

            ' Ignore the first HttpUnhandledException
            Dim currExc As Exception = If(Server.GetLastError().GetType = GetType(HttpUnhandledException), Server.GetLastError().InnerException, Server.GetLastError())

            ' Reverse the order of the exceptions
            While currExc IsNot Nothing
                exceptions.Add(currExc)

                currExc = currExc.InnerException
            End While

            exceptions.Reverse()
        End If
    End Sub

End Class