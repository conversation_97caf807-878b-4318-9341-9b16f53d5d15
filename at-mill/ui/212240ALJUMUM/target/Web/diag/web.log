UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 09:50:04.819]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.Control.ValidateEvent(String uniqueID, String eventArgument)
   in System.Web.UI.WebControls.HiddenField.LoadPostData(String postDataKey, NameValueCollection postCollection)
   in System.Web.UI.Page.ProcessPostData(NameValueCollection postData, Boolean fBeforeLoad)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.default_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:14:19.442]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 624
   in System.Web.UI.WebControls.CheckBox.OnCheckedChanged(EventArgs e)
   in System.Web.UI.Page.RaiseChangedEvents()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:18:49.277]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?session=1&sessionExpired=YES
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 10:22:10.038]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 725
   in GenericsEvents.EventDelegate.AppendCallEvent(Control root, Field f) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 234
   in WUC_WUC_Edit.Page_Load(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 141
   in System.Web.UI.Control.OnLoad(EventArgs e)
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:22:14.192]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 725
   in GenericsEvents.EventDelegate.AppendCallEvent(Control root, Field f) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 234
   in WUC_WUC_Edit.Page_Load(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 141
   in System.Web.UI.Control.OnLoad(EventArgs e)
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:24:49.818]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 725
   in GenericsEvents.EventDelegate.AppendCallEvent(Control root, Field f) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 234
   in WUC_WUC_Edit.Page_Load(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 141
   in System.Web.UI.Control.OnLoad(EventArgs e)
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:24:53.888]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 725
   in GenericsEvents.EventDelegate.AppendCallEvent(Control root, Field f) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 234
   in WUC_WUC_Edit.Page_Load(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 141
   in System.Web.UI.Control.OnLoad(EventArgs e)
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:28:43.873]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 10:29:07.308]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 792
   in GenericsEvents.EventDelegate.AppendCallEvent(Control root, Field f) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 234
   in WUC_WUC_Edit.Page_Load(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 141
   in System.Web.UI.Control.OnLoad(EventArgs e)
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:30:14.389]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 694
   in System.Web.UI.WebControls.CheckBox.OnCheckedChanged(EventArgs e)
   in System.Web.UI.Page.RaiseChangedEvents()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:32:20.150]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 10:32:42.831]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 761
   in System.Web.UI.WebControls.CheckBox.OnCheckedChanged(EventArgs e)
   in System.Web.UI.Page.RaiseChangedEvents()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:36:41.892]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 10:42:06.086]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 627
   in GenericsEvents.EventDelegate.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 355
   in System.Web.UI.WebControls.CheckBox.OnCheckedChanged(EventArgs e)
   in System.Web.UI.Page.RaiseChangedEvents()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:42:06.783]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 627
   in GenericsEvents.EventDelegate.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 355
   in System.Web.UI.WebControls.CheckBox.OnCheckedChanged(EventArgs e)
   in System.Web.UI.Page.RaiseChangedEvents()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 10:58:54.649]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 10:59:02.274]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=3
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 660
   in GenericsEvents.EventDelegate.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 355
   in GenericsEvents.EventDelegate.AppendCallEvent(Control root, Field f) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 234
   in WUC_WUC_Edit.Page_Load(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 141
   in System.Web.UI.Control.OnLoad(EventArgs e)
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Control.LoadRecursive()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 11:01:46.588]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.InvalidOperationException
La raccolta è stata modificata. L'operazione di enumerazione potrebbe non essere eseguita.
   in System.ThrowHelper.ThrowInvalidOperationException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext()
   in System.Linq.Enumerable.<CastIterator>d__97`1.MoveNext()
   in System.String.Join[T](String separator, IEnumerable`1 values)
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.InvalidOperationException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: La raccolta è stata modificata. L'operazione di enumerazione potrebbe non essere eseguita.

========================================================================================================================================================================================================
[2025/02/25 11:01:57.555]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTS&menuname=MENU_LOTS&session=1
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.Control.ValidateEvent(String uniqueID, String eventArgument)
   in System.Web.UI.WebControls.HiddenField.LoadPostData(String postDataKey, NameValueCollection postCollection)
   in System.Web.UI.Page.ProcessPostData(NameValueCollection postData, Boolean fBeforeLoad)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.default_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 11:02:52.083]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 11:06:32.659]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 11:08:30.113]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 11:08:42.470]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=3
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.Control.ValidateEvent(String uniqueID, String eventArgument)
   in System.Web.UI.WebControls.HiddenField.LoadPostData(String postDataKey, NameValueCollection postCollection)
   in System.Web.UI.Page.ProcessPostData(NameValueCollection postData, Boolean fBeforeLoad)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.default_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 11:10:50.694]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 11:14:29.718]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 11:20:57.330]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 12:06:19.906]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.Control.ValidateEvent(String uniqueID, String eventArgument)
   in System.Web.UI.WebControls.HiddenField.LoadPostData(String postDataKey, NameValueCollection postCollection)
   in System.Web.UI.Page.ProcessPostData(NameValueCollection postData, Boolean fBeforeLoad)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.default_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 12:09:08.853]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 12:09:08.853]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 12:11:22.390]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.InvalidOperationException
La raccolta è stata modificata. L'operazione di enumerazione potrebbe non essere eseguita.
   in System.ThrowHelper.ThrowInvalidOperationException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext()
   in System.Linq.Enumerable.<CastIterator>d__97`1.MoveNext()
   in System.String.Join[T](String separator, IEnumerable`1 values)
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.InvalidOperationException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: La raccolta è stata modificata. L'operazione di enumerazione potrebbe non essere eseguita.

========================================================================================================================================================================================================
[2025/02/25 12:11:22.390]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 12:13:00.034]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 12:18:38.148]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 12:20:17.349]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 12:22:57.215]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 12:29:19.975]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 13:35:22.891]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 13:38:31.439]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 13:38:51.859]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.FormatException
Input string was not in a correct format.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ParseDouble(String Value, NumberFormatInfo NumberFormat)
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)

System.InvalidCastException
Conversion from string "" to type 'Double' is not valid.
   in Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(String Value, NumberFormatInfo NumberFormat)
   in GenericsEvents.SpecificEvents.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\SpecificEvents.vb:riga 647
   in GenericsEvents.EventDelegate.EventStartupBin_C04_AutofillDest(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\dev\web\GenericsEvents\GenericsEvents.vb:riga 363
   in System.Web.UI.WebControls.CheckBox.OnCheckedChanged(EventArgs e)
   in System.Web.UI.Page.RaiseChangedEvents()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 13:44:31.806]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 13:44:31.806]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 13:45:49.352]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 13:54:50.188]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 14:02:27.296]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 14:07:25.798]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentOutOfRangeException
Index was out of range. Must be non-negative and less than the size of the collection.
Nome parametro: index
   in System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   in System.Collections.Generic.List`1.get_Item(Int32 index)
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1336
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 14:10:29.746]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 14:12:00.445]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.Collections.Generic.KeyNotFoundException
The given key was not present in the dictionary.
   in System.ThrowHelper.ThrowKeyNotFoundException()
   in System.Collections.Generic.Dictionary`2.get_Item(TKey key)
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1339
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 14:12:15.631]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.InvalidCastException
Unable to cast object of type 'UsersGUI.myRadioButton' to type 'UsersGUI.myDropDownList'.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1234
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 14:14:21.195]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.InvalidCastException
Unable to cast object of type 'UsersGUI.myRadioButton' to type 'UsersGUI.myDropDownList'.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1234
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 14:14:32.437]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.Collections.Generic.KeyNotFoundException
The given key was not present in the dictionary.
   in System.ThrowHelper.ThrowKeyNotFoundException()
   in System.Collections.Generic.Dictionary`2.get_Item(TKey key)
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1341
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 14:14:44.287]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.Collections.Generic.KeyNotFoundException
The given key was not present in the dictionary.
   in System.ThrowHelper.ThrowKeyNotFoundException()
   in System.Collections.Generic.Dictionary`2.get_Item(TKey key)
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1348
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 14:17:43.546]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 14:17:48.958]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/HttpErrorPage.aspx?session=1
Severity: Exception

System.FormatException
The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters. 
   in System.Convert.FromBase64_Decode(Char* startInputPtr, Int32 inputLength, Byte* startDestPtr, Int32 destLength)
   in System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   in System.Convert.FromBase64String(String s)
   in System.Web.UI.ObjectStateFormatter.Deserialize(String inputString, Purpose purpose)
   in System.Web.UI.Util.DeserializeWithAssert(IStateFormatter2 formatter, String serializedState, Purpose purpose)
   in System.Web.UI.HiddenFieldPageStatePersister.Load()

System.Web.UI.ViewStateException
Invalid viewstate. 
	Client IP: 127.0.0.1
	Port: 56897
	Referer: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
	Path: /212240ALJUMUM/HttpErrorPage.aspx
	User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:135.0) Gecko/20100101 Firefox/135.0
	ViewState: Warning$TimerReloadWUC


System.Web.HttpException
The state information is invalid for this page and might be corrupted.
   in System.Web.UI.ViewStateException.ThrowError(Exception inner, String persistedState, String errorPageMessage, Boolean macValidationError)
   in System.Web.UI.HiddenFieldPageStatePersister.Load()
   in System.Web.UI.Page.LoadPageStateFromPersistenceMedium()
   in System.Web.UI.Page.LoadAllState()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.httperrorpage_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: The state information is invalid for this page and might be corrupted.

========================================================================================================================================================================================================
[2025/02/25 14:25:51.853]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentOutOfRangeException
Index was out of range. Must be non-negative and less than the size of the collection.
Nome parametro: index
   in System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   in System.Collections.Generic.List`1.get_Item(Int32 index)
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1340
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 15:04:16.730]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 15:05:22.838]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 15:07:23.296]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1628
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 15:59:46.556]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 15:59:46.951]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:04:23.438]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:07:22.676]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 16:07:22.989]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:11:10.690]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:27:32.508]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/25 16:28:00.968]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:31:10.985]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:33:20.157]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:36:52.117]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:39:54.877]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 16:39:55.172]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:43:01.321]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 16:44:03.079]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig()
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 16:44:31.037]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/25 16:55:53.298]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/25 16:56:03.318]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.Control.ValidateEvent(String uniqueID, String eventArgument)
   in System.Web.UI.WebControls.HiddenField.LoadPostData(String postDataKey, NameValueCollection postCollection)
   in System.Web.UI.Page.ProcessPostData(NameValueCollection postData, Boolean fBeforeLoad)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.default_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 08:14:26.885]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/26 08:20:05.546]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 68
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/26 08:20:31.792]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1629
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 09:29:22.603]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.Web.HttpRequestValidationException
A potentially dangerous Request.Form value was detected from the client (WebEdit$HiddenAskUserConfirmDataCycle="...bin SL502:<br>Bin SL502 some l...").
   in System.Web.HttpRequest.ValidateString(String value, String collectionKey, RequestValidationSource requestCollection)
   in System.Web.HttpRequest.ValidateHttpValueCollection(HttpValueCollection collection, RequestValidationSource requestCollection)
   in System.Web.HttpRequest.get_Form()
   in System.Web.HttpRequest.get_HasForm()
   in System.Web.UI.Page.GetCollectionBasedOnMethod(Boolean dontReturnNull)
   in System.Web.UI.Page.DeterminePostBackMode()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpRequestValidationException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: A potentially dangerous Request.Form value was detected from the client (WebEdit$HiddenAskUserConfirmDataCycle="...bin SL502:<br>Bin SL502 some l...").

========================================================================================================================================================================================================
[2025/02/26 09:31:27.068]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1394&session=1
Severity: Exception

System.Web.HttpRequestValidationException
A potentially dangerous Request.Form value was detected from the client (WebEdit$HiddenAskUserConfirmDataCycle="...bin SL401:<br>Job final produc...").
   in System.Web.HttpRequest.ValidateString(String value, String collectionKey, RequestValidationSource requestCollection)
   in System.Web.HttpRequest.ValidateHttpValueCollection(HttpValueCollection collection, RequestValidationSource requestCollection)
   in System.Web.HttpRequest.get_Form()
   in System.Web.HttpRequest.get_HasForm()
   in System.Web.UI.Page.GetCollectionBasedOnMethod(Boolean dontReturnNull)
   in System.Web.UI.Page.DeterminePostBackMode()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpRequestValidationException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: A potentially dangerous Request.Form value was detected from the client (WebEdit$HiddenAskUserConfirmDataCycle="...bin SL401:<br>Job final produc...").

========================================================================================================================================================================================================
[2025/02/26 10:28:58.236]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1395&session=1
Severity: Exception

System.ArgumentOutOfRangeException
Index was out of range. Must be non-negative and less than the size of the collection.
Nome parametro: index
   in System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   in System.Collections.Generic.List`1.get_Item(Int32 index)
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1686
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 10:31:33.977]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=3
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:33.977]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di origine insufficiente. Controllare srcIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di origine insufficiente. Controllare srcIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:34.424]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:34.840]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:35.844]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:36.849]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:37.058]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/HttpErrorPage.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:37.862]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:38.871]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:39.883]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:40.895]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:41.909]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:42.503]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:42.911]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:43.291]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=3
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:43.924]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:44.929]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:45.932]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:46.930]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:47.932]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:48.955]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:49.978]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:50.006]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:50.977]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:51.996]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:53.018]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:53.291]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=3
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:53.491]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:54.030]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:55.029]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:56.042]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:57.053]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:58.070]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:31:59.080]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:00.191]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:01.214]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:02.212]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:03.310]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:04.315]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:05.355]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:06.314]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:07.545]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:08.543]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:09.559]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:10.565]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:11.564]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:12.560]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:13.577]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=3
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:14.466]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:14.595]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:15.594]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:16.604]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:17.604]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:23.522]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:32:37.774]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:33:39.384]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:33:41.834]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:33:44.932]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:34:34.021]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:34:58.073]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:34:59.393]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:35:07.241]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:35:19.385]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:35:40.359]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:35:59.205]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:36:07.828]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:36:07.950]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:36:08.031]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=2
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:36:08.123]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:36:09.648]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/HttpErrorPage.aspx?session=2
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:37:15.306]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1
Severity: Exception

System.IndexOutOfRangeException
Indice oltre i limiti della matrice.
   in System.Array.Clear(Array array, Int32 index, Int32 length)
   in System.Collections.Generic.Dictionary`2.Clear()
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 66
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.IndexOutOfRangeException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Indice oltre i limiti della matrice.

========================================================================================================================================================================================================
[2025/02/26 10:42:18.798]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1395&session=1
Severity: Exception

System.ArgumentOutOfRangeException
Index was out of range. Must be non-negative and less than the size of the collection.
Nome parametro: index
   in System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   in System.Collections.Generic.List`1.get_Item(Int32 index)
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1692
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 10:56:29.831]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1395&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1630
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 10:57:36.529]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 68
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 48
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/26 11:49:47.212]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/26 11:55:13.617]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1396&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1630
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 12:07:21.138]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=FLOW_LOGS_ACTIVE&menuname=MENU_STOCKS&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/26 12:07:33.112]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1398&session=1
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 12:12:21.535]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/26 12:12:23.442]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1398&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1630
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 12:14:00.387]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_4&menuname=MENU_CYCLES&Id=1398&session=1
Severity: Exception

System.NullReferenceException
Object reference not set to an instance of an object.
   in myFunction.CtrlDataCycle(Screen scr, String& error_msg, Control root) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myFunction.vb:riga 1630
   in WUC_WUC_Edit.btnSubmit_Click(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\UserControl\WUC_Edit.ascx.vb:riga 267
   in System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   in System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 12:21:57.626]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/new.aspx?control=new&pagename=STOCK_MANUAL_CORR_REQ&menuname=MENU_STOCKS&session=1
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/26 14:52:32.456]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 14:52:32.469]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 14:52:32.711]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTION_REPORTS&menuname=MENU_LOTS&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 14:52:32.723]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTION_REPORTS&menuname=MENU_LOTS&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 15:16:42.439]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 15:16:42.451]
Browser: Chrome
UserHostAddress: *************
UserHostName: *************
Url: http://n094ocrim.ocrimdomain.local/212240ALJUMUM/default.aspx?control=view&pagename=CYCLE_4&menuname=MENU_CYCLES&CYC_ID=4&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 15:18:41.607]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTION_REPORTS&menuname=MENU_LOTS&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 15:18:41.617]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTION_REPORTS&menuname=MENU_LOTS&session=1
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 15:18:43.291]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTION_REPORTS&menuname=MENU_LOTS&session=4
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 15:18:43.303]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTION_REPORTS&menuname=MENU_LOTS&session=4
Severity: Exception

System.Data.OleDb.OleDbException
L'acccesso non è riuscito per l'utente 'sa'.
   at System.Data.OleDb.OleDbConnectionInternal..ctor(OleDbConnectionString constr, OleDbConnection connection)
   at System.Data.OleDb.OleDbConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningObject)
   at System.Data.ProviderBase.DbConnectionFactory.CreateNonPooledConnection(DbConnection owningConnection, DbConnectionPoolGroup poolGroup, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionInternal.OpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory)
   at System.Data.OleDb.OleDbConnection.Open()
   at WebDataBaseLayer.DataBase.ExecuteSQL_DataTable(String p_sql, Boolean bOnlyStringColumns) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 43

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL_DataTable, Message: strSQL: SELECT * FROM SYSTEM_USERS WHERE ID='1' L'acccesso non è riuscito per l'utente 'sa'.

========================================================================================================================================================================================================
[2025/02/26 15:39:42.335]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=status&pagename=status&menuname=MENU_SYSTEM&session=4
Severity: Exception

System.ArgumentException
È già stato aggiunto un elemento con la stessa chiave.
   in System.ThrowHelper.ThrowArgumentException(ExceptionResource resource)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: È già stato aggiunto un elemento con la stessa chiave.

========================================================================================================================================================================================================
[2025/02/26 15:42:41.478]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?control=view&pagename=PRODUCTION_REPORTS&menuname=MENU_LOTS&session=1
Severity: Exception

System.ArgumentException
Invalid postback or callback argument.  Event validation is enabled using <pages enableEventValidation="true"/> in configuration or <%@ Page EnableEventValidation="true" %> in a page.  For security purposes, this feature verifies that arguments to postback or callback events originate from the server control that originally rendered them.  If the data is valid and expected, use the ClientScriptManager.RegisterForEventValidation method in order to register the postback or callback data for validation.
   in System.Web.UI.ClientScriptManager.ValidateEvent(String uniqueId, String argument)
   in System.Web.UI.Control.ValidateEvent(String uniqueID, String eventArgument)
   in System.Web.UI.WebControls.HiddenField.LoadPostData(String postDataKey, NameValueCollection postCollection)
   in System.Web.UI.Page.ProcessPostData(NameValueCollection postData, Boolean fBeforeLoad)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)

System.Web.HttpUnhandledException
Generata eccezione di tipo 'System.Web.HttpUnhandledException'.
   in System.Web.UI.Page.HandleError(Exception e)
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.default_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpUnhandledException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Generata eccezione di tipo 'System.Web.HttpUnhandledException'.

========================================================================================================================================================================================================
[2025/02/26 15:48:01.824]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/Time.aspx
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/26 15:48:01.824]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?session=1&sessionExpired=YES
Severity: Exception

System.ArgumentException
Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.
   in System.Array.Copy(Array sourceArray, Int32 sourceIndex, Array destinationArray, Int32 destinationIndex, Int32 length, Boolean reliable)
   in System.Collections.Generic.Dictionary`2.Resize(Int32 newSize, Boolean forceNewHashCodes)
   in System.Collections.Generic.Dictionary`2.Insert(TKey key, TValue value, Boolean add)
   in ReportsTools.myReports.GetReportTypePerCycleConfig() in C:\ATMILL\212240ALJUMUM\dev\web\ReportsTools\myReports.vb:riga 75
   in myScript.UpdateManagedCycleReports() in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 78
   in myScript.UpdateJSResources(config m_config) in C:\ATMILL\212240ALJUMUM\target\Web\App_Code\myScript.vb:riga 99
   in ASP.global_asax.Session_Start(Object sender, EventArgs e) in C:\ATMILL\212240ALJUMUM\target\Web\global.asax:riga 53
   in System.Web.SessionState.SessionStateModule.CompleteAcquireState()
   in System.Web.SessionState.SessionStateModule.BeginAcquireState(Object source, EventArgs e, AsyncCallback cb, Object extraData)
   in System.Web.HttpApplication.AsyncEventExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.ArgumentException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: Lunghezza della matrice di destinazione insufficiente. Controllare destIndex, la lunghezza e i limiti inferiori della matrice.

========================================================================================================================================================================================================
[2025/02/26 15:48:22.750]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/edit.aspx?topmenuname=0&control=edit&pagename=CYCLE_10&menuname=MENU_CYCLES&Id=1394&session=4
Severity: Exception

System.Web.HttpRequestValidationException
A potentially dangerous Request.Form value was detected from the client (WebEdit$HiddenAskUserConfirmDataCycle="...bin SL604:<br>Job final produc...").
   in System.Web.HttpRequest.ValidateString(String value, String collectionKey, RequestValidationSource requestCollection)
   in System.Web.HttpRequest.ValidateHttpValueCollection(HttpValueCollection collection, RequestValidationSource requestCollection)
   in System.Web.HttpRequest.get_Form()
   in System.Web.HttpRequest.get_HasForm()
   in System.Web.UI.Page.GetCollectionBasedOnMethod(Boolean dontReturnNull)
   in System.Web.UI.Page.DeterminePostBackMode()
   in System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   in System.Web.UI.Page.ProcessRequest()
   in System.Web.UI.Page.ProcessRequest(HttpContext context)
   in ASP.edit_aspx.ProcessRequest(HttpContext context)
   in System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   in System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   in System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpRequestValidationException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: A potentially dangerous Request.Form value was detected from the client (WebEdit$HiddenAskUserConfirmDataCycle="...bin SL604:<br>Job final produc...").

========================================================================================================================================================================================================
[2025/02/26 16:44:34.489]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?topmenuname=MENU_RECIPES&control=view&deleted=yes&pagename=RECIPE_2&menuname=MENU_RECIPES&Id=150&session=1&MTR_ID=2&sql_where=+MTR_ID='2'
Severity: Exception

System.Data.OleDb.OleDbException
L'istruzione è stata interrotta.
L'istruzione DELETE è in conflitto con il vincolo REFERENCE "FK_PRODUCTION_REPORTS_RECIPE_LOGS". Il conflitto si è verificato nella tabella "dbo.PRODUCTION_REPORTS", column 'RECIPE_LOG_ID' del database "212240ALJUMUM".
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteNonQuery()
   at WebDataBaseLayer.DataBase.ExecuteSQL(String p_sql) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 19

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL, Message: L'istruzione è stata interrotta.
L'istruzione DELETE è in conflitto con il vincolo REFERENCE "FK_PRODUCTION_REPORTS_RECIPE_LOGS". Il conflitto si è verificato nella tabella "dbo.PRODUCTION_REPORTS", column 'RECIPE_LOG_ID' del database "212240ALJUMUM".

========================================================================================================================================================================================================
[2025/02/26 16:44:44.659]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?topmenuname=MENU_RECIPES&control=view&deleted=yes&pagename=RECIPE_2&menuname=MENU_RECIPES&Id=150&session=1&MTR_ID=2&sql_where=+MTR_ID='2'
Severity: Exception

System.Data.OleDb.OleDbException
L'istruzione è stata interrotta.
L'istruzione DELETE è in conflitto con il vincolo REFERENCE "FK_PRODUCTION_REPORTS_RECIPE_LOGS". Il conflitto si è verificato nella tabella "dbo.PRODUCTION_REPORTS", column 'RECIPE_LOG_ID' del database "212240ALJUMUM".
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteNonQuery()
   at WebDataBaseLayer.DataBase.ExecuteSQL(String p_sql) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 19

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL, Message: L'istruzione è stata interrotta.
L'istruzione DELETE è in conflitto con il vincolo REFERENCE "FK_PRODUCTION_REPORTS_RECIPE_LOGS". Il conflitto si è verificato nella tabella "dbo.PRODUCTION_REPORTS", column 'RECIPE_LOG_ID' del database "212240ALJUMUM".

========================================================================================================================================================================================================
[2025/02/26 16:55:37.313]
Browser: Firefox
UserHostAddress: 127.0.0.1
UserHostName: 127.0.0.1
Url: http://localhost/212240ALJUMUM/default.aspx?topmenuname=MENU_RECIPES&control=view&deleted=yes&pagename=RECIPE_2&menuname=MENU_RECIPES&Id=150&session=1&MTR_ID=2&sql_where=+MTR_ID='2'
Severity: Exception

System.Data.OleDb.OleDbException
L'istruzione è stata interrotta.
L'istruzione DELETE è in conflitto con il vincolo REFERENCE "FK_PRODUCTION_REPORTS_RECIPE_LOGS". Il conflitto si è verificato nella tabella "dbo.PRODUCTION_REPORTS", column 'RECIPE_LOG_ID' del database "212240ALJUMUM".
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteNonQuery()
   at WebDataBaseLayer.DataBase.ExecuteSQL(String p_sql) in C:\ATMILL\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\Database.vb:line 19

myException.myException
System.Data.OleDb.OleDbException - Assembly: WebDataBaseLayer, Class: DataBase, Method: ExecuteSQL, Message: L'istruzione è stata interrotta.
L'istruzione DELETE è in conflitto con il vincolo REFERENCE "FK_PRODUCTION_REPORTS_RECIPE_LOGS". Il conflitto si è verificato nella tabella "dbo.PRODUCTION_REPORTS", column 'RECIPE_LOG_ID' del database "212240ALJUMUM".

========================================================================================================================================================================================================
[2025/03/04 16:14:55.226]
Browser: Chrome
UserHostAddress: ::1
UserHostName: ::1
Url: http://localhost:49756/212240ALJUMUM/default.aspx?control=view&pagename=CELLS_ARCHIVE&menuname=MENU_STOCKS&session=1
Severity: Exception

System.Web.HttpException
The file '/212240ALJUMUM/default.aspx' does not exist.
   at System.Web.UI.Util.CheckVirtualFileExists(VirtualPath virtualPath)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   at System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   at System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   at System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   at System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   at System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: The file '/212240ALJUMUM/default.aspx' does not exist.

========================================================================================================================================================================================================
[2025/03/04 16:21:57.588]
Browser: Chrome
UserHostAddress: ::1
UserHostName: ::1
Url: http://localhost:49756/212240ALJUMUM/default.aspx?control=view&pagename=CELLS_ARCHIVE&menuname=MENU_STOCKS&session=1
Severity: Exception

System.Web.HttpException
The file '/212240ALJUMUM/default.aspx' does not exist.
   at System.Web.UI.Util.CheckVirtualFileExists(VirtualPath virtualPath)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   at System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   at System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   at System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   at System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   at System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: The file '/212240ALJUMUM/default.aspx' does not exist.

========================================================================================================================================================================================================
[2025/03/04 16:27:56.289]
Browser: Chrome
UserHostAddress: ::1
UserHostName: ::1
Url: http://localhost:49756/212240ALJUMUM/default.aspx?control=view&pagename=CELLS_ARCHIVE&menuname=MENU_STOCKS&session=1
Severity: Exception

System.Web.HttpException
The file '/212240ALJUMUM/default.aspx' does not exist.
   at System.Web.UI.Util.CheckVirtualFileExists(VirtualPath virtualPath)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   at System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   at System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   at System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   at System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   at System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: The file '/212240ALJUMUM/default.aspx' does not exist.

========================================================================================================================================================================================================
[2025/03/04 16:28:42.291]
Browser: Chrome
UserHostAddress: ::1
UserHostName: ::1
Url: http://localhost:49756/212240ALJUMUM/default.aspx?control=view&pagename=CELLS_ARCHIVE&menuname=MENU_STOCKS&session=1
Severity: Exception

System.Web.HttpException
The file '/212240ALJUMUM/default.aspx' does not exist.
   at System.Web.UI.Util.CheckVirtualFileExists(VirtualPath virtualPath)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   at System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   at System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   at System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   at System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   at System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   at System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)

myException.myException
System.Web.HttpException - Assembly: unhandled, Class: unhandled, Method: unhandled, Message: The file '/212240ALJUMUM/default.aspx' does not exist.

