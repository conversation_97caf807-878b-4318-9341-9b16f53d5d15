﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "Web", ".", "{40C29D33-EE74-4D74-9A09-CD57B727E285}"
	ProjectSection(ProjectDependencies) = postProject
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0} = {B70CDF77-F848-4489-AE96-3894CEA4E9C0}
	EndProjectSection
	ProjectSection(WebsiteProperties) = preProject
		SccProjectName = ""
		SccAuxPath = ""
		SccLocalPath = ""
		SccProvider = ""
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.8"
		ProjectReferences = "{7133BF8E-ADD6-4A8C-9FB0-70CD843035AA}|WebDataBaseLayer.dll;{A68913B9-4797-4F7F-B04E-49D8E51317C7}|UsersGUI.dll;{25A9C187-E619-44FD-B642-38E638C7AD95}|WebTools.dll;{61157CE1-707A-4345-899E-542C3235375C}|ReportsTools.dll;{C27E9B42-2E53-45B9-AED2-633BCC2F2955}|GenericsEvents.dll;{B70CDF77-F848-4489-AE96-3894CEA4E9C0}|myException.dll;{47E61E14-51B6-45C9-B689-254AEB4568D8}|LogTools.dll;{BEE40E48-7F7B-4420-9ADB-017104FCFA93}|CommonDefines.dll;{1E6274F5-BD5C-4882-AF0E-FEF8E8934941}|AtMillConfig.dll;{A79A0F4E-01F1-412A-AADD-D34F64C49EDE}|StandardDefines.dll;{1092601A-37D1-4C81-9C83-1E551AB488F0}|myGhostId.dll;"
		Debug.AspNetCompiler.VirtualPath = "/web"
		Debug.AspNetCompiler.PhysicalPath = "..\web\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\web\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/web"
		Release.AspNetCompiler.PhysicalPath = "..\web\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\web\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "49756"
		VWDVirtualPath = "/200000CORE"
		SlnRelativePath = "..\Web\"
	EndProjectSection
	ProjectSection(SvnOrigin) = preProject
		@Keys = Enlist
		Enlist = Required
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "UsersGUI", "..\..\..\WEB2.0\UsersGUI\UsersGUI\UsersGUI.vbproj", "{A68913B9-4797-4F7F-B04E-49D8E51317C7}"
	ProjectSection(ProjectDependencies) = postProject
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0} = {B70CDF77-F848-4489-AE96-3894CEA4E9C0}
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "WebDataBaseLayer", "..\..\..\WEB2.0\WebDataBaseLayer\WebDataBaseLayer\WebDataBaseLayer.vbproj", "{7133BF8E-ADD6-4A8C-9FB0-70CD843035AA}"
	ProjectSection(ProjectDependencies) = postProject
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0} = {B70CDF77-F848-4489-AE96-3894CEA4E9C0}
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "WebTools", "..\..\..\WEB2.0\WebTools\WebTools\WebTools.vbproj", "{25A9C187-E619-44FD-B642-38E638C7AD95}"
	ProjectSection(ProjectDependencies) = postProject
		{C27E9B42-2E53-45B9-AED2-633BCC2F2955} = {C27E9B42-2E53-45B9-AED2-633BCC2F2955}
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0} = {B70CDF77-F848-4489-AE96-3894CEA4E9C0}
		{A68913B9-4797-4F7F-B04E-49D8E51317C7} = {A68913B9-4797-4F7F-B04E-49D8E51317C7}
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "GenericsEvents", "..\..\dev\web\GenericsEvents\GenericsEvents.vbproj", "{C27E9B42-2E53-45B9-AED2-633BCC2F2955}"
	ProjectSection(ProjectDependencies) = postProject
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0} = {B70CDF77-F848-4489-AE96-3894CEA4E9C0}
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ReportsTools", "..\..\dev\web\ReportsTools\ReportsTools.vbproj", "{61157CE1-707A-4345-899E-542C3235375C}"
	ProjectSection(ProjectDependencies) = postProject
		{C27E9B42-2E53-45B9-AED2-633BCC2F2955} = {C27E9B42-2E53-45B9-AED2-633BCC2F2955}
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0} = {B70CDF77-F848-4489-AE96-3894CEA4E9C0}
	EndProjectSection
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "myException", "..\..\..\WEB2.0\myException\myException.vbproj", "{B70CDF77-F848-4489-AE96-3894CEA4E9C0}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "LogTools", "..\..\..\WEB2.0\LogTools\LogTools\LogTools.vbproj", "{47E61E14-51B6-45C9-B689-254AEB4568D8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CommonDefines", "..\..\dev\web\CommonDefines\CommonDefines.csproj", "{BEE40E48-7F7B-4420-9ADB-017104FCFA93}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AtMillConfig", "..\..\..\WEB2023\AtMillConfig\AtMillConfig.csproj", "{1E6274F5-BD5C-4882-AF0E-FEF8E8934941}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "StandardDefines", "..\..\..\WEB2023\StandardDefines\StandardDefines.csproj", "{A79A0F4E-01F1-412A-AADD-D34F64C49EDE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "myGhostId", "..\..\..\WEB2023\myGhostId\myGhostId\myGhostId.csproj", "{1092601A-37D1-4C81-9C83-1E551AB488F0}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{40C29D33-EE74-4D74-9A09-CD57B727E285}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{40C29D33-EE74-4D74-9A09-CD57B727E285}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{40C29D33-EE74-4D74-9A09-CD57B727E285}.Release|Any CPU.ActiveCfg = Debug|Any CPU
		{40C29D33-EE74-4D74-9A09-CD57B727E285}.Release|Any CPU.Build.0 = Debug|Any CPU
		{A68913B9-4797-4F7F-B04E-49D8E51317C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A68913B9-4797-4F7F-B04E-49D8E51317C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A68913B9-4797-4F7F-B04E-49D8E51317C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A68913B9-4797-4F7F-B04E-49D8E51317C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{7133BF8E-ADD6-4A8C-9FB0-70CD843035AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7133BF8E-ADD6-4A8C-9FB0-70CD843035AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7133BF8E-ADD6-4A8C-9FB0-70CD843035AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7133BF8E-ADD6-4A8C-9FB0-70CD843035AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{25A9C187-E619-44FD-B642-38E638C7AD95}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25A9C187-E619-44FD-B642-38E638C7AD95}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25A9C187-E619-44FD-B642-38E638C7AD95}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25A9C187-E619-44FD-B642-38E638C7AD95}.Release|Any CPU.Build.0 = Release|Any CPU
		{C27E9B42-2E53-45B9-AED2-633BCC2F2955}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C27E9B42-2E53-45B9-AED2-633BCC2F2955}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C27E9B42-2E53-45B9-AED2-633BCC2F2955}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C27E9B42-2E53-45B9-AED2-633BCC2F2955}.Release|Any CPU.Build.0 = Release|Any CPU
		{61157CE1-707A-4345-899E-542C3235375C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61157CE1-707A-4345-899E-542C3235375C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61157CE1-707A-4345-899E-542C3235375C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61157CE1-707A-4345-899E-542C3235375C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B70CDF77-F848-4489-AE96-3894CEA4E9C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{47E61E14-51B6-45C9-B689-254AEB4568D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47E61E14-51B6-45C9-B689-254AEB4568D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47E61E14-51B6-45C9-B689-254AEB4568D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47E61E14-51B6-45C9-B689-254AEB4568D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{BEE40E48-7F7B-4420-9ADB-017104FCFA93}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BEE40E48-7F7B-4420-9ADB-017104FCFA93}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BEE40E48-7F7B-4420-9ADB-017104FCFA93}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BEE40E48-7F7B-4420-9ADB-017104FCFA93}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E6274F5-BD5C-4882-AF0E-FEF8E8934941}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E6274F5-BD5C-4882-AF0E-FEF8E8934941}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E6274F5-BD5C-4882-AF0E-FEF8E8934941}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E6274F5-BD5C-4882-AF0E-FEF8E8934941}.Release|Any CPU.Build.0 = Release|Any CPU
		{A79A0F4E-01F1-412A-AADD-D34F64C49EDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A79A0F4E-01F1-412A-AADD-D34F64C49EDE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A79A0F4E-01F1-412A-AADD-D34F64C49EDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A79A0F4E-01F1-412A-AADD-D34F64C49EDE}.Release|Any CPU.Build.0 = Release|Any CPU
		{1092601A-37D1-4C81-9C83-1E551AB488F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1092601A-37D1-4C81-9C83-1E551AB488F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1092601A-37D1-4C81-9C83-1E551AB488F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1092601A-37D1-4C81-9C83-1E551AB488F0}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F017A2B4-8C37-4FA6-926F-1F43DC3ADECF}
	EndGlobalSection
	GlobalSection(SubversionScc) = preSolution
		Svn-Managed = True
		Manager = AnkhSVN - Subversion Support for Visual Studio
	EndGlobalSection
EndGlobal
