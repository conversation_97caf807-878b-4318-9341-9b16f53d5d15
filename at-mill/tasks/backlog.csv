Task ID,Title,Phase,Priority,Estimated Hours,Assigned To,Status,Due Date,Description
task_001,Setup Backend .NET Core 9 e Architettura API-First,phase_01,Critical,24,Tech Lead + Backend Developer,Not Started,2025-07-07,Configurazione progetto .NET Core 9 con Clean Architecture
task_002,Analisi Database e Creazione API Core Entities,phase_02,Critical,40,Backend Developer + Tech Lead,Not Started,2025-07-14,Analisi database SQL Server e implementazione API REST per entità core
task_003,Sistema Configurazione XML Parser,phase_02,Critical,32,Backend Developer,Not Started,2025-07-21,Migrazione sistema di configurazione XML per gestione dinamica UI
task_004,Autenticazione JWT e Autorizzazione,phase_02,Critical,24,Backend Developer,Not Started,2025-07-28,Implementazione sistema autenticazione JWT e gestione permessi
task_005,API Business Logic Services,phase_03,High,48,Backend Developer,Not Started,2025-08-11,Migrazione logiche VB.NET in Services C# con pattern CQRS
task_006,API Modulo Stocks (Giacenze),phase_03,High,32,Backend Developer,Not Started,2025-08-25,Implementazione API complete per gestione giacenze e inventario
task_007,API Modulo Maintenance (Manutenzione),phase_03,High,32,Backend Developer,Not Started,2025-09-08,Implementazione API per gestione manutenzione e ricambi
task_008,API Modulo Production (Produzione),phase_03,High,40,Backend Developer,Not Started,2025-09-22,Implementazione API per gestione produzione e ricette
task_009,Sistema Reporting e Analytics,phase_04,Medium,24,Backend Developer,Not Started,2025-10-06,Implementazione API per report dinamici e analytics
task_010,Integrazione PLM e Command Interface,phase_04,High,32,Backend Developer + Tech Lead,Not Started,2025-10-20,Migrazione integrazione PLM e interfaccia comandi
task_011,Performance Optimization e Caching,phase_04,Medium,16,Backend Developer,Not Started,2025-11-03,Ottimizzazione performance API e implementazione caching
task_012,Testing Completo e Quality Assurance,phase_05,High,32,QA Engineer + Backend Developer,Not Started,2025-11-17,Testing completo API e quality assurance
task_013,Documentazione API e Wiki Tecnico,phase_05,Medium,16,Tech Lead,Not Started,2025-12-01,Documentazione completa API e creazione wiki tecnico
task_014,Setup Frontend React Base,phase_06,High,32,Frontend Developer,Not Started,2025-12-15,Setup progetto React TypeScript per consumo API
task_015,Componenti UI per Moduli Core,phase_06,High,48,Frontend Developer,Not Started,2026-01-12,Sviluppo componenti React per moduli principali
task_016,Dashboard e Interfacce Avanzate,phase_06,Medium,32,Frontend Developer,Not Started,2026-02-02,Dashboard real-time e interfacce utente avanzate
task_017,Integrazione Frontend-Backend Completa,phase_06,High,24,Full Team,Not Started,2026-02-16,Integrazione completa frontend con API backend
task_018,Deploy Production e Go-Live,phase_07,Critical,40,DevOps + Tech Lead,Not Started,2026-03-02,Deployment produzione e go-live sistema
task_019,Utility Azure DevOps Python,phase_01,Low,16,Tech Lead,Not Started,2025-07-07,Sviluppo utility Python per integrazione Azure DevOps
task_020,Handover e Training Team,phase_07,Medium,24,Tech Lead,Not Started,2026-03-16,Handover finale e training team di manutenzione