# OCRIM AtMill - Task Management Tools

Strumenti per la gestione e monitoraggio dei task del progetto OCRIM AtMill Revamping.

## 📁 File Principali

- **`backlog.csv`** - Backlog principale del progetto con tutti i task
- **`dashboard.html`** - Dashboard web per visualizzazione stato task
- **`task_sync.py`** - Script Python per sincronizzazione con Azure DevOps
- **`serve_dashboard.py`** - Server HTTP locale per la dashboard
- **`task_001.json`**, **`task_002.json`**, etc. - Dettagli specifici dei task

## 🚀 Quick Start

### 1. Visualizzare la Dashboard

```bash
# Avvia server locale
python serve_dashboard.py

# Oppure specifica una porta diversa
python serve_dashboard.py --port 8081
```

La dashboard si aprirà automaticamente nel browser su `http://localhost:8080/dashboard.html`

### 2. Sincronizzazione con Azure DevOps

```bash
# Installa dipendenze
pip install -r requirements.txt

# Configura variabili d'ambiente
export PAT="your_azure_devops_personal_access_token"
export AZURE_DEVOPS_ORG="https://dev.azure.com/ocrim-atmill/"
export AZURE_DEVOPS_PROJECT="AtMill-Revamping"

# Esegui sincronizzazione
python task_sync.py
```

## 📊 Dashboard Features

La dashboard HTML/JavaScript offre:

- **📈 Statistiche Real-time**: Contatori task per stato e priorità
- **📊 Progress Bar**: Visualizzazione progresso generale
- **🗂️ Organizzazione per Fasi**: Task raggruppati per fase di progetto
- **🎨 UI Moderna**: Interfaccia responsive e user-friendly
- **🔄 Auto-refresh**: Aggiornamento automatico ogni 5 minuti
- **📱 Mobile-friendly**: Ottimizzata per dispositivi mobili

### Colori e Priorità

- 🔴 **Critical** - Rosso
- 🟠 **High** - Arancione  
- 🔵 **Medium** - Blu
- 🟢 **Low** - Verde

### Stati Task

- ⚪ **Not Started** - Da iniziare
- 🟡 **In Progress** - In corso
- 🟢 **Completed** - Completato

## 🔧 Task Sync Features

Lo script `task_sync.py` permette di:

- **📤 Creare work items** in Azure DevOps da task CSV
- **🔄 Sincronizzare stati** bidirezionalmente
- **📝 Mappare campi** (priorità, assegnazioni, date)
- **🏷️ Gestire tag** per categorizzazione
- **💾 Mantenere mapping** task_id ↔ work_item_id

### Mapping Campi

| CSV Field | Azure DevOps Field |
|-----------|-------------------|
| Title | System.Title |
| Description | System.Description |
| Status | System.State |
| Priority | Microsoft.VSTS.Common.Priority |
| Estimated Hours | Microsoft.VSTS.Scheduling.OriginalEstimate |
| Assigned To | System.AssignedTo |
| Due Date | Microsoft.VSTS.Scheduling.DueDate |

## 📝 Formato CSV

Il file `backlog.csv` deve avere le seguenti colonne:

```csv
Task ID,Title,Phase,Priority,Estimated Hours,Assigned To,Status,Due Date,Description
task_001,Setup Backend .NET Core 9,phase_01,Critical,24,Tech Lead,Not Started,2025-07-07,Configurazione progetto...
```

### Campi Obbligatori

- **Task ID**: Identificativo univoco (es. task_001)
- **Title**: Titolo del task
- **Phase**: Fase del progetto (phase_01, phase_02, etc.)
- **Priority**: Critical, High, Medium, Low
- **Status**: Not Started, In Progress, Completed
- **Estimated Hours**: Ore stimate (numero intero)

## 🔐 Configurazione Azure DevOps

### 1. Personal Access Token (PAT)

1. Vai su Azure DevOps → User Settings → Personal Access Tokens
2. Crea nuovo token con permessi:
   - **Work Items**: Read & Write
   - **Project and Team**: Read
3. Copia il token e impostalo come variabile d'ambiente `PAT`

### 2. Variabili d'Ambiente

```bash
# Linux/Mac
export PAT="your_token_here"
export AZURE_DEVOPS_ORG="https://dev.azure.com/ocrim-atmill/"
export AZURE_DEVOPS_PROJECT="AtMill-Revamping"

# Windows
set PAT=your_token_here
set AZURE_DEVOPS_ORG=https://dev.azure.com/ocrim-atmill/
set AZURE_DEVOPS_PROJECT=AtMill-Revamping
```

### 3. File .env (Alternativo)

Crea un file `.env` nella directory tasks:

```env
PAT=your_azure_devops_personal_access_token
AZURE_DEVOPS_ORG=https://dev.azure.com/ocrim-atmill/
AZURE_DEVOPS_PROJECT=AtMill-Revamping
```

## 🛠️ Utilizzo Avanzato

### Aggiornamento Status Task

```python
from task_sync import TaskSynchronizer

sync = TaskSynchronizer('backlog.csv')
sync.update_csv_status('task_001', 'In Progress')
```

### Sincronizzazione Selettiva

```python
# Sincronizza solo task critici
tasks = sync.load_tasks_from_csv()
critical_tasks = [t for t in tasks if t.priority == 'Critical']
sync.sync_to_azure_devops(azure_client, critical_tasks)
```

## 📋 Workflow Consigliato

1. **📝 Aggiorna backlog.csv** con nuovi task o modifiche
2. **🔄 Esegui task_sync.py** per sincronizzare con Azure DevOps
3. **📊 Monitora dashboard.html** per tracking progresso
4. **🔁 Ripeti** il ciclo per mantenere allineamento

## 🐛 Troubleshooting

### Dashboard non carica dati

- Verifica che `backlog.csv` sia nella stessa directory di `dashboard.html`
- Controlla console browser per errori JavaScript
- Assicurati che il server HTTP sia avviato correttamente

### Errori sincronizzazione Azure DevOps

- Verifica PAT token e permessi
- Controlla connessione internet
- Verifica nome progetto e organization URL
- Consulta log in `task_sync.log`

### Formato CSV non valido

- Verifica header CSV corretto
- Controlla encoding UTF-8
- Assicurati che non ci siano virgole extra nei campi

## 📚 Risorse

- [Azure DevOps REST API](https://docs.microsoft.com/en-us/rest/api/azure/devops/)
- [Python Azure DevOps SDK](https://github.com/Microsoft/azure-devops-python-api)
- [CSV Format Specification](https://tools.ietf.org/html/rfc4180)

---

*Creato per il progetto OCRIM AtMill Revamping - 2025*
