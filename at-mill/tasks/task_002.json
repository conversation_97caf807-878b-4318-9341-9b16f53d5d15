{"task_id": "task_002", "title": "Analisi Database e Creazione API Core Entities", "phase": "phase_02", "priority": "Critical", "estimated_hours": 40, "assigned_to": "Backend Developer + Tech Lead", "status": "Not Started", "created_date": "2025-06-30", "due_date": "2025-07-14", "description": "Analisi approfondita del database SQL Server esistente, creazione modelli Entity Framework e implementazione API REST per entità core del sistema OCRIM AtMill", "acceptance_criteria": ["Database SQL Server analizzato e documentato", "Modelli Entity Framework Core creati per tutte le entità principali", "Repository pattern implementato per entità core", "API REST CRUD funzionanti per entità principali", "Validazioni business implementate con FluentValidation", "Swagger documentation completa per tutte le API", "Unit test per repository e API controllers"], "technical_requirements": {"database_analysis": {"target_tables": ["SYSTEM_USERS", "SYSTEM_GROUPS", "SYSTEM_USERS_GROUPS", "SYSTEM_PARAMETERS", "PRODUCTS", "EQUIPMENTS", "EQUIPMENT_MODELS", "STOCKS", "BINS", "LOTS", "RECIPES", "PRODUCTION_PLANS", "PRODUCTION_REPORTS", "MAINTENANCE_PROCEDURES", "SPARE_PARTS", "FLOW_LOGS"], "analysis_tasks": ["Schema analysis e relazioni", "Constraints e indici", "Stored procedures utilizzate", "Views e funzioni", "<PERSON><PERSON> es<PERSON>"]}, "api_endpoints": {"core_entities": ["/api/users", "/api/groups", "/api/products", "/api/equipments", "/api/stocks", "/api/recipes"], "operations": ["GET /api/{entity} - List with pagination", "GET /api/{entity}/{id} - Get by ID", "POST /api/{entity} - Create new", "PUT /api/{entity}/{id} - Update", "DELETE /api/{entity}/{id} - Delete"]}}, "implementation_steps": [{"step": 1, "title": "Database Schema Analysis", "description": "Analisi completa dello schema database esistente", "tasks": ["Connessione al database SQL Server esistente", "Generazione diagramma ER delle tabelle principali", "Documentazione relazioni e constraints", "Identificazione stored procedures e views utilizzate", "Analisi indici e performance"], "estimated_hours": 8}, {"step": 2, "title": "Entity Framework Models", "description": "Creazione modelli Entity Framework Core", "tasks": ["Scaffold-DbContext per generazione automatica modelli", "Refactoring modelli generati secondo convenzioni", "Configurazione relazioni con Fluent API", "Setup Data Annotations per validazioni", "Configurazione DbContext con Dependency Injection"], "estimated_hours": 12}, {"step": 3, "title": "Repository Pattern Implementation", "description": "Implementazione Repository pattern e Unit of Work", "tasks": ["Creazione interfacce IRepository generiche", "Implementazione BaseRepository con operazioni CRUD", "Creazione repository specifici per entità complesse", "Implementazione Unit of Work pattern", "Setup Dependency Injection per repositories"], "estimated_hours": 10}, {"step": 4, "title": "API Controllers Development", "description": "Sviluppo API REST controllers per entità core", "tasks": ["Creazione BaseController con operazioni comuni", "Implementazione controllers per entità principali", "Setup AutoMapper per DTO mapping", "Implementazione paginazione e filtering", "Configurazione Swagger annotations"], "estimated_hours": 8}, {"step": 5, "title": "Validation e Testing", "description": "Implementazione validazioni e testing", "tasks": ["Setup FluentValidation per DTOs", "Implementazione validazioni business", "Creazione unit test per repositories", "Creazione integration test per API", "Setup test database con TestContainers"], "estimated_hours": 8}], "dependencies": ["task_001"], "database_entities": {"system_entities": [{"table": "SYSTEM_USERS", "description": "Utenti del sistema", "key_fields": ["ID", "USERNAME", "PASSWORD", "EMAIL"], "relationships": ["SYSTEM_USERS_GROUPS"]}, {"table": "SYSTEM_GROUPS", "description": "<PERSON><PERSON><PERSON>/ruoli utenti", "key_fields": ["ID", "NAME", "DESCRIPTION"], "relationships": ["SYSTEM_USERS_GROUPS"]}, {"table": "SYSTEM_PARAMETERS", "description": "Parametri di configurazione sistema", "key_fields": ["ID", "PARAMETER_NAME", "PARAMETER_VALUE"], "relationships": []}], "production_entities": [{"table": "PRODUCTS", "description": "Prodotti finiti e intermedi", "key_fields": ["ID", "NAME", "DESCRIPTION", "UNIT_OF_MEASURE"], "relationships": ["RECIPES", "STOCKS", "PRODUCTION_REPORTS"]}, {"table": "EQUIPMENTS", "description": "Macchine e attrezzature", "key_fields": ["ID", "DESCRIPTION", "MODEL_ID", "STATUS"], "relationships": ["EQUIPMENT_MODELS", "MAINTENANCE_PROCEDURES"]}, {"table": "RECIPES", "description": "Ricette di produzione", "key_fields": ["ID", "NAME", "DESCRIPTION", "PRODUCT_ID"], "relationships": ["PRODUCTS", "PRODUCTION_PLANS"]}], "inventory_entities": [{"table": "STOCKS", "description": "<PERSON><PERSON><PERSON><PERSON> correnti", "key_fields": ["ID", "PRODUCT_ID", "BIN_ID", "QUANTITY"], "relationships": ["PRODUCTS", "BINS", "LOTS"]}, {"table": "BINS", "description": "Celle di stoccaggio", "key_fields": ["ID", "NAME", "CAPACITY", "CURRENT_QUANTITY"], "relationships": ["STOCKS", "FLOW_LOGS"]}, {"table": "FLOW_LOGS", "description": "Log movimentazioni", "key_fields": ["ID", "BIN_ID", "PRODUCT_ID", "QUANTITY", "TIMESTAMP"], "relationships": ["BINS", "PRODUCTS", "LOTS"]}]}, "api_specifications": {"base_url": "/api/v1", "authentication": "JWT <PERSON>", "response_format": "JSON", "error_handling": "RFC 7807 Problem Details", "pagination": "Offset-based with metadata", "filtering": "OData-style query parameters", "sorting": "Multiple field sorting support"}, "testing_strategy": {"unit_tests": {"framework": "xUnit", "coverage_target": "90%", "focus": ["Repository methods", "Business logic", "Validations"]}, "integration_tests": {"framework": "ASP.NET Core Test Host", "database": "TestContainers SQL Server", "focus": ["API endpoints", "Database operations", "Authentication"]}}, "deliverables": [{"name": "Database Analysis Document", "type": "Documentation", "description": "Documentazione completa schema database e relazioni"}, {"name": "Entity Framework Models", "type": "Code", "description": "Modelli EF Core per tutte le entità principali"}, {"name": "Repository Layer", "type": "Code", "description": "Repository pattern implementato con Unit of Work"}, {"name": "Core API Controllers", "type": "Code", "description": "API REST per entità core con CRUD operations"}, {"name": "API Documentation", "type": "Documentation", "description": "Swagger/OpenAPI documentation completa"}, {"name": "Test Suite", "type": "Code", "description": "Unit e integration test per API e repository"}], "risks": [{"risk": "Complessità schema database esistente", "impact": "High", "probability": "Medium", "mitigation": "Analisi graduale e reverse engineering assistito"}, {"risk": "Performance issues con Entity Framework", "impact": "Medium", "probability": "Medium", "mitigation": "Ottimizzazione query e lazy loading configurato"}], "notes": ["Questo task è critico per stabilire le fondamenta API", "La qualità dei modelli EF impatterà tutto lo sviluppo futuro", "Focus su entità core per permettere sviluppo parallelo frontend"]}