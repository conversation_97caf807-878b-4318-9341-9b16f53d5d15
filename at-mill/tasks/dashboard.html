<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCRIM AtMill - Project Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .critical { color: #e74c3c; }
        .high { color: #f39c12; }
        .medium { color: #3498db; }
        .low { color: #27ae60; }

        .progress-section {
            padding: 30px;
        }

        .progress-bar {
            background: #ecf0f1;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .tasks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            padding: 30px;
        }

        .task-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #3498db;
            transition: all 0.3s ease;
        }

        .task-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .task-id {
            background: #ecf0f1;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .task-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .task-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
            color: #666;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-not-started {
            background: #ecf0f1;
            color: #7f8c8d;
        }

        .status-in-progress {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1em;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #2980b9;
            transform: scale(1.05);
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .phase-section {
            margin: 30px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .phase-header {
            background: #34495e;
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .phase-tasks {
            padding: 20px;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .tasks-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 OCRIM AtMill Revamping</h1>
            <p>Project Dashboard - Real-time Task Monitoring</p>
        </div>

        <div class="stats-grid" id="statsGrid">
            <div class="loading">Caricamento statistiche...</div>
        </div>

        <div class="progress-section">
            <h3>Progresso Generale</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <p id="progressText">0% completato</p>
        </div>

        <div id="phasesContainer">
            <div class="loading">Caricamento task...</div>
        </div>
    </div>

    <button class="refresh-btn" onclick="loadData()" title="Aggiorna dati">
        🔄 Aggiorna
    </button>

    <script>
        let tasks = [];
        let stats = {};

        async function loadCSV() {
            try {
                const response = await fetch('backlog.csv');
                const csvText = await response.text();
                return parseCSV(csvText);
            } catch (error) {
                console.error('Errore nel caricamento del CSV:', error);
                return [];
            }
        }

        function parseCSV(csvText) {
            const lines = csvText.trim().split('\n');
            const headers = lines[0].split(',');
            const data = [];

            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',');
                const row = {};
                headers.forEach((header, index) => {
                    row[header.trim()] = values[index] ? values[index].trim() : '';
                });
                data.push(row);
            }

            return data;
        }

        function calculateStats(tasks) {
            const stats = {
                total: tasks.length,
                notStarted: 0,
                inProgress: 0,
                completed: 0,
                critical: 0,
                high: 0,
                medium: 0,
                low: 0,
                totalHours: 0
            };

            tasks.forEach(task => {
                // Status
                switch (task.Status.toLowerCase()) {
                    case 'not started':
                        stats.notStarted++;
                        break;
                    case 'in progress':
                        stats.inProgress++;
                        break;
                    case 'completed':
                        stats.completed++;
                        break;
                }

                // Priority
                switch (task.Priority.toLowerCase()) {
                    case 'critical':
                        stats.critical++;
                        break;
                    case 'high':
                        stats.high++;
                        break;
                    case 'medium':
                        stats.medium++;
                        break;
                    case 'low':
                        stats.low++;
                        break;
                }

                // Hours
                const hours = parseInt(task['Estimated Hours']) || 0;
                stats.totalHours += hours;
            });

            return stats;
        }

        function renderStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            const completionRate = ((stats.completed / stats.total) * 100).toFixed(1);

            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total}</div>
                    <div class="stat-label">Task Totali</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #27ae60">${stats.completed}</div>
                    <div class="stat-label">Completati</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f39c12">${stats.inProgress}</div>
                    <div class="stat-label">In Corso</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #95a5a6">${stats.notStarted}</div>
                    <div class="stat-label">Da Iniziare</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number critical">${stats.critical}</div>
                    <div class="stat-label">Critici</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.totalHours}h</div>
                    <div class="stat-label">Ore Stimate</div>
                </div>
            `;

            // Update progress bar
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            progressFill.style.width = `${completionRate}%`;
            progressText.textContent = `${completionRate}% completato (${stats.completed}/${stats.total} task)`;
        }

        function getStatusClass(status) {
            switch (status.toLowerCase()) {
                case 'not started': return 'status-not-started';
                case 'in progress': return 'status-in-progress';
                case 'completed': return 'status-completed';
                default: return 'status-not-started';
            }
        }

        function getPriorityColor(priority) {
            switch (priority.toLowerCase()) {
                case 'critical': return '#e74c3c';
                case 'high': return '#f39c12';
                case 'medium': return '#3498db';
                case 'low': return '#27ae60';
                default: return '#95a5a6';
            }
        }

        function renderTasks(tasks) {
            const phases = {};
            
            // Group tasks by phase
            tasks.forEach(task => {
                const phase = task.Phase || 'phase_01';
                if (!phases[phase]) {
                    phases[phase] = [];
                }
                phases[phase].push(task);
            });

            const container = document.getElementById('phasesContainer');
            container.innerHTML = '';

            Object.keys(phases).sort().forEach(phase => {
                const phaseDiv = document.createElement('div');
                phaseDiv.className = 'phase-section';
                
                const phaseHeader = document.createElement('div');
                phaseHeader.className = 'phase-header';
                phaseHeader.textContent = `Fase ${phase.replace('phase_', '')}`;
                
                const tasksGrid = document.createElement('div');
                tasksGrid.className = 'tasks-grid';
                
                phases[phase].forEach(task => {
                    const taskCard = document.createElement('div');
                    taskCard.className = 'task-card';
                    taskCard.style.borderLeftColor = getPriorityColor(task.Priority);
                    
                    taskCard.innerHTML = `
                        <div class="task-header">
                            <span class="task-id">${task['Task ID']}</span>
                            <span class="status-badge ${getStatusClass(task.Status)}">${task.Status}</span>
                        </div>
                        <div class="task-title">${task.Title}</div>
                        <div class="task-meta">
                            <div><strong>Priorità:</strong> <span style="color: ${getPriorityColor(task.Priority)}">${task.Priority}</span></div>
                            <div><strong>Ore:</strong> ${task['Estimated Hours']}h</div>
                            <div><strong>Assegnato:</strong> ${task['Assigned To']}</div>
                            <div><strong>Scadenza:</strong> ${task['Due Date']}</div>
                        </div>
                    `;
                    
                    tasksGrid.appendChild(taskCard);
                });
                
                phaseDiv.appendChild(phaseHeader);
                phaseDiv.appendChild(tasksGrid);
                container.appendChild(phaseDiv);
            });
        }

        async function loadData() {
            try {
                tasks = await loadCSV();
                stats = calculateStats(tasks);
                renderStats(stats);
                renderTasks(tasks);
                
                // Update timestamp
                const now = new Date().toLocaleString('it-IT');
                document.title = `OCRIM AtMill Dashboard - Aggiornato: ${now}`;
            } catch (error) {
                console.error('Errore nel caricamento dei dati:', error);
                document.getElementById('statsGrid').innerHTML = '<div class="loading">Errore nel caricamento dei dati</div>';
            }
        }

        // Load data on page load
        document.addEventListener('DOMContentLoaded', loadData);

        // Auto-refresh every 5 minutes
        setInterval(loadData, 5 * 60 * 1000);
    </script>
</body>
</html>
