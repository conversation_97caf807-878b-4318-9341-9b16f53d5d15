#!/usr/bin/env python3
"""
Simple HTTP server per servire la dashboard localmente
"""

import http.server
import socketserver
import webbrowser
import os
from pathlib import Path

def serve_dashboard(port=8080):
    """Avvia server HTTP per la dashboard"""
    
    # Cambia directory alla cartella tasks
    tasks_dir = Path(__file__).parent
    os.chdir(tasks_dir)
    
    # Configura server
    handler = http.server.SimpleHTTPRequestHandler
    
    # Aggiungi header CORS per permettere fetch del CSV
    class CORSRequestHandler(handler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
    
    try:
        with socketserver.TCPServer(("", port), CORSRequestHandler) as httpd:
            print(f"🚀 Dashboard server avviato su http://localhost:{port}")
            print(f"📊 Dashboard disponibile su: http://localhost:{port}/dashboard.html")
            print("🔄 Premi Ctrl+C per fermare il server")
            
            # Apri automaticamente il browser
            webbrowser.open(f'http://localhost:{port}/dashboard.html')
            
            # Avvia server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server fermato")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Porta {port} già in uso. Prova con un'altra porta:")
            print(f"   python serve_dashboard.py --port {port + 1}")
        else:
            print(f"❌ Errore nell'avvio del server: {e}")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Serve OCRIM AtMill Dashboard')
    parser.add_argument('--port', type=int, default=8080, help='Porta del server (default: 8080)')
    
    args = parser.parse_args()
    serve_dashboard(args.port)
