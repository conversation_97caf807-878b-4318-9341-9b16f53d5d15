#!/usr/bin/env python3
"""
OCRIM AtMill - Task Synchronization Utility
Sincronizza task dal backlog.csv con Azure DevOps work items
"""

import os
import csv
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass
from pathlib import Path

try:
    from azure.devops.connection import Connection
    from azure.devops.v7_1.work_item_tracking import WorkItemTrackingClient
    from azure.devops.v7_1.work_item_tracking.models import JsonPatchOperation
    from msrest.authentication import BasicAuthentication
except ImportError:
    print("⚠️  Azure DevOps SDK non installato. Installare con: pip install azure-devops")
    exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('task_sync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class Task:
    """Rappresenta un task del progetto"""
    task_id: str
    title: str
    phase: str
    priority: str
    estimated_hours: int
    assigned_to: str
    status: str
    due_date: str
    description: str
    azure_work_item_id: Optional[int] = None

class AzureDevOpsClient:
    """Client per interagire con Azure DevOps API"""
    
    def __init__(self, organization_url: str, personal_access_token: str, project_name: str):
        self.organization_url = organization_url
        self.project_name = project_name
        
        # Setup authentication
        credentials = BasicAuthentication('', personal_access_token)
        self.connection = Connection(base_url=organization_url, creds=credentials)
        self.wit_client: WorkItemTrackingClient = self.connection.clients.get_work_item_tracking_client()
        
        logger.info(f"✅ Connesso ad Azure DevOps: {organization_url}")

    def create_work_item(self, task: Task) -> Optional[int]:
        """Crea un nuovo work item in Azure DevOps"""
        try:
            # Mappa priorità
            priority_map = {
                'Critical': 1,
                'High': 2, 
                'Medium': 3,
                'Low': 4
            }
            
            # Mappa stato
            state_map = {
                'Not Started': 'New',
                'In Progress': 'Active',
                'Completed': 'Closed'
            }
            
            # Crea patch operations per i campi del work item
            patch_operations = [
                JsonPatchOperation(
                    op='add',
                    path='/fields/System.Title',
                    value=task.title
                ),
                JsonPatchOperation(
                    op='add',
                    path='/fields/System.Description',
                    value=task.description
                ),
                JsonPatchOperation(
                    op='add',
                    path='/fields/System.State',
                    value=state_map.get(task.status, 'New')
                ),
                JsonPatchOperation(
                    op='add',
                    path='/fields/Microsoft.VSTS.Common.Priority',
                    value=priority_map.get(task.priority, 3)
                ),
                JsonPatchOperation(
                    op='add',
                    path='/fields/Microsoft.VSTS.Scheduling.OriginalEstimate',
                    value=task.estimated_hours
                ),
                JsonPatchOperation(
                    op='add',
                    path='/fields/System.AssignedTo',
                    value=task.assigned_to
                ),
                JsonPatchOperation(
                    op='add',
                    path='/fields/System.Tags',
                    value=f"OCRIM;AtMill;{task.phase};{task.task_id}"
                )
            ]
            
            # Aggiungi due date se specificata
            if task.due_date and task.due_date != '':
                try:
                    due_date_obj = datetime.strptime(task.due_date, '%Y-%m-%d')
                    patch_operations.append(
                        JsonPatchOperation(
                            op='add',
                            path='/fields/Microsoft.VSTS.Scheduling.DueDate',
                            value=due_date_obj.isoformat()
                        )
                    )
                except ValueError:
                    logger.warning(f"⚠️  Formato data non valido per task {task.task_id}: {task.due_date}")
            
            # Crea il work item
            work_item = self.wit_client.create_work_item(
                document=patch_operations,
                project=self.project_name,
                type='User Story'
            )
            
            logger.info(f"✅ Creato work item {work_item.id} per task {task.task_id}")
            return work_item.id
            
        except Exception as e:
            logger.error(f"❌ Errore nella creazione work item per task {task.task_id}: {str(e)}")
            return None

    def update_work_item(self, work_item_id: int, task: Task) -> bool:
        """Aggiorna un work item esistente"""
        try:
            state_map = {
                'Not Started': 'New',
                'In Progress': 'Active', 
                'Completed': 'Closed'
            }
            
            patch_operations = [
                JsonPatchOperation(
                    op='replace',
                    path='/fields/System.Title',
                    value=task.title
                ),
                JsonPatchOperation(
                    op='replace',
                    path='/fields/System.State',
                    value=state_map.get(task.status, 'New')
                ),
                JsonPatchOperation(
                    op='replace',
                    path='/fields/Microsoft.VSTS.Scheduling.OriginalEstimate',
                    value=task.estimated_hours
                )
            ]
            
            self.wit_client.update_work_item(
                document=patch_operations,
                id=work_item_id,
                project=self.project_name
            )
            
            logger.info(f"✅ Aggiornato work item {work_item_id} per task {task.task_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore nell'aggiornamento work item {work_item_id}: {str(e)}")
            return False

    def get_work_items_by_tag(self, tag: str) -> List[Dict]:
        """Recupera work items per tag"""
        try:
            # Query WIQL per trovare work items con tag specifico
            wiql_query = f"""
            SELECT [System.Id], [System.Title], [System.Tags], [System.State]
            FROM WorkItems
            WHERE [System.TeamProject] = '{self.project_name}'
            AND [System.Tags] CONTAINS '{tag}'
            """
            
            result = self.wit_client.query_by_wiql(wiql={'query': wiql_query})
            work_items = []
            
            if result.work_items:
                ids = [item.id for item in result.work_items]
                items = self.wit_client.get_work_items(ids=ids)
                
                for item in items:
                    work_items.append({
                        'id': item.id,
                        'title': item.fields.get('System.Title', ''),
                        'tags': item.fields.get('System.Tags', ''),
                        'state': item.fields.get('System.State', '')
                    })
            
            return work_items
            
        except Exception as e:
            logger.error(f"❌ Errore nel recupero work items: {str(e)}")
            return []

class TaskSynchronizer:
    """Gestisce la sincronizzazione tra CSV e Azure DevOps"""
    
    def __init__(self, csv_file: str, mapping_file: str = 'task_mapping.json'):
        self.csv_file = csv_file
        self.mapping_file = mapping_file
        self.task_mapping = self.load_mapping()
        
    def load_mapping(self) -> Dict[str, int]:
        """Carica il mapping task_id -> work_item_id"""
        if os.path.exists(self.mapping_file):
            with open(self.mapping_file, 'r') as f:
                return json.load(f)
        return {}
    
    def save_mapping(self):
        """Salva il mapping task_id -> work_item_id"""
        with open(self.mapping_file, 'w') as f:
            json.dump(self.task_mapping, f, indent=2)
    
    def load_tasks_from_csv(self) -> List[Task]:
        """Carica task dal file CSV"""
        tasks = []
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    task = Task(
                        task_id=row['Task ID'],
                        title=row['Title'],
                        phase=row['Phase'],
                        priority=row['Priority'],
                        estimated_hours=int(row['Estimated Hours']) if row['Estimated Hours'] else 0,
                        assigned_to=row['Assigned To'],
                        status=row['Status'],
                        due_date=row['Due Date'],
                        description=row['Description'],
                        azure_work_item_id=self.task_mapping.get(row['Task ID'])
                    )
                    tasks.append(task)
                    
            logger.info(f"📊 Caricati {len(tasks)} task dal CSV")
            return tasks
            
        except Exception as e:
            logger.error(f"❌ Errore nel caricamento CSV: {str(e)}")
            return []
    
    def sync_to_azure_devops(self, azure_client: AzureDevOpsClient, tasks: List[Task]):
        """Sincronizza task con Azure DevOps"""
        created_count = 0
        updated_count = 0
        
        for task in tasks:
            if task.azure_work_item_id:
                # Aggiorna work item esistente
                if azure_client.update_work_item(task.azure_work_item_id, task):
                    updated_count += 1
            else:
                # Crea nuovo work item
                work_item_id = azure_client.create_work_item(task)
                if work_item_id:
                    self.task_mapping[task.task_id] = work_item_id
                    created_count += 1
        
        # Salva mapping aggiornato
        self.save_mapping()
        
        logger.info(f"🎯 Sincronizzazione completata: {created_count} creati, {updated_count} aggiornati")
    
    def update_csv_status(self, task_id: str, new_status: str):
        """Aggiorna lo status di un task nel CSV"""
        try:
            # Leggi il CSV
            rows = []
            with open(self.csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                fieldnames = reader.fieldnames
                for row in reader:
                    if row['Task ID'] == task_id:
                        row['Status'] = new_status
                    rows.append(row)
            
            # Scrivi il CSV aggiornato
            with open(self.csv_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            
            logger.info(f"✅ Aggiornato status task {task_id} a '{new_status}'")
            
        except Exception as e:
            logger.error(f"❌ Errore nell'aggiornamento CSV: {str(e)}")

def main():
    """Funzione principale"""
    # Configurazione da variabili d'ambiente
    organization_url = os.getenv('AZURE_DEVOPS_ORG', 'https://dev.azure.com/ocrim-atmill/')
    project_name = os.getenv('AZURE_DEVOPS_PROJECT', 'AtMill-Revamping')
    pat_token = os.getenv('PAT')
    
    if not pat_token:
        logger.error("❌ Personal Access Token non trovato. Impostare la variabile PAT")
        return
    
    # Inizializza componenti
    csv_file = 'backlog.csv'
    if not os.path.exists(csv_file):
        logger.error(f"❌ File CSV non trovato: {csv_file}")
        return
    
    try:
        # Connetti ad Azure DevOps
        azure_client = AzureDevOpsClient(organization_url, pat_token, project_name)
        
        # Inizializza sincronizzatore
        synchronizer = TaskSynchronizer(csv_file)
        
        # Carica task dal CSV
        tasks = synchronizer.load_tasks_from_csv()
        
        if not tasks:
            logger.error("❌ Nessun task trovato nel CSV")
            return
        
        # Sincronizza con Azure DevOps
        synchronizer.sync_to_azure_devops(azure_client, tasks)
        
        logger.info("🎉 Sincronizzazione completata con successo!")
        
    except Exception as e:
        logger.error(f"❌ Errore durante la sincronizzazione: {str(e)}")

if __name__ == '__main__':
    main()
