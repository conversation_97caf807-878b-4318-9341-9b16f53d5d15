{"task_id": "task_019", "title": "Utility Python per Azure DevOps Integration", "phase": "phase_01", "priority": "Low", "estimated_hours": 16, "assigned_to": "Tech Lead", "status": "Not Started", "created_date": "2025-06-30", "due_date": "2025-07-07", "description": "Sviluppo utility Python per integrazione con Azure DevOps, gestione work items, aggiornamento stato progetto e generazione wiki di documentazione", "acceptance_criteria": ["Script Python per connessione Azure DevOps API", "Utility per creazione e aggiornamento work items", "Generazione automatica wiki da file markdown", "Sincronizzazione task JSON con Azure DevOps", "Report di stato progetto automatizzato", "Documentazione utility e esempi d'uso"], "technical_requirements": {"python_version": "3.11+", "packages": ["azure-devops", "requests", "python-dotenv", "click", "jinja2", "markdown", "pyyaml", "pandas"], "azure_devops": {"organization": "https://dev.azure.com/ocrim-atmill/", "project": "AtMill-Revamping", "pat_token": "Environment variable PAT"}}, "implementation_steps": [{"step": 1, "title": "Azure DevOps Connection Setup", "description": "Configurazione connessione Azure DevOps API", "tasks": ["Setup azure-devops Python SDK", "Configurazione autenticazione con PAT token", "Test connessione e permessi", "Creazione classe base AzureDevOpsClient", "Gestione errori e retry logic"], "estimated_hours": 4}, {"step": 2, "title": "Work Items Management", "description": "Utility per gestione work items", "tasks": ["Creazione work items da task JSON", "Aggiornamento stato work items", "Sincronizzazione bidirezionale", "Gestione relazioni parent-child", "Bulk operations per work items"], "estimated_hours": 6}, {"step": 3, "title": "Wiki Generation", "description": "Generazione automatica wiki da documentazione", "tasks": ["Parser per file markdown", "Template Jinja2 per wiki pages", "Upload automatico wiki su Azure DevOps", "Gestione struttura gerarchica wiki", "Aggiornamento incrementale contenuti"], "estimated_hours": 4}, {"step": 4, "title": "Reporting e Dashboard", "description": "Report automatizzati stato progetto", "tasks": ["Generazione report progresso task", "Metriche di avanzamento progetto", "Export dati per dashboard", "Notifiche automatiche milestone", "Integration con email/Teams"], "estimated_hours": 2}], "utility_scripts": [{"script": "azure_devops_client.py", "description": "Client principale per Azure DevOps API", "functions": ["connect_to_devops()", "create_work_item()", "update_work_item()", "get_work_items()", "create_wiki_page()"]}, {"script": "task_sync.py", "description": "Sincronizzazione task JSON con work items", "functions": ["sync_tasks_to_devops()", "update_task_status()", "create_backlog_items()", "generate_sprint_plan()"]}, {"script": "wiki_generator.py", "description": "Generazione wiki da documentazione markdown", "functions": ["parse_markdown_files()", "generate_wiki_structure()", "upload_wiki_pages()", "update_wiki_index()"]}, {"script": "project_reporter.py", "description": "Report e metriche progetto", "functions": ["generate_progress_report()", "calculate_metrics()", "send_notifications()", "export_dashboard_data()"]}, {"script": "cli.py", "description": "Command line interface per utility", "commands": ["sync-tasks", "update-wiki", "generate-report", "create-workitems"]}], "configuration": {"config_file": "azure_config.yaml", "environment_variables": ["PAT - Personal Access Token Azure DevOps", "AZURE_DEVOPS_ORG - Organization URL", "AZURE_DEVOPS_PROJECT - Project name"], "settings": {"work_item_type": "User Story", "area_path": "AtMill-Revamping", "iteration_path": "AtMill-Revamping\\Sprint 1", "wiki_root": "Technical Documentation"}}, "deliverables": [{"name": "Azure DevOps Python Client", "type": "Code", "description": "Libreria Python per integrazione Azure DevOps"}, {"name": "Task Synchronization Tool", "type": "Tool", "description": "Utility per sincronizzazione task con work items"}, {"name": "Wiki Generator", "type": "Tool", "description": "Generatore automatico wiki da markdown"}, {"name": "Project Reporter", "type": "Tool", "description": "Tool per report e metriche progetto"}, {"name": "CLI Interface", "type": "Tool", "description": "Command line interface per tutte le utility"}, {"name": "Documentation", "type": "Documentation", "description": "Documentazione completa utility e esempi"}], "usage_examples": [{"command": "python cli.py sync-tasks --file tasks.json", "description": "Sincronizza task JSON con Azure DevOps work items"}, {"command": "python cli.py update-wiki --source docs/", "description": "Aggiorna wiki Azure DevOps da file markdown"}, {"command": "python cli.py generate-report --format html", "description": "Genera report HTML stato progetto"}, {"command": "python cli.py create-workitems --backlog backlog.csv", "description": "Crea work items da backlog CSV"}], "testing_strategy": {"unit_tests": "pytest per funzioni core", "integration_tests": "Test con Azure DevOps sandbox", "mock_tests": "Mock API calls per testing offline"}, "dependencies": ["task_001"], "notes": ["Utility supporta team per gestione progetto", "Automazione riduce overhead amministrativo", "Wiki automatico migliora documentazione", "<PERSON><PERSON>e a<PERSON> tracking progresso"]}