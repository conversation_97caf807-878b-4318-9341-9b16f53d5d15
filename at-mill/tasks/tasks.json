{"project": {"name": "OCRIM AtMill Revamping", "description": "Migrazione da ASP.NET VB.NET a .NET Core C# con frontend React", "version": "1.0.0", "created": "2025-06-30", "azure_devops": {"organization": "https://dev.azure.com/ocrim-atmill/", "project": "AtMill-Revamping"}}, "phases": [{"id": "phase_01", "name": "Setup e Architettura", "description": "Configurazione iniziale del progetto e definizione architettura", "duration_weeks": "4-6", "priority": "Critical", "dependencies": [], "deliverables": ["Architettura sistema definita", "Repository configurati", "Standard di sviluppo definiti", "Ambiente di sviluppo pronto"]}, {"id": "phase_02", "name": "Database e Configurazione", "description": "Migrazione database e sistema di configurazione XML", "duration_weeks": "3-4", "priority": "Critical", "dependencies": ["phase_01"], "deliverables": ["Modelli Entity Framework Core", "Sistema configurazione XML migrato", "Migrazioni database pronte"]}, {"id": "phase_03", "name": "Backend API Core", "description": "Sviluppo API REST e business logic core", "duration_weeks": "6-8", "priority": "High", "dependencies": ["phase_02"], "deliverables": ["API REST funzionanti", "Autenticazione implementata", "Business logic migrata"]}, {"id": "phase_04", "name": "Frontend React", "description": "Sviluppo interfaccia utente React", "duration_weeks": "4-6", "priority": "High", "dependencies": ["phase_03"], "deliverables": ["Componenti UI base", "Sistema routing", "Integrazione API"]}, {"id": "phase_05", "name": "Moduli Funzionali", "description": "Migrazione moduli specifici del dominio", "duration_weeks": "8-12", "priority": "Medium", "dependencies": ["phase_04"], "deliverables": ["<PERSON><PERSON><PERSON> migrato", "<PERSON><PERSON>lo <PERSON> migrato", "Modulo Production migrato"]}, {"id": "phase_06", "name": "Integrazione e Deploy", "description": "Integrazione PLM, testing e deployment", "duration_weeks": "2-4", "priority": "High", "dependencies": ["phase_05"], "deliverables": ["Integrazione PLM funzionante", "Sistema in produzione", "Documentazione completa"]}], "technology_stack": {"backend": {"framework": ".NET Core 8", "language": "C#", "orm": "Entity Framework Core", "database": "SQL Server", "api": "REST/OpenAPI", "authentication": "JWT/OAuth2"}, "frontend": {"framework": "React 18", "language": "TypeScript", "build_tool": "Vite", "ui_library": "Material-UI/Ant Design", "state_management": "Zustand + React Query", "routing": "React Router"}, "devops": {"ci_cd": "Azure DevOps Pipelines", "containerization": "<PERSON>er", "cloud": "Azure", "monitoring": "Application Insights", "logging": "Serilog"}}, "team_structure": {"roles": [{"role": "Tech Lead", "responsibilities": ["Architettura", "Coordinamento tecnico", "Code review"], "skills_required": [".NET Core", "React", "Azure", "Leadership"]}, {"role": "Backend Developer", "responsibilities": ["API development", "Business logic", "Database"], "skills_required": [".NET Core", "C#", "Entity Framework", "SQL Server"]}, {"role": "Frontend Developer", "responsibilities": ["UI/UX", "React components", "State management"], "skills_required": ["React", "TypeScript", "CSS", "UI/UX"]}, {"role": "DevOps Engineer", "responsibilities": ["CI/CD", "Infrastructure", "Deployment"], "skills_required": ["Azure", "<PERSON>er", "DevOps", "Monitoring"]}, {"role": "QA Engineer", "responsibilities": ["Testing", "Quality assurance", "Performance"], "skills_required": ["Testing frameworks", "Automation", "Performance testing"]}]}}