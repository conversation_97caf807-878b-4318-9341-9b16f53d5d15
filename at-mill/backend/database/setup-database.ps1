# OCRIM AtMill - Database Setup Script (PowerShell)
# Avvia SQL Server in Docker e ripristina il backup del database

param(
    [string]$Action = "setup"
)

# Configurazione
$CONTAINER_NAME = "ocrim-sqlserver"
$SA_PASSWORD = "OcrimAtMill2025!"
$DATABASE_NAME = "212240ALJUMUM"
$BACKUP_FILE = "212240ALJUMUM.bak"
$SQL_PORT = "1433"
$DOCKER_IMAGE = "mcr.microsoft.com/mssql/server:2022-latest"

# Funzioni di utilità
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Verifica prerequisiti
function Test-Prerequisites {
    Write-Info "Verifica prerequisiti..."
    
    # Verifica Docker
    try {
        $null = Get-Command docker -ErrorAction Stop
    }
    catch {
        Write-Error "Docker non è installato. Installare Docker Desktop."
        exit 1
    }
    
    # Verifica che Docker sia in esecuzione
    try {
        docker info | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "Docker non risponde"
        }
    }
    catch {
        Write-Error "Docker non è in esecuzione. Avviare Docker Desktop."
        exit 1
    }
    
    # Verifica file backup
    if (-not (Test-Path $BACKUP_FILE)) {
        Write-Error "File backup $BACKUP_FILE non trovato nella directory corrente."
        Write-Info "Copiare il file backup dalla directory as-is:"
        Write-Info "Copy-Item ..\..\as-is\212240ALJUMUM\target\Web\212240ALJUMUM.bak ."
        exit 1
    }
    
    Write-Success "Prerequisiti verificati"
}

# Ferma e rimuove container esistente
function Remove-ExistingContainer {
    Write-Info "Pulizia container esistente..."
    
    $existingContainer = docker ps -a --format "{{.Names}}" | Where-Object { $_ -eq $CONTAINER_NAME }
    
    if ($existingContainer) {
        Write-Warning "Container $CONTAINER_NAME esistente trovato. Rimozione..."
        docker stop $CONTAINER_NAME 2>$null
        docker rm $CONTAINER_NAME 2>$null
        Write-Success "Container rimosso"
    }
}

# Avvia SQL Server container
function Start-SqlServer {
    Write-Info "Avvio SQL Server container..."
    
    $currentPath = (Get-Location).Path
    
    docker run -d `
        --name $CONTAINER_NAME `
        -e "ACCEPT_EULA=Y" `
        -e "SA_PASSWORD=$SA_PASSWORD" `
        -p "${SQL_PORT}:1433" `
        -v "${currentPath}:/backup" `
        $DOCKER_IMAGE
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Container SQL Server avviato"
        Write-Info "Nome container: $CONTAINER_NAME"
        Write-Info "Porta: $SQL_PORT"
        Write-Info "Password SA: $SA_PASSWORD"
    }
    else {
        Write-Error "Errore nell'avvio del container"
        exit 1
    }
}

# Attende che SQL Server sia pronto
function Wait-ForSqlServer {
    Write-Info "Attesa avvio SQL Server..."
    
    $maxAttempts = 30
    $attempt = 1
    
    while ($attempt -le $maxAttempts) {
        try {
            docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd `
                -S localhost -U sa -P $SA_PASSWORD `
                -Q "SELECT 1" 2>$null | Out-Null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "SQL Server è pronto"
                return
            }
        }
        catch {
            # Continua il loop
        }
        
        Write-Info "Tentativo $attempt/$maxAttempts - SQL Server non ancora pronto..."
        Start-Sleep -Seconds 5
        $attempt++
    }
    
    Write-Error "SQL Server non si è avviato entro il timeout"
    exit 1
}

# Ripristina il database dal backup
function Restore-Database {
    Write-Info "Ripristino database dal backup..."
    
    # Ripristina il database
    Write-Info "Esecuzione ripristino database..."
    
    $restoreQuery = @"
RESTORE DATABASE [$DATABASE_NAME] FROM DISK = '/backup/$BACKUP_FILE' WITH REPLACE, 
MOVE '212240ALJUMUM' TO '/var/opt/mssql/data/${DATABASE_NAME}.mdf',
MOVE '212240ALJUMUM_Log' TO '/var/opt/mssql/data/${DATABASE_NAME}_Log.ldf'
"@
    
    docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd `
        -S localhost -U sa -P $SA_PASSWORD `
        -Q $restoreQuery
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Database ripristinato con successo"
    }
    else {
        Write-Error "Errore nel ripristino del database"
        exit 1
    }
}

# Verifica il database
function Test-Database {
    Write-Info "Verifica database..."
    
    # Verifica che il database esista
    $checkQuery = "SELECT name FROM sys.databases WHERE name = '$DATABASE_NAME'"
    
    $result = docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd `
        -S localhost -U sa -P $SA_PASSWORD `
        -Q $checkQuery -h -1 -W
    
    if ($result -match $DATABASE_NAME) {
        Write-Success "Database $DATABASE_NAME verificato"
        
        # Mostra alcune statistiche
        Write-Info "Statistiche database:"
        $statsQuery = @"
USE [$DATABASE_NAME]; 
SELECT 
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE') as 'Tabelle',
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS) as 'Viste',
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE') as 'Stored Procedures'
"@
        
        docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd `
            -S localhost -U sa -P $SA_PASSWORD `
            -Q $statsQuery
    }
    else {
        Write-Error "Database $DATABASE_NAME non trovato"
        exit 1
    }
}

# Crea script di connessione
function New-ConnectionScripts {
    Write-Info "Creazione script di connessione..."
    
    # Script PowerShell per connessione diretta
    $connectScript = @"
# Script per connettersi al database OCRIM AtMill
docker exec -it $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd ``
    -S localhost -U sa -P "$SA_PASSWORD" ``
    -d "$DATABASE_NAME"
"@
    
    $connectScript | Out-File -FilePath "connect-db.ps1" -Encoding UTF8
    
    # Connection string per .NET
    $connectionInfo = @"
# Connection String per .NET Core
Server=localhost,$SQL_PORT;Database=$DATABASE_NAME;User Id=sa;Password=$SA_PASSWORD;TrustServerCertificate=true;

# Connection String per Entity Framework Core
"DefaultConnection": "Server=localhost,$SQL_PORT;Database=$DATABASE_NAME;User Id=sa;Password=$SA_PASSWORD;TrustServerCertificate=true;"

# Docker container info
Container Name: $CONTAINER_NAME
SA Password: $SA_PASSWORD
Port: $SQL_PORT
Database: $DATABASE_NAME
"@
    
    $connectionInfo | Out-File -FilePath "connection-string.txt" -Encoding UTF8
    
    Write-Success "Script di connessione creati:"
    Write-Info "  - connect-db.ps1: Connessione diretta al database"
    Write-Info "  - connection-string.txt: Connection string per .NET"
}

# Mostra informazioni finali
function Show-FinalInfo {
    Write-Host ""
    Write-Success "=== SETUP COMPLETATO ==="
    Write-Host ""
    Write-Info "📊 Database: $DATABASE_NAME"
    Write-Info "🐳 Container: $CONTAINER_NAME"
    Write-Info "🔌 Porta: $SQL_PORT"
    Write-Info "🔑 Password SA: $SA_PASSWORD"
    Write-Host ""
    Write-Info "🔧 Comandi utili:"
    Write-Info "  Connetti al DB:     .\connect-db.ps1"
    Write-Info "  Stop container:     docker stop $CONTAINER_NAME"
    Write-Info "  Start container:    docker start $CONTAINER_NAME"
    Write-Info "  Rimuovi container:  docker rm -f $CONTAINER_NAME"
    Write-Host ""
    Write-Info "📝 Connection string salvata in: connection-string.txt"
    Write-Host ""
}

# Funzione principale
function Invoke-Setup {
    Write-Host ""
    Write-Info "🏭 OCRIM AtMill - Database Setup"
    Write-Info "================================"
    Write-Host ""
    
    Test-Prerequisites
    Remove-ExistingContainer
    Start-SqlServer
    Wait-ForSqlServer
    Restore-Database
    Test-Database
    New-ConnectionScripts
    Show-FinalInfo
}

# Gestione azioni
switch ($Action.ToLower()) {
    "stop" {
        Write-Info "Fermando container $CONTAINER_NAME..."
        docker stop $CONTAINER_NAME
        Write-Success "Container fermato"
    }
    "start" {
        Write-Info "Avviando container $CONTAINER_NAME..."
        docker start $CONTAINER_NAME
        Write-Success "Container avviato"
    }
    "remove" {
        Write-Info "Rimuovendo container $CONTAINER_NAME..."
        docker stop $CONTAINER_NAME 2>$null
        docker rm $CONTAINER_NAME 2>$null
        Write-Success "Container rimosso"
    }
    "status" {
        Write-Info "Status container $CONTAINER_NAME:"
        docker ps -a --filter name=$CONTAINER_NAME --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    }
    default {
        Invoke-Setup
    }
}
