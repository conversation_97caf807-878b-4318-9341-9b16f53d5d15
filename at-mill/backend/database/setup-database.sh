#!/bin/bash

# OCRIM AtMill - Database Setup Script
# Avvia SQL Server in Docker e ripristina il backup del database

set -e  # Exit on any error

# Configurazione
CONTAINER_NAME="ocrim-sqlserver"
SA_PASSWORD="OcrimAtMill2025!"
DATABASE_NAME="212240ALJUMUM"
BACKUP_FILE="212240ALJUMUM.bak"
SQL_PORT="1433"
DOCKER_IMAGE="mcr.microsoft.com/mssql/server:2022-latest"

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funzioni di utilità
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verifica prerequisiti
check_prerequisites() {
    log_info "Verifica prerequisiti..."
    
    # Verifica Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker non è installato. Installare Docker Desktop."
        exit 1
    fi
    
    # Verifica che Docker sia in esecuzione
    if ! docker info &> /dev/null; then
        log_error "Docker non è in esecuzione. Avviare Docker Desktop."
        exit 1
    fi
    
    # Verifica file backup
    if [ ! -f "$BACKUP_FILE" ]; then
        log_error "File backup $BACKUP_FILE non trovato nella directory corrente."
        log_info "Copiare il file backup dalla directory as-is:"
        log_info "cp ../../as-is/212240ALJUMUM/target/Web/212240ALJUMUM.bak ."
        exit 1
    fi
    
    log_success "Prerequisiti verificati"
}

# Ferma e rimuove container esistente
cleanup_existing_container() {
    log_info "Pulizia container esistente..."
    
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_warning "Container $CONTAINER_NAME esistente trovato. Rimozione..."
        docker stop $CONTAINER_NAME 2>/dev/null || true
        docker rm $CONTAINER_NAME 2>/dev/null || true
        log_success "Container rimosso"
    fi
}

# Avvia SQL Server container
start_sqlserver() {
    log_info "Avvio SQL Server container..."
    
    docker run -d \
        --name $CONTAINER_NAME \
        -e "ACCEPT_EULA=Y" \
        -e "SA_PASSWORD=$SA_PASSWORD" \
        -p $SQL_PORT:1433 \
        -v "$(pwd):/backup" \
        $DOCKER_IMAGE
    
    log_success "Container SQL Server avviato"
    log_info "Nome container: $CONTAINER_NAME"
    log_info "Porta: $SQL_PORT"
    log_info "Password SA: $SA_PASSWORD"
}

# Attende che SQL Server sia pronto
wait_for_sqlserver() {
    log_info "Attesa avvio SQL Server..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd \
            -S localhost -U sa -P "$SA_PASSWORD" \
            -Q "SELECT 1" &> /dev/null; then
            log_success "SQL Server è pronto"
            return 0
        fi
        
        log_info "Tentativo $attempt/$max_attempts - SQL Server non ancora pronto..."
        sleep 5
        ((attempt++))
    done
    
    log_error "SQL Server non si è avviato entro il timeout"
    exit 1
}

# Ripristina il database dal backup
restore_database() {
    log_info "Ripristino database dal backup..."
    
    # Ottieni informazioni sui file logici nel backup
    log_info "Analisi file backup..."
    docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd \
        -S localhost -U sa -P "$SA_PASSWORD" \
        -Q "RESTORE FILELISTONLY FROM DISK = '/backup/$BACKUP_FILE'" \
        -o /tmp/filelist.txt
    
    # Ripristina il database
    log_info "Esecuzione ripristino database..."
    docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd \
        -S localhost -U sa -P "$SA_PASSWORD" \
        -Q "RESTORE DATABASE [$DATABASE_NAME] FROM DISK = '/backup/$BACKUP_FILE' WITH REPLACE, 
            MOVE '212240ALJUMUM' TO '/var/opt/mssql/data/${DATABASE_NAME}.mdf',
            MOVE '212240ALJUMUM_Log' TO '/var/opt/mssql/data/${DATABASE_NAME}_Log.ldf'"
    
    if [ $? -eq 0 ]; then
        log_success "Database ripristinato con successo"
    else
        log_error "Errore nel ripristino del database"
        exit 1
    fi
}

# Verifica il database
verify_database() {
    log_info "Verifica database..."
    
    # Verifica che il database esista
    local db_exists=$(docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd \
        -S localhost -U sa -P "$SA_PASSWORD" \
        -Q "SELECT name FROM sys.databases WHERE name = '$DATABASE_NAME'" \
        -h -1 -W | tr -d ' \r\n')
    
    if [ "$db_exists" = "$DATABASE_NAME" ]; then
        log_success "Database $DATABASE_NAME verificato"
        
        # Mostra alcune statistiche
        log_info "Statistiche database:"
        docker exec $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd \
            -S localhost -U sa -P "$SA_PASSWORD" \
            -Q "USE [$DATABASE_NAME]; SELECT 
                (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE') as 'Tabelle',
                (SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS) as 'Viste',
                (SELECT COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE') as 'Stored Procedures'"
    else
        log_error "Database $DATABASE_NAME non trovato"
        exit 1
    fi
}

# Crea script di connessione
create_connection_scripts() {
    log_info "Creazione script di connessione..."
    
    # Script per connessione diretta
    cat > connect-db.sh << EOF
#!/bin/bash
# Script per connettersi al database OCRIM AtMill
docker exec -it $CONTAINER_NAME /opt/mssql-tools/bin/sqlcmd \\
    -S localhost -U sa -P "$SA_PASSWORD" \\
    -d "$DATABASE_NAME"
EOF
    chmod +x connect-db.sh
    
    # Connection string per .NET
    cat > connection-string.txt << EOF
# Connection String per .NET Core
Server=localhost,$SQL_PORT;Database=$DATABASE_NAME;User Id=sa;Password=$SA_PASSWORD;TrustServerCertificate=true;

# Connection String per Entity Framework Core
"DefaultConnection": "Server=localhost,$SQL_PORT;Database=$DATABASE_NAME;User Id=sa;Password=$SA_PASSWORD;TrustServerCertificate=true;"

# Docker container info
Container Name: $CONTAINER_NAME
SA Password: $SA_PASSWORD
Port: $SQL_PORT
Database: $DATABASE_NAME
EOF
    
    log_success "Script di connessione creati:"
    log_info "  - connect-db.sh: Connessione diretta al database"
    log_info "  - connection-string.txt: Connection string per .NET"
}

# Mostra informazioni finali
show_final_info() {
    echo
    log_success "=== SETUP COMPLETATO ==="
    echo
    log_info "📊 Database: $DATABASE_NAME"
    log_info "🐳 Container: $CONTAINER_NAME"
    log_info "🔌 Porta: $SQL_PORT"
    log_info "🔑 Password SA: $SA_PASSWORD"
    echo
    log_info "🔧 Comandi utili:"
    log_info "  Connetti al DB:     ./connect-db.sh"
    log_info "  Stop container:     docker stop $CONTAINER_NAME"
    log_info "  Start container:    docker start $CONTAINER_NAME"
    log_info "  Rimuovi container:  docker rm -f $CONTAINER_NAME"
    echo
    log_info "📝 Connection string salvata in: connection-string.txt"
    echo
}

# Funzione principale
main() {
    echo
    log_info "🏭 OCRIM AtMill - Database Setup"
    log_info "================================"
    echo
    
    check_prerequisites
    cleanup_existing_container
    start_sqlserver
    wait_for_sqlserver
    restore_database
    verify_database
    create_connection_scripts
    show_final_info
}

# Gestione opzioni command line
case "${1:-}" in
    "stop")
        log_info "Fermando container $CONTAINER_NAME..."
        docker stop $CONTAINER_NAME
        log_success "Container fermato"
        ;;
    "start")
        log_info "Avviando container $CONTAINER_NAME..."
        docker start $CONTAINER_NAME
        log_success "Container avviato"
        ;;
    "remove")
        log_info "Rimuovendo container $CONTAINER_NAME..."
        docker stop $CONTAINER_NAME 2>/dev/null || true
        docker rm $CONTAINER_NAME 2>/dev/null || true
        log_success "Container rimosso"
        ;;
    "status")
        log_info "Status container $CONTAINER_NAME:"
        docker ps -a --filter name=$CONTAINER_NAME --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        ;;
    *)
        main
        ;;
esac
