<?xml version="1.0" encoding="utf-8"?>
<Interface xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<screens>
		<screen>
			<EditFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<Decimal>0</Decimal>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</ViewField>
				<EditField>
					<FieldDB>NUMBER</FieldDB>
					<FieldName>SS_PO_NUMBER</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>false</updateField>
					<insertField>true</insertField>
					<PropertyName>Number</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>NUMBER</FieldDB>
					<FieldName>SS_PO_NUMBER</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>Number</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>PO_NUMBER_ERP</FieldDB>
					<FieldName>SS_PO_NUMBER_ERP</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>Number</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ID_STATUS</FieldDB>
					<FieldName>Status</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>STATUS</SelectColumns>
					<From>PO_NUMBER_STATUS</From>
					<AddNull>false</AddNull>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>Status</PropertyName>
					<GetDataFrom>Standard</GetDataFrom>
				</EditField>
				<EditField>
					<FieldDB>NOTES</FieldDB>
					<FieldName>NOTES</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>Notes</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>INSERT_USER</FieldDB>
					<FieldName>SS_INSERT_USER</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>InsertUser</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>CLOSED_USER</FieldDB>
					<FieldName>SS_CLOSED_USER</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>ClosedUser</PropertyName>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<Decimal>0</Decimal>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PO_NUMBER_ERP</FieldDB>
					<FieldName>SS_PO_NUMBER</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
					<PropertyName>Description</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>INSERT_DATE</FieldDB>
					<FieldName>SS_INSERT_DATE</FieldName>
					<FieldType>date</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>INSERT_USER_NAME</FieldDB>
					<FieldName>SS_INSERT_USER</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>USER_NAME</SelectColumns>
					<From>SYSTEM_USERS</From>
					<AddNull>false</AddNull>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CLOSED_DATE</FieldDB>
					<FieldName>SS_CLOSED_DATE</FieldName>
					<FieldType>date</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CLOSED_USER_NAME</FieldDB>
					<FieldName>SS_CLOSED_USER</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>USER_NAME</SelectColumns>
					<From>SYSTEM_USERS</From>
					<AddNull>false</AddNull>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NOTES</FieldDB>
					<FieldName>NOTES</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
					<PropertyName>Notes</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>STATUS</FieldDB>
					<FieldName>Status</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>STATUS</SelectColumns>
					<From>PO_NUMBER_STATUS</From>
					<AddNull>false</AddNull>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STATUS_LED</FieldDB>
					<FieldName>SS_STATUS_LED</FieldName>
					<FieldType>Led</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns />
			<OrderBy>INSERT_DATE DESC</OrderBy>
			<DBName>VIEW_PO_NUMBER_LIST</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PO_NUMBER_LIST</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>72</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>IdScalesBatchSize</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>SCALE_ID</FieldDB>
					<FieldName>Scale</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>ITEM_NAME</SelectColumns>
					<From>SCALES</From>
					<AddNull>false</AddNull>
					<updateField>true</updateField>
					<IsReadOnly>true</IsReadOnly>
					<insertField>false</insertField>
					<PropertyName>IdScale</PropertyName>
					<GetDataFrom>Standard</GetDataFrom>
				</EditField>
				<EditField>
					<FieldDB>BATCH_SIZE_TAG_ID</FieldDB>
					<FieldName>PLC_Tag</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>ALIASNAME</SelectColumns>
					<From>TAGS</From>
					<AddNull>false</AddNull>
					<updateField>true</updateField>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<insertField>false</insertField>
					<PropertyName>IdTagBatchSize</PropertyName>
					<GetDataFrom>Standard</GetDataFrom>
				</EditField>
				<EditField>
					<FieldDB>BATCH_SIZE_PLC</FieldDB>
					<FieldName>ACT_PLC_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>2</Decimal>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>BatchSizePLC</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>BATCH_SIZE</FieldDB>
					<FieldName>INIT_OPER_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>2</Decimal>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>BatchSize</PropertyName>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<Decimal>0</Decimal>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ITEM_NAME</FieldDB>
					<FieldName>Scale</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
					<PropertyName>Scale</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>BATCH_SIZE_PLC</FieldDB>
					<FieldName>ACT_PLC_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>BATCH_SIZE</FieldDB>
					<FieldName>INIT_OPER_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns />
			<OrderBy>ITEM_NAME</OrderBy>
			<DBName>VIEW_SCALES_BATCH_SIZE</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SCALES_BATCH_SIZE</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>false</HasSearchButton>
			<EnumPageNameCode>74</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<Decimal>0</Decimal>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PO_NUMBER_ERP</FieldDB>
					<FieldName>SS_PO_NUMBER</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
					<PropertyName>Description</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>INSERT_DATE</FieldDB>
					<FieldName>SS_INSERT_DATE</FieldName>
					<FieldType>date</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>INSERT_USER_NAME</FieldDB>
					<FieldName>SS_INSERT_USER</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>UPDATE_DATE</FieldDB>
					<FieldName>SS_UPDATE_DATE</FieldName>
					<FieldType>date</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>UPDATE_USER_NAME</FieldDB>
					<FieldName>SS_UPDATE_USER</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CLOSED_DATE</FieldDB>
					<FieldName>SS_CLOSED_DATE</FieldName>
					<FieldType>date</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CLOSED_USER_NAME</FieldDB>
					<FieldName>SS_CLOSED_USER</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NOTES</FieldDB>
					<FieldName>NOTES</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
					<PropertyName>Notes</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>STATUS</FieldDB>
					<FieldName>Status</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>OPERATION</FieldDB>
					<FieldName>Operations</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns />
			<OrderBy>NUMBER desc,ID DESC</OrderBy>
			<DBName>VIEW_PO_NUMBER_LIST_LOG</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PO_NUMBER_LIST_LOG</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>72</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>IdCells</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Bin</FieldName>
					<AddNull>false</AddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>Description</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>CT_ID</FieldDB>
					<FieldName>Type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELL_TYPES</From>
					<SelectColumns>CT_TYPE</SelectColumns>
					<AddNull>false</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>IdCellType</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>CURRENT_AMOUNT</FieldDB>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>3</Decimal>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>CurrentAmount</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>PREVIOUS_AMOUNT</FieldDB>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>3</Decimal>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>PreviousAmount</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>VOLUMIC_CAPACITY</FieldDB>
					<FieldName>Capacity</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>0</Decimal>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>VolumicCapacity</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ENABLE_AUTOCORRECT_FULL_LEVEL</FieldDB>
					<FieldName>ENABLE_AUTOCORRECT_FULL_LEVEL</FieldName>
					<AddNull>false</AddNull>
					<FieldType>CheckBoxOnOff</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>EnableAutocorrectFullLevel</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ENABLE_AUTOCORRECT_EMPTY_LEVEL</FieldDB>
					<FieldName>ENABLE_AUTOCORRECT_EMPTY_LEVEL</FieldName>
					<AddNull>false</AddNull>
					<FieldType>CheckBoxOnOff</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>EnableAutocorrectEmptyLevel</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>EMPTY_LEVEL_CORRECTION_AMOUNT</FieldDB>
					<FieldName>EMPTY_LEVEL_CORRECTION_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>false</updateField>
					<insertField>false</insertField>
					<PropertyName>EmptyLevelCorrectionAmount</PropertyName>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<Decimal>0</Decimal>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
					<PropertyName>IdCells</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
					<PropertyName>Description</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>VOLUMIC_CAPACITY</FieldDB>
					<FieldName>Capacity</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CT_ID</FieldDB>
					<FieldName>BIN_TYPE</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>CT_TYPE</SelectColumns>
					<From>CELL_TYPES</From>
					<AddNull>True</AddNull>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
					<PropertyName>IdCellType</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CONTINUOUS_LEVEL_AMOUNT</FieldDB>
					<FieldName>CONTINUOUS_LEVEL_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CURRENT_AMOUNT</FieldDB>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
					<PropertyName>CurrentAmount</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>LOAD_ENABLE_LED</FieldDB>
					<FieldName>LOAD_ENABLE</FieldName>
					<FieldType>Led</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DOWNLOAD_ENABLE_LED</FieldDB>
					<FieldName>DOWNLOAD_ENABLE</FieldName>
					<FieldType>Led</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>VIEW_BINLAYERS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>CEL_ID</fieldDB>
					</parameters>
					<scriptName>../binlayers.aspx?control=binlayers&amp;excol=true&amp;pagename=CELLS_ARCHIVE&amp;menuname=MENU_STOCK&amp;drawflow=1&amp;typeview=stocks</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<SumColumns>
				<Fields>
					<Field>CURRENT_AMOUNT</Field>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<OrderBy>ID ASC</OrderBy>
			<DBName>VIEW_CELLS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>CELLS_ARCHIVE</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>15</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ANOM_ID</FieldDB>
					<FieldName>DB_ANOMALIES_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>ANOMALY</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ANOM_ID</FieldDB>
					<FieldName>DB_ANOMALIES_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>ANOMALY</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>ANOM_DATE</FieldDB>
					<FieldName>DB_DATE</FieldName>
					<Decimal>0</Decimal>
					<FieldType>TDate</FieldType>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header />
			<OrderBy>ID DESC</OrderBy>
			<DBName>VIEW_PROCESS_ANOMALIES</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PROCESS_ANOMALIES</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAllDeleteButton>true</HasAllDeleteButton>
			<HasAddButton>false</HasAddButton>
			<HasSearchButton>true</HasSearchButton>
			<HasEditButton>false</HasEditButton>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ANOM_ID</FieldDB>
					<FieldName>DB_ANOMALIES_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>ANOMALY</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ANOM_ID</FieldDB>
					<FieldName>DB_ANOMALIES_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMAx</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>ANOMALY</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>ANOM_DATE</FieldDB>
					<FieldName>DB_DATE</FieldName>
					<Decimal>0</Decimal>
					<FieldType>TDate</FieldType>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header />
			<OrderBy>ID DESC</OrderBy>
			<DBName>VIEW_SYSTEM_ANOMALIES</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SYSTEM_ANOMALIES</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasAllDeleteButton>true</HasAllDeleteButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ANOM_ID</FieldDB>
					<FieldName>DB_ANOMALIES_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>ANOMALY</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ANOM_ID</FieldDB>
					<FieldName>DB_ANOMALIES_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMAx</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>ANOMALY</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>ANOM_DATE</FieldDB>
					<FieldName>DB_DATE</FieldName>
					<Decimal>0</Decimal>
					<FieldType>TDate</FieldType>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header />
			<OrderBy>ID DESC</OrderBy>
			<DBName>VIEW_SYSTEM_INFORMATIONS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SYSTEM_INFORMATIONS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasAllDeleteButton>true</HasAllDeleteButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
		</screen>
		<screen>
			<EditFields />
			<ViewFields />
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>ID DESC</OrderBy>
			<DBName>GRAPHICS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>GRAPHICS</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasAllDeleteButton>false</HasAllDeleteButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>false</HasSearchButton>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>DB_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdUser</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>DB_SYSTEM_NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>USER_NAME</FieldDB>
					<FieldName>DB_SYSTEM_USERS_NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>UserName</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>PASSWORD</FieldDB>
					<FieldName>DB_SYSTEM_USERS_PWD</FieldName>
					<FieldType>Password</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Password</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>PASSWORD_CONFIRM</FieldDB>
					<FieldName>DB_SYSTEM_USERS_PWD_CONF</FieldName>
					<FieldType>Password</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>DB_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdUser</PropertyName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>DB_SYSTEM_NAME</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>USER_NAME</FieldDB>
					<FieldName>DB_SYSTEM_USERS_NAME</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>UserName</PropertyName>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>USER_NAME</OrderBy>
			<DBName>VIEW_SYSTEM_USERS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SYSTEM_USERS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>2</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>DB_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdGroup</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>GROUP_NAME</FieldDB>
					<FieldName>DB_SYSTEM_GROUP_NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>GroupName</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>DB_ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdGroup</PropertyName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>GROUP_NAME</FieldDB>
					<FieldName>DB_SYSTEM_GROUP_NAME</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>GroupName</PropertyName>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<Where>ID &lt;&gt; 1</Where>
			<OrderBy>GROUP_NAME</OrderBy>
			<DBName>SYSTEM_GROUPS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SYSTEM_GROUPS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>1</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID_USER</FieldDB>
					<FieldName>DB_SYSTEM_USERS_NAME</FieldName>
					<FieldType>LIST</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>USER_NAME</SelectColumns>
					<From>SYSTEM_USERS</From>
					<AddNull>True</AddNull>
					<Where>ID &lt;&gt; 1</Where>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdUser</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ID_USER</FieldDB>
					<FieldName>DB_SYSTEM_USERS_NAME</FieldName>
					<FieldType>LIST</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>USER_NAME</SelectColumns>
					<From>SYSTEM_USERS</From>
					<AddNull>True</AddNull>
					<Where>ID &lt;&gt; 1</Where>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdUser</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>ID_GROUP</FieldDB>
					<FieldName>DB_ACCESS_LEVEL</FieldName>
					<FieldType>ChkList</FieldType>
					<SelectedItemColumn>ID_GROUP</SelectedItemColumn>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>GROUP_NAME</SelectColumns>
					<From>SYSTEM_GROUPS</From>
					<Where>ID &lt;&gt; 1</Where>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdGroup</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID_USER</FieldDB>
					<FieldName>DB_SYSTEM_USERS_NAME</FieldName>
					<FieldType>LIST</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>USER_NAME</SelectColumns>
					<From>SYSTEM_USERS</From>
					<AddNull>True</AddNull>
					<Where>ID &lt;&gt; 1</Where>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>false</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>ID_GROUP</FieldDB>
					<FieldName>DB_SYSTEM_GROUP_NAME</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>GROUP_NAME</SelectColumns>
					<From>SYSTEM_GROUPS</From>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<AddNull>True</AddNull>
					<queryField>true</queryField>
					<visibleField>false</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>USER_NAME</FieldDB>
					<FieldName>DB_SYSTEM_USERS_NAME</FieldName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
					<FieldType>WString</FieldType>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>DB_SYSTEM_NAME</FieldName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
					<FieldType>WString</FieldType>
				</ViewField>
				<ViewField>
					<FieldDB>GROUP_NAME</FieldDB>
					<FieldName>DB_SYSTEM_GROUP_NAME</FieldName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
					<FieldType>WString</FieldType>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>USER_NAME</OrderBy>
			<DBName>VIEW_SYSTEM_USERS_GROUPS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SYSTEM_USERS_GROUPS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>4</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields />
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>GROUP_NAME,ACCESS_LEVEL</OrderBy>
			<DBName>VIEW_SYSTEM_GROUPS_RIGHTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SYSTEM_GROUPS_RIGHTS</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>5</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>YR_ID</FieldDB>
					<FieldName>YR_ID</FieldName>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>IdYr</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>DB_REPORT_NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<EnableKeyEntry>true</EnableKeyEntry>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>PRINTABLE</FieldDB>
					<FieldName>DB_PRINT_REPORT</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Printable</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>MAILABLE</FieldDB>
					<FieldName>DB_MAIL_REPORT</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Mailable</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SEND_TO_LIST</FieldDB>
					<FieldName>DB_SEND_TO_LIST</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SendToList</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>DB_REPORT_NAME</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<EnableKeyEntry>true</EnableKeyEntry>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>SEND_TO_LIST</FieldDB>
					<FieldName>DB_SEND_TO_LIST</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SendToList</PropertyName>
					<queryField>false</queryField>
					<visibleField>false</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>PRINTABLE_TRAD</FieldDB>
					<FieldName>DB_PRINT</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>MAILABLE_TRAD</FieldDB>
					<FieldName>DB_MAIL</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<queryField>false</queryField>
					<visibleField>false</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>ID</OrderBy>
			<FilterColumnsName>
				<FieldDB>YR_ID</FieldDB>
			</FilterColumnsName>
			<DBName>VIEW_YIELDS_PRINTOUTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>VIEW_YIELDS_PRINTOUTS</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>6</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdEuipmentModels</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>REFERENCE</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Reference</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>REFERENCE</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Reference</PropertyName>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>DESCRIPTION</OrderBy>
			<DBName>EQUIPMENT_MODELS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>EQUIPMENT_MODELS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>7</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EML_ID</FieldDB>
					<FieldName>Model</FieldName>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>REFERENCE,DESCRIPTION</SelectColumns>
					<From>EQUIPMENT_MODELS</From>
					<OrderBy>REFERENCE</OrderBy>
					<AddNull>True</AddNull>
					<FieldType>LIST</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdEquipmentModel</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ET_ID</FieldDB>
					<FieldName>Equipment type</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>1</FixedRuntimeValue>
					<PropertyName>IdEquipmentType</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>LAST_MAINT_DATE</FieldDB>
					<FieldName>Last Check</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>LastMaintDate</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>OPERATION_NUMBER</FieldDB>
					<FieldName>Operation number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>OperationNumber</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WORKING_TIME</FieldDB>
					<FieldName>Working time</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>WorkingTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>MACHINE_ITEM</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>MILL_SECTOR</FieldDB>
					<FieldName>MILL_SECTOR</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>MillSector</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>STICKY_NOTE</FieldDB>
					<FieldName>Sticky note</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StickyNote</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>MTBF</FieldDB>
					<FieldName>Mtbf</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>MTBF</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>EML_ID</FieldDB>
					<FieldName>Model</FieldName>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>REFERENCE,DESCRIPTION</SelectColumns>
					<From>EQUIPMENT_MODELS</From>
					<OrderBy>REFERENCE</OrderBy>
					<AddNull>True</AddNull>
					<FieldType>LIST</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdEquipmentModel</PropertyName>
					<queryField>true</queryField>
					<visibleField>false</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>MACHINE_ITEM</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>MILL_SECTOR</FieldDB>
					<FieldName>MILL_SECTOR</FieldName>
					<FieldType>String</FieldType>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>STICKY_NOTE</FieldDB>
					<FieldName>Sticky note</FieldName>
					<FieldType>String</FieldType>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>LAST_MAINT_DATE</FieldDB>
					<FieldName>Last Check</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>LastMaintDate</PropertyName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>OPERATION_NUMBER</FieldDB>
					<FieldName>Operation number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>OperationNumber</PropertyName>
					<queryField>false</queryField>
					<visibleField>false</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>WORKING_TIME</FieldDB>
					<FieldName>Working time</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<UnitDB>h</UnitDB>
					<UnitASP>h</UnitASP>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>WorkingTime</PropertyName>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>EQUIPMENT_DOCS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>EML_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=EQUIPMENT_DOCS&amp;menuname=MENU_MAINTENANCE</scriptName>
					<imageSrc>image\documents.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>DESCRIPTION</OrderBy>
			<DBName>VIEW_EQUIPMENTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>EQUIPMENTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>8</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>IdProcedure</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>CODE</FieldDB>
					<FieldName>Procedure code</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>Code</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>MPT_ID</FieldDB>
					<FieldName>Type of procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MAINT_PROC_TYPES</From>
					<SelectColumns>MPT_TYPE</SelectColumns>
					<OrderBy>MPT_TYPE</OrderBy>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<AddNull>true</AddNull>
					<PropertyName>IdMaintProcType</PropertyName>
					<EnableKeyEntry>true</EnableKeyEntry>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>Description</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>MACHINE_STATUS</FieldDB>
					<FieldName>This procedure can be made only once the machine is</FieldName>
					<ValueColumn>MACHINE_STATUS</ValueColumn>
					<From>MAINTENANCE_PROCEDURES</From>
					<SelectColumns>DISTINCT MACHINE_STATUS</SelectColumns>
					<OrderBy>1</OrderBy>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>MachineStatus</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>CONS_MATERIAL</FieldDB>
					<FieldName>Material</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>ConsMaterial</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>TOOLING</FieldDB>
					<FieldName>Needed Tools</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Tooling</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>TECHNICAL_REFERENCES</FieldDB>
					<FieldName>Technical References</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>TechnicalReferences</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>SAFETY_RULES</FieldDB>
					<FieldName>Safety rules</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SafetyRules</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>INSTRUCTIONS</FieldDB>
					<FieldName>Instructions</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Instructions</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>WORKING_TIME_COUNTER_RESET_FLA</FieldDB>
					<FieldName>Do you want to reset the effective times counter after the execution of this procedure?</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>NO</FixedRuntimeValue>
					<PropertyName>WorkingTimeCounterResetFla</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EVENT_NUMBER_COUNTER_RESET_FLA</FieldDB>
					<FieldName>Do you want to reset the events counter after the execution of this procedure?</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>NO</FixedRuntimeValue>
					<PropertyName>EventNumberCounterResetFla</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ALARM_NUMBER_COUNTER_RESET_FLA</FieldDB>
					<FieldName>Do you want to reset the alarms counter after the execution of this procedure?</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>NO</FixedRuntimeValue>
					<PropertyName>AlarmNumberCounterResetFla</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ELAPSED_TIME_COUNTER_RESET_FLA</FieldDB>
					<FieldName>Do you want to reset the absolut times counter after the execution of this procedure?</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>NO</FixedRuntimeValue>
					<PropertyName>ElapsedTimeCounterResetFla</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>OPER_NUMBER_COUNTER_RESET_FLA</FieldDB>
					<FieldName>Do you want to reset the operations counter after the execution of this procedure?</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>NO</FixedRuntimeValue>
					<PropertyName>OperNumberCounterResetFla</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<queryField>false</queryField>
					<visibleField>false</visibleField>
					<PropertyName>IdProcedure</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>CODE</FieldDB>
					<FieldName>Procedure code</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
					<PropertyName>Code</PropertyName>
				</ViewField>
				<ViewField>
					<FieldDB>MPT_ID</FieldDB>
					<FieldName>Type of procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MAINT_PROC_TYPES</From>
					<SelectColumns>MPT_TYPE</SelectColumns>
					<OrderBy>MPT_TYPE</OrderBy>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<AddNull>true</AddNull>
					<PropertyName>IdMaintProcType</PropertyName>
					<EnableKeyEntry>true</EnableKeyEntry>
					<queryField>true</queryField>
					<visibleField>false</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>MPT_Type</FieldDB>
					<FieldName>Type</FieldName>
					<EnableKeyEntry>true</EnableKeyEntry>
					<FieldType>String</FieldType>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
					<PropertyName>Description</PropertyName>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>MAINTENANCE_PROCEDURES</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\edit.aspx?control=edit&amp;excol=true&amp;pagename=MAINTENANCE_PROCEDURES&amp;readonly=1&amp;menuname=MENU_MAINTENANCE</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>DESCRIPTION</OrderBy>
			<DBName>VIEW_MAINTENANCE_PROCEDURES</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>MAINTENANCE_PROCEDURES</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>9</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>ID</FieldName>
					<AddNull>false</AddNull>
					<FieldType>AUTO</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdProcedureDoc</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>LINK</FieldDB>
					<FieldName>Documentation link</FieldName>
					<FieldType>Link</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Link</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>MP_ID</FieldDB>
					<FieldName>Related procedure code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>Request.QueryString("MP_ID")</FixedRuntimeValue>
					<PropertyName>IdMaintenanceProcedures</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<AddNull>false</AddNull>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>LINK</FieldDB>
					<FieldName>Documentation link</FieldName>
					<FieldType>Link</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Link</PropertyName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<AddButtonParams>
				<Parameter>MP_ID</Parameter>
			</AddButtonParams>
			<OrderBy>DESCRIPTION</OrderBy>
			<DBName>PROCEDURE_DOCS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PROCEDURE_DOCS</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>10</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>ID</FieldName>
					<AddNull>false</AddNull>
					<FieldType>AUTO</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdEquipmentDoc</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>LINK</FieldDB>
					<FieldName>Documentation link</FieldName>
					<FieldType>Link</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Link</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EML_ID</FieldDB>
					<FieldName>Related procedure code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRuntimeValue>Request.QueryString("EML_ID")</FixedRuntimeValue>
					<PropertyName>IdMaintenanceEquipment</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<AddNull>false</AddNull>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<queryField>false</queryField>
					<visibleField>true</visibleField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>EQUIPMENT_DOCS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>LINK</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=pdf&amp;excol=true&amp;pagename=EQUIPMENT_DOCS&amp;menuname=MENU_MAINTENANCE</scriptName>
					<imageSrc>image\acroread.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
				<MasterDBName>EQUIPMENT_MODELS</MasterDBName>
				<FilterColumns>
					<FilterColumn>
						<ColumnName>ID</ColumnName>
						<FilterValueParamName>EML_ID</FilterValueParamName>
					</FilterColumn>
				</FilterColumns>
				<EditFields>
					<EditField>
						<FieldDB>REFERENCE</FieldDB>
						<FieldName>Reference model</FieldName>
						<FieldType>String</FieldType>
					</EditField>
					<EditField>
						<FieldDB>DESCRIPTION</FieldDB>
						<FieldName>Description</FieldName>
						<FieldType>String</FieldType>
					</EditField>
				</EditFields>
			</Header>
			<AddButtonParams>
				<Parameter>EML_ID</Parameter>
			</AddButtonParams>
			<OrderBy>DESCRIPTION</OrderBy>
			<DBName>EQUIPMENT_DOCS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>EQUIPMENT_DOCS</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>44</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>SP_ID</FieldDB>
					<FieldName>Code article</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>SPARE_PARTS</From>
					<SelectColumns>CODE, DESCRIPTION</SelectColumns>
					<OrderBy>CODE ASC</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdSparePart</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>MPA_ID</FieldDB>
					<FieldName>Code procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_ASSIGNEMENTS</From>
					<SelectColumns>PROCCODE,PDESC,SPECIAL_NOTES</SelectColumns>
					<OrderBy>PROCCODE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdMaintProcAssign</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>MP_ID</FieldDB>
					<FieldName>Code procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_MAINTENANCE_PROCEDURES</From>
					<SelectColumns>CODE,DESCRIPTION</SelectColumns>
					<OrderBy>CODE ASC</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdMaintProcAssign</PropertyName>
					<FixedRuntimeValue>Request.QueryString("MP_ID")</FixedRuntimeValue>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>REQUEST_QTY</FieldDB>
					<FieldName>Quantità richiesta</FieldName>
					<FieldType>Number</FieldType>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>RerquestQty</PropertyName>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>SP_ID</FieldDB>
					<FieldName>Code article</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>SPARE_PARTS</From>
					<SelectColumns>CODE, DESCRIPTION</SelectColumns>
					<OrderBy>CODE ASC</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdSparePart</PropertyName>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MPA_ID</FieldDB>
					<FieldName>Code procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_ASSIGNEMENTS</From>
					<SelectColumns>PROCCODE,PDESC,SPECIAL_NOTES</SelectColumns>
					<OrderBy>PROCCODE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdMaintProcAssign</PropertyName>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MP_ID</FieldDB>
					<FieldName>Code procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_MAINTENANCE_PROCEDURES</From>
					<SelectColumns>CODE,DESCRIPTION</SelectColumns>
					<OrderBy>CODE ASC</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdMaintProcAssign</PropertyName>
					<FixedRuntimeValue>Request.QueryString("MP_ID")</FixedRuntimeValue>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EQU_ID</FieldDB>
					<FieldName>Equipment</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION,MILL_TYPE</SelectColumns>
					<OrderBy>DESCRIPTION,MILL_TYPE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CODE_SP_DESC</FieldDB>
					<FieldName>Code article</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SP</FieldDB>
					<FieldName>Description article</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CODE_PROC</FieldDB>
					<FieldName>Code pocedure</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_PROC</FieldDB>
					<FieldName>Procedure</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>REQUEST_QTY</FieldDB>
					<FieldName>Quantità richiesta</FieldName>
					<FieldType>Number</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
					<PropertyName>RerquestQty</PropertyName>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
				</ViewField>
				<ViewField>
					<FieldDB>EQUIPMENTS</FieldDB>
					<FieldName>Equipments</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MILL_TYPE</FieldDB>
					<FieldName>Mill</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<AddMenuItemNames>
				<AddMenuItemName>
					<NameLink>Procedure</NameLink>
					<NameMenu>MENU_MAINTENANCE</NameMenu>
				</AddMenuItemName>
			</AddMenuItemNames>
			<OrderBy>MP_ID</OrderBy>
			<DBName>VIEW_NEEDED_SPARE_PARTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>ASSIGNEMENT_SPARE_PARTS_MP</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>12</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>ID</FieldName>
					<AddNull>false</AddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EQU_ID</FieldDB>
					<FieldName>Machine</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION, MILL_SECTOR</SelectColumns>
					<OrderBy>DESCRIPTION, MILL_SECTOR</OrderBy>
					<AddNull>false</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>IdEquipment</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>MP_ID</FieldDB>
					<FieldName>Procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MAINTENANCE_PROCEDURES</From>
					<SelectColumns>CODE,DESCRIPTION</SelectColumns>
					<OrderBy>CODE,DESCRIPTION</OrderBy>
					<AddNull>false</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>IdMaintenanceProcedures</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ELAPSED_TIME_INTERVAL_DAY</FieldDB>
					<FieldName>ABS_TIME_TO_TRIG_PROC</FieldName>
					<UnitDB>days</UnitDB>
					<UnitASP>days</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>ElapsedTimeIntervalDay</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>WORKED_TIME_INTERVAL_HOUR</FieldDB>
					<FieldName>WORK_TIME_TO_TRIG_PROC</FieldName>
					<UnitDB>h</UnitDB>
					<UnitASP>h</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>WorkedTimeIntervalHour</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ALARM_NUMBER</FieldDB>
					<FieldName>ALARM_NUM_TO_TRIG_PROC</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>AlarmNumber</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>OPERATION_NUMBER</FieldDB>
					<FieldName>OPERATION_NUM_TO_TRIG_PROC</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>OperationNumber</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>EVENT_NUMBER</FieldDB>
					<FieldName>Events number to trigger the procedure</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>EventNumber</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>DAY_DELAY</FieldDB>
					<FieldName>DAYS_TO_EXP</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>DayDelay</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>WORK_TIME_DELAY</FieldDB>
					<FieldName>WORK_HOURS_TO_EXP</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>WorkTimeDelay</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ALARM_DELAY</FieldDB>
					<FieldName>ALARM_NUM_TO_EXP</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>AlarmDelay</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>OPERATION_DELAY</FieldDB>
					<FieldName>OPERATION_NUM_TO_EXP</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>OperationDelay</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>EVENT_DELAY</FieldDB>
					<FieldName>Events to expiration</FieldName>
					<AddNull>false</AddNull>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<PropertyName>EventDelay</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>SPECIAL_NOTES</FieldDB>
					<FieldName>DB_SPECIAL_NOTE</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>SpecialNotes</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>TIME_NEEDED</FieldDB>
					<FieldName>NEEDED_TIME</FieldName>
					<UnitASP>mn</UnitASP>
					<UnitDB>mn</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>TimeNeeded</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>LAST_MAINT_DATE</FieldDB>
					<FieldName>Date</FieldName>
					<AddNull>false</AddNull>
					<FieldType>TDate</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>LastMaintDate</PropertyName>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>EDESC</FieldDB>
					<FieldName>Item</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MILL_SECTOR</FieldDB>
					<FieldName>MILL_SECTOR</FieldName>
					<FieldType>String</FieldType>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>SPECIAL_NOTES</FieldDB>
					<FieldName>DB_SPECIAL_NOTE</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PROCCODE</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PDesc</FieldDB>
					<FieldName>Procedure</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DAY_DELAY</FieldDB>
					<FieldName>DAYS_TO_EXP</FieldName>
					<AddNull>false</AddNull>
					<FieldType>NUMBER</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WORK_TIME_DELAY</FieldDB>
					<FieldName>WORK_HOURS_TO_EXP</FieldName>
					<AddNull>false</AddNull>
					<FieldType>NUMBER</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>PROCEXECUTION</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=procexecution&amp;excol=true&amp;pagename=MAINT_PROC_EXEC_REQ&amp;menuname=MENU_MAINTENANCE</scriptName>
					<imageSrc>image\done.png</imageSrc>
					<imageToolTip>Run</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>MODED</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\edit.aspx?control=edit&amp;readonly=1&amp;excol=true&amp;pagename=MAINTENANCE_PROCEDURES_ASSIGNMENT&amp;menuname=MENU_MAINTENANCE</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>View</imageToolTip>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>EDESC, MILL_SECTOR, PROCCODE</OrderBy>
			<DBName>VIEW_ASSIGNEMENTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>MAINTENANCE_PROCEDURES_ASSIGNMENT</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>13</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Auto</FieldType>
					<IsHidden>true</IsHidden>
					<updateField>false</updateField>
					<insertField>true</insertField>
					<PropertyName>IdSpareParts</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>CODE</FieldDB>
					<FieldName>Code article</FieldName>
					<FieldType>WString</FieldType>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>Code</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description article</FieldName>
					<FieldType>WString</FieldType>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>Description</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>CODE_LOCATION</FieldDB>
					<FieldName>Code location</FieldName>
					<FieldType>WString</FieldType>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>CodeDescription</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>STOCK_QTY</FieldDB>
					<FieldName>Stock qty</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>StockQty</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ORDER_QTY</FieldDB>
					<FieldName>Order qty</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>OrderQty</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>NUMBER_DA</FieldDB>
					<FieldName>Number DA</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>NumberDa</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>ORDER_NUMBER</FieldDB>
					<FieldName>Order number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<PropertyName>OrderNumber</PropertyName>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>CODE</FieldDB>
					<FieldName>Code article</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>REF_SUPPLIER</FieldDB>
					<FieldName>ref_supplier</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description article</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CODE_LOCATION</FieldDB>
					<FieldName>Code location</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOCK_QTY</FieldDB>
					<FieldName>Stock qty</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ORDER_QTY</FieldDB>
					<FieldName>Order qty</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NUMBER_DA</FieldDB>
					<FieldName>Number DA</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ORDER_NUMBER</FieldDB>
					<FieldName>Order number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>CODE</OrderBy>
			<DBName>VIEW_SPARE_PARTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SPARE_PARTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>true</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>14</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Manual equipment</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION,MILL_TYPE</SelectColumns>
					<Where>AUTO='M'</Where>
					<OrderBy>DESCRIPTION</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>list</FieldType>
					<PropertyName>IdEquipment</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EQU_ID_MAINT_DATA</FieldDB>
					<FieldName>Automatic equipment</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION,MILL_TYPE</SelectColumns>
					<Where>AUTO='A'</Where>
					<OrderBy>DESCRIPTION</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<PropertyName>EquIdMaintData</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>RATIO</FieldDB>
					<FieldName>Ratio</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>Equal</NumBound>
					<PropertyName>Ratio</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Manual equipment</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION_MAINT_DATA</FieldDB>
					<FieldName>Automatic equipment</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Manual equipment</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION,MILL_TYPE</SelectColumns>
					<Where>AUTO='M'</Where>
					<OrderBy>DESCRIPTION</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>list</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EQU_ID_MAINT_DATA</FieldDB>
					<FieldName>Automatic equipment</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION,MILL_TYPE</SelectColumns>
					<Where>AUTO='A'</Where>
					<OrderBy>DESCRIPTION</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>RATIO</FieldDB>
					<FieldName>Ratio</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>Equal</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>RATIO</FieldDB>
					<FieldName>Ratio</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>ID</OrderBy>
			<DBName>VIEW_EQUIPMENTS_MAINT_DATA</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>VIEW_EQUIPMENTS_MAINT_DATA_EQU</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>17</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>NOTES</FieldDB>
					<FieldName>Note</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Note</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>REFERENCE</FieldDB>
					<FieldName>Reference model</FieldName>
					<ValueColumn>REFERENCE</ValueColumn>
					<From>EQUIPMENT_MODELS</From>
					<SelectColumns>REFERENCE</SelectColumns>
					<OrderBy>REFERENCE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EQU_ID</FieldDB>
					<FieldName>Equipment</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION,MILL_TYPE</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MACHINE_STATUS</FieldDB>
					<FieldName>Machine status LED</FieldName>
					<ValueColumn>MACHINE_STATUS</ValueColumn>
					<From>MAINTENANCE_PROCEDURES</From>
					<SelectColumns>DISTINCT MACHINE_STATUS</SelectColumns>
					<OrderBy>1</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MPT_ID</FieldDB>
					<FieldName>Type of procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MAINT_PROC_TYPES</From>
					<SelectColumns>MPT_TYPE</SelectColumns>
					<OrderBy>MPT_TYPE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MP_ID</FieldDB>
					<FieldName>Procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MAINTENANCE_PROCEDURES</From>
					<SelectColumns>CODE, DESCRIPTION</SelectColumns>
					<OrderBy>CODE, DESCRIPTION</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EQDESC</FieldDB>
					<FieldName>Equipment</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MILL_SECTOR</FieldDB>
					<FieldName>MILL_SECTOR</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CODE</FieldDB>
					<FieldName>Procedure</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PDesc</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EXPIRY_DATE</FieldDB>
					<FieldName>Expiration date</FieldName>
					<FieldType>TDATE</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MPL_DELAY</FieldDB>
					<FieldName>Days since expired</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NOTES</FieldDB>
					<FieldName>Note</FieldName>
					<FieldType>WString</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>PROCEXECUTION</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=procexecution&amp;readonly=0&amp;excol=true&amp;pagename=EXECUTED_MAINT_PROC&amp;menuname=MENU_MAINTENANCE</scriptName>
					<imageSrc>image\done.png</imageSrc>
					<imageToolTip>Run</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>ID</OrderBy>
			<DBName>VIEW_MAINTENANCE_PLANNING</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>MAINTENANCE_PLANNING</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>18</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Equipment</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CODE</FieldDB>
					<FieldName>Procedure</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>ProcDesc</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>PROGRAMMED_FLAG</FieldDB>
					<FieldName>Programmed</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>EXEC_MODE</FieldDB>
					<FieldName>Mode execution</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>EXPIRY_DATE</FieldDB>
					<FieldName>Expiration date</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>TDate</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CLOSING_DATE</FieldDB>
					<FieldName>Execution date</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>TDate</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>USERNAME</FieldDB>
					<FieldName>By user?</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DAY_DELAY</FieldDB>
					<FieldName>Delay in days</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>ALARM_DELAY</FieldDB>
					<FieldName>Delay allarms</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>EVENT_DELAY</FieldDB>
					<FieldName>Delay event</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>OPERATION_DELAY</FieldDB>
					<FieldName>Delay operation</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WORK_TIME_DELAY</FieldDB>
					<FieldName>Delay working time</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>INTERVENTION_REPORT</FieldDB>
					<FieldName>Intervention report</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>REFERENCE</FieldDB>
					<FieldName>Reference model</FieldName>
					<ValueColumn>REFERENCE</ValueColumn>
					<From>EQUIPMENT_MODELS</From>
					<SelectColumns>REFERENCE</SelectColumns>
					<OrderBy>REFERENCE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EQU_ID</FieldDB>
					<FieldName>Equipment</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>EQUIPMENTS</From>
					<SelectColumns>DESCRIPTION, MILL_TYPE</SelectColumns>
					<OrderBy>DESCRIPTION</OrderBy>
					<AddNull>True</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MPT_ID</FieldDB>
					<FieldName>Type of procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MAINT_PROC_TYPES</From>
					<SelectColumns>MPT_TYPE</SelectColumns>
					<OrderBy>MPT_TYPE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>LIST</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MP_ID</FieldDB>
					<FieldName>Procedure</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MAINTENANCE_PROCEDURES</From>
					<SelectColumns>CODE, DESCRIPTION</SelectColumns>
					<OrderBy>CODE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>INTERVENTION_REPORT</FieldDB>
					<FieldName>Text inside report</FieldName>
					<FieldType>String</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Equipment</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MILL_SECTOR</FieldDB>
					<FieldName>MILL_SECTOR</FieldName>
					<FieldType>String</FieldType>
					<queryField>true</queryField>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>CODE</FieldDB>
					<FieldName>Procedure</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ProcDesc</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PROGRAMMED_FLAG</FieldDB>
					<FieldName>Programmed</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EXPIRY_DATE</FieldDB>
					<FieldName>Date expiration</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CLOSING_DATE</FieldDB>
					<FieldName>Date execution</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>USERNAME</FieldDB>
					<FieldName>By User?</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Mod</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\edit.aspx?control=edit&amp;readonly=1&amp;excol=true&amp;pagename=MAINT_PROC_REPORT&amp;menuname=MENU_MAINTENANCE</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>View</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>ID DESC,CLOSING_DATE DESC</OrderBy>
			<DBName>VIEW_MAINTENANCE_REPORTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>MAINT_PROC_REPORT</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>19</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>PT_TYPE</FieldDB>
					<FieldName>Type</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>ID</FieldName>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>PT_ID</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_TYPE</FieldDB>
					<FieldName>Type</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>ID</OrderBy>
			<DBName>PRODUCT_TYPES</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PRODUCT_TYPES</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>20</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Auto</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdErp</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>SHORT_NAME</FieldDB>
					<FieldName>Short Name</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>ShortName</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>PT_ID</FieldDB>
					<FieldName>Type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>PRODUCT_TYPES</From>
					<SelectColumns>PT_TYPE</SelectColumns>
					<OrderBy>PT_TYPE</OrderBy>
					<Where>ID &lt;&gt; 999</Where>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdProductType</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IsObsolete</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>HECTOLITRIC_WEIGHT</FieldDB>
					<FieldName>HECTOLITRIC_WEIGHT</FieldName>
					<UnitDB>
					</UnitDB>
					<UnitASP>
					</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>Equal</NumBound>
					<FixedRuntimeValue>1</FixedRuntimeValue>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>HectolitricWeight</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>CREATION_DATE</FieldDB>
					<FieldName>Creation date</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>CreationDate</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<FieldType>String</FieldType>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SHORT_NAME</FieldDB>
					<FieldName>Short Name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CREATION_DATE</FieldDB>
					<FieldName>Creation date</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>LAST_UPDATE</FieldDB>
					<FieldName>Last update</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_TYPE</FieldDB>
					<FieldName>Type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>PRODUCT_TYPES</From>
					<SelectColumns>PT_TYPE</SelectColumns>
					<QueryFieldName>PT_ID</QueryFieldName>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>HECTOLITRIC_WEIGHT</FieldDB>
					<FieldName>HECTOLITRIC_WEIGHT</FieldName>
					<UnitDB>
					</UnitDB>
					<UnitASP>
					</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>NAME</OrderBy>
			<DBName>VIEW_PRODUCTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PRODUCTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>21</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>Job</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CYC_ID</FieldDB>
					<FieldName>Cycle type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CYCLES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<OrderBy>ID ASC</OrderBy>
					<FieldType>list</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Cycle type</FieldName>
					<FieldType>String</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>RECIPE_DESCRIPTION</FieldDB>
					<FieldName>Recipe</FieldName>
					<Summable>false</Summable>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_DATE</FieldDB>
					<FieldName>Start date</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_DATE</FieldDB>
					<FieldName>STOP_DATE</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PRODUCED_AMOUNT</FieldDB>
					<FieldName>Quantity</FieldName>
					<UnitASP>t</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Flows</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>COUNTER</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_PRODUCTION_REPORTS&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>View</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>VIEW_PRODUCTION_REPORTS_COMPLETED</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>COUNTER</fieldDB>
						<fieldDB>PRT_ID</fieldDB>
						<fieldDB>CYC_ID</fieldDB>
					</parameters>
					<scriptName>..\ViewCycleReports.aspx?excol=true&amp;control=view&amp;pagename=CYCLE_&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\report.png</imageSrc>
					<imageToolTip>View</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>Report</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>COUNTER</fieldDB>
						<fieldDB>CYC_ID</fieldDB>
					</parameters>
					<scriptName>..\ProductionReports.aspx?excol=true&amp;control=report&amp;pagename=PRODUCTION_REPORTS_&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\print.png</imageSrc>
					<imageToolTip>View</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>PRT_ID DESC</OrderBy>
			<DBName>VIEW_PRODUCTION_REPORTS_HISTORY</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PRODUCTION_REPORTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>22</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>PA_ID</FieldDB>
					<FieldName>Parcel</FieldName>
					<FieldType>Number</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DATE</FieldDB>
					<FieldName>date</FieldName>
					<FieldType>TDate</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>JOB_COUNTER</FieldDB>
					<FieldName>Job</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>Number</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SUP_NAME</FieldDB>
					<FieldName>Supplier</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SUP_ID</FieldDB>
					<FieldName>Supplier</FieldName>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdSupplier</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CAR_NAME</FieldDB>
					<FieldName>Carrier</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CAR_ID</FieldDB>
					<FieldName>Carrier</FieldName>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdCarrier</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Quantity</FieldName>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Amount</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>PA_ID</FieldDB>
					<FieldName>Parcel</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DATE</FieldDB>
					<FieldName>date</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CYCLE_TYPE</FieldDB>
					<FieldName>CYCLE_NAME</FieldName>
					<FieldType>String</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CYC_ID</FieldDB>
					<FieldName>CYCLE_NAME</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CYCLES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<Where>ID=0</Where>
					<!-- TODO da configurare per ogni commessa -->
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<EnableKeyEntry>true</EnableKeyEntry>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>JOB_COUNTER</FieldDB>
					<FieldName>Job</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SUP_NAME</FieldDB>
					<FieldName>Supplier</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SUP_ID</FieldDB>
					<FieldName>Supplier</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>SUPPLIERS</From>
					<SelectColumns>NAME</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CAR_NAME</FieldDB>
					<FieldName>Carrier</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CAR_ID</FieldDB>
					<FieldName>Carrier</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CARRIERS</From>
					<SelectColumns>NAME</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Quantity</FieldName>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Fows All</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>PA_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_PARCEL&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<SumColumns>
				<Fields>
					<Field>AMOUNT</Field>
					<FieldName>Quantity</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<Decimal>3</Decimal>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>DATE DESC</OrderBy>
			<DBName>VIEW_PARCELS_CONFIRMED</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PARCELS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>47</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>PA_ID</FieldDB>
					<FieldName>Parcel</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DATE</FieldDB>
					<FieldName>date</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SUP_NAME</FieldDB>
					<FieldName>Supplier</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CAR_NAME</FieldDB>
					<FieldName>Carrier</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Quantity</FieldName>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Fows All</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>PA_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_PARCEL&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>Fows 999</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>PA_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_PARCEL_999&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\configure.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>Fows 666</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>PA_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_PARCEL_666&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\edit.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>ConfirmParcel</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>PA_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;operation=ConfirmParcel&amp;pagename=PARCELS_TO_BE_CONFIRMED&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\done.png</imageSrc>
					<imageToolTip>CONFIRM</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<SumColumns>
				<Fields>
					<Field>AMOUNT</Field>
					<FieldName>Quantity</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<Decimal>3</Decimal>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>DATE DESC</OrderBy>
			<DBName>VIEW_PARCELS_TO_BE_CONFIRMED</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>PARCELS_TO_BE_CONFIRMED</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>48</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>PA_ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<Decimal>3</Decimal>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_TO_PARCEL</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_TO_PARCEL</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>49</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>PA_ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_TO_PARCEL_999</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_TO_PARCEL_999</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>false</HasSearchButton>
			<EnumPageNameCode>50</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>PA_ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_TO_PARCEL_666</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_TO_PARCEL_666</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>false</HasSearchButton>
			<EnumPageNameCode>51</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>SH_ID</FieldDB>
					<FieldName>Shipment</FieldName>
					<FieldType>Number</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DATE</FieldDB>
					<FieldName>date</FieldName>
					<FieldType>TDate</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>JOB_COUNTER</FieldDB>
					<FieldName>Job</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>Number</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CUS_NAME</FieldDB>
					<FieldName>Customer</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CUS_ID</FieldDB>
					<FieldName>Customer</FieldName>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdSupplier</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CAR_NAME</FieldDB>
					<FieldName>Carrier</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>CAR_ID</FieldDB>
					<FieldName>Carrier</FieldName>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdCarrier</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Quantity</FieldName>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Amount</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>SH_ID</FieldDB>
					<FieldName>Shipment</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DATE</FieldDB>
					<FieldName>date</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CYCLE_TYPE</FieldDB>
					<FieldName>CYCLE_NAME</FieldName>
					<FieldType>String</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CYC_ID</FieldDB>
					<FieldName>CYCLE_NAME</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CYCLES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<Where>ID=0</Where>
					<!-- TODO da configurare per ogni commessa -->
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<EnableKeyEntry>true</EnableKeyEntry>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>JOB_COUNTER</FieldDB>
					<FieldName>Job</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CUS_NAME</FieldDB>
					<FieldName>Customer</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CUS_ID</FieldDB>
					<FieldName>Customer</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CUSTOMERS</From>
					<SelectColumns>NAME</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CAR_NAME</FieldDB>
					<FieldName>Carrier</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CAR_ID</FieldDB>
					<FieldName>Carrier</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CARRIERS</From>
					<SelectColumns>NAME</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>BAGS_NUMBER</FieldDB>
					<FieldName>Bags number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>BAG_SIZE</FieldDB>
					<FieldName>Bag size</FieldName>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PALLETS_NUMBER</FieldDB>
					<FieldName>Pallets number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PALLET_SIZE</FieldDB>
					<FieldName>Pallet size</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Quantity</FieldName>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Fows All</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>SH_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_SHIPMENT&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<SumColumns>
				<Fields>
					<Field>AMOUNT</Field>
					<FieldName>Quantity</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>DATE DESC</OrderBy>
			<DBName>VIEW_SHIPMENTS_CONFIRMED</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SHIPMENTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>52</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>SH_ID</FieldDB>
					<FieldName>Shipment</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DATE</FieldDB>
					<FieldName>date</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CUS_NAME</FieldDB>
					<FieldName>Customer</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CAR_NAME</FieldDB>
					<FieldName>Carrier</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Quantity</FieldName>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Fows All</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>SH_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_SHIPMENT&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>Fows 999</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>SH_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_SHIPMENT_999&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\configure.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>Fows 666</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>SH_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;pagename=FLOW_LOGS_TO_SHIPMENT_666&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\edit.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
				<column>
					<name>ConfirmShipment</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>SH_ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;excol=true&amp;operation=ConfirmShipment&amp;pagename=SHIPMENTS_TO_BE_CONFIRMED&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\done.png</imageSrc>
					<imageToolTip>CONFIRM</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<SumColumns>
				<Fields>
					<Field>AMOUNT</Field>
					<FieldName>Quantity</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>DATE DESC</OrderBy>
			<DBName>VIEW_SHIPMENTS_TO_BE_CONFIRMED</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SHIPMENTS_TO_BE_CONFIRMED</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>53</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>SH_ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_TO_SHIPMENT</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_TO_SHIPMENT</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>54</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<Decimal>0</Decimal>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>SH_ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_TO_SHIPMENT_999</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_TO_SHIPMENT_999</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>false</HasSearchButton>
			<EnumPageNameCode>55</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<Decimal>0</Decimal>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>SH_ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_TO_SHIPMENT_666</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_TO_SHIPMENT_666</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>false</HasSearchButton>
			<EnumPageNameCode>56</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>CYC_ID</FieldDB>
					<FieldName>Cycle ID</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CYC_NAME</FieldDB>
					<FieldName>CYCLE_NAME</FieldName>
					<FieldType>String</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>Job</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>REC_NAME</FieldDB>
					<FieldName>Recipe name</FieldName>
					<FieldType>String</FieldType>
					<HideIfEmpty>true</HideIfEmpty>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_DATE</FieldDB>
					<FieldName>Start date</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>ForceCompleteOrder</name>
					<RequiredAccessLevel>System</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\default.aspx?control=view&amp;ForceCompleteOrder=yes&amp;excol=true&amp;pagename=VIEW_PRODUCTION_PLAN_ACTIVE&amp;menuname=MENU_CYCLES</scriptName>
					<imageSrc>image\unlock.png</imageSrc>
					<imageToolTip>FORCE_COMPLETE</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>CYC_ID, LINE_POSITION</OrderBy>
			<DBName>VIEW_PRODUCTION_PLAN_ACTIVE</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>VIEW_PRODUCTION_PLAN_ACTIVE</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>25</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>BATCH_NUMBER</FieldDB>
					<FieldName>Executed batch number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<HideIfEmpty>true</HideIfEmpty>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>FFL_ID, SOURCE_CELL</OrderBy>
			<DBName>VIEW_FLOW_LOGS_ACTIVE</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_ACTIVE</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>26</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<Decimal>3</Decimal>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<UnitASP>kg</UnitASP>
					<UnitDB>kg</UnitDB>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>BATCH_NUMBER</FieldDB>
					<FieldName>Executed batch number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<HideIfEmpty>true</HideIfEmpty>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_HISTORY</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>27</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Description</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Bin source</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>SourceBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Bin destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>DestBin</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<FieldType>Number</FieldType>
					<Decimal>0</Decimal>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StartTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDATE</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>StopTime</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Product</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FFL_ID</FieldDB>
					<FieldName>Family</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>FLOW_FAMILIES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<FieldType>List</FieldType>
					<AddNull>true</AddNull>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MANUAL_INPUT</FieldDB>
					<FieldName>Manual</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<DBName>VIEW_FLOW_LOGS_ARCHIVED</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_ARCHIVED</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>28</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>DESCRIPTOR</FieldDB>
					<FieldName>Lot</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>COUNTER</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product name</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_TYPE</FieldDB>
					<FieldName>Product type</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CREATION_DATE</FieldDB>
					<FieldName>Date</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>TDate</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<IsSQLExcluded>false</IsSQLExcluded>
					<IsVisibleButNotEditable>false</IsVisibleButNotEditable>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AUTO_NOTES</FieldDB>
					<FieldName>AUTO_NOTES</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>USER_NOTES</FieldDB>
					<FieldName>USER_NOTES</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>HAS_USER_NOTES</FieldDB>
					<FieldName>HAS_USER_NOTES</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<FieldType>CheckBoxYesNo</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Lot size</FieldName>
					<Summable>false</Summable>
					<DateFormat>0</DateFormat>
					<UnitDB>Kg</UnitDB>
					<UnitASP>Kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Lots</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>../Lot_History.aspx?control=lothistory&amp;pagename=lothistory&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\Lots.png</imageSrc>
					<imageToolTip>Lot</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>ID DESC</OrderBy>
			<DBName>VIEW_LOTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>LOTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>31</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdCustomer</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdErp</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>FULL_NAME</FieldDB>
					<FieldName>FULL_NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>FullName</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Address</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ZIP_CODE</FieldDB>
					<FieldName>ZIP_CODE</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>ZipCode</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>CITY</FieldDB>
					<FieldName>CITY</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>City</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>COUNTRY</FieldDB>
					<FieldName>COUNTRY</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Country</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>TELEPHONE</FieldDB>
					<FieldName>TELEPHONE</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Telephone</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EMAIL</FieldDB>
					<FieldName>EMAIL</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Email</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IsObsolete</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<FieldType>String</FieldType>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ZIP_CODE</FieldDB>
					<FieldName>ZIP_CODE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CITY</FieldDB>
					<FieldName>CITY</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTRY</FieldDB>
					<FieldName>COUNTRY</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>TELEPHONE</FieldDB>
					<FieldName>TELEPHONE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EMAIL</FieldDB>
					<FieldName>EMAIL</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>CUSTOMERS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\edit.aspx?control=edit&amp;excol=true&amp;pagename=CUSTOMERS&amp;readonly=1&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>false</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>NAME</OrderBy>
			<DBName>CUSTOMERS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>CUSTOMERS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>32</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdSupplier</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdErp</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>FULL_NAME</FieldDB>
					<FieldName>FULL_NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>FullName</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Address</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ZIP_CODE</FieldDB>
					<FieldName>ZIP_CODE</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>ZipCode</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>CITY</FieldDB>
					<FieldName>CITY</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>City</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>COUNTRY</FieldDB>
					<FieldName>COUNTRY</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Country</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>TELEPHONE</FieldDB>
					<FieldName>TELEPHONE</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Telephone</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EMAIL</FieldDB>
					<FieldName>EMAIL</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Email</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IsObsolete</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<FieldType>String</FieldType>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ZIP_CODE</FieldDB>
					<FieldName>ZIP_CODE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CITY</FieldDB>
					<FieldName>CITY</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTRY</FieldDB>
					<FieldName>COUNTRY</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>TELEPHONE</FieldDB>
					<FieldName>TELEPHONE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EMAIL</FieldDB>
					<FieldName>EMAIL</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>SUPPLIERS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\edit.aspx?control=edit&amp;excol=true&amp;pagename=SUPPLIERS&amp;readonly=1&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>false</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>NAME</OrderBy>
			<DBName>SUPPLIERS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>SUPPLIERS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>33</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdCarrier</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdErp</PropertyName>
					<updateField>false</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>FULL_NAME</FieldDB>
					<FieldName>FULL_NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>FullName</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Address</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ZIP_CODE</FieldDB>
					<FieldName>ZIP_CODE</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>ZipCode</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>CITY</FieldDB>
					<FieldName>CITY</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>City</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>COUNTRY</FieldDB>
					<FieldName>COUNTRY</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Country</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>TELEPHONE</FieldDB>
					<FieldName>TELEPHONE</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Telephone</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>EMAIL</FieldDB>
					<FieldName>EMAIL</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Email</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>CheckBoxYesNo</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IsObsolete</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID_ERP</FieldDB>
					<FieldName>ID_ERP</FieldName>
					<FieldType>String</FieldType>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ZIP_CODE</FieldDB>
					<FieldName>ZIP_CODE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CITY</FieldDB>
					<FieldName>CITY</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>COUNTRY</FieldDB>
					<FieldName>COUNTRY</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>TELEPHONE</FieldDB>
					<FieldName>TELEPHONE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>EMAIL</FieldDB>
					<FieldName>EMAIL</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>IS_OBSOLETE</FieldDB>
					<FieldName>IS_OBSOLETE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>CARRIERS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\edit.aspx?control=edit&amp;excol=true&amp;pagename=CARRIERS&amp;readonly=1&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>false</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>NAME</OrderBy>
			<DBName>CARRIERS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>CARRIERS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>34</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdCustomer</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Address</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>POST_CODE</FieldDB>
					<FieldName>Post code</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>PostCode</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>TELEPHONE_NUMBER</FieldDB>
					<FieldName>Telephone number</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>TelephoneNumber</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>FAX_NUMBER</FieldDB>
					<FieldName>Fax number</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>FaxNumber</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>ADDRESS</FieldDB>
					<FieldName>Address</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>POST_CODE</FieldDB>
					<FieldName>Post code</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>TELEPHONE_NUMBER</FieldDB>
					<FieldName>Telephone number</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FAX_NUMBER</FieldDB>
					<FieldName>Fax number</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>NAME</OrderBy>
			<DBName>AGENTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>AGENTS</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>35</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdCustomer</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Long</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>NAME</OrderBy>
			<DBName>WAREHOUSES</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>WAREHOUSES</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>36</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>JOB_NUMBER</FieldDB>
					<FieldName>Job_vuoto</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<FixedRunTimeValue>1</FixedRunTimeValue>
					<insertField>true</insertField>
					<updateField>true</updateField>
				</EditField>
				<EditField>
					<FieldDB>CEL_ID</FieldDB>
					<FieldName>Bin code</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<Where>CT_ID=2 AND ID &gt; 200</Where>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdCell</PropertyName>
					<insertField>true</insertField>
					<updateField>false</updateField>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTOR</FieldDB>
					<FieldName>LABEL_TEXT</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>LabelText</PropertyName>
					<insertField>false</insertField>
					<updateField>false</updateField>
				</EditField>
				<EditField>
					<FieldDB>INSERT_DATE</FieldDB>
					<FieldName>DB_DATE</FieldName>
					<FieldType>TDate</FieldType>
					<insertField>false</insertField>
					<updateField>true</updateField>
					<PropertyName>InsertDate</PropertyName>
				</EditField>
				<EditField>
					<FieldDB>READY_DATE</FieldDB>
					<FieldName>READY_DATE</FieldName>
					<FieldType>TDate</FieldType>
					<insertField>false</insertField>
					<updateField>true</updateField>
					<PropertyName>ReadyDate</PropertyName>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CEL_ID</FieldDB>
					<FieldName>Bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID, DESCRIPTION</SelectColumns>
					<Where>CT_ID = 6</Where>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>JOB_NUMBER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>INSERT_DATE</FieldDB>
					<FieldName>DB_DATE</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>READY_DATE</FieldDB>
					<FieldName>READY_DATE</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>VIEW_BINLAYERS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>CEL_ID</fieldDB>
					</parameters>
					<scriptName>../BinLayers.aspx?DRAWFLOW=0&amp;TYPEVIEW=JOBS&amp;menuname=MENU_STOCKS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>View</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<OrderBy>CEL_ID,INSERT_DATE DESC</OrderBy>
			<DBName>VIEW_JOB_TO_CEL</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>ALL_JOB_TO_CEL</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>38</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>LO_ID</FieldDB>
					<FieldName>ID Lot</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<IsSQLExcluded>true</IsSQLExcluded>
				</EditField>
				<EditField>
					<FieldDB>LO_NAME</FieldDB>
					<FieldName>lot</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<IsSQLExcluded>true</IsSQLExcluded>
				</EditField>
				<EditField>
					<FieldDB>CEL_ID</FieldDB>
					<FieldName>Bin</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<IsSQLExcluded>true</IsSQLExcluded>
				</EditField>
				<EditField>
					<FieldDB>CEL_NAME</FieldDB>
					<FieldName>Bin</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>false</insertField>
					<IsSQLExcluded>true</IsSQLExcluded>
				</EditField>
				<EditField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<Decimal>0</Decimal>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
					<IsSQLExcluded>true</IsSQLExcluded>
					<PropertyName>Amount</PropertyName>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>CEL_ID</FieldDB>
					<FieldName>Bin</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CEL_NAME</FieldDB>
					<FieldName>Nome cella</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CT_ID</FieldDB>
					<FieldName>BIN_TYPE</FieldName>
					<FieldType>LIST</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>CT_TYPE</SelectColumns>
					<From>CELL_TYPES</From>
					<AddNull>true</AddNull>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>LO_NAME</FieldDB>
					<FieldName>lot</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>0</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>INSERT_DATE</FieldDB>
					<FieldName>Date</FieldName>
					<FieldType>TDATE</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>TYPE_NAME</FieldDB>
					<FieldName>BIN_TYPE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>VIEW_BINLAYERS</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>CEL_ID</fieldDB>
						<fieldDB>TRACING_MODEL</fieldDB>
					</parameters>
					<scriptName>../BinLayers.aspx?drawflow=1&amp;typeview=LOTS&amp;menuname=MENU_STOCKS</scriptName>
					<imageSrc>image\detail.png</imageSrc>
					<imageToolTip>View</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<SumColumns>
				<Fields>
					<Field>AMOUNT</Field>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<DBName>VIEW_LOTS_TO_CELL</DBName>
			<OrderBy>CEL_ID, INSERT_DATE DESC</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>LOTS_TO_CELL</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>75</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_TYPE</FieldDB>
					<FieldName>Product type</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_ID</FieldDB>
					<FieldName>Product type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>PRODUCT_TYPES</From>
					<SelectColumns>PT_TYPE</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>Total amount</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>AMOUNT</Field>
					<FieldName>Total amount</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<DBName>VIEW_STOCK_AMOUNTS</DBName>
			<OrderBy>PT_ID, NAME</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>STOCKS_AMOUNT</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_TYPE</FieldDB>
					<FieldName>Product type</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_ID</FieldDB>
					<FieldName>Product type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>PRODUCT_TYPES</From>
					<SelectColumns>PT_TYPE</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SA_DATE</FieldDB>
					<FieldName>DB_DATE</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>INITIAL_AMOUNT</FieldDB>
					<FieldName>Initial amount</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>FINAL_AMOUNT</FieldDB>
					<FieldName>Final amount</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DIFFERENCE</FieldDB>
					<FieldName>Difference</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>INITIAL_AMOUNT</Field>
					<FieldName>Initial amount</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
				<Fields>
					<Field>FINAL_AMOUNT</Field>
					<FieldName>Final amount</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
				<Fields>
					<Field>DIFFERENCE</Field>
					<FieldName>Difference</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<DBName>VIEW_STOCK_DAILY_AMOUNTS</DBName>
			<OrderBy>SA_DATE DESC, PT_ID, NAME</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>STOCK_DAILY_AMOUNTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>USER_NAME</FieldDB>
					<FieldName>User</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SU_ID</FieldDB>
					<FieldName>User</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>SYSTEM_USERS</From>
					<SelectColumns>USER_NAME</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_ID</FieldDB>
					<FieldName>Product type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>PRODUCT_TYPES</From>
					<SelectColumns>PT_TYPE</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PRO_NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PRO_ID</FieldDB>
					<FieldName>Product</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>PRODUCTS</From>
					<SelectColumns>NAME</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CEL_NAME</FieldDB>
					<FieldName>Bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SMC_DATE</FieldDB>
					<FieldName>Date</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CEL_ID</FieldDB>
					<FieldName>Bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT_BIN_BEFORE_CORR</FieldDB>
					<FieldName>Stock bin amount before</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT_BIN_AFTER_CORR</FieldDB>
					<FieldName>Stock bin amount after</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT_BEFORE_CORRECTION</FieldDB>
					<FieldName>Stock amount before</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>AMOUNT_AFTER_CORRECTION</FieldDB>
					<FieldName>Stock amount after</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>CORRECTION_VALUE</FieldDB>
					<FieldName>Correction value</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>AMOUNT_BEFORE_CORRECTION</Field>
					<FieldName>Stock amount before</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
				<Fields>
					<Field>AMOUNT_AFTER_CORRECTION</Field>
					<FieldName>Stock amount after</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
				<Fields>
					<Field>CORRECTION_VALUE</Field>
					<FieldName>Correction value</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<DBName>VIEW_STOCK_MANUAL_CORR</DBName>
			<OrderBy>SMC_DATE DESC</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>STOCK_MANUAL_CORR</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>ID</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Auto</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>CEL_ID</FieldDB>
					<FieldName>Bin</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>CEL_ID</ValueColumn>
					<From>VIEW_CELLS_TO_SMC_PT_ID</From>
					<SelectColumns>DESCRIPTION, PRO_NAME, CURRENT_AMOUNT</SelectColumns>
					<OrderBy>CEL_ID</OrderBy>
					<AddNull>true</AddNull>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdCel</PropertyName>
					<CallEvent>EventSMCCell</CallEvent>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>PRO_ID</FieldDB>
					<FieldName>Product name</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_PRODUCTS</From>
					<SelectColumns>NAME</SelectColumns>
					<OrderBy>NAME</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdProduct</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>LT_ID</FieldDB>
					<FieldName>Lot type</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>LOT_TYPES</From>
					<SelectColumns>TYPE</SelectColumns>
					<OrderBy>TYPE</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdLotType</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>AMOUNT</FieldDB>
					<FieldName>DB_AMOUNT</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Amount</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>SU_ID</FieldDB>
					<FieldName>User ID</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Long</NumType>
					<NumBound>Equal</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>true</IsHidden>
					<PropertyName>IdUser</PropertyName>
					<updateField>false</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<extraColumns />
			<Header>
			</Header>
			<DBName>STOCK_MANUAL_CORR_REQ</DBName>
			<OrderBy>ID DESC</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>STOCK_MANUAL_CORR_REQ</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>39</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>BATCH_NUMBER</FieldDB>
					<FieldName>Executed batch number</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<HideIfEmpty>true</HideIfEmpty>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Description</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_SOURCECELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>SOURCE_CELL</FieldDB>
					<FieldName>Source bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCR_DESTCELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination bin</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>CELLS</From>
					<SelectColumns>ID,DESCRIPTION</SelectColumns>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>t</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>3</Decimal>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>Product</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<Header>
				<MasterDBName>VIEW_PRODUCTION_REPORTS_HISTORY</MasterDBName>
				<FilterColumns>
					<FilterColumn>
						<ColumnName>COUNTER</ColumnName>
						<FilterValueParamName>COUNTER</FilterValueParamName>
					</FilterColumn>
				</FilterColumns>
				<EditFields>
					<EditField>
						<FieldDB>DESCRIPTION</FieldDB>
						<FieldName>CYCLE_NAME</FieldName>
						<FieldType>WString</FieldType>
						<EnableKeyEntry>true</EnableKeyEntry>
					</EditField>
					<EditField>
						<FieldDB>COUNTER</FieldDB>
						<FieldName>Job</FieldName>
						<FieldType>String</FieldType>
						<TextBefore>NewRow</TextBefore>
					</EditField>
					<EditField>
						<FieldDB>PRODUCED_AMOUNT</FieldDB>
						<FieldName>Quantity</FieldName>
						<UnitASP>t</UnitASP>
						<UnitDB>kg</UnitDB>
						<FieldType>Number</FieldType>
						<NumType>Double</NumType>
						<Decimal>3</Decimal>
						<NumBound>MinMax</NumBound>
					</EditField>
					<EditField>
						<FieldDB>START_DATE</FieldDB>
						<FieldName>YIELDS_START_DATE</FieldName>
						<FieldType>String</FieldType>
					</EditField>
					<EditField>
						<FieldDB>STOP_DATE</FieldDB>
						<FieldName>YIELDS_STOP_DATE</FieldName>
						<FieldType>String</FieldType>
					</EditField>
				</EditFields>
			</Header>
			<FilterColumnsName>
				<FieldDB>COUNTER</FieldDB>
			</FilterColumnsName>
			<DBName>VIEW_FLOW_LOGS_HISTORY</DBName>
			<OrderBy>STOP_TIME DESC</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>FLOW_LOGS_PRODUCTION_REPORTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>false</HasSearchButton>
			<EnumPageNameCode>37</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<CanBeNull>false</CanBeNull>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<CanBeNull>false</CanBeNull>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>TDate</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<CanBeNull>false</CanBeNull>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>TDate</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<CanBeNull>false</CanBeNull>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<ListAddEmpty>false</ListAddEmpty>
					<ListAddNull>false</ListAddNull>
					<FieldType>Number</FieldType>
					<Decimal>0</Decimal>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<CanBeNull>false</CanBeNull>
					<PropertyName>Weight</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>Job</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DEST_CELL</FieldDB>
					<FieldName>Destination</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>WEIGHT</FieldDB>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>0</Decimal>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>Fows</name>
					<RequiredAccessLevel>Editing</RequiredAccessLevel>
					<parameters>
						<fieldDB>ID</fieldDB>
						<fieldDB>COUNTER</fieldDB>
						<fieldDB>CYC_ID</fieldDB>
					</parameters>
					<scriptName>..\edit.aspx?control=edit&amp;excol=true&amp;pagename=CONFIRM_FLOW_LOGS&amp;menuname=MENU_LOTS</scriptName>
					<imageSrc>image\configure.png</imageSrc>
					<imageToolTip>VIEW</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<FilterColumnsName>
				<FieldDB>COUNTER</FieldDB>
				<FieldDB>CYC_ID</FieldDB>
			</FilterColumnsName>
			<SumColumns>
				<Fields>
					<Field>WEIGHT</Field>
					<FieldName>Weight</FieldName>
					<UnitDB>kg</UnitDB>
					<UnitASP>kg</UnitASP>
				</Fields>
			</SumColumns>
			<DBName>VIEW_CONFIRM_FLOW_LOGS</DBName>
			<OrderBy>START_TIME ASC</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>CONFIRM_FLOW_LOGS</ScreenName>
			<HasReportButton>false</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>false</HasSearchButton>
			<EnumPageNameCode>40</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>OPERATOR</FieldDB>
					<FieldName>DB_SYSTEM_USERS_NAME</FieldName>
					<ValueColumn>USER_NAME</ValueColumn>
					<From>SYSTEM_USERS</From>
					<SelectColumns>USER_NAME</SelectColumns>
					<OrderBy>USER_NAME</OrderBy>
					<Where>ID &lt;&gt; 1</Where>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MTR_ID</FieldDB>
					<FieldName>RECIPE_TYPE</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>META_RECIPES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>DESCRIPTION ASC</OrderBy>
					<Where>ID &lt;&gt; 100 AND ID &lt;&gt; 999</Where>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MTR_DESCRIPTION</FieldDB>
					<FieldName>RECIPE_TYPE</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>REC_ID</FieldDB>
					<FieldName>Recipe</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_RECIPES</From>
					<SelectColumns>DESCRIPTION, MTR_DESCRIPTION</SelectColumns>
					<OrderBy>MTR_ID, DESCRIPTION ASC</OrderBy>
					<Where>ID &lt;&gt; 1</Where>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>Recipe name</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>LAST_UPDATE</FieldDB>
					<FieldName>Last update</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>VERSION</FieldDB>
					<FieldName>Recipe version</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns>
				<column>
					<name>VIEW_RECIPE_HISTORY</name>
					<RequiredAccessLevel>Accessing</RequiredAccessLevel>
					<parameters>
						<fieldDB>MTR_ID</fieldDB>
						<fieldDB>ID</fieldDB>
					</parameters>
					<scriptName>..\ViewRecipeHistory.aspx?excol=true&amp;control=view&amp;pagename=RECIPE_PARAMS_&amp;menuname=MENU_RECIPES</scriptName>
					<imageSrc>image\report.png</imageSrc>
					<imageToolTip>View</imageToolTip>
					<Visible>true</Visible>
				</column>
			</extraColumns>
			<Header>
			</Header>
			<DBName>VIEW_RECIPE_LOGS</DBName>
			<OrderBy>MTR_ID, DESCRIPTION, VERSION DESC</OrderBy>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>RECIPE_LOGS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>43</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields />
			<ViewFields>
				<ViewField>
					<FieldDB>COUNTER</FieldDB>
					<FieldName>JOB</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>START_TIME</FieldDB>
					<FieldName>Start time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_TIME</FieldDB>
					<FieldName>Stop time</FieldName>
					<FieldType>TDate</FieldType>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_CODE</FieldDB>
					<FieldName>STOP_CODE</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MILL_FEEDINGS_STOP_CODES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<EnableKeyEntry>true</EnableKeyEntry>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_CODE</FieldDB>
					<FieldName>STOP_CODE</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>MILL_FEEDINGS_STOP_CODES</From>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<OrderBy>ID</OrderBy>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>DESCRIPTION</FieldDB>
					<FieldName>DB_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<EnableKeyEntry>true</EnableKeyEntry>
					<PropertyName>Product</PropertyName>
					<visibleField>true</visibleField>
				</ViewField>
				<ViewField>
					<FieldDB>FEEDINGS_STATUS_LED</FieldDB>
					<FieldName>FEEDINGS_STATUS_LED</FieldName>
					<FieldType>Led</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>RUNNING_INTERVAL</FieldDB>
					<FieldName>RUNNING_INTERVAL</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>MinMax</NumBound>
					<TimeSpanFormatDB>s</TimeSpanFormatDB>
					<TimeSpanFormatASP>dhms</TimeSpanFormatASP>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>STOP_INTERVAL</FieldDB>
					<FieldName>STOP_INTERVAL</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<Decimal>2</Decimal>
					<NumBound>MinMax</NumBound>
					<TimeSpanFormatDB>s</TimeSpanFormatDB>
					<TimeSpanFormatASP>dhms</TimeSpanFormatASP>
					<visibleField>true</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<SumColumns>
				<Fields>
					<Field>RUNNING_INTERVAL</Field>
					<FieldName>RUNNING_INTERVAL</FieldName>
					<TimeSpanFormatDB>s</TimeSpanFormatDB>
					<TimeSpanFormatASP>yMdhms</TimeSpanFormatASP>
				</Fields>
				<Fields>
					<Field>STOP_INTERVAL</Field>
					<FieldName>STOP_INTERVAL</FieldName>
					<TimeSpanFormatDB>s</TimeSpanFormatDB>
					<TimeSpanFormatASP>yMdhms</TimeSpanFormatASP>
				</Fields>
			</SumColumns>
			<Header>
			</Header>
			<OrderBy>ID DESC</OrderBy>
			<DBName>VIEW_MILL_FEEDINGS_LOG</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>MILL_FEEDINGS_LOG</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>false</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>45</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Auto</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>true</IsHidden>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>REC_ID</FieldDB>
					<FieldName>Recipe</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_RECIPES</From>
					<SelectColumns>DESCRIPTION, MTR_DESCRIPTION</SelectColumns>
					<OrderBy>MTR_DESCRIPTION, DESCRIPTION</OrderBy>
					<Where>MTR_ID = 2 OR MTR_ID = 3 OR MTR_ID = 4</Where>
					<AddNull>true</AddNull>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdRecipe</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
				<EditField>
					<FieldDB>PRO_ID</FieldDB>
					<FieldName>Product</FieldName>
					<ValueColumn>ID</ValueColumn>
					<From>VIEW_PRODUCTS</From>
					<SelectColumns>NAME, PT_TYPE</SelectColumns>
					<Where>PT_ID = 1 OR PT_ID = 2 OR PT_ID = 3</Where>
					<AddNull>true</AddNull>
					<OrderBy>PT_ID, NAME ASC</OrderBy>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>IdProduct</PropertyName>
					<updateField>true</updateField>
					<insertField>true</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>META_RECIPE_DESCRIPTION</FieldDB>
					<FieldName>META_RECIPE_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>MTR_ID</FieldDB>
					<FieldName>META_RECIPE_DESCRIPTION</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>DESCRIPTION</SelectColumns>
					<From>META_RECIPES</From>
					<Where>ID = 2 OR ID = 3 OR ID = 4</Where>
					<AddNull>true</AddNull>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>RECIPE_DESCRIPTION</FieldDB>
					<FieldName>RECIPE_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>REC_ID</FieldDB>
					<FieldName>RECIPE_DESCRIPTION</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>DESCRIPTION, MTR_DESCRIPTION</SelectColumns>
					<From>VIEW_RECIPES</From>
					<Where>MTR_ID = 2 OR MTR_ID = 3 OR MTR_ID = 4</Where>
					<AddNull>true</AddNull>
					<OrderBy>MTR_ID, DESCRIPTION ASC</OrderBy>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PRODUCT_TYPE_DESCRIPTION</FieldDB>
					<FieldName>PRODUCT_TYPE_DESCRIPTION</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PT_ID</FieldDB>
					<FieldName>PRODUCT_TYPE_DESCRIPTION</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>PT_TYPE</SelectColumns>
					<From>PRODUCT_TYPES</From>
					<Where>ID = 1 OR ID = 2 OR ID = 3</Where>
					<AddNull>true</AddNull>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PRODUCT_NAME</FieldDB>
					<FieldName>PRODUCT_NAME</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>PRO_ID</FieldDB>
					<FieldName>PRODUCT_NAME</FieldName>
					<FieldType>List</FieldType>
					<ValueColumn>ID</ValueColumn>
					<SelectColumns>NAME, PT_TYPE</SelectColumns>
					<From>VIEW_PRODUCTS</From>
					<Where>PT_ID = 1 OR PT_ID = 2 OR PT_ID = 3</Where>
					<AddNull>true</AddNull>
					<OrderBy>PT_ID, NAME ASC</OrderBy>
					<visibleField>false</visibleField>
					<queryField>true</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<OrderBy>MTR_ID, RECIPE_DESCRIPTION, PT_ID, PRODUCT_NAME</OrderBy>
			<DBName>VIEW_RECIPE_TO_PRODUCTS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>RECIPE_TO_PRODUCTS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>true</HasDelButton>
			<HasAddButton>true</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasImportFileButton>false</HasImportFileButton>
			<HasSearchButton>true</HasSearchButton>
			<EnumPageNameCode>68</EnumPageNameCode>
		</screen>
		<screen>
			<EditFields>
				<EditField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Id</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>NAME</FieldDB>
					<FieldName>NAME</FieldName>
					<FieldType>WString</FieldType>
					<IsReadOnly>true</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>Name</PropertyName>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
				<EditField>
					<FieldDB>LOG_LEVEL</FieldDB>
					<FieldName>LOG_LEVEL</FieldName>
					<FieldType>List</FieldType>
					<IsReadOnly>false</IsReadOnly>
					<IsHidden>false</IsHidden>
					<PropertyName>LogLevel</PropertyName>
					<CallEvent>EventThreadsLogLevel</CallEvent>
					<updateField>true</updateField>
					<insertField>false</insertField>
				</EditField>
			</EditFields>
			<ViewFields>
				<ViewField>
					<FieldDB>ID</FieldDB>
					<FieldName>Code</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>NAME</FieldDB>
					<FieldName>NAME</FieldName>
					<FieldType>String</FieldType>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>LOG_LEVEL</FieldDB>
					<FieldName>LOG_LEVEL</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>RUN_TIME_AVG</FieldDB>
					<FieldName>RUN_TIME_AVG</FieldName>
					<UnitDB>ms</UnitDB>
					<UnitASP>ms</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>3</Decimal>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>IDLE_TIME_AVG</FieldDB>
					<FieldName>IDLE_TIME_AVG</FieldName>
					<UnitDB>ms</UnitDB>
					<UnitASP>ms</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>3</Decimal>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>OPC_TOT_TIME_AVG</FieldDB>
					<FieldName>OPC_TOT_TIME_AVG</FieldName>
					<UnitDB>ms</UnitDB>
					<UnitASP>ms</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>3</Decimal>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>OPC_CONTENTION_TIME_AVG</FieldDB>
					<FieldName>OPC_CONTENTION_TIME_AVG</FieldName>
					<UnitDB>ms</UnitDB>
					<UnitASP>ms</UnitASP>
					<FieldType>Number</FieldType>
					<NumType>Double</NumType>
					<NumBound>MinMax</NumBound>
					<Decimal>3</Decimal>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>OPC_INTERACTIONS_AVG</FieldDB>
					<FieldName>OPC_INTERACTIONS_AVG</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
				<ViewField>
					<FieldDB>LOOPS</FieldDB>
					<FieldName>LOOPS</FieldName>
					<FieldType>Number</FieldType>
					<NumType>Integer</NumType>
					<NumBound>MinMax</NumBound>
					<visibleField>true</visibleField>
					<queryField>false</queryField>
				</ViewField>
			</ViewFields>
			<extraColumns />
			<Header>
			</Header>
			<AddMenuItemNames />
			<AddButtonParams />
			<FilterColumnsName />
			<OrderBy>ID</OrderBy>
			<DBName>THREADS</DBName>
			<LinesPerPage>20</LinesPerPage>
			<ScreenName>THREADS</ScreenName>
			<HasReportButton>true</HasReportButton>
			<HasSimplePrintButton>false</HasSimplePrintButton>
			<HasDelButton>false</HasDelButton>
			<HasAddButton>false</HasAddButton>
			<HasEditButton>true</HasEditButton>
			<HasSearchButton>false</HasSearchButton>
			<HasImportFileButton>false</HasImportFileButton>
			<EnumPageNameCode>71</EnumPageNameCode>
		</screen>
		<!-- Job-specific screens start below HERE -->
	</screens>
	<menus>
		<menu>
			<Name>MENU_STOCKS</Name>
			<Title>Giacenze</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/stocks.png</MenuIcon>
			<Color>#269229</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>Bins</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=CELLS_ARCHIVE&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_LOTS_TO_CELL_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=LOTS_TO_CELL&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_ALL_JOB_TO_CEL_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=ALL_JOB_TO_CEL&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_STOCKS_AMOUNT_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=STOCKS_AMOUNT&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_STOCK_DAILY_AMOUNTS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=STOCK_DAILY_AMOUNTS&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_STOCK_MANUAL_CORR_REQ_TITLE</ItemName>
					<ItemLink>new.aspx?control=new&amp;pagename=STOCK_MANUAL_CORR_REQ&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_STOCK_MANUAL_CORR_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=STOCK_MANUAL_CORR&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Flussi attivi</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=FLOW_LOGS_ACTIVE&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Archivio flussi</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=FLOW_LOGS&amp;menuname=MENU_STOCKS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
			<TitleMenus>
				<TitleMenu>Bins</TitleMenu>
				<TitleMenu>Stocks</TitleMenu>
				<TitleMenu>MAIN_FLOWS_TITLE</TitleMenu>
			</TitleMenus>
			<NLinkTitleMenus>
				<NLinkTitleMenu>0-3</NLinkTitleMenu>
				<NLinkTitleMenu>3-4</NLinkTitleMenu>
				<NLinkTitleMenu>7-2</NLinkTitleMenu>
			</NLinkTitleMenus>
		</menu>
		<menu>
			<Name>MENU_RECIPES</Name>
			<Title>Ricette</Title>
			<Visible>true</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/recipes.png</MenuIcon>
			<Color>#D4A121</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>MAIN_RECIPE_TO_PRODUCTS_TITLE</ItemName>
					<ItemLink>default.aspx?topmenuname=MENU_RECIPES&amp;control=view&amp;pagename=RECIPE_TO_PRODUCTS&amp;menuname=MENU_RECIPES</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_RECIPE_LOGS_TITLE</ItemName>
					<ItemLink>default.aspx?topmenuname=MENU_RECIPES&amp;control=view&amp;pagename=RECIPE_LOGS&amp;menuname=MENU_RECIPES</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
		</menu>
		<menu>
			<Name>MENU_CYCLES</Name>
			<Title>Cicli</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/cycles.png</MenuIcon>
			<Color>#39A69E</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>Cicli attivi</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=VIEW_PRODUCTION_PLAN_ACTIVE&amp;menuname=MENU_CYCLES</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
		</menu>
		<menu>
			<Name>MENU_ERP</Name>
			<Title>ERP</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/erp.png</MenuIcon>
			<Color>#39A69E</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>MAIN_PO_NUMBER_LIST_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PO_NUMBER_LIST&amp;menuname=MENU_ERP</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_PO_NUMBER_LIST_LOG_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PO_NUMBER_LIST_LOG&amp;menuname=MENU_ERP</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
			<TitleMenus />
			<NLinkTitleMenus />
		</menu>
		<menu>
			<Name>MENU_MAINTENANCE</Name>
			<Title>Manutenzione</Title>
			<Visible>true</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/maint.png</MenuIcon>
			<Color>#D47373</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>Modelli di macchine</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=EQUIPMENT_MODELS&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Macchine</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=EQUIPMENTS&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Procedure</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=MAINTENANCE_PROCEDURES&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Assegnazione procedure-macchine</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=MAINTENANCE_PROCEDURES_ASSIGNMENT&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Pezzi di ricambio</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SPARE_PARTS&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Assegnazione pezzi di ricambio</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=ASSIGNEMENT_SPARE_PARTS&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Avanzamento automatico</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=VIEW_EQUIPMENTS_MAINT_DATA_EQU&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Maintenance planning</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=MAINTENANCE_PLANNING&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Maintenance reports</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=MAINT_PROC_REPORT&amp;menuname=MENU_MAINTENANCE</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
		</menu>
		<menu>
			<Name>MENU_LOTS</Name>
			<Title>Archivi</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/archive.png</MenuIcon>
			<Color>#2B58DE</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>MAIN_CUSTOMERS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=CUSTOMERS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_SUPPLIERS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SUPPLIERS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_CARRIERS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=CARRIERS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Prodotti</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PRODUCTS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Tipi di prodotto</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PRODUCT_TYPES&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Gestione lotti</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=LOTS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>PARCELS_LIST</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PARCELS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>PARCELS_TO_BE_CONFIRMED_LIST</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PARCELS_TO_BE_CONFIRMED&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>SHIPMENTS_LIST</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SHIPMENTS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Rapporti di produzione</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PRODUCTION_REPORTS&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_MILL_FEEDINGS_LOG_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=MILL_FEEDINGS_LOG&amp;menuname=MENU_LOTS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
			<TitleMenus>
				<TitleMenu>Gestione Anagrafiche</TitleMenu>
				<TitleMenu>Gestione Prodotti</TitleMenu>
				<TitleMenu>Gestione lotti</TitleMenu>
				<TitleMenu>Gestione ricevimenti</TitleMenu>
				<TitleMenu>Gestione spedizioni</TitleMenu>
				<TitleMenu>Rapporti di produzione</TitleMenu>
			</TitleMenus>
			<NLinkTitleMenus>
				<NLinkTitleMenu>0-3</NLinkTitleMenu>
				<NLinkTitleMenu>3-2</NLinkTitleMenu>
				<NLinkTitleMenu>5-1</NLinkTitleMenu>
				<NLinkTitleMenu>6-2</NLinkTitleMenu>
				<NLinkTitleMenu>8-1</NLinkTitleMenu>
				<NLinkTitleMenu>9-2</NLinkTitleMenu>
			</NLinkTitleMenus>
		</menu>
		<menu>
			<Name>MENU_YIELDS</Name>
			<Title>Rese</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/yields.png</MenuIcon>
			<Color>#4682B4</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>MAIN_INSTANT_REPORT_TITLE</ItemName>
					<ItemLink>Reports.aspx?control=report&amp;pagename=INSTANT_REPORT&amp;typereport=InstantReport&amp;yieldsid=1&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_GRAPHICS_TITLE</ItemName>
					<ItemLink>Graphics.aspx?control=graph&amp;type_graph=1&amp;pagename=GRAPHICS&amp;plant_id=1&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_PERIODIC_YIELDS_TITLE</ItemName>
					<ItemLink>Reports.aspx?control=report&amp;pagename=PERIODIC_YIELDS&amp;typereport=PeriodicReport&amp;yieldsid=1&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_JOB_YIELDS_TITLE</ItemName>
					<ItemLink>Reports.aspx?control=report&amp;pagename=JOB_YIELDS&amp;typereport=JobReport&amp;yieldsid=3&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_GRAINS_SUMMATION_TITLE</ItemName>
					<ItemLink>Reports.aspx?control=report&amp;pagename=GRAINS_SUMMATION&amp;typereport=GrainsSummationReport&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_PRODUCTS_SUMMATION_TITLE</ItemName>
					<ItemLink>Reports.aspx?control=report&amp;pagename=PRODUCTS_SUMMATION&amp;typereport=ProductsSummationReport&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_ALARM_CONF_TITLE</ItemName>
					<ItemLink>default.aspx?control=alarmconf&amp;pagename=ALARM_CONF&amp;plant_id=1&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_VIEW_YIELDS_PRINTOUTS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=VIEW_YIELDS_PRINTOUTS&amp;menuname=MENU_YIELDS</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
		</menu>
		<menu>
			<Name>MENU_POWER</Name>
			<Title>Power</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/power.png</MenuIcon>
			<Color>#4682B4</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>Consumi correnti</ItemName>
					<ItemLink>Reports.aspx?control=report&amp;pagename=INSTANT_POWER_REPORT&amp;typereport=InstantPowerReport&amp;yieldsid=2&amp;menuname=MENU_POWER</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Trend Graph</ItemName>
					<ItemLink>Graphics.aspx?control=graph&amp;type_graph=1&amp;pagename=GRAPHICS&amp;plant_id=11&amp;menuname=MENU_POWER</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Periodic Consumption</ItemName>
					<ItemLink>Reports.aspx?control=report&amp;pagename=PERIODIC_POWER_REPORT&amp;typereport=PeriodicPowerReport&amp;yieldsid=2&amp;menuname=MENU_POWER</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
		</menu>
		<menu>
			<Name>MENU_ANOMALIES</Name>
			<Title>Avvisi</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/Anomalies.png</MenuIcon>
			<Color>slategray</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>Avvisi di processo</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=PROCESS_ANOMALIES&amp;menuname=MENU_ANOMALIES</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Avvisi di sistema</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SYSTEM_ANOMALIES&amp;menuname=MENU_ANOMALIES</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Informazioni di sistema</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SYSTEM_INFORMATIONS&amp;menuname=MENU_ANOMALIES</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
		</menu>
		<menu>
			<Name>MENU_SYSTEM</Name>
			<Title>Sistema</Title>
			<Visible>True</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/Configuration.png</MenuIcon>
			<Color>ORCHID</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>MAIN_SYSTEM_USERS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SYSTEM_USERS&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_SYSTEM_GROUPS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SYSTEM_GROUPS&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_SYSTEM_USERS_GROUPS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SYSTEM_USERS_GROUPS&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_SYSTEM_GROUPS_RIGHTS_TITLE</ItemName>
					<ItemLink>default.aspx?control=SystemGroupsRights&amp;pagename=SYSTEM_GROUPS_RIGHTS&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Configurazione impianto</ItemName>
					<ItemLink>default.aspx?control=plantconf&amp;pagename=plantconf&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Configurazione Batch Size</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=SCALES_BATCH_SIZE&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_TASK_SCHEDULER_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=TASK_SCHEDULER&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_TASK_AUTOMATIONS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=TASK_AUTOMATIONS&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>MAIN_THREADS_TITLE</ItemName>
					<ItemLink>default.aspx?control=view&amp;pagename=THREADS&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>false</Visible>
				</MenuItem>
				<MenuItem>
					<ItemName>Informazioni di sistema</ItemName>
					<ItemLink>default.aspx?control=status&amp;pagename=status&amp;menuname=MENU_SYSTEM</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
			<TitleMenus>
				<TitleMenu>Gestione Utenti</TitleMenu>
				<TitleMenu>Gestione Pagine</TitleMenu>
				<TitleMenu>Configurazione impianto</TitleMenu>
				<TitleMenu>Configurazione Batch Size</TitleMenu>
				<TitleMenu>MAIN_THREADS_TITLE</TitleMenu>
				<TitleMenu>Informazioni di sistema</TitleMenu>
			</TitleMenus>
			<NLinkTitleMenus>
				<NLinkTitleMenu>0-3</NLinkTitleMenu>
				<NLinkTitleMenu>3-1</NLinkTitleMenu>
				<NLinkTitleMenu>4-1</NLinkTitleMenu>
				<NLinkTitleMenu>5-3</NLinkTitleMenu>
				<NLinkTitleMenu>8-1</NLinkTitleMenu>
				<NLinkTitleMenu>9-1</NLinkTitleMenu>
			</NLinkTitleMenus>
		</menu>
		<menu>
			<Name>MENU_HELP</Name>
			<Title>Guida</Title>
			<Visible>true</Visible>
			<ParentMenuName />
			<IsTopLevel>true</IsTopLevel>
			<MenuIcon>./image/navigation/help.png</MenuIcon>
			<Color>red</Color>
			<MenuItems>
				<MenuItem>
					<ItemName>Guida</ItemName>
					<ItemLink>default.aspx?control=pdf&amp;pagename=HELP&amp;menuname=MENU_HELP&amp;LINK=documents\Management@mill_ReferenceManual.pdf</ItemLink>
					<Visible>true</Visible>
				</MenuItem>
			</MenuItems>
		</menu>
	</menus>
	<Languages>
		<CustomerLanguage>1</CustomerLanguage>
		<currentLanguage>1</currentLanguage>
		<DbLanguages>true</DbLanguages>
		<Language>
			<Type>Francese</Type>
			<CurrentInfo>fr</CurrentInfo>
			<ImgFlag>flags\32\France.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
		<Language>
			<Type>Inglese</Type>
			<CurrentInfo>en-gb</CurrentInfo>
			<ImgFlag>flags\32\United Kingdom(Great Britain).png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>true</Visible>
		</Language>
		<Language>
			<Type>Italiano</Type>
			<CurrentInfo>it</CurrentInfo>
			<ImgFlag>flags\32\Italy.png</ImgFlag>
			<FormatDateTimeCalendar>dd/MM/yyyy HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>dd/MM/yyyy HH:mm:ss</FormatDateTime>
			<FormatDate>dd/MM/yyyy</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
		<Language>
			<Type>Spagnolo</Type>
			<CurrentInfo>es</CurrentInfo>
			<ImgFlag>flags\32\Spain.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>true</Visible>
		</Language>
		<Language>
			<Type>Russo</Type>
			<CurrentInfo>ru</CurrentInfo>
			<ImgFlag>flags\32\Russian Federation.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
		<Language>
			<Type>Portoghese</Type>
			<CurrentInfo>pt-br</CurrentInfo>
			<ImgFlag>flags\32\Brazil.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
		<Language>
			<Type>Tedesco</Type>
			<CurrentInfo>de</CurrentInfo>
			<ImgFlag>flags\32\Germany.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
		<Language>
			<Type>Indiano</Type>
			<CurrentInfo>hi</CurrentInfo>
			<ImgFlag>flags\32\India.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
		<Language>
			<Type>Arabo</Type>
			<CurrentInfo>ar-sa</CurrentInfo>
			<ImgFlag>flags\32\Saudi Arabia.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
		<Language>
			<Type>Altro</Type>
			<CurrentInfo>en-gb</CurrentInfo>
			<ImgFlag>flags\32\Olimpic Movement.png</ImgFlag>
			<FormatDateTimeCalendar>yyyy-MM-dd HH:mm</FormatDateTimeCalendar>
			<FormatDateTime>yyyy-MM-dd HH:mm:ss</FormatDateTime>
			<FormatDate>yyyy-MM-dd</FormatDate>
			<FormatTime>HH:mm:ss</FormatTime>
			<Visible>false</Visible>
		</Language>
	</Languages>
	<Charts>
		<TemplateName>SkyBlue.xml</TemplateName>
	</Charts>
	<Commessa>200000</Commessa>
</Interface>