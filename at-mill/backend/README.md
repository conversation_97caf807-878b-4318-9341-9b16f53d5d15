# OCRIM AtMill Backend

Backend .NET Core 9 per il progetto OCRIM AtMill Revamping.

## 📁 Struttura Directory

```
at-mill/backend/
├── config/
│   └── config.xml          # File di configurazione UI dal sistema AS-IS
├── database/
│   ├── 200000CORE.bak      # Backup database SQL Server dal sistema AS-IS
│   ├── setup-database.sh   # Script setup database (Linux/Mac)
│   └── setup-database.ps1  # Script setup database (Windows)
└── README.md               # Questo file
```

## 🗄️ Database

### File Backup
- **Nome**: `200000CORE.bak`
- **Origine**: Sistema AS-IS ATMILL_CORE_2022
- **Tipo**: SQL Server backup completo
- **Utilizzo**: Restore per ambiente di sviluppo

### Setup Database con Docker

#### Linux/Mac
```bash
cd database
./setup-database.sh
```

#### Windows (PowerShell)
```powershell
cd database
.\setup-database.ps1
```

### Informazioni Connessione
Dopo il setup, il database sarà disponibile con:
- **Server**: localhost,1433
- **Database**: 200000CORE
- **Username**: sa
- **Password**: ****************
- **Container**: ocrim-sqlserver

### Connection String .NET
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost,1433;Database=200000CORE;User Id=sa;Password=****************;TrustServerCertificate=true;"
  }
}
```

## ⚙️ Configurazione UI

### File config.xml
- **Origine**: Sistema AS-IS ATMILL_CORE_2022
- **Dimensione**: ~21.000 righe
- **Funzione**: Configurazione dinamica dell'interfaccia utente
- **Contenuto**:
  - Definizione schermate (Screens)
  - Menu di navigazione (Menus)
  - Supporto multilingua (Languages)
  - Campi e validazioni
  - Permessi e visibilità

### Struttura XML Principale

#### Screens
Definisce le schermate per ogni entità:
```xml
<screen>
    <ScreenName>SYSTEM_USERS</ScreenName>
    <DBName>SYSTEM_USERS</DBName>
    <EditFields>
        <EditField>
            <FieldDB>USERNAME</FieldDB>
            <FieldName>Username</FieldName>
            <FieldType>WString</FieldType>
        </EditField>
    </EditFields>
</screen>
```

#### Menus
Definisce la navigazione:
```xml
<menu>
    <n>MENU_STOCKS</n>
    <Title>Giacenze</Title>
    <MenuIcon>./image/navigation/stocks.png</MenuIcon>
    <Color>#269229</Color>
    <MenuItems>
        <MenuItem>
            <ItemName>Bins</ItemName>
            <ItemLink>default.aspx?control=view&amp;pagename=CELLS_ARCHIVE</ItemLink>
        </MenuItem>
    </MenuItems>
</menu>
```

## 🎯 Prossimi Passi

### 1. Setup Progetto .NET Core 9
```bash
# Creare soluzione
dotnet new sln -n AtMill

# Creare progetti
dotnet new webapi -n AtMill.API
dotnet new classlib -n AtMill.Core
dotnet new classlib -n AtMill.Infrastructure
dotnet new classlib -n AtMill.Shared

# Aggiungere progetti alla soluzione
dotnet sln add AtMill.API AtMill.Core AtMill.Infrastructure AtMill.Shared
```

### 2. Analisi Database
- Connessione al database ripristinato
- Reverse engineering con Entity Framework Core
- Generazione modelli per entità principali

### 3. Parser Configurazione XML
- Analisi struttura config.xml
- Creazione parser per schermate dinamiche
- Implementazione sistema di configurazione

### 4. API Development
- Implementazione API REST per entità core
- Sistema di autenticazione JWT
- Validazioni business

## 🔧 Comandi Utili Database

### Gestione Container
```bash
# Avvia database
./setup-database.sh

# Ferma container
./setup-database.sh stop

# Riavvia container
./setup-database.sh start

# Rimuovi container
./setup-database.sh remove

# Status container
./setup-database.sh status
```

### Connessione Diretta
```bash
# Dopo il setup, usa lo script generato
./connect-db.sh
```

### Query di Test
```sql
-- Verifica tabelle principali
SELECT name FROM sys.tables ORDER BY name;

-- Conta record nelle tabelle principali
SELECT 
    'SYSTEM_USERS' as Tabella, COUNT(*) as Records FROM SYSTEM_USERS
UNION ALL
SELECT 
    'PRODUCTS' as Tabella, COUNT(*) as Records FROM PRODUCTS
UNION ALL
SELECT 
    'EQUIPMENTS' as Tabella, COUNT(*) as Records FROM EQUIPMENTS;
```

## 📚 Risorse

### Documentazione
- [Features Analysis](../docs/features.md) - Analisi funzionalità sistema AS-IS
- [Project Plan](../docs/plan.md) - Piano di migrazione completo
- [Task Management](../tasks/) - Gestione task e backlog

### File Chiave AS-IS
- **Database**: `as-is/ATMILL_CORE_2022/target/Web/200000CORE.bak`
- **Config XML**: `as-is/ATMILL_CORE_2022/target/Web/config/config.xml`
- **Applicazione**: `as-is/ATMILL_CORE_2022/target/Web/`

---

*Creato per il progetto OCRIM AtMill Revamping - 2025*
