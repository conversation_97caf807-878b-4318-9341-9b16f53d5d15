# Analisi Features Sistema OCRIM AtMill AS-IS

## 📋 Sistema di Configurazione XML

### Struttura del file config.xml
Il sistema utilizza un file XML (`config/config.xml`) per configurare dinamicamente:

#### 1. **Screens (Schermate)**
- **Definizione dinamica** di schermate per gestione entità
- **EditFields**: Campi modificabili con validazioni
- **ViewFields**: Campi di visualizzazione e query
- **Proprietà supportate**:
  - `FieldType`: Number, WString, String, list, DateTime
  - `NumType`: Integer, Decimal
  - `IsReadOnly`, `IsHidden`, `AddNull`
  - `updateField`, `insertField`
- **Funzionalità per schermata**:
  - `HasAddButton`, `HasEditButton`, `HasDelButton`
  - `HasReportButton`, `HasSearchButton`
  - `HasImportFileButton`
  - `LinesPerPage` per paginazione

#### 2. **Menus (Menu di navigazione)**
- **Menu gerarchici** con icone e colori
- **MenuItem** con link dinamici
- **Visibilità condizionale** per permessi
- **TitleMenus** e **NLinkTitleMenus** per raggruppamenti

#### 3. **Languages (Multilingua)**
- **Supporto multilingua** (Italiano, Inglese, Francese, Russo, Portoghese)
- **Formati data/ora** localizzati
- **Flag e CurrentInfo** per ogni lingua

## 🏭 Moduli Funzionali Principali

### 1. **MENU_STOCKS (Gestione Giacenze)**
- **Bins**: Gestione celle di stoccaggio
- **Stocks**: Inventario e giacenze
- **Flow Logs**: Registrazione flussi materiali
- **Archivio flussi**: Storico movimentazioni

### 2. **MENU_MAINTENANCE (Manutenzione)**
- **Equipment Models**: Modelli di macchine
- **Equipments**: Gestione macchine/attrezzature
- **Maintenance Procedures**: Procedure di manutenzione
- **Spare Parts**: Gestione ricambi
- **Maintenance Planning**: Pianificazione interventi

### 3. **MENU_RECIPES (Ricette)**
- **Recipe Management**: Gestione ricette di produzione
- **Recipe to Products**: Associazione ricette-prodotti
- **Recipe Parameters**: Parametri di processo

### 4. **MENU_PRODUCTION (Produzione)**
- **Production Plans**: Piani di produzione
- **Production Reports**: Report di produzione
- **Job Management**: Gestione commesse
- **Cycles**: Gestione cicli produttivi

### 5. **MENU_SYSTEM (Sistema)**
- **System Users**: Gestione utenti
- **System Groups**: Gestione gruppi/ruoli
- **System Users Groups**: Associazioni utenti-gruppi
- **System Parameters**: Parametri di sistema
- **Configuration**: Configurazione impianto

### 6. **MENU_ANOMALIES (Anomalie)**
- **Process Anomalies**: Avvisi di processo
- **System Anomalies**: Avvisi di sistema
- **System Informations**: Informazioni di sistema

## 🗄️ Entità del Dominio Identificate

### Core Entities
- **SYSTEM_USERS**: Utenti del sistema
- **SYSTEM_GROUPS**: Gruppi/ruoli
- **SYSTEM_PARAMETERS**: Parametri di configurazione

### Production Entities
- **EQUIPMENTS**: Macchine e attrezzature
- **EQUIPMENT_MODELS**: Modelli di macchine
- **PRODUCTS**: Prodotti
- **RECIPES**: Ricette di produzione
- **PRODUCTION_PLANS**: Piani di produzione
- **PRODUCTION_REPORTS**: Report di produzione
- **CYCLES**: Cicli produttivi

### Inventory Entities
- **STOCKS**: Giacenze
- **BINS**: Celle di stoccaggio
- **FLOW_LOGS**: Log dei flussi
- **LOTS**: Lotti di materiale

### Maintenance Entities
- **MAINTENANCE_PROCEDURES**: Procedure di manutenzione
- **SPARE_PARTS**: Ricambi
- **MAINTENANCE_PLANNING**: Pianificazione manutenzione

### External Integration
- **COMMAND_INTERFACE**: Interfaccia comandi PLM
- **PLC_STATUS**: Stato PLC

## 🔧 Funzionalità Tecniche

### 1. **Sistema di Permessi**
- **Controllo accessi granulare** per ogni schermata
- **Visibilità condizionale** di menu e funzioni
- **Gruppi utenti** con permessi specifici

### 2. **Reporting System**
- **Report dinamici** configurabili via XML
- **Instant Reports**: Report istantanei
- **Periodic Reports**: Report periodici
- **Export capabilities**: Esportazione dati

### 3. **Import/Export**
- **Import File Button** per caricamento dati
- **Export functions** per estrazione dati
- **Batch operations** per operazioni massive

### 4. **Unità di Misura**
- **Supporto multiple unità**: Kg, Ton, Lbs, Cwt, L
- **Conversioni automatiche**
- **Decimali configurabili** per unità

### 5. **Integrazione PLM**
- **Command Interface** per comunicazione
- **PLC Status monitoring**
- **Real-time data exchange**

## 🔄 Workflow di Business

### 1. **Production Workflow**
1. Creazione **Production Plan**
2. Assegnazione **Recipe** al piano
3. Esecuzione **Job** di produzione
4. Generazione **Production Report**
5. Aggiornamento **Stocks**

### 2. **Maintenance Workflow**
1. Definizione **Maintenance Procedure**
2. **Planning** interventi
3. Assegnazione **Spare Parts**
4. Esecuzione manutenzione
5. Report completamento

### 3. **Inventory Workflow**
1. Registrazione **Flow Logs**
2. Aggiornamento **Stocks**
3. Gestione **Bins**
4. Controllo **Lots**

## 🎯 Priorità di Migrazione

### Fase 1 - Core System (Critica)
1. **Sistema di configurazione XML**
2. **Autenticazione e autorizzazione**
3. **Gestione utenti e gruppi**
4. **Parametri di sistema**

### Fase 2 - Production Core (Alta)
1. **Products management**
2. **Recipes management**
3. **Equipment management**
4. **Basic reporting**

### Fase 3 - Operations (Media)
1. **Production planning**
2. **Stocks management**
3. **Flow logs**
4. **Maintenance procedures**

### Fase 4 - Advanced (Bassa)
1. **Advanced reporting**
2. **PLM integration**
3. **Anomalies management**
4. **Advanced analytics**

## 📊 Complessità Tecnica

### Alto Impatto
- **XML Configuration Parser**: Sistema complesso di parsing
- **Dynamic UI Generation**: Generazione dinamica interfacce
- **PLM Integration**: Integrazione sistemi esterni
- **Multi-language Support**: Supporto multilingua

### Medio Impatto
- **CRUD Operations**: Operazioni standard su entità
- **Reporting System**: Sistema di report
- **User Management**: Gestione utenti
- **Validation System**: Sistema di validazioni

### Basso Impatto
- **Static Data**: Dati di configurazione statici
- **Simple Entities**: Entità semplici
- **Basic UI Components**: Componenti UI base

---

*Documento creato il: 2025-06-30*  
*Versione: 1.0*  
*Analisi basata su: config.xml, codice VB.NET esistente*
