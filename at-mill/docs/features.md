# Features Sistema OCRIM AtMill AS-IS

## 🎯 Overview
Il sistema OCRIM AtMill è una piattaforma per la gestione di impianti molitori che utilizza un approccio **XML-driven** per la configurazione dinamica dell'interfaccia utente e delle funzionalità.

## 🔧 Sistema di Configurazione XML

### File config.xml
Il cuore del sistema è il file `config/config.xml` che definisce:

#### **Screens (Schermate)**
Ogni schermata è configurata con:
- **DBName**: Tabella/vista database di riferimento
- **ScreenName**: Nome identificativo della schermata
- **EditFields**: Campi modificabili con proprietà:
  - `FieldDB`: Nome campo database
  - `FieldName`: Nome visualizzato
  - `FieldType`: Number, WString, String, list, DateTime
  - `IsReadOnly`, `IsHidden`, `AddNull`
  - `updateField`, `insertField`: Controllo operazioni CRUD
- **ViewFields**: Campi di visualizzazione e query
- **Funzionalità**: HasAdd<PERSON>utton, HasEditButton, HasDelButton, HasReportButton, HasSearchButton, HasImportFileButton
- **Paginazione**: LinesPerPage

#### **Menus (Navigazione)**
Sistema di menu gerarchico con:
- **Menu principali** con icone, colori e visibilità
- **MenuItem** con link dinamici alle schermate
- **Permessi** basati su visibilità condizionale
- **Raggruppamenti** tramite TitleMenus

#### **Languages (Multilingua)**
Supporto per:
- Italiano, Inglese, Francese, Russo, Portoghese
- Formati data/ora localizzati
- Flag e identificatori cultura

## 🏭 Moduli Funzionali

### 1. **STOCKS (Gestione Giacenze)**
**Entità principali:**
- `BINS`: Celle di stoccaggio con capacità e stato
- `STOCKS`: Inventario corrente per prodotto/cella
- `FLOW_LOGS`: Registrazione movimentazioni materiali
- `LOTS`: Gestione lotti con tracciabilità

**Funzionalità:**
- Monitoraggio giacenze in tempo reale
- Tracciabilità movimentazioni
- Gestione capacità celle
- Report giacenze e movimenti

### 2. **MAINTENANCE (Manutenzione)**
**Entità principali:**
- `EQUIPMENT_MODELS`: Modelli di macchine
- `EQUIPMENTS`: Macchine/attrezzature installate
- `MAINTENANCE_PROCEDURES`: Procedure di manutenzione
- `SPARE_PARTS`: Ricambi e componenti
- `MAINTENANCE_PLANNING`: Pianificazione interventi

**Funzionalità:**
- Gestione anagrafica macchine
- Pianificazione manutenzioni preventive
- Gestione ricambi e magazzino
- Storico interventi
- Report manutenzioni

### 3. **PRODUCTION (Produzione)**
**Entità principali:**
- `PRODUCTS`: Prodotti finiti e intermedi
- `RECIPES`: Ricette di produzione
- `PRODUCTION_PLANS`: Piani di produzione
- `PRODUCTION_REPORTS`: Report di produzione
- `CYCLES`: Cicli produttivi
- `JOBS`: Commesse di lavoro

**Funzionalità:**
- Gestione ricette e parametri processo
- Pianificazione produzione
- Monitoraggio avanzamento commesse
- Report produzione e rese
- Controllo qualità

### 4. **SYSTEM (Sistema)**
**Entità principali:**
- `SYSTEM_USERS`: Utenti del sistema
- `SYSTEM_GROUPS`: Gruppi/ruoli utenti
- `SYSTEM_USERS_GROUPS`: Associazioni utenti-gruppi
- `SYSTEM_PARAMETERS`: Parametri di configurazione

**Funzionalità:**
- Gestione utenti e autenticazione
- Sistema di permessi granulare
- Configurazione parametri sistema
- Audit trail e logging

### 5. **ANOMALIES (Gestione Anomalie)**
**Entità principali:**
- `PROCESS_ANOMALIES`: Anomalie di processo
- `SYSTEM_ANOMALIES`: Anomalie di sistema
- `SYSTEM_INFORMATIONS`: Informazioni sistema

**Funzionalità:**
- Monitoraggio anomalie processo
- Alert e notifiche
- Dashboard stato sistema
- Report anomalie

## 🔗 Integrazione PLM

### Command Interface
- `COMMAND_INTERFACE`: Tabella per comandi PLM
- `PLC_STATUS`: Monitoraggio stato PLC
- Comunicazione real-time con sistemi controllo
- Sincronizzazione dati processo

### Funzionalità PLM
- Invio comandi a PLC
- Ricezione dati processo
- Monitoraggio stato macchine
- Allarmi e notifiche

## 📊 Sistema di Reporting

### Tipi di Report
- **Instant Reports**: Report istantanei
- **Periodic Reports**: Report periodici
- **Shipment Reports**: Report spedizioni
- **Mixing Reports**: Report miscelazioni
- **Production Reports**: Report produzione

### Funzionalità Reporting
- Generazione automatica report
- Export in vari formati
- Parametrizzazione report
- Scheduling automatico

## 🔒 Sistema di Sicurezza

### Autenticazione
- Login utente/password
- Sessioni gestite
- Timeout automatico

### Autorizzazione
- Permessi granulari per schermata
- Controllo operazioni CRUD
- Visibilità condizionale menu
- Gruppi utenti con ruoli

## 🌐 Caratteristiche Tecniche

### Unità di Misura
- Supporto multiple unità: Kg, Ton, Lbs, Cwt, L
- Conversioni automatiche
- Decimali configurabili

### Import/Export
- Import file CSV/Excel
- Export dati in vari formati
- Operazioni batch
- Validazione dati import

### Configurabilità
- Schermate completamente configurabili via XML
- Campi dinamici con validazioni
- Menu personalizzabili
- Workflow configurabili

## 🔄 Workflow Principali

### Workflow Produzione
1. **Pianificazione**: Creazione Production Plan
2. **Preparazione**: Assegnazione Recipe e risorse
3. **Esecuzione**: Avvio Job di produzione
4. **Monitoraggio**: Controllo avanzamento
5. **Completamento**: Generazione Production Report
6. **Aggiornamento**: Update Stocks e giacenze

### Workflow Manutenzione
1. **Pianificazione**: Scheduling interventi
2. **Preparazione**: Assegnazione risorse e ricambi
3. **Esecuzione**: Intervento manutenzione
4. **Registrazione**: Report completamento
5. **Aggiornamento**: Update stato equipment

### Workflow Giacenze
1. **Movimentazione**: Registrazione Flow Logs
2. **Aggiornamento**: Update Stocks
3. **Controllo**: Verifica capacità Bins
4. **Tracciabilità**: Gestione Lots
5. **Reporting**: Report giacenze

## 🎯 Priorità Migrazione

### **Fase 1 - Foundation (Critica)**
- Sistema configurazione XML
- Autenticazione/autorizzazione
- Gestione utenti e gruppi
- Parametri sistema

### **Fase 2 - Core Business (Alta)**
- Products management
- Equipment management
- Basic CRUD operations
- Core reporting

### **Fase 3 - Operations (Media)**
- Production planning
- Stocks management
- Maintenance procedures
- Flow logs

### **Fase 4 - Advanced (Bassa)**
- PLM integration
- Advanced reporting
- Anomalies management
- Analytics dashboard

---

*Documento creato: 2025-06-30*  
*Versione: 1.0*  
*Fonte: Analisi config.xml e codice AS-IS*
